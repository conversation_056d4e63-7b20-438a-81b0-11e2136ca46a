# 上线失败 SOP
```
> ssh ali  
> cd /data/code/image2doc-processor-server  
> docker build -t "registry.cn-shanghai.aliyuncs.com/sigmalove/xdoc_server:2024-02-29-10-45" .  
> docker run -it a5e4299687c4 /bin/bash  
> docker images  
> npm run restart  
> docker login --username=<EMAIL> registry.cn-shanghai.aliyuncs.com  
> docker push registry.cn-shanghai.aliyuncs.com/sigmalove/xdoc_server:2024-02-29-10-45  

@运明纯

# 安装依赖
需要注意，由于 node-gyp 的兼容性问题，这里要使用 node10

# rpc-master
rpc控制中心，图像流程控制中心
一个更加复杂的rpc负载均衡器

# @todo
1.api登录权限控制  
2.登录账户的操作记录  
3.动态扩容自动化方案，rbs暂时不支持自动化，需要手动修改worker数目重启  

## 测试 ZeroRPC 连接

在应用启动前，可以使用以下命令测试 ZeroRPC 连接是否正常：

```bash
# 简单连接测试
npm run test-zerorpc

# 带方法调用的完整测试
npm run test-zerorpc-with-method
```

这些测试脚本会检查 ZeroRPC 服务器是否可访问，对于诊断远程过程调用相关问题非常有用。

应用启动时也会自动检查 ZeroRPC 连接，但不会中断启动过程。

