/**
 * @file 项目统一入口页面
 * <AUTHOR>
 */

'use strict';
import { Application } from 'egg';
import loadRedisCommand from './app/core/loadRedisCommand';
import { createMongo, createMongoIndex } from './mongo/mongo';
import * as zerorpc from 'zerorpc';

/**
 * 测试 zerorpc 连接是否可用
 * @param app Egg.js 应用实例
 */
async function testZerorpcAvailability(app: Application) {
  const { config, logger } = app;

  if (!config.remoteRpcServer || !config.remoteRpcServer.imageCrop) {
    logger.warn('未找到 zerorpc 配置，跳过可用性测试');
    return;
  }

  const rpcConfig = config.remoteRpcServer.imageCrop;
  logger.info(`正在测试 zerorpc 连接... 主机: ${rpcConfig.host}, 方法: ${rpcConfig.method}`);

  try {
    // 创建 zerorpc 客户端
    const client = new zerorpc.Client({
      timeout: 5, // 设置较短的超时时间用于测试
      heartbeat: null,
      heartbeatInterval: 20000,
    });

    // 连接到服务器
    client.connect(rpcConfig.host);

    // 添加错误处理
    let connectionError = false;
    client.on('error', (error) => {
      connectionError = true;
      logger.error('zerorpc 连接错误:', error);
    });

    // 等待一段时间以检查连接是否成功
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 关闭客户端连接
    client.close();

    if (connectionError) {
      logger.error('zerorpc 可用性测试失败，但应用将继续启动。请检查配置和连接。');
    } else {
      logger.info('zerorpc 可用性测试通过，连接正常');
    }
  } catch (error) {
    logger.error('测试 zerorpc 连接时出错:', error);
    logger.warn('应用将继续启动，但 zerorpc 相关功能可能无法正常工作');
  }
}

export default (app: Application) => {
  // 应用会等待这个函数执行完成才启动
  app.beforeStart(async() => {
    const { redis, config } = app;

    // 测试 zerorpc 是否可用
    await testZerorpcAvailability(app);

    /*
     * await app.model.sync({ force: false });
     * 加载redis的自定义命令
     */
    await loadRedisCommand(app);
    // 删除定时器的单一串行锁
    const commandList: string[][] = [];
    Object.keys(config.scheduleLockKey).forEach((key) => {
      commandList.push(['del', config.scheduleLockKey[key]]);
    });
    await redis.pipeline(commandList).exec();
    app.mongo = await createMongo(config.mongo.client);
    await createMongoIndex(app.mongo.db);
  });
  // 设置路由前缀
  app.router.prefix('/api');
};
