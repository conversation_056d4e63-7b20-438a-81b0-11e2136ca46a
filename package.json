{"name": "image2doc-processor-server", "version": "1.0.0", "description": "nothing", "private": true, "dependencies": {"@types/string.prototype.matchall": "^4.0.1", "ali-oss": "^4.11.4", "archiver": "^3.1.1", "axios": "^0.27.2", "cheerio": "^1.0.0-rc.3", "crypto": "^1.0.1", "dateformat": "^3.0.3", "docx": "git+ssh://*************:sigmaai/docx.git#e9f8a955f9d48694d20bbb218e13ee9d4412e55c", "egg": "^2.0.0", "egg-redis": "1.0.2", "egg-router-plus": "^1.2.1", "egg-scripts": "^2.1.0", "egg-sequelize": "^4.2.0", "eslint-config-hexin": "git+ssh://*************:sigmaai/eslint-config-hexin.git#75bc0a86c03054768b311599eaf1d38d72ad8558", "fuzzball": "^2.2.2", "he": "^1.2.0", "himalaya": "^1.1.0", "iconv-lite": "^0.5.0", "jsdom": "^15.0.0", "jszip": "^3.2.2", "katex": "^0.11.1", "lodash": "^4.17.15", "mongodb": "^3.4.1", "mysql2": "^1.7.0", "node-pandoc": "^0.3.0", "parameter": "^3.6.0", "pdf-lib": "^1.17.1", "request": "^2.88.2", "rimraf": "^2.6.2", "sha1": "^1.1.1", "sharp": "^0.30.7", "string.prototype.matchall": "^4.0.8", "urllib": "^2.34.2", "uuid": "^3.2.1", "xlsx": "^0.15.4", "xml-js": "^1.6.11", "zerorpc": "^0.9.8"}, "devDependencies": {"@types/dateformat": "^3.0.0", "@types/ioredis": "4.0.4", "@types/mocha": "^5.2.2", "@types/node": "^10.3.3", "@types/rimraf": "^2.0.2", "@types/sequelize": "^4.27.48", "@types/supertest": "^2.0.4", "@typescript-eslint/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^4.4.0", "autod": "^3.0.1", "autod-egg": "^1.1.0", "egg-bin": "^4.11.0", "egg-ci": "^1.8.0", "egg-mock": "^3.17.2", "egg-ts-helper": "1.25.2", "eslint": "^7.5.0", "eslint-plugin-tslint": "^3.1.0", "husky": "^3.1.0", "lint-staged": "^9.5.0", "sequelize-cli": "^5.4.0", "tslib": "^1.9.2", "tslint": "^5.10.0", "tslint-config-airbnb": "^5.9.2", "typescript": "~3.9.6", "webstorm-disable-index": "^1.2.0"}, "engines": {"node": ">=8.9.0"}, "scripts": {"debug-dev": "npm run clean && export EGG_SERVER_ENV=local; egg-bin debug --ts tsconfig.json", "debug-local": "export EGG_SERVER_ENV=local; export NODE_ENV=test; export XDOC_DONT_AUTH=true; egg-bin debug --env=local --ts tsconfig.json", "_start-local": "eggctl start --env=local --workers=1 --title=xdoc-server", "_start-dev": "eggctl start --env=dev --daemon --workers=2 --title=image2doc-processor-server", "_start-prod": "eggctl start --env=prod --daemon --title=image2doc-processor-server", "_start-pre": "eggctl start --env=pre --workers=1 --title=image2doc-processor-server", "stop": "eggctl stop --title=image2doc-processor-server", "stop-task": "eggctl stop --title=xdoc-task", "start": "npm run clean && npm run tsc && npm run _start-prod", "restart": "npm run clean && npm run tsc && npm run stop && npm run _start-prod", "start-local": "npm run clean && npm run tsc && npm run _start-local", "restart-local": "npm run clean && npm run tsc && npm run stop && npm run _start-local", "start-pre": "npm run clean && npm run tsc && npm run _start-pre", "restart-pre": "npm run clean && npm run tsc && npm run stop && npm run _start-pre", "start-dev": "npm run clean && npm run tsc && npm run _start-dev", "restart-dev": "npm run clean && npm run tsc && npm run stop && npm run _start-dev", "test": "npm run lint && npm run test-local", "dev": "npm run clean && npm run tsc && npm run _start-local", "tsc": "ets && tsc -p tsconfig.json", "lint": "tslint --project . -c tslint.json --fix", "lint-eslint": "npx eslint --fix --ext .js,.ts,.tsx --ignore-path .gitignore .", "clean": "ets clean", "ets": "ets", "ci": "npm run lint && npm run cov && npm run tsc", "autod": "autod", "migrate:init": "npx sequelize init:migrations", "migrate:new": "npx sequelize migration:generate --name", "migrate:up": "npm run tsc && npx sequelize db:migrate", "migrate:down": "npm run tsc && npx sequelize db:migrate:undo", "upload-dev": "ssh root@192.168.1.15 'source /etc/profile && cd /workstation/deploy && ./test_deploy.sh -n image2doc-processor-server -b master'", "_start-server-prod": "eggctl start --env=prod --title=xdoc-server", "start-server": "npm run clean && npm run tsc && npm run _start-server-prod", "restart-server": "npm run clean && npm run tsc && npm run stop && npm run _start-server-prod", "_start-task-pre": "eggctl start --env=pre --daemon --title=xdoc-task", "start-task": "npm run clean && npm run tsc && npm run _start-task-pre", "restart-task": "npm run clean && npm run tsc && npm run stop-task && npm run _start-task-pre", "build": "npm run clean && npm run tsc && npm run stop"}, "ci": {"version": "8"}, "repository": {"type": "git", "url": ""}, "author": "<EMAIL>", "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"app/**/*.ts": "tslint"}}