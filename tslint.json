{"extends": "tslint-config-airbnb", "rules": {"no-this-assignment": false, "import-name": false, "max-line-length": false, "no-boolean-literal-compare": false, "no-duplicate-imports": false, "trailing-comma": false, "align": false, "quotemark": false, "space-before-function-paren": false, "ter-arrow-parens": false, "object-shorthand-properties-first": false, "variable-name": false, "prefer-const": false, "function-name": [true, "check-format", "/^[a-zA-Z0-9_]+$/"]}}