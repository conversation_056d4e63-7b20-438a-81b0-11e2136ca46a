module.exports = {
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  extends: ['plugin:@typescript-eslint/recommended', 'hexin'],
  env: {
    browser: false,
    node: true,
    es6: true,
  },
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  rules: { 'multiline-comment-style': 'off', 'max-len': ['warn',120], 'no-use-before-define': 'off',
    // 对象前后需要加空格
    'object-curly-spacing': ['error', 'always'],
  },
  overrides: [
    {
      files: ['*'],
      rules: {
        'no-return-assign': 0,
        'no-extra-parens': 0,
        'camelcase': 0,
        'require-jsdoc': [
          'warn',
          {
            require: {
              FunctionDeclaration: false,
              MethodDefinition: false,
              ClassDeclaration: false,
              ArrowFunctionExpression: false,
              FunctionExpression: false,
            },
          }
        ],
        'valid-jsdoc': ['warn', {
          requireParamDescription: false,
          requireReturnDescription: false,
          requireReturnType: true,
          preferType: {
            String: 'string',
            Number: 'number',
            Boolean: 'boolean',
            Object: 'object',
            Array: 'array',
            Function: 'function',
          },
        }],
      },
    }
  ],
};
