/**
 * @file 框架扩展
 * <AUTHOR>
 */

import { Application } from 'egg';
import axios from 'axios';
// import * as fs from 'fs';
// import * as uuid from 'uuid';

const crypto = require('crypto');

export default {
  // rpc 扩展
  invokeRpc(this: Application, rpcName: string, option: any): Promise<string | null> {
    // this 就是 app 对象，在其中可以调用 app 上的其他方法，或访问属性
    const { logger, config } = this;
    const rpcConfig = config.remoteRpcServer[rpcName];
    const logPrefix = option.blockId ? `${option.uid || option.imageUid}:${option.blockId}` : option.uid;
    const zerorpc = require('zerorpc');
    let client = new zerorpc.Client({
      timeout: 30,
      heartbeat: null,
      heartbeatInterval: 20000,
    });
    const startTime = Number(new Date);
    client.connect(rpcConfig.host);
    client.on('error', (err) => {
      logger.error(`${logPrefix} rpc client error:`, err);
    });
    return new Promise((resolve, reject) => {
      const args = [rpcConfig.method];
      const callback = (error, res) => {
        if (error || (res && res.code.toString() !== '0')) {
          logger.error(`detect ${logPrefix} with ${rpcName} failed,error :`,
            error || res.msg.toString(), `cost time : ${Number(new Date) - startTime}ms`);
          return reject(error || res.msg.toString());
        }
        logger.info(`detect ${logPrefix} with ${rpcName} successfully!`,
          `cost time : ${Number(new Date) - startTime}ms`);
        client.close();
        client = null;
        resolve(res.data.toString());
      };
      switch (rpcName) {
      case 'imageCrop':
        args.push(JSON.stringify([{
          fname: `${option.uid}.jpg`,
          multiple: true, // 是否多栏
          url: option.url,
          points: option.points,
        }]));
        break;
      case 'imageCropRerun':
        args.push(JSON.stringify([{
          fname: `${option.uid}.jpg`,
          multiple: true, // 是否多栏
          url: option.url,
          points: option.points,
        }]));
        break;
      }
      logger.info(`start detect ${logPrefix} with ${rpcName}, data : ${JSON.stringify(args)}`);
      args.push(callback);
      client.invoke(...args);
    });
  },

  async getLatex(this: Application, url: string) {
    try {
      let imageData = '';
      let md5 = '';
      if (/^http.*/.test(url)) {
        const res = await this.curl(url);
        imageData = `data:image/jpg;base64,${res.data.toString('base64')}`;
        md5 = crypto.createHash('md5').update(res.data).digest('hex');
        const cache = await this.mongo.db.collection('latex_cache').findOne({ content_md5: md5 });
        if (cache && cache.response) {
          this.logger.info(`get latex info from cache : ${url}`);
          return cache.response;
        }
      } else if (url.indexOf(',') > -1) {
        imageData = url;
      } else {
        imageData = `data:image/jpg;base64,${url}`;
      }
      const { data } = await this.curl('https://api.mathpix.com/v1/snips', {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          src: imageData,
          metadata: {
            version: '2.0',
            platform: 'macOS 10.13.4',
            count: 9,
          },
          config: {
            math_display_delimiters: ['\n$$\n', '\n$$\n'],
            math_inline_delimiters: ['\\(', '\\)'],
            rm_spaces: true,
          },
        }),
        headers: {
          authorization: 'Bearer SkphXTcjluJTroi7gMGH8C__QVbX6tnxsAAcx4eZJAMA41GtZ5o2perYNfErOitymikJ9ewwWhtz9RZFG5xk1w',
          cookie: 'token=SkphXTcjluJTroi7gMGH8C__QVbX6tnxsAAcx4eZJAMA41GtZ5o2perYNfErOitymikJ9ewwWhtz9RZFG5xk1w',
          'user-agent': 'Mathpix%20Snipping%20Tool/94 CFNetwork/897.15 Darwin/17.5.0 (x86_64)',
        },
        timeout: 20 * 1000,
      });
      if (md5) {
        await this.mongo.db.collection('latex_cache').insertOne({
          content_md5: md5,
          available: true,
          response: {
            latex: data.latex,
            confidence: Math.floor(data.confidence * 100) / 100,
            error: !data.latex ? (data && data.errors ? data.errors[0].message : '服务异常') : undefined,
          },
          file_url: url,
        });
      }
      // this.logger.info(`get latex info : ${data ? JSON.stringify(data) : null}`);
      return {
        latex: data.latex,
        confidence: Math.floor(data.confidence * 100) / 100,
        error: !data.latex ? (data && data.errors ? data.errors[0].message : '服务异常') : undefined,
      };
    } catch (e) {
      return null;
    }
  },

  async getLatexV2(this: Application, url: string) {
    try {
      let imageData = '';
      let md5 = '';
      if (/^http.*/.test(url)) {
        const res = await this.curl(url);
        imageData = `data:image/jpg;base64,${res.data.toString('base64')}`;
        md5 = crypto.createHash('md5').update(res.data).digest('hex');
        const cache = await this.mongo.db.collection('latex_cache').findOne({ content_md5: md5 });
        if (cache && cache.response && cache.available && cache.response.latex) {
          this.logger.info(`get latex info from cache : ${url}`);
          return cache.response;
        }
      } else if (url.indexOf(',') > -1) {
        imageData = url;
      } else {
        imageData = `data:image/jpg;base64,${url}`;
      }
      const { data } = await this.curl('https://api.mathpix.com/v3/latex', {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          src: imageData,
          ocr: ['math', 'text'],
          skip_recrop: true,
          formats: ['text', 'latex_styled', 'asciimath', 'mathml'],
          format_options: {
            text: {
              transforms: ['rm_spaces', 'rm_newlines'],
              math_delims: ['$', '$'],
            },
            latex_styled: { transforms: ['rm_spaces'] },
          },
        }),
        headers: {
          app_id: 'zhilun_outlook_com',
          app_key: '91c6f3c090c39473f6b7',
          'Content-type': 'application/json',
        },
        timeout: 20 * 1000,
      });
      // this.logger.info(`get latex info : ${data ? JSON.stringify(data) : null}`);
      if (md5 && data && data.latex_styled) {
        await this.mongo.db.collection('latex_cache').insertOne({
          content_md5: md5,
          available: true,
          response: {
            latex: data.latex_styled,
            confidence: Math.floor(data.latex_confidence * 100) / 100,
            error: data.error,
          },
          file_url: url,
        });
      }
      return {
        latex: data.latex_styled,
        confidence: Math.floor(data.latex_confidence * 100) / 100,
        error: data.error,
      };
    } catch (e) {
      return null;
    }
  },

  async getLatexInside(this: Application, url: string) {
    // const tempPath = this.config.baseDir + '/' + uuid().substring(0, 9).replace(/-/g, '');
    try {
      // const response = await axios.get(url, {responseType: 'arraybuffer'});
      // fs.writeFileSync(tempPath, Buffer.from(response.data));
      // const stream = fs.createReadStream(tempPath);
      const {data: buffer} = await axios.get(url, {responseType: 'arraybuffer'});
      // 转 base64
      const imageData = Buffer.from(buffer).toString('base64');
      const data = {image: JSON.stringify([imageData])};
      this.logger.info('get latex info : ', url);
      const res = await axios.post('http://mathocr.frps.hexinedu.com:7011/predict_math', data,
        {headers: {'Content-Type': 'application/json'}});
      return res.data;
    } catch (e) {
      this.logger.info(`get latex info error : ${e}`);
      return null;
    }
  },

  async logRunningTime(this: Application, runningTime: number, msg: string, skipProtect = false) {
    const coverTime = Number(new Date()) - runningTime;
    if (coverTime < 3000 && !skipProtect) return; // 超过3s打日志

    this.logger.info(`${coverTime}ms  runningTime  ${msg}`);
  },
};
