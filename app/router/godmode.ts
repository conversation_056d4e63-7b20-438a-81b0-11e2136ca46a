'use strict';
import { Application } from 'egg';

export default (app: Application) => {

  const { controller, router } = app;
  const { task } = controller.godmode;
  const adminRouter = router.namespace('/godmode');

  // 获取（客户平台）任务列表
  adminRouter.get('/tasks', task.getList);
  // 触发任务的 mark.json 生成
  adminRouter.post('/task/startMarkJson', task.startMarkJson);
  // 任务审核完成
  adminRouter.post('/task/finishMarkJson', task.finishMarkJson);
  // 测试 openApi 回调
  adminRouter.post('/task/testCb', task.testCb);
  // xdoc 任务审核完成
  adminRouter.post('/task/confirm', task.confirm);
};
