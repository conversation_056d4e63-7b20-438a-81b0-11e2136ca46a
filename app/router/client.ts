/**
 * @file 开放路由（针对客户端用户）
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';

export default (app: Application) => {
  const { router, middleware, controller } = app;
  const { auth } = middleware;
  const { base, task, appl, project, image } = controller.client;
  const adminProject = controller.admin.project;
  const adminImage = controller.admin.image;
  const openTask = controller.open.task;
  // 对于所有的客户端接口，需要进行登录验证
  const clientRouter = router.namespace('/client', auth.login());

  clientRouter.get('/getConfig', base.getConfig);
  clientRouter.get('/application/getAll', appl.getAllByUser);

  clientRouter.post('/project/startMarkJson', project.startMarkJson);
  clientRouter.post('/project/finishMarkJson', project.finishMarkJson);
  clientRouter.get('/project/getOneById', project.getOneById);
  clientRouter.get('/project/getList', auth.client(), project.getList);
  clientRouter.get('/project/separate', adminProject.separate);

  clientRouter.get('/image/getUploadToken', auth.client(), openTask.getUploadToken);
  clientRouter.get('/image/getUrls', adminImage.getUrls);
  clientRouter.get('/image/getOriginalUrls', adminImage.getOriginalUrls);

  clientRouter.get('/task/getList', auth.client(), task.getList);
  clientRouter.post('/task/create', auth.client(), task.create);
  clientRouter.post('/task/startMarkJson', task.startMarkJson);
  clientRouter.post('/task/finishMarkJson', task.finishMarkJson);

  clientRouter.get('/task/image/getAll', image.getAllByTaskId);
  clientRouter.get('/task/getJsonUrls', task.getJsonUrls);
  clientRouter.post('/task/saveJson', task.saveJson);
};
