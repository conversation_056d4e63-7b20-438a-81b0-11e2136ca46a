/**
 * @file 管理员授权码
 */
'use strict';

import { Service } from 'egg';

export default class AuthCodeService extends Service {
  // 获取管理员用户生成的授权码。返回授权码及其有效时长
  async getCode(userId: number) {
    const { redis } = this.app;
    const key = this.getKey(userId);
    const [[, code], [, ttl]] = await redis.pipeline().get(key).ttl(key).exec();
    if (ttl <= 0 || !code) {
      return;
    }
    return { code: this.encodeCode(userId, code), ttl };
  }

  // 管理员用户生成新授权码。返回授权码及其有效时长
  async generateCode(userId: number, expire = 60) {
    const { redis } = this.app;
    const key = this.getKey(userId);
    const code = `${Math.floor(Math.random() * 1000000)}`.padStart(6, '0');
    await redis.setex(key, expire, code);
    return { code: this.encodeCode(userId, code), ttl: expire };
  }

  // 使用管理员的授权码。返回授权码所属管理员userId
  async authCode(encodedCode: string) {
    const { redis } = this.app;
    const { userId, code } = this.decodeCode(encodedCode);
    if (!userId || !code) {
      return;
    }
    const key = this.getKey(userId);
    const success = await redis.delIfEquals(key, code);
    if (success) {
      return userId;
    }
  }

  private getKey(userId: number) {
    return `xdoc:auth_code:${userId}`;
  }

  private encodeCode(userId: number, code: string) {
    return `${userId.toString(16).padStart(6, '0').toUpperCase()}:${code}`;
  }

  private decodeCode(encodedCode: string) {
    const [userId, code] = encodedCode.split(':');
    return {
      userId: parseInt(userId, 16),
      code,
    };
  }
}
