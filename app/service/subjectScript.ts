'use strict';

import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/subjectScript';
import { ETaskResourceType } from '../model/task';
import { ESubjectScriptConfigTaskType } from '../model/subjectScriptConfig';
import { cleanJsonNodes } from '../core/utils/htmlToJsonV4';

export default class SubjectScriptService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.SubjectScript, defineAttributes);
  }

  public async generateFormattedData(task: any) {
    const { service } = this;
    const { taskId, appKey } = task;
    const { statuses } = service.task.base;
    const html = await service.task.base.getOssData(appKey, taskId, 'html');
    await service.task.base.copyOssData(appKey, taskId, 'html', 'formatted.html');
    const json = await service.task.base.convert2Json(task.appKey, taskId, html);
    await service.task.base.setOssData(task.appKey, taskId, 'formatted.internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'formatted.json', cleanJsonNodes(json));
    const cleanHtml = await service.task.taskV2.cleanHtml(html);
    await service.task.base.setOssData(task.appKey, taskId, 'clean.formatted.html', cleanHtml);
    /*
     * const formattedDocKey = service.task.base.getOssKey(task.appKey, taskId, 'formatted.docx');
     * await service.oss.convertHtmlToDocAndUpload(formattedDocKey, html, true);
     */
    await service.task.base.update(
      { status: statuses.reviewed },
      { where: { taskId } }
    );
    // 状态更新到审核完成后，机器人通知
    await service.robot.sendRobotMessageWhenTaskReviewed(taskId, task.bookId);
  }

  public async runSubjectScript(taskId: number, isNeedCallback = false, reRunTimes = 0, type = 'server') {
    const { service, app, ctx } = this;
    const { statuses } = service.task.base;

    const runningTime = Number(new Date());

    try {
      const task = await service.task.base.getOne({ where: { taskId } });
      if (!task) {
        return;
      }
      const { subject } = await service.task.meta.getMetas({ taskId });
      const { resourceType } = task;
      // 对应 ETaskTypeResourceType
      const taskType = [0, 1, 2, 3].includes(resourceType!) && ESubjectScriptConfigTaskType.image ||
        ETaskResourceType.WORDV2 === resourceType && ESubjectScriptConfigTaskType.wordV2 ||
        ETaskResourceType.FBDV2 === resourceType && ESubjectScriptConfigTaskType.fbdV2;
      const config = await service.subjectScriptConfig.getAll({ attributes: ['subject', 'script', 'taskType'] });
      const subjects = await service.content.getSubjectList();
      let targetConfig = config.find((item) =>
        // 这里需要注意 task 的 subject 存在中文或者英文的情况，而 config 中的 subject 都是英文
        (subject === item.subject || subjects.find((s) => s.name === subject)?.key === item.subject) &&
        item.taskType === taskType);
      this.logger.info(`数据清洗传参config : ${taskId} ${JSON.stringify(targetConfig)} 学科:${subject}`);
      if (!targetConfig) {
        targetConfig = config.find((item) =>
          item.subject === 'default' &&
          item.taskType === taskType);
      }
      this.logger.info(`[runSubjectScript] taskId=${taskId}`);
      const download_path = await service.task.base.getUrl(
        task.appKey,
        taskId,
        'html',
        false,
        false
      );
      const upload_path = await service.task.base.getUrl(
        task.appKey,
        taskId,
        'formatted.html',
        false,
        false
      );
      // type 用来判断是在 task 环境下还是在 server 中运行， 默认 server
      const res = await ctx.curl(`${type === 'task' ? app.config.jsonPreprocess.apiTask : app.config.jsonPreprocess.api}/api/json-preprocess/task/preprocessing`, {
        method: 'POST',
        contentType: 'json',
        data: {
          task_id: taskId,
          is_need_callback: isNeedCallback,
          oss_bucket: app.config.aliOss.bucket,
          post_data: [{ download_path, upload_path }],
          script_list: targetConfig!.script ? targetConfig!.script.split(',') : [],
        },
      });
      if (res.status !== 200) {
        throw new Error(`[runSubjectScript] taskId=${taskId} 请求 jsonPreprocess 状态: ${res?.status}, statusMessage=${res?.res?.statusMessage}`);
      }
      this.logger.info(`[runSubjectScript] taskId=${taskId} 已进入数据清洗阶段`);
    } catch (e) {
      if (reRunTimes < 3) {
        await this.runSubjectScript(taskId, isNeedCallback, reRunTimes + 1);
      } else {
        const errorInfo = `清洗脚本 error: ${e}, reRunTimes: ${reRunTimes}`;
        this.logger.info(errorInfo);
        await service.task.base.update(
          {
            status: statuses.dataCleanfailed,
            errorInfo,
          },
          { where: { taskId } }
        );
      }
    }

    this.app.logRunningTime(runningTime, ` /script/runSubjectScript 数据清洗 task:${JSON.stringify(taskId)}`);
  }
}
