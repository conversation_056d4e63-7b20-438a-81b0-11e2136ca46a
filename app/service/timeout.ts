'use strict';

import { Service } from 'egg';

interface TimeoutTask {
  taskId: string;
  type: string;
  timeout: number; // 超时时间（毫秒）
  startTime: number; // 开始时间（时间戳）
  notifyData: {
    title: string;
    content: string;
    receiveId?: number;
    extra?: any;
  };
}

export default class TimeoutService extends Service {
  private readonly TIMEOUT_TASK_KEY = 'timeout:tasks';
  private readonly TIMEOUT_INDEX_KEY = 'timeout:index';

  /**
   * 添加超时监控任务
   * @param taskId 任务ID
   * @param type 任务类型
   * @param timeoutMs 超时时间（毫秒）
   * @param notifyData 通知数据
   */
  public async addTimeoutTask(
    taskId: string,
    type: string,
    timeoutMs: number,
    notifyData: {
      title: string;
      content: string;
      receiveId?: number;
      extra?: any;
    }
  ): Promise<void> {
    const { app } = this;
    const startTime = Date.now();
    const expireTime = startTime + timeoutMs;

    const task: TimeoutTask = {
      taskId,
      type,
      timeout: timeoutMs,
      startTime,
      notifyData,
    };

    // 存储任务详情
    const taskKey = `${this.TIMEOUT_TASK_KEY}:${taskId}:${type}`;
    await app.redis.setex(taskKey, Math.ceil(timeoutMs / 1000) + 60, JSON.stringify(task));

    // 添加到有序集合中，用于按过期时间排序
    await app.redis.zadd(this.TIMEOUT_INDEX_KEY, expireTime.toString(), `${taskId}:${type}`);

    this.logger.info(`[TimeoutService] 添加超时任务: ${taskId}:${type}, 超时时间: ${timeoutMs}ms`);
  }

  /**
   * 释放超时监控任务
   * @param taskId 任务ID
   * @param type 任务类型
   */
  public async releaseTimeoutTask(taskId: string, type: string): Promise<boolean> {
    const { app } = this;
    const taskKey = `${this.TIMEOUT_TASK_KEY}:${taskId}:${type}`;
    const indexKey = `${taskId}:${type}`;

    // 从Redis中删除任务
    const [deleted, removed] = await Promise.all([
      app.redis.del(taskKey),
      app.redis.zrem(this.TIMEOUT_INDEX_KEY, indexKey)
    ]);

    if (deleted > 0 || removed > 0) {
      this.logger.info(`[TimeoutService] 释放超时任务: ${taskId}:${type}`);
      return true;
    }

    return false;
  }

  /**
   * 获取所有已超时的任务
   */
  public async getExpiredTasks(): Promise<TimeoutTask[]> {
    const { app } = this;
    const now = Date.now();

    // 获取所有已过期的任务键
    const expiredKeys = await app.redis.zrangebyscore(
      this.TIMEOUT_INDEX_KEY,
      0,
      now
    );

    if (!expiredKeys.length) {
      return [];
    }

    // 获取任务详情
    const tasks: TimeoutTask[] = [];
    for (const key of expiredKeys) {
      const taskKey = `${this.TIMEOUT_TASK_KEY}:${key}`;
      const taskData = await app.redis.get(taskKey);

      if (taskData) {
        try {
          const task = JSON.parse(taskData) as TimeoutTask;
          tasks.push(task);
        } catch (error) {
          this.logger.error(`[TimeoutService] 解析任务数据失败: ${key}`, error);
        }
      }
    }

    return tasks;
  }

  /**
   * 清理过期任务
   * @param expiredTasks 过期任务列表
   */
  public async cleanupExpiredTasks(expiredTasks: TimeoutTask[]): Promise<void> {
    const { app } = this;

    for (const task of expiredTasks) {
      const taskKey = `${this.TIMEOUT_TASK_KEY}:${task.taskId}:${task.type}`;
      const indexKey = `${task.taskId}:${task.type}`;

      await Promise.all([
        app.redis.del(taskKey),
        app.redis.zrem(this.TIMEOUT_INDEX_KEY, indexKey)
      ]);
    }

    this.logger.info(`[TimeoutService] 清理过期任务: ${expiredTasks.length}个`);
  }

  /**
   * 发送超时通知
   * @param task 超时任务
   */
  public async sendTimeoutNotification(task: TimeoutTask): Promise<void> {
    const { service } = this;

    try {
      const errorData = {
        type: 'error',
        receive_id: 25,
        server: { name: 'AI-EDIT 流程监控' },
        content: {
          title: task.notifyData.title,
          text: `任务超时通知\n
任务ID: ${task.taskId}\n
任务类型: ${task.type}\n
${task.notifyData.extra}`,
        },
      };

      await service.robot.sendRobotMessage(errorData);
      this.logger.info(`[TimeoutService] 发送超时通知: ${task.taskId}:${task.type}`);
    } catch (error) {
      this.logger.error(`[TimeoutService] 发送超时通知失败: ${task.taskId}:${task.type}`, error);
    }
  }

  /**
   * 获取任务剩余时间
   * @param taskId 任务ID
   * @param type 任务类型
   */
  public async getRemainingTime(taskId: string, type: string): Promise<number | null> {
    const { app } = this;
    const taskKey = `${this.TIMEOUT_TASK_KEY}:${taskId}:${type}`;

    const taskData = await app.redis.get(taskKey);
    if (!taskData) {
      return null;
    }

    try {
      const task = JSON.parse(taskData) as TimeoutTask;
      const now = Date.now();
      const expireTime = task.startTime + task.timeout;
      return Math.max(0, expireTime - now);
    } catch (error) {
      this.logger.error(`[TimeoutService] 解析任务数据失败: ${taskId}:${type}`, error);
      return null;
    }
  }

  /**
   * 获取所有监控中的任务
   */
  public async getAllMonitoringTasks(): Promise<TimeoutTask[]> {
    const { app } = this;

    // 获取所有任务键
    const allKeys = await app.redis.zrange(this.TIMEOUT_INDEX_KEY, 0, -1);

    const tasks: TimeoutTask[] = [];
    for (const key of allKeys) {
      const taskKey = `${this.TIMEOUT_TASK_KEY}:${key}`;
      const taskData = await app.redis.get(taskKey);

      if (taskData) {
        try {
          const task = JSON.parse(taskData) as TimeoutTask;
          tasks.push(task);
        } catch (error) {
          this.logger.error(`[TimeoutService] 解析任务数据失败: ${key}`, error);
        }
      }
    }

    return tasks;
  }

  /**
   * 清理所有超时任务（谨慎使用）
   */
  public async clearAllTimeoutTasks(): Promise<void> {
    const { app } = this;

    // 获取所有任务键
    const allKeys = await app.redis.zrange(this.TIMEOUT_INDEX_KEY, 0, -1);

    // 删除所有任务详情
    for (const key of allKeys) {
      const taskKey = `${this.TIMEOUT_TASK_KEY}:${key}`;
      await app.redis.del(taskKey);
    }

    // 清空索引
    await app.redis.del(this.TIMEOUT_INDEX_KEY);

    this.logger.info(`[TimeoutService] 清理所有超时任务: ${allKeys.length}个`);
  }
}
