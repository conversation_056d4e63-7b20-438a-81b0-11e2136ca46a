'use strict';

import { Service } from 'egg';

interface ISubject {
  stage: string;
  stageName: string;
  academic: string;
  key: string;
  name: string;
  order: number;
}

export default class ContentService extends Service {

  public async getSubjectList() {
    const { ctx } = this;

    try {
      // @todo：Host 应该通过配置来做相应的调整
      const { data: { data: subjects } } = await ctx.curl('http://content-server.hexinedu.com/api/content/base/subjectRel/getList?academic=k12', {
        dataType: 'json',
        method: 'GET',
        data: { academic: 'k12' },
      });
      return subjects as ISubject[];
    } catch (e) {
      this.logger.info(`[content_service] getSubjectList exception: ${e}`);
      return [];
    }
  }

}
