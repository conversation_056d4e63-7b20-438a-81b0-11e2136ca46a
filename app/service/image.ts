/**
 * @file image
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/image';
import { JSDOM } from 'jsdom';

type supportExtension = 'html';

export default class ImageService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Image, defineAttributes);
  }

  private readonly imageTaskQueueKey = 'local:image:task:update:queue';

  public getFileOrder(filename: string) {
    const numReg = /[^0-9]/ig;
    const testOrder = Number(filename.replace(numReg, ''));
    const names = filename.split('.');
    /*
     * 制定规则，数组最后一位为文件后缀，倒数第二位为附属序号（需要符合 $_xxx_$.jpg ）
     * 如 "a10test2.$_2_$.jpg" 顺序为 102.2
     */
    if (names.length <= 2 || isNaN(testOrder)) {
      return testOrder;
    }
    let attachingOrder = 0;
    if (/^\$_[^_$]*_\$$/.test(names[names.length - 2])) {
      attachingOrder = Number(names[names.length - 2].replace(numReg, ''));
      if (isNaN(attachingOrder)) {
        return attachingOrder;
      }
    }
    const mainOrder = Number(names.splice(0, names.length - 2).join('').replace(numReg, ''));
    return isNaN(mainOrder) ? mainOrder : Number(`${mainOrder}.${attachingOrder}`);
  }

  public setFileOrder(filename: string, attachingOrder: number) {
    // 如果文件名已经是 name.$_d1_$.jpg 形式。更新为 name.$_d1d2_$.jpg
    let name = filename.replace(/\$_(\d+)_\$/, (_, g) => `$_${g}${attachingOrder}_$`);
    if (name !== filename) {
      return name;
    }
    // 否则，更新为 name.$_d2_$.jpg
    name = filename.replace(/(.*?)(\.\w+)?$/, (_, g1, g2) => `${g1}.$_${attachingOrder}_$${g2 || ''}`);
    return name;
  }

  public sortImageByFile(images: any[]) {
    // @todo：应该是用mysql的排序，taskOrder,fileOrder（根据filename生成的冗余字段）,id依次排序
    const { getFileOrder } = this;
    const hasFileOrder = images.length && images[0].filename && !isNaN(getFileOrder(images[0].filename));
    const hasTaskOrder = images.length && images[0].taskOrder > 0;
    // 如果没有排序规则，使用默认排序
    if (!hasFileOrder && !hasTaskOrder) {
      return images;
    }
    return _.orderBy(images, [(image) => image.taskOrder, (image) => getFileOrder(image.filename)]);
  }

  public getUrl(appKey: string, imageId: string, extension: '.ooriginal.jpg' | 'jpg' | 'html' | 'docx', timestamp = false, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}open/${appKey}/image/${imageId}.${extension}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getMissImageUrl(appKey: string, imageId: string) {
    const { config } = this;
    return `${config.aliOss.host}open/${appKey}/image/${imageId}-miss.jpg`;
  }

  public getOssData(appKey: string, imageId: string, extension: supportExtension) {
    const url = this.getUrl(appKey, imageId, extension, true, true);
    return this.service.oss.fetch(url, extension);
  }

  public async setOssData(appKey: string, imageId: string, extension: supportExtension, data: string | object) {
    const key = `open/${appKey}/image/${imageId}.${extension}`;
    const url = await this.service.oss.upload(key, data, 'string');
    return url;
  }

  public async syncFormulaHtml(appKey: string, imageId: string, formulaId: string, latex: string) {
    const html = await this.getOssData(appKey, imageId, 'html');
    const { document } = (new JSDOM(html)).window;
    const el: HTMLElement | undefined = document.getElementById(formulaId);
    const originLatex = el ? el.innerText : '';
    this.logger.info(`syncFormulaHtml ${imageId}, origin latex: ${originLatex}, new latex: ${latex}`);
    if (el && this.modifyLatexEl(appKey, el, latex)) {
      // 上传新html到oss
      await this.setOssData(appKey, imageId, 'html', document.body.innerHTML);
    }
  }

  private modifyLatexEl(appKey: string, el: HTMLElement, latex: string): boolean {
    if (this.service.appl.isNewVersion(appKey)) {
      if (el.dataset.value !== latex) {
        el.dataset.value = latex;
        el.innerText = latex;
        return true;
      }
    } else {
      if (el.textContent !== latex) {
        el.textContent = latex;
        return true;
      }
    }
    return false;
  }

  public async getImageInfo(appKey: string, imageId: string) {
    const url = this.getUrl(appKey, imageId, 'jpg', false, true);
    const resp = await this.app.curl(`${url}?x-oss-process=image/info`, {
      method: 'GET',
      dataType: 'json',
    });
    if (resp.status && !/^([23]).*/.test(`${resp.status}`)) {
      throw new Error(`获取图片信息错误 ${resp.status}`);
    }
    const data = resp.data;
    let w = Number(data.ImageWidth.value);
    let h = Number(data.ImageHeight.value);
    const o = data.Orientation ? Number(data.Orientation.value) : undefined;
    const f = data.Format ? data.Format.value as string : undefined;
    if (o && o >= 5) {
      [w, h] = [h, w];
    }
    return { w, h, o, f };
  }

  public async pushToQueue(data: string) {
    await this.app.redis.rpush(this.imageTaskQueueKey, data);
  }

  public async popFromQueue(): Promise<any> {

    const { redis } = this.app;
    const queueKey = this.imageTaskQueueKey;
    const data = await redis.lpop(queueKey);
    if (data) {
      const parsedData = JSON.parse(data);
      this.logger.info(`[popFromQueue] 取出普通任务，taskId: ${parsedData.taskId}, imageId: ${parsedData.imageId}`);
      return parsedData;
    }
    // 获取队列长度
    const queueLength = await redis.llen(queueKey);
    if (queueLength === 0) {
      return null;
    }

    // 扫描队列寻找高优先级的任务
    let highPriorityItem;
    let highPriorityIndex = -1;

    // 从头部开始扫描，寻找优先级为 true 的任务
    for (let i = 0; i < Math.min(queueLength, 10); i++) { // 限制扫描前100个，避免性能问题
      const item = await redis.lindex(queueKey, i);
      if (!item) continue;

      try {
        const parsedItem = JSON.parse(item);
        if (parsedItem.priority === true) {
          highPriorityItem = item;
          highPriorityIndex = i;
          break; // 找到第一个高优先级任务就返回
        }
      } catch (e) {
        // 解析失败的项目跳过
        this.logger.warn(`[popFromQueue] 队列项解析失败: ${item}`);
      }
    }

    if (highPriorityItem) {
      // 删除高优先级任务
      await redis.lrem(queueKey, 1, highPriorityItem);
      const parsedHighPriority = JSON.parse(highPriorityItem);
      this.logger.info(`[popFromQueue] 取出高优先级任务，位置: ${highPriorityIndex}, taskId: ${parsedHighPriority.taskId}, imageId: ${parsedHighPriority.imageId}`);
      return parsedHighPriority;
    }

    // 没有高优先级任务，按正常顺序取队列头部
    // const data = await redis.lpop(queueKey);
    // if (data) {
    //   const parsedData = JSON.parse(data);
    //   this.logger.info(`[popFromQueue] 取出普通任务，taskId: ${parsedData.taskId}, imageId: ${parsedData.imageId}`);
    //   return parsedData;
    // }
    return null;
  }

  /**
   * 将指定任务的队列项设置为高优先级
   * @param taskId 任务ID
   */
  public async prioritizeTaskInQueue(taskId: number) {
    const { redis } = this.app;
    const queueKey = this.imageTaskQueueKey;

    this.logger.info(`[prioritizeTaskInQueue] 开始为任务 ${taskId} 设置高优先级`);

    // 获取队列长度
    const queueLength = await redis.llen(queueKey);
    this.logger.info(`[prioritizeTaskInQueue] 当前队列长度: ${queueLength}`);

    if (queueLength === 0) {
      this.logger.info(`[prioritizeTaskInQueue] 队列为空，无需设置优先级任务 ${taskId}`);
      return { modified: 0, total: 0 };
    }

    // 获取所有队列项
    const allItems = await redis.lrange(queueKey, 0, -1);
    const updatedItems: string[] = [];
    let modifiedCount = 0;

    this.logger.info(`[prioritizeTaskInQueue] 开始扫描队列设置优先级标志，任务ID: ${taskId}`);

    // 扫描所有项目，为匹配的任务设置优先级
    for (const item of allItems) {
      try {
        const parsedItem = JSON.parse(item);
        if (parsedItem.taskId === taskId) {
          // 设置优先级标志
          parsedItem.priority = true;
          updatedItems.push(JSON.stringify(parsedItem));
          modifiedCount++;
          this.logger.info(`[prioritizeTaskInQueue] 设置优先级，imageId: ${parsedItem.imageId}, type: ${parsedItem.type}`);
        } else {
          // 保持原样
          updatedItems.push(item);
        }
      } catch (e) {
        // 解析失败的项目保持原样
        this.logger.warn(`[prioritizeTaskInQueue] 队列项解析失败: ${item}, 错误: ${e}`);
        updatedItems.push(item);
      }
    }

    if (modifiedCount === 0) {
      this.logger.info(`[prioritizeTaskInQueue] 队列中未找到任务 ${taskId} 的相关项目`);
      return { modified: 0, total: queueLength };
    }

    // 重新设置整个队列
    await redis.del(queueKey);
    if (updatedItems.length > 0) {
      await redis.rpush(queueKey, ...updatedItems);
    }

    this.logger.info(`[prioritizeTaskInQueue] 操作完成，任务 ${taskId} 的 ${modifiedCount} 个队列项已设置为高优先级`);

    return { modified: modifiedCount, total: queueLength };
  }

}
