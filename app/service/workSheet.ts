'use strict';

import { Service } from 'egg';
import axios from 'axios';

export interface IRelatedIds {
  old_project_id: string;
  new_project_id: string;
}

export default class WorkSheetService extends Service {
  public async publishWorkSheet(data: any) {
    try {
      const res = await this.app.curl(`${this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify(data),
      });
      this.logger.info('工单发布！！' + JSON.stringify(res));
      return res;
    } catch (e) {
      throw new Error(e);
    }
  }

  public async submitError({
    ticketId,
    errorInfo,
  }: {
    ticketId: string;
    errorInfo: string;
  }) {
    try {
      const res = await this.app.curl(`${this.config.workOrder.api}/api/open/ticket/v1/error`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket_id: ticketId,
          error_info: errorInfo,
        }),
      });
      return res;
    } catch (e) {
      throw new Error(e);
    }
  }

  // 重新关联
  public async reLinkTickedId(type: 'task' | 'server', ticketId: string, ids: IRelatedIds[]) {
    try {
      const res = await axios.post(`${type == 'server' ? this.config.workOrder.api : this.config.workOrder.apiTask}/api/open/project/v1/link_update`, {
        ticket_id: ticketId,
        project_link: ids,
      });
      this.logger.info(`工单重新关联！！${JSON.stringify(res)}`);
      return res?.data;
    } catch (e) {
      throw new Error(e);
    }
  }
}
