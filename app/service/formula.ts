/**
 * @file formula
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/formula';

export default class FormulaService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Formula, defineAttributes);
  }

  public getUrl(appKey: string, imageId: string, coordinate: string, quality?: number) {
    if (!coordinate) {
      return '';
    }
    const [x, y, w, h] = coordinate.split(',');
    const { config } = this;
    let url = `${config.aliOss.host}open/${appKey}/image/${imageId}.jpg?x-oss-process=image/crop,x_${x},y_${y},w_${w},h_${h}`;
    if (quality) {
      url = `${url}/format,jpg/quality,Q_${quality}`;
    }
    return url;
  }
}
