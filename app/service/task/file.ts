/**
 * @file 任务文件信息
 */

'use strict';

import { Context } from 'egg';
import FileService from '../../core/base/fileService';
import { Attributes, defineAttributes, Instance, ITaskFiles, TASK_DEFAULT_FILES } from '../../model/taskFile';

export default class TaskMetaService
  extends FileService<'taskId', number, ITaskFiles, Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.TaskFile, defineAttributes, TASK_DEFAULT_FILES, 'taskId');
  }
}
