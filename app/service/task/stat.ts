/**
 * @file 任务统计
 * <AUTHOR>
 */

'use strict';

import {Context} from 'egg';
import ModelService from '../../core/base/modelService';
import superSequelize from '../../../typings/app/core/modelService';
import {Attributes, defineAttributes, Instance} from '../../model/taskStat';
import {htmlToJson} from '../../core/utils/htmlToJsonV4';
import {htmlToJsonV2} from '../../core/utils/htmlToJsonV2';
import {statHtml, statJson} from '../../core/utils/taskStat';
import axios from 'axios';

type StatParams = {
  html: string;
  json?: any[];
  taskId: number;
  appKey: string;
  resourceType: number;
  type: 'machine' | 'mark' | 'review' | 'project_check' | 'admin_check' | 'pre_crop';
  subject: string;
  imageCount: number;
  pdfCount?: number;
  cost?: number;
  userId?: number;
  preCropCount?: number;
};

export default class TaskStatService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.TaskStat, defineAttributes);
  }

  private readonly unstatTaskQueueKey = 'unstat:task:sorted:set';

  public types = ['column', 'mark', 'review'];

  public async push(taskId: number) {
    const { redis } = this.app;
    await redis.zadd(this.unstatTaskQueueKey, `${Number(new Date())}`, `${taskId}`);
  }

  public async getAllUnstatTask(score: number): Promise<string[]> {
    const { redis } = this.app;
    return await redis.zrangebyscore(this.unstatTaskQueueKey, 0, score);
  }

  public async del(taskId: number) {
    const { redis } = this.app;
    await redis.zrem(this.unstatTaskQueueKey, `${taskId}`);
  }

  public async getRelatedList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service } = this;
    const stats = await super.getList({
      page: options.page,
      pageSize: options.pageSize,
      where: options.where || {},
      order: options.order || [['id', 'ASC']],
      attributes: options.attributes,
      group: options.group,
    });
    if (!stats.length) {
      return [];
    }
    const userMap = {};
    const taskMap = {};
    const userIds: number[] = [];
    const taskIds: number[] = [];
    for (const { userId, taskId } of stats) {
      if (taskId && !taskIds.includes(taskId)) {
        taskIds.push(taskId);
      }
      if (userId && !userIds.includes(userId)) {
        userIds.push(userId);
      }
    }
    // 搜索用户名
    if (userIds.length) {
      const users = await service.user.search(userIds);
      for (const { userId, nickname } of users) {
        userMap[userId] = nickname;
      }
    }
    // 获取任务详情
    if (taskIds.length) {
      const tasks = await service.task.base.getAll({ where: { taskId: taskIds } });
      for (const { subject, taskId, taskName } of tasks) {
        taskMap[taskId] = { subject, taskName };
      }
    }
    stats.forEach((stat: any) => {
      stat.username = userMap[stat.userId] || '';
      if (stat.taskId) {
        stat.subject = taskMap[stat.taskId] ? taskMap[stat.taskId].subject : '';
        stat.taskName = taskMap[stat.taskId] ? taskMap[stat.taskId].taskName : '';
      }
      if (stat.imageCount) {
        stat.imageCount = Number(stat.imageCount) || 0;
        stat.leafQuestionCount = Number(stat.leafQuestionCount) || 0;
        stat.parentQuestionCount = Number(stat.parentQuestionCount) || 0;
        stat.costTime = Number(stat.costTime) || 0;
      }
    });
    return stats;
  }

  // 机器识别成功后，需要暂存机器结果
  public async stashMachineHtml(appKey: string, taskId: number, imageIds: string[]) {
    const { service } = this;
    const html = await service.task.base.combineByImageIds(appKey, imageIds, taskId);
    // 🤓 在这里做一遍 latex 的清洗，然后变成 machine.html，再把真正的 machine.html 改名为 origin.html 存到 oss
    const processedHtml = await this.processLatex(html, taskId);
    await service.task.base.setOssData(appKey, taskId, 'origin.html', html);
    await service.task.base.setOssData(appKey, taskId, 'machine.html', processedHtml);
    this.logger.info(`taskId: ${taskId} latex process end`);
    return html;
  }

  // 标注提交后，需要暂存标注结果
  public async stashMarkHtml(appKey: string, taskId: number) {
    await this.service.task.base.copyOssData(appKey, taskId, 'html', 'mark.html');
  }

  // 新的统计（存mongo）
  public async newStat(
    { html, json, taskId, type, appKey, subject, imageCount, cost, userId, pdfCount, resourceType, preCropCount }: StatParams,
    newVersion?: boolean
  ) {
    const { mongo } = this.app;
    const statAt = new Date().getTime();
    let elements = json;
    if (!elements) {
      if (newVersion || this.service.appl.isNewVersion(appKey)) {
        elements = htmlToJson({ html, isOfficial: true });
      } else {
        elements = htmlToJsonV2({ html, isOfficial: true });
      }
    }
    const htmlStat = elements ? statHtml(elements) : ''; // 计算html中各统计数量
    const jsonStat = statJson(elements!); // 计算json中各统计数量
    const statCost = new Date().getTime() - statAt;
    await mongo.db.collection('stat').updateOne( // 覆盖
      { task_id: taskId, type }, // 任务和类型唯一
      {
        $set: {
          app_key: appKey,
          subject,
          resourceType,
          image_count: imageCount,
          pdf_count: pdfCount || 0,
          preCropCount: preCropCount || 0,
          cost, // 任务耗时
          user_id: userId, // 操作人
          html: htmlStat, // html中各标签的统计
          json: jsonStat, // 生成的json中的各节点统计
          stat_cost: statCost, // 统计耗时 （ms）
          stat_at: statAt, // 统计时间点（ms）
        },
      },
      { upsert: true }
    );
  }

  public async aggStat(taskIds: number[], type: 'machine' | 'mark' | 'review') {
    const { mongo } = this.app;
    const { fn, col } = this.app.model;
    let [[stat], [task]] = await Promise.all([
      mongo.db.collection('stat').aggregate([
        {
          $match: {
            task_id: { $in: taskIds },
            type,
          },
        },
        {
          $group: {
            _id: null,
            enChar: { $sum: '$html.p.en_char' },
            cnChar: { $sum: '$html.p.cn_char' },
            punctChar: { $sum: '$html.p.punct_char' },
            latexCount: { $sum: '$html.latex.count' },
            imgCount: { $sum: '$html.img.count' },
            tableCount: { $sum: '$html.table.count' },
            tdCount: { $sum: '$html.td.count' },
            questionCount: { $sum: '$json.question.count' },
          },
        }
      ]).toArray(),
      this.service.task.base.getAll({
        where: { taskId: taskIds },
        attributes: [
          [fn('count', col('id')), 'taskCount'],
          [fn('sum', col('imageCount')), 'imageCount']
        ],
      })
    ]);
    stat = stat || {};
    delete stat._id;
    stat.taskCount = Number((task as any).taskCount);
    stat.imageCount = Number((task as any).imageCount);
    return stat;
  }

  public getCostTime(histories: { type: number; costTime: number; userId: number; }[], operatorUserId: number, statType: string) {
    if (!histories.length) {
      return 0;
    }
    const { service } = this;
    let time = 0;
    const breakTypes = {
      column: service.task.history.columnTypes.apply.id,
      mark: service.task.history.markTypes.apply.id,
      review: service.task.history.reviewTypes.apply.id,
    };
    const timeTypes = {
      column: service.task.history.columnTypes.save.id,
      mark: service.task.history.markTypes.confirm.id,
      review: service.task.history.reviewTypes.confirm.id,
    };
    for (const { type, costTime, userId } of histories) {
      if (userId === operatorUserId && type === timeTypes[statType]) {
        time += costTime;
      }
      if (type === breakTypes[statType]) {
        break;
      }
    }
    return time;
  }

  public async getDesignatedHTMLStatData(appKey:string, taskId: number, tagName: string) {
    try {
      const json = await this.service.task.base.getOssData(appKey, taskId,'json');
      const stat = statHtml(json);
      if (stat?.[tagName]) {
        this.logger.info('stat', JSON.stringify(stat));
        return stat?.[tagName];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  public readonly exportKey = 'xdoc:task:export:queue';

  public async pushToExport(inputData: {
    taskIds?: number[];
    projectIds?: number[];
    userIds?: number[];
    types?: string[];
    appKeys?: string[];
    subjects?: string[];
    startTime?: number;
    endTime?: number;
    columns: { title: string; prop: string }[];
  }) {
    await this.app.redis.lpush(this.exportKey, JSON.stringify(inputData));
  }

  public async popFromExport(): Promise<{
    taskIds?: number[];
    projectIds?: number[];
    userIds?: number[];
    types?: string[];
    appKeys?: string[];
    subjects?: string[];
    startTime?: number;
    endTime?: number;
    key?: string;
    ticketIds?: string[];
    columns: { title: string; prop: string }[];
    sort: {
      item: string;
      order: 'asc' | 'desc';
    }
  }> {
    const inputData = await this.app.redis.rpop(this.exportKey);
    return JSON.parse(inputData || '{}');
  }

  public async processLatex(html: string, taskId: number) {
    try {
      const latexMap = new Map();
      const res: string[] = [];
      let index = 0;
      this.logger.info(`taskId: ${taskId} latex process start`);
      const meta = await this.service.task.meta.getMetas({ taskId, key: 'subject' });
      const task = await this.service.task.base.getOne({
        where: { taskId },
        attributes: ['resourceType'],
      });
      this.logger.info(`taskId: ${taskId} latex process start, meta: ${JSON.stringify(meta)}`);
      return html;
      // @ts-ignore
      if (['physics', 'math', 'chemistry'].includes(meta?.subject || '') && task?.resourceType !== 5) {
        this.logger.info(`taskId: ${taskId} latex process start, resourceType: ${task?.resourceType}`);
        const _html = html.replace(/>([^<>]+)</g, (...args) => {
          //  非公式的括号，改成中文括号
          let content = args[1];
          content = content.replace( /\$\$([^$]+)\$\$([(（])([^)）]+)([)）])/g, (_match: any, p1: any, p2: any, p3: string | any[], p4: any) => {
            if (p3.length <= 2) {
              return `$$${p1}(${p3})$$`;
            }
            return `$$${p1}$$${p2}${p3}${p4}`;
          });
          // 处理公式内容，将公式内容临时替换
          content = content.replace(/(\$\$[\s\S]*?\$\$)/g, (_match: any, latex: string) => {
            index++;
            res.push(latex);
            latexMap.set(`◭${index}◭`, latex);
            return `◭${index}◭`;
          });
          // 处理非公式内容
          if (meta.subject === 'physics') {
            content = content
              .replace(/\(/g, '（')
              .replace(/\)/g, '）')
              .replace(/:/g, '：');
            const protectedText = content.replace(/&nbsp;/g, '__NBSP__');
            // 替换其他分号
            const replacedText = protectedText.replace(/;/g, '；');
            content = replacedText.replace(/__NBSP__/g, '&nbsp;');
          }
          return `>${content}<`;
        });
        const response = await axios.post(`${this.config.jsonPreprocess.api}/api/json-preprocess/latex/clean`, {
          latex_list: res,
          subject: meta.subject,
        });
        res.forEach((latex_1, i) => {
          const latexProcessed = response.data.data[latex_1];
          const mapKey = `◭${i + 1}◭`;
          latexMap.set(mapKey, latexProcessed);
        });
        this.logger.info(`taskId: ${taskId} latex process end , res: ${res.length}, latexMap: ${latexMap.size}`);
        return _html.replace(/◭(\d+)◭/g, (...args_1: (string | number)[]) => latexMap.get(args_1[0]));
      }
      return html;
    } catch (e) {
      this.logger.info(`taskId: ${taskId} latex process error`, JSON.stringify(e));
      return html;
    }
  }
}
