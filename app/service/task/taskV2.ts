'use strict';

import { Context } from 'egg';
import { Attributes, defineAttributes, ETaskResourceType, ETaskType, Instance } from '../../model/task';
import ModelService from '../../core/base/modelService';
import * as urllib from 'urllib';
import { getHtml, parseHtml, getText } from '../../core/utils/htmlHelper';
import { iterateNode } from '../../core/utils/treeHelper';
import * as sequelize from 'sequelize';
import { ITaskFiles, ITaskPdfs } from '../../model/taskFile';
import { ITaskMetas } from '../../model/taskMeta';
import { PDFDocument } from 'pdf-lib';
import * as uuid from 'uuid/v4';
import * as SourceImageModel from '../../model/sourceImage';
import { ESourceImageStatus } from '../../model/sourceImage';
import { TJsonNode, validateJsonNodes } from '../../core/utils/htmlToJsonV5';

export interface Task {
  taskId: number;
  path: string;
  html: string;
  length: number;
  name: string;
  bookOrder: number;
  meta: ITaskMetas;
}

export default class TaskService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Task, defineAttributes);
  }

  public statuses = this.service.task.base.statuses;

  public async splitPdf() { // 切割PDF用的，先扔个案例。
    const { service } = this;
    const pdfDoc = await PDFDocument.create();
    const pdf = await service.task.base.getOssData('fc7539b21810cd4f0f0fb620', '14188.3e57ac63-a05e-45fb-8362-61d20136ec39', 'pdf');
    const ossPdfDoc = await PDFDocument.load(pdf);
    const pages = await pdfDoc.copyPages(ossPdfDoc, 'a'.repeat(50).split('').map((_: any, index) => index));
    pages.forEach((item: any) => {
      pdfDoc.addPage(item);
    });
    const pdfBytes = await pdfDoc.saveAsBase64();
    const ossKey = await service.task.base.getOssKey('fc7539b21810cd4f0f0fb620', '14188.3e57ac63-a05e-45fb-8362-61d20136ec39.1', 'pdf');
    await service.oss.upload(ossKey, pdfBytes, 'base64');
  }

  // 机器识别成功后，需要暂存机器结果
  public async stashMachineHtml(appKey: string, taskId: number) {
    const { service } = this;
    const html = await service.task.base.getOssData(appKey, taskId, 'html');
    await service.task.base.setOssData(appKey, taskId, 'machine.html', html);
    return html;
  }

  // 暂存多FBD/Word任务Html
  public async stashPreHandleMachineHtml(appKey: string, id: string) {
    const { service } = this;
    const html = await service.task.base.getOssData(appKey, id, 'html');
    await service.task.base.setOssData(appKey, id, 'machine.html', html);
    return html;
  }

  public async mergePreHandleHtml(
    appKey: string,
    taskId: number,
    files: any
  ) {
    const { service } = this;
    const bodysHtmlArr = await Promise.all(files.body.map(({ id }) => service.task.base.getOssData(appKey, encodeURIComponent(`${taskId}.${id}`), 'html')));
    const answersHtmlArr = await Promise.all(files.answer.map(({ id }) => service.task.base.getOssData(appKey, encodeURIComponent(`${taskId}.${id}`), 'html')));
    const bodysHtml = bodysHtmlArr.join('<hr data-label="group_split_line" />');
    const answersHtml = answersHtmlArr.join('<hr data-label="group_split_line" />');
    const bodyAnswerLine = bodysHtml && answersHtml ? '<hr data-label="text_answer_split_line" />' : '';
    const html = bodysHtml + bodyAnswerLine + answersHtml;
    const processedHtml = await this.service.task.stat.processLatex(html, taskId);
    await service.task.base.setOssData(appKey, taskId, 'html', processedHtml);
    await service.task.base.setOssData(appKey, taskId, 'machine.html', processedHtml);
    await service.task.base.setOssData(appKey, taskId, 'origin.html', html);
    return html;
  }

  public async convert2IA(url: string) {
    if (!url) return;
    const { config, service } = this;
    const bucket = config.workOrder.bucket;
    const ossClient = service.oss.createOss(bucket);
    const options = { headers:{ 'x-oss-storage-class':'IA' } };
    if (url.indexOf('files') === -1) return;
    const tempUrl = url.slice(url.indexOf('files'));
    await ossClient.copy(tempUrl, tempUrl, options);
    this.logger.info(`initV2Queue convert2IA success url:${url}`);
  }

  private async initV2Queue(urlParam: string, appkey: string, taskid: string, subject: 'chinese' | 'math' | 'en-math', fbds: { id: string; fbdName: string, type: 'body' | 'answer' }[], meta: any, type: 'word' | 'pdf' | 'fbd' = 'fbd', projectId?: number, taskName?: string, isNewWord = false, origin_url= '') {
    const { config } = this;
    let url = urlParam;
    await this.transformMeta(appkey, meta);
    if (typeof url === 'string') url = url.replace('-internal', '');

    // 文件转低频
    try {
      await this.convert2IA(url);
    } catch (e) {
      this.logger.info(`initV2Queue convert2IA error taskid:${taskid} url:${url}`);
    }

    // meta book_type 判断是否进新流程
    // this.logger.info(`initV2Queue - meta ${taskid}- ${JSON.stringify(meta)} isNewWord ${isNewWord}`);
    let book_type = 'old';
    if (/(试卷|练习册)/.test((meta.bookType as string || ''))) {
      book_type = 'new';
    }

    if (type === 'word') book_type = 'old';

    // 如果是 new word 任务，直接调新队列
    if (isNewWord) {
      this.logger.info(`initV2Queue taskid: ${taskid} ${JSON.stringify({
        url, origin_url, appkey, taskid, subject, fbds, project_id: projectId, task_name: taskName, book_type,
        // k8s 内部调用要用 ip 的形式
        callback_url: 'http://**************/api/open/taskV2/updateWordNew',
        // callback_url: 'http://xdoc.open.hexinedu.com/api/open/taskV2/updateWordNew',
      })}`);
      const res: urllib.HttpClientResponse<{ code: number; message: string }> = await urllib.
        request(
          this.app.env === 'prod' ? config.taskV2Rbs.initQueueWordUrlNew! : config.taskV2Rbs.initQueueWordUrlNewTask!,
          {
            method: 'POST',
            contentType: 'json',
            dataType: 'json',
            data: {
              task_id: taskid,
              task_type: 'to_aspose', // xdoc_word_html_new
              task_level: meta.isAIEdit ? 0 : undefined,
              task_info: {
                // url 是用来生成 html 的，url_for_pdf 是用来生成 pdf 的
                // ai-edit 场景中，origin_url 是用来生成 pdf 的，url 是用来转 html 的
                // 其他 word 场景中，origin_url 是用来转 html 的，url 是用来生成 pdf 的
                // 另外，origin_url 可能是空的，因为一些 word 不需要转换（压缩、打标记）
                url: (meta.isAIEdit ? url : origin_url) || url,
                url_for_pdf: meta.isAIEdit ? origin_url : url,
                appkey,
                taskid,
                subject, fbds, project_id: projectId, task_name: taskName, book_type, meta_subject: meta.subject,
                stage: meta.stage || 'junior',
                business_type: 'xdoc_w_html',
                // callback_url: 'http://**************/api/open/taskV2/updateWordNew',
                callback_url: `${config.ngrok.callbackUrl}/api/open/taskV2/updateWordNew`,
                callback_extras: ['taskid'], // 控制是否需要走 ai 修复
              },
            },
          }
        );
      this.logger.info(`initV2Queue new - taskid - ${taskid}, fbds- ${JSON.stringify(fbds)}, url - ${url}, res - ${res.data.message}`);
      return;
    }

    const resp: urllib.HttpClientResponse<{ code: number; message: string }> = await urllib.request(
      type === 'fbd' ? config.taskV2Rbs.initQueueFbdUrl : config.taskV2Rbs.initQueueWordUrl,
      {
        method: 'POST',
        data: { url, appkey, taskid, subject, fbds, meta: JSON.stringify(meta), project_id: projectId, task_name: taskName, book_type },
        contentType: 'json',
        dataType: 'json',
      }
    );

    const data = resp.data;
    if (data.code !== 0) return data.message;
    this.logger.info(`initV2Queue - taskid - ${taskid}, taskInfo- ${JSON.stringify({ url, appkey, taskid, subject, fbds, meta: JSON.stringify(meta), project_id: projectId, task_name: taskName, book_type })}`);
  }

  private async transformMeta(appKey: string, meta: any) {
    const { service, ctx } = this;
    // stage 学段，subject 学科，gradeUid 年级
    const { stage, subject, gradeUid } = meta;
    const app = await service.appl.getOneByUc({
      where: { appKey },
      attributes: ['userId'],
    });
    if (app?.userId) {
      const client = await service.client.base.getOne({
        where: { userId: app.userId },
        attributes: ['clientName'],
      });
      meta.clientName = client?.clientName ?? '';
    }
    try {
      const [respStages, respSubjects, respGrades] = await Promise.all([
        ctx.curl('http://content-server.hexinedu.com/api/content/base/stage/getList?academic=k12', { dataType: 'json', method: 'GET', data: { academic: 'k12' } }),
        ctx.curl('http://content-server.hexinedu.com/api/content/base/subjectRel/getList?academic=k12', { dataType: 'json', method: 'GET', data: { academic: 'k12' } }),
        ctx.curl('http://content-server.hexinedu.com/api/content/base/grade/getList?academic=k12', { dataType: 'json', method: 'GET', data: { academic: 'k12' } })
      ]);
      meta.stageName = respStages?.data?.data?.find((item) => item.key === stage)?.name || stage || '';
      meta.subjectName = respSubjects?.data?.data?.find((item) => item.stage === stage && item.key === subject)?.name || subject || '';
      meta.gradeName = respGrades?.data?.data?.find((item) => item.uid === gradeUid)?.name ?? '';
    } catch (e) {
      Object.assign(meta, {
        stageName: '',
        subjectName: '',
        gradeName: '',
      });
      this.logger.info(`[transformMeta] meta=${JSON.stringify(meta)}`, e);
    }
  }

  public async wordCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      meta: Partial<ITaskMetas>;
      file: Partial<ITaskFiles>;
      projectId?: number;
    }
  ) {
    const { ctx, service, statuses, logger } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      callbackUrl,
      extra,
      taskName,
      priority,
      file,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
      meta,
      projectId,
    } = task;
    await service.task.base.create({
      taskId,
      appKey,
      subject,
      callbackUrl,
      isTest,
      priority,
      extra,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.init,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      preprocessUserId: 0,
      operatAdminUserId: 0,
      imageCount: 0,
      catalog: '',
      bookId: task.bookId || 0,
      bookOrder: task.bookOrder || 0,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      resourceType: ETaskResourceType.WORDV2,
    });
    // 如果meta中没有这个字段，或者存在这个字段并且值为true时，结果为true
    // const runAiFixed = meta.runAiFixed !== false;
    const runAiFixed = true;
    // 保存到task_meta
    await service.task.meta.setMetas([taskId], { ...meta, runAiFixed });

    const words = file.words!;
    /*
     * let pdfs: ITaskWords;
     * if (file.pdfs) {
     *   pdfs = file.pdfs;
     * }
     */
    const handleOne = async(_taskId: number) => {
      let errorInfo: string | undefined;
      try {
        await Promise.all(words.body.map(async(word) => {
          errorInfo = await this.initV2Queue(word.url, appKey, `${_taskId}.${word.id}`, subject, [{ id: `${_taskId}.${word.id}`, fbdName: '', type: 'body' }], meta, 'word', projectId, taskName, true, word.origin_url);
        }));
        await Promise.all(words.answer.map(async(word) => {
          errorInfo = await this.initV2Queue(word.url, appKey, `${_taskId}.${word.id}`, subject, [{ id: `${_taskId}.${word.id}`, fbdName: '', type: 'answer' }], meta, 'word', projectId, taskName, true, word.origin_url);
        }));
        if (!errorInfo) {
          await super.update({ status: statuses.pending }, { where: { taskId: _taskId } });
        }
      } catch (e) {
        logger.error(`service.task.taskV2.wordCreate.handleOne：${e}, task: ${_taskId}`);
        errorInfo = (e as any).message || '系统异常';
      }
      if (errorInfo) {
        await this.update({ errorInfo, status: statuses.error }, { where: { taskId: _taskId } });
      }
    };
    ctx.runInBackground(async() => {
      try {
        await handleOne(taskId);
      } catch (e) {
        logger.error(`service.task.taskV2.wordCreate.runInBackground：${e}, task: ${taskId}`);
      }
    });
    return taskId;
  }

  public async pdfCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      meta: Partial<ITaskMetas>;
      file: Partial<ITaskFiles>;
    }
  ) {
    const { service, statuses, logger } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      callbackUrl,
      extra,
      taskName,
      priority,
      file,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
    } = task;
    await service.task.base.create({
      taskId,
      appKey,
      subject,
      callbackUrl,
      isTest,
      priority,
      extra,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.init,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      preprocessUserId: 0,
      operatAdminUserId: 0,
      imageCount: 0,
      catalog: '',
      bookId: task.bookId || 0,
      bookOrder: task.bookOrder || 0,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      resourceType: ETaskResourceType.PDF,
    });
    let errorInfo: string | undefined;
    try {
      // pdf 答案正文映射
      file.pdfs!.map((pdf) => {
        pdf.type = (pdf.type === 'answer' || Number(pdf.type!) === 0) ? 'answer' : 'body';
      });
      // 拿到pdf2img task_id 改状态为pdf预处理中
      errorInfo = await this.pdf2image(file.pdfs! , false, true);
      if (!errorInfo) {
        await super.update({ status: statuses.pending }, { where: { taskId } });
      }
    } catch (e) {
      logger.error(`service.task.taskV2.fbdCreate.handleOne：${e}, task: ${taskId}`);
      errorInfo = (e as any).message || '系统异常';
    }
    if (errorInfo) {
      await this.update({ errorInfo, status: statuses.error }, { where: { taskId } });
    }
    return taskId;
  }

  public async fbdCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      meta: Partial<ITaskMetas>;
      file: Partial<ITaskFiles>;
      projectId?: number;
    }
  ) {
    const { service, statuses, logger, ctx } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      callbackUrl,
      extra,
      taskName,
      priority,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
      meta,
      file,
      projectId,
    } = task;
    await service.task.base.create({
      taskId,
      appKey,
      subject,
      callbackUrl,
      isTest,
      priority,
      extra,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.init,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      preprocessUserId: 0,
      operatAdminUserId: 0,
      imageCount: 0,
      catalog: '',
      bookId: task.bookId || 0,
      bookOrder: task.bookOrder || 0,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      resourceType: ETaskResourceType.FBDV2,
    });
    const zips = file.zips!;
    // 初始化 fbd 全走新流程
    (meta as any).bookType = '试卷';
    const handleOne = async(taskId: number) => {
      let errorInfo: string | undefined;
      try {
        await Promise.all(zips.map(async(zip) => {
          errorInfo = await this.initV2Queue(zip.url, appKey, `${taskId}.${zip.id}`, subject, zip.fbds.filter((fbd) => fbd.able && fbd.isPreHandle).map((fbd) => ({ id: `${taskId}.${zip.id}.${fbd.name}`, fbdName: fbd.name, type: fbd.type })), meta, 'fbd', projectId, taskName);
        }));
        errorInfo = await this.pdf2image(file.pdfs!);
        if (!errorInfo) {
          await super.update({ status: statuses.pending }, { where: { taskId } });
        }
      } catch (e) {
        logger.error(`service.task.taskV2.fbdCreate.handleOne：${e}, task: ${taskId}`);
        errorInfo = (e as any).message || '系统异常';
      }
      if (errorInfo) {
        await this.update({ errorInfo, status: statuses.error }, { where: { taskId } });
      }
    };
    ctx.runInBackground(async() => {
      try {
        await handleOne(taskId);
      } catch (e) {
        logger.error(`service.task.taskV2.pdfCreate.runInBackground：${e}, task: ${taskId}`);
      }
    });
    return taskId;
  }

  /**
   *
   * @param pdfs pdf文件列表
   * @param isCallback
   * @param isPdfCreate 是否是创建大pdf任务
   * @returns
   */
  public async createPdf2imageTasks(pdfs: ITaskPdfs, isCallback = false, isPdfCreate = false) {
    const { app, config } = this;
    return await Promise.all(pdfs.map((pdf) => {
      const url = isPdfCreate ? `${config.workOrder.api}/api/open/queue/v1/pdf_to_image` : `${config.workOrder.api}/api/open/queue/v1/pdf_to_image_solr`;
      this.logger.info(`[pdf2image] send req md5:${pdf.id} url:${pdf.url} is_callback: ${isCallback}`);
      return app.curl(url, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          md5: pdf.id,
          pdf_url: pdf.url,
          is_callback: isCallback,
        }),
      });
    }));
  }

  public async pdf2image(pdfs: ITaskPdfs, isCallback = false, isPdfCreate = false) {
    try {
      const res = await this.createPdf2imageTasks(pdfs, isCallback, isPdfCreate);
      const taskIds = res.map((item) => item?.data?.data?.task_id);
      if (taskIds.length === pdfs.length) {
        taskIds.forEach((item: string, index: number) => {
          if (item) pdfs[index].task_id = item;
        });
      }
    } catch (e) {
      this.logger.error(`PDF转图片异常：${e}`);
      return `pdf转图片异常 ${e}`;
    }
  }

  public async splitCreate(
    task: {
      parentTaskId: number;
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      taskNames?: string[];
      open: boolean;
      isTest: boolean;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      resourceType: ETaskResourceType.FBDV2 | ETaskResourceType.WORDV2 ;
      bookId: number;
      bookOrder: number;
      length: number;
    },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service, statuses } = this;
    const {
      parentTaskId,
      appKey,
      subject,
      callbackUrl,
      extra,
      taskName,
      taskNames,
      priority,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
      resourceType,
      bookId,
      bookOrder,
      length,
    } = task;
    const taskIds: number[] = await service.task.base.getBatchIds(length);
    if (taskIds.length !== length) {
      // 不一致的话 去掉没有的
      // taskIds.splice(length, taskIds.length - length);
      throw new Error('service.taskV2.splitCreate.if: taskIds.length不对');
    }
    const tasks = taskIds.map((taskId, index) => {
      return {
        parentTaskId,
        taskId: Number(taskId),
        appKey,
        subject,
        callbackUrl,
        isTest,
        priority,
        extra,
        taskName: taskNames?.[index] || `${taskName}-${index + 1}`,
        isCallback: false,
        callbackError: false,
        status: statuses.unmarked,
        errorInfo: '',
        markUserId: 0,
        reviewUserId: 0,
        splitUserId: 0,
        preprocessUserId: 0,
        operatAdminUserId: 0,
        imageCount: 0,
        catalog: '',
        bookId,
        bookOrder: bookOrder + index + 1,
        taskType,
        timeLimit,
        timeWarning,
        countdown: timeLimit,
        resourceType,
      };
    });
    await service.task.base.bulkCreate(tasks, { ...options });
    return taskIds;
  }

  public async splitPdfCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      open: boolean;
      isTest: boolean;
      bookId: number;
      bookOrder: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      parentTaskId: number;
      images: string[] | { id?: string; uri?: string; name?: string; filename?: string }[];
      pdfImageCount?: number;
      meta: Partial<ITaskMetas>;
    },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service, statuses, ctx, logger } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    logger.info(`[创建任务] Task ${taskId} relatedCreate start.`);
    const {
      appKey,
      priority,
      subject,
      callbackUrl,
      extra,
      taskName,
      images,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
      bookId,
      bookOrder,
      parentTaskId,
      pdfImageCount,
      meta,
    } = task;
    await service.task.base.create({
      parentTaskId,
      taskId,
      appKey,
      priority,
      subject,
      callbackUrl,
      isTest,
      extra,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.init,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      operatAdminUserId: 0,
      preprocessUserId: 0,
      imageCount: images.length,
      catalog: '',
      bookId: bookId || 0,
      bookOrder: bookOrder || 0,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      pdfImageCount,
    } , { ...options });
    logger.info(`[创建任务] Task ${taskId} create done.`);
    // 异步创建图片任务
    ctx.runInBackground(async() => {
      logger.info(`[创建任务] Task ${taskId} execute source images in background start.`);
      try {
        const sourceImages: SourceImageModel.Attributes[] = [];
        let errorInfo = '';
        for (let i = 0; i < images.length; i += 1) {
          const item = images[i];
          /*
           * Tips：
           * 这里拿到的图片 uri 可能是前端计算出来的 md5 值，
           * 通过工单系统传递过来的。
           */
          const uri = typeof item === 'string' ? item : item.uri || item.id;
          const filename = typeof item === 'string' ? `${i * 5}.jpg` : item.name || item.filename || `${i * 5}.jpg`;
          const imageId = task.open ? uuid().replace(/-/g, '') : uri;
          /*
           * 过滤掉重复出现的图片
           * 原创建图片任务后续的流程 更新image表
           */
          if (sourceImages.some((s) => s.imageId === imageId)) {
            continue;
          }
          if (task.open) {
            const error = await this.service.task.base.uploadImage(appKey, uri, imageId);
            if (error) {
              errorInfo = error;
              break;
            }
          }
          sourceImages.push({
            taskId,
            imageId,
            appKey,
            filename,
            bookId: task.bookId || 0,
            taskOrder: i + 1,
            status: ESourceImageStatus.init,
            result: '',
            info: '',
          });
        }
        if (errorInfo || !sourceImages.length && task.open) {
          errorInfo = errorInfo || '没有图片';
          await this.update({ errorInfo, status: statuses.error }, { where: { taskId } });
          await service.task.callback.callback({
            taskId,
            appKey,
            callbackUrl,
            errorInfo,
            extra: extra || '',
            taskName: taskName || '',
            status: statuses.error,
          });
          return;
        }
        await service.sourceImage.bulkCreate(sourceImages);
        logger.info(`[创建任务] Task ${taskId} bulk create source images done.`);
        await super.update({ status: statuses.preCropCheck }, { where: { taskId } });
        logger.info(`[创建任务] Task ${taskId} update status to statuses.preCropCheck done.`);
        (task as any).taskId = taskId;
        await this.service.task.base.pushPreCropCheck([task], { type: meta.imageHtmlVersion || '' });
        logger.info(`[创建任务] Task ${taskId} push pre-crop queue done.`);
        logger.info(`[创建任务] Task ${taskId} meta: ${JSON.stringify(meta)}`);
      } catch (e) {
        logger.error(e);
        await this.update({ errorInfo: '创建任务异常: ' + (e as any).message, status: statuses.error }, { where: { taskId } });
        await service.task.callback.callback({
          taskId,
          appKey,
          callbackUrl,
          errorInfo: '系统异常',
          extra: extra || '',
          taskName: taskName || '',
          status: statuses.error,
        });
      }
    });
    return taskId;
  }

  public async getOneById(taskId: number) {
    const { service } = this;
    const task: any = await service.task.base.getOne({
      where: { taskId },
      attributes: [
        'taskId', 'bookId', 'taskName', 'bookOrder', 'createTime',
        'subject', 'appKey', 'imageCount', 'status',
        'markUserId', 'reviewUserId', 'preprocessUserId', 'splitUserId', 'operatAdminUserId',
        'errorInfo', 'isCallback', 'callbackError',
        'startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime',
        'rerun', 'rerunTimes', 'rerunProcessUserId',
        'taskType', 'timeLimit', 'timeWarning', 'countDown', 'warning',
        'isTest', 'resourceType', 'priority', 'parentTaskId'
      ],
    });
    if (!task) {
      return null;
    }
    task.taskCode = service.task.base.getTaskCode(task.createTime.getTime(), task.taskId);

    // 搜索用户名
    const idMap = {};
    if (task.markUserId || task.reviewUserId) {
      const users = await service.user.search([task.markUserId, task.reviewUserId, task.operatAdminUserId]);
      for (const { userId, nickname } of users) {
        idMap[userId] = nickname;
      }
    }
    task.reviewUsername = idMap[task.reviewUserId] || '';
    task.markUsername = idMap[task.markUserId] || '';
    task.operatadminusername = idMap[task.operatAdminUserId] || '';

    const appl = await service.appl.getOneByUc({ where: { appKey: task.appKey } });
    task.appName = appl ? appl.appName : '';

    task.createTime = Number(new Date(task.createTime));
    task.startMarkTime = task.startMarkTime ? Number(new Date(task.startMarkTime)) : null;
    task.endMarkTime = task.endMarkTime ? Number(new Date(task.endMarkTime)) : null;
    task.startReviewTime = task.startReviewTime ? Number(new Date(task.startReviewTime)) : null;
    task.endReviewTime = task.endReviewTime ? Number(new Date(task.endReviewTime)) : null;

    return task;
  }

  public async changeChapter({ appKey, taskId, index, body, level }: { appKey: string, taskId: number; index: number, body?: string, level?: number }) {
    const { service } = this;
    const html = await service.task.base.getOssData(appKey, taskId, 'html');
    const nodes = parseHtml(html);
    let initial = 0;
    let changeChapter: any = null;
    // 按索引查找对应标题；
    for (const { node } of iterateNode(nodes)) {
      if (node.type === 'element' && node.dataset.label === 'header') {
        initial += 1;
        if (index === initial - 1) {
          changeChapter = node;
          break;
        }
      }
    }
    if (typeof level === 'number' && changeChapter) {
      changeChapter.dataset.level = level.toString();
      changeChapter.attrs['data-level'] = level.toString();
    }
    if (typeof body === 'string' && changeChapter) {
      const parseBody: any = parseHtml(body)[0] ? parseHtml(body)[0] : { cls: {}, style: {}, children: [] };
      changeChapter.children = parseBody.children;
      changeChapter.cls = parseBody.cls;
      if (parseBody.attrs.class) {
        changeChapter.attrs.class = parseBody.attrs.class;
      } else {
        delete changeChapter.attrs.class;
      }
      changeChapter.style = parseBody.style;
    }
    return getHtml(nodes);
  }

  public async refreshChapter({ appKey, taskId }: { appKey: string, taskId: number; }) {
    const { service, ctx } = this;
    const html = await service.task.base.getOssData(appKey, taskId, 'html');
    const nodes = parseHtml(html);
    const chapterNodeArr: any[] = [];
    // 按索引查找对应标题；
    for (const { node } of iterateNode(nodes)) {
      if (node.type === 'element' && node.dataset.label === 'header') {
        chapterNodeArr.push(node);
      }
    }
    const chapterHtmlArr = chapterNodeArr.map((node) => getText([node]));
    let chapterLevelArr: number[] = [];
    try {
      const res = await ctx.curl('http://192.168.2.91:5314/header_classify', {
        method: 'POST',
        dataType: 'json',
        data: { text_list: JSON.stringify(chapterHtmlArr) },
        timeout: 60 * 1000,
      });
      const preds = res.data.preds;
      if (Array.isArray(preds)) {
        const levelArr = preds.map((item) => Number(item.replace('header', '')));
        if (levelArr.every((item) => !isNaN(item))) {
          chapterLevelArr = levelArr;
        }
      }
      // this.logger.info(`调用机器调整输出层级, 输入：${JSON.stringify(chapterHtmlArr)}, 返回：${JSON.stringify(res)}`);
    } catch (e) {
      this.logger.error(`调用机器调整标题接口出错：${e}`);
    }
    if (chapterHtmlArr.length !== chapterLevelArr.length) return '';
    chapterNodeArr.forEach((node, i) => {
      const level = chapterLevelArr[i];
      if (typeof level === 'number') {
        node.dataset.level = level.toString();
        node.attrs['data-level'] = level.toString();
      }
    });
    return getHtml(nodes);
  }

  public async analysisChapterLevel(text_list: string[]) {
    const { ctx } = this;
    try {
      const res = await ctx.curl('http://192.168.2.91:5314/header_classify', {
        method: 'POST',
        dataType: 'json',
        data: { text_list: JSON.stringify(text_list) },
        timeout: 60 * 1000,
      });
      const preds = res.data.preds;
      if (Array.isArray(preds)) {
        const levelArr = preds.map((item) => Number(item.replace('header', '')));
        if (levelArr.every((item) => !isNaN(item))) {
          return levelArr;
        }
      }
      this.logger.info(`调用机器调整输出层级, 输入：${JSON.stringify(text_list)}, 返回：${JSON.stringify(res)}`);
    } catch (e) {
      this.logger.error(`调用机器调整标题接口出错：${e}`);
    }
  }

  public async restart(
    {
      resourceType,
      appKey,
      taskId,
      subject,
      rerun,
      bookId,
      taskName,
    }: {
      resourceType: ETaskResourceType,
      appKey: string,
      taskId: number,
      subject: 'chinese' | 'math' | 'en-math',
      rerun: boolean,
      bookId?: number,
      taskName: string,
    },
    file: Partial<ITaskFiles>,
    meta: Partial<ITaskMetas>,
    isNewWord?: boolean,
    runAiFixed?: boolean,
    options?: { transaction?: sequelize.Transaction }) {
    const { service } = this;
    const { statuses } = service.task.base;
    let errorInfo;

    // 加入projectId
    const book = await service.book.getOne({ where: { id: bookId } });
    this.logger.info(`restart task ${taskId} projectId ${book?.projectId}`);

    if (resourceType === ETaskResourceType.WORDV2) {
      const words = file.words!;
      await Promise.all(words.body.filter((word) => word.isPreHandle).map(async(word) => {
        errorInfo = await this.initV2Queue(word.url, appKey, `${taskId}.${word.id}`, subject, [{ id: `${taskId}.${word.id}`, fbdName: '', type: 'body' }], meta, 'word', book?.projectId, taskName, isNewWord, word.origin_url);
      }));
      await Promise.all(words.answer.filter((word) => word.isPreHandle).map(async(word) => {
        errorInfo = await this.initV2Queue(word.url, appKey, `${taskId}.${word.id}`, subject, [{ id: `${taskId}.${word.id}`, fbdName: '', type: 'answer' }], meta, 'word', book?.projectId, taskName, isNewWord, word.origin_url);
      }));
    } else if (resourceType === ETaskResourceType.FBDV2) {
      const zips = file.zips!;
      await Promise.all(zips.map(async(zip) => {
        errorInfo = await this.initV2Queue(zip.url, appKey, `${taskId}.${zip.id}`, subject, zip.fbds.filter((fbd) => fbd.able && fbd.isPreHandle).map((fbd) => ({ id: `${taskId}.${zip.id}.${fbd.name}`, fbdName: fbd.name, type: fbd.type })), meta, 'fbd', book?.projectId, taskName);
      }));
    }
    if (!errorInfo) {
      // 重置任务状态
      service.task.base.update(
        {
          status: statuses.pending,
          markUserId: rerun ? undefined : 0,
          reviewUserId: 0,
          splitUserId: 0,
          isCallback: false,
          callbackError: false,
          startMarkTime: rerun ? undefined : null,
          endMarkTime: rerun ? undefined : null,
          startReviewTime: null,
          endReviewTime: null,
        },
        { ...options, where: { taskId } }
      );
      // 将runAiFixed保存到task_meta表
      if (typeof runAiFixed === 'boolean') {
        // 获取现有meta，更新runAiFixed，然后设置全量meta
        const currentMeta = await service.task.meta.getMetas({ taskId });
        await service.task.meta.setMetas(
          [taskId],
          { ...currentMeta, runAiFixed }
        );
      }
    } else {
      await this.update({ errorInfo, status: statuses.error }, { where: { taskId } });
    }
  }

  public async cleanHtml(html: string) {
    const nodes = parseHtml(html);
    for (const { node } of iterateNode(nodes)) {
      if (node.type === 'element') {
        const { dataset, attrs } = node;
        if (attrs['data-check']) {
          delete attrs['data-check'];
        }
        if (node.tagName === 'img') {
          delete node.attrs.alt;
        }
        if (dataset.label === 'latex' && node.children?.[0]?.type === 'text' && node.children.length === 1) {
          const content = node.children?.[0]?.content;
          Object.keys(node).forEach((key) => {
            delete node[key];
          });
          (node as any).type = 'text';
          (node as any).content = content;
        }
      }
    }
    return getHtml(nodes);
  }

  public splitTaskHtml(html: string) {
    const htmlArr = html.split(/<hr data-label="text_answer_split_line" ?\/?>/);
    const bodyHtml = htmlArr[0];
    const answerHtml = htmlArr[1];
    const bodyGroups: any[] = bodyHtml ? bodyHtml.split(/<hr data-label="group_split_line" ?\/?>/) : [];
    const answerGroups: any[] = answerHtml ? answerHtml.split(/<hr data-label="group_split_line" ?\/?>/) : [];

    let groups: { body: string; answer: string; }[][] = [];
    if (bodyGroups.length > answerGroups.length) {
      groups = bodyGroups.map((html, index) => {
        const taskBodys = html.split(/<hr data-label="text_split_line" ?\/?>/);
        const answerGroupHtml = answerGroups[index];
        const taskAnswers = answerGroupHtml ? answerGroupHtml.split(/<hr data-label="answer_split_line" ?\/?>/) : [];
        let tasks: { body: string; answer: string; }[] = [];
        if (taskBodys.length > taskAnswers.length) {
          tasks = taskBodys.map((html: string, index: number) => {
            return {
              body: html,
              answer: taskAnswers[index] || '',
            };
          });
        } else {
          tasks = taskAnswers.map((html: string, index: number) => {
            return {
              body: taskBodys[index] || '',
              answer: html,
            };
          });
        }
        return tasks;
      });
    } else {
      groups = answerGroups.map((html, index) => {
        const taskAnswers = html.split(/<hr data-label="answer_split_line" ?\/?>/);
        const bodyGroupHtml = bodyGroups[index];
        const taskBodys = bodyGroupHtml ? bodyGroupHtml.split(/<hr data-label="text_split_line" ?\/?>/) : [];
        let tasks: { body: string; answer: string; }[] = [];
        if (taskBodys.length > taskAnswers.length) {
          tasks = taskBodys.map((html: string, index: number) => {
            return {
              body: html,
              answer: taskAnswers[index] || '',
            };
          });
        } else {
          tasks = taskAnswers.map((html: string, index: number) => {
            return {
              body: taskBodys[index] || '',
              answer: html,
            };
          });
        }
        return tasks;
      });
    }
    const htmls: string[] = [];
    groups.forEach((item) => {
      item.forEach(({ body, answer }: { body: string; answer: string; }) => {
        const html = body ? answer ? `${body}<p>&nbsp;</p><hr data-label="answer_separator" /><p>&nbsp;</p>${answer}` : body : answer;
        htmls.push(html);
      });
    });
    return htmls;
  }

  public getValidateJson(json: TJsonNode[]) {
    const processInfo = { isError: false };
    validateJsonNodes(json, processInfo);
    return { validatedJson: json, isError: processInfo.isError };
  }

  public groupTasks(tasks: Task[]): Task[][] {
    const grouped: Task[][] = [];
    let currentGroup: Task[] = [];
    let currentTotal = 0;

    tasks.forEach((task) => {
      if (currentTotal + task.length < 300) {
        currentGroup.push(task);
        currentTotal += task.length;
      } else {
        grouped.push(currentGroup);
        currentGroup = [task];
        currentTotal = task.length;
      }
    });

    if (currentGroup.length > 0) {
      grouped.push(currentGroup);
    }
    return grouped;
  }
}
