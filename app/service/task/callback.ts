/**
 * @file task回调
 * <AUTHOR>
 */

'use strict';

import { Service } from 'egg';
import { IClientMetas } from '../../model/clientMeta';
import { json2docx7day, json2docxZip7day } from '../../core/utils/json2docx7day';

export default class TaskCallbackService extends Service {

  private readonly queueKey = 'task:callback:error:queue';
  private readonly callbackDuration = 5 * 60 * 1000;
  private readonly defaultCallbackTimes = 2;

  public async callback(
    task: {
      taskId: number;
      callbackUrl: string;
      isCallback?: boolean;
      extra?: string;
      taskName: string;
      errorInfo: string;
      status: number;
      appKey: string;
      ticketId?: string;
    },
    times: number | undefined = this.defaultCallbackTimes,
    delayTime: number | undefined = 0
  ) {
    const { app, service, logger } = this;
    const { statuses, openStatuses } = service.task.base;
    const logPrefix = `task callback ing: ${task.taskId}`;
    logger.info(`start ${logPrefix}!`);
    if (delayTime) {
      logger.info(`${logPrefix} is delayed, delayTime: ${delayTime} !`);
      return await this.push(task.taskId, times, delayTime);
    }
    if (!/^http.*/.test(task.callbackUrl) || times <= 0) {
      logger.info(`${logPrefix} is over, some params error, callbackUrl: ${task.callbackUrl}, times: ${times}, delayTime: ${delayTime}!`);
      if (!task.isCallback) {
        await service.task.base.update({
          isCallback: true,
          callbackError: true,
          callbackTime: new Date(),
        }, { where: { taskId: task.taskId } });
      }
      return;
    }
    let isCallback = false;
    let clientMetas: Partial<IClientMetas> | undefined;
    const appl = await service.appl.getOneByUc({ where: { appKey: task.appKey }, attributes: ['userId'] });
    if (appl && appl.userId) {
      clientMetas = await service.client.meta.getMetas({
        userId: appl.userId,
        key: ['exportJson', 'exportDocx', 'api7dayDoc', 'api7dayDocFlow', 'api7dayDocSeparate'],
      });
    }
    const config = service.client.meta.setDefaultMetas(clientMetas || {});
    const data: any = {
      taskId: task.taskId,
      taskName: task.taskName,
      extra: task.extra || '',
      status: task.status === statuses.error ? openStatuses.failed : openStatuses.successful,
      errorInfo: task.status === statuses.error ? task.errorInfo : null,
    };
    data.meta = await service.task.base.getOssData(task.appKey, task.taskId, 'meta.json');
    if (data.meta) {
      data.metaJson = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json', false);
    }
    if (task.status !== statuses.error) {
      if (config.exportJson) {
        data.json = service.task.base.getUrl(task.appKey, task.taskId, 'formatted.json', false);
      }
      if (config.exportDocx) {
        data.docx = service.task.base.getUrl(task.appKey, task.taskId, 'formatted.docx', false);
      }
      let json: any = null;
      if (config.api7dayDoc) {
        json = await service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json');
        const buffer = await json2docx7day(json, 'flow_body_answer');
        await service.task.base.setOssData(task.appKey, task.taskId, 'json.docx', buffer);
        data.jsonDocx = service.task.base.getUrl(task.appKey, task.taskId, 'json.docx', false);
      }
      if (config.api7dayDocFlow) {
        json = json || await service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json');
        const buffer = await json2docx7day(json, 'flow');
        await service.task.base.setOssData(task.appKey, task.taskId, 'flow.json.docx', buffer);
        data.jsonDocxFlow = service.task.base.getUrl(task.appKey, task.taskId, 'flow.json.docx', false);
      }
      if (config.api7dayDocSeparate) {
        json = json || await service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json');
        const buffer = await json2docxZip7day(json);
        await service.task.base.setOssData(task.appKey, task.taskId, 'json.docx.zip', buffer);
        data.jsonDocxZip = service.task.base.getUrl(task.appKey, task.taskId, 'json.docx.zip', false);
      }
    }
    try {
      const res = await app.curl(task.callbackUrl, {
        method: 'POST',
        timeout: 20 * 1000,
        data,
      });
      if (res.status === 200) {
        isCallback = true;
      } else {
        logger.error(`${logPrefix} error,  response status : ${res.status}`);
      }
    } catch (e) {
      logger.error(`${logPrefix} error`, e);
    }
    if (isCallback) {
      logger.info(`${logPrefix} ok!`);
      return await service.task.base.update({
        isCallback: true,
        callbackError: false,
        callbackTime: new Date(),
      }, { where: { taskId: task.taskId } });
    }
    if (times > 1) {
      logger.info(`${logPrefix} repeat, last times : ${times - 1}!`);
      await this.push(task.taskId, times - 1);
    } else {
      logger.info(`${logPrefix} error, after some times repeat!`);
      await service.task.base.update({
        isCallback: true,
        callbackError: true,
        callbackTime: new Date(),
      }, { where: { taskId: task.taskId } });
    }
  }

  public async push(taskId: number, times: number, callbackDuration: number = this.callbackDuration) {
    await this.app.redis.zadd(this.queueKey, `${Number(new Date()) + callbackDuration}`, `${taskId}:${times}`);
  }

  public async getAllExpiredTasks(): Promise<{ taskId: number; times: number }[]> {
    const { redis } = this.app;
    const now = Number(new Date());
    const list = await redis.zrangebyscore(this.queueKey, 0, now);
    if (!list.length) {
      return [];
    }
    await redis.zremrangebyscore(this.queueKey, 0, now);
    return list.map((item: string) => {
      const [taskId, times] = item.split(':');
      return {
        times: Number(times),
        taskId: Number(taskId),
      };
    });
  }
}
