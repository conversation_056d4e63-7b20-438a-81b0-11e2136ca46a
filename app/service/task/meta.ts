/**
 * @file 任务配置信息
 */

'use strict';

import { Context } from 'egg';
import MetaService from '../../core/base/metaService';
import { Attributes, defineAttributes, Instance, ITaskMetas, TASK_DEFAULT_METAS } from '../../model/taskMeta';

export default class TaskMetaService
  extends MetaService<'taskId', number, ITaskMetas, Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.TaskMeta, defineAttributes, TASK_DEFAULT_METAS, 'taskId');
  }
}
