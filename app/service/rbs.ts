'use strict';

import { Service } from 'egg';

export default class RBSService extends Service {
  async initRBSQueue({
    task_id,
    task_level,
    task_type,
    task_info,
  }: {
    task_id: string;
    task_type: string;
    task_level?: number;
    task_info: any;
  }) {
    try {
      const res = await this.app.curl(this.config.taskV2Rbs.initQueueWordUrlNewTask!, {
        method: 'POST',
        dataType: 'json',
        headers: { 'x-request-from': 'hexin', 'Content-Type': 'application/json' },
        data: JSON.stringify({
          task_id,
          task_type,
          task_level,
          task_info,
        }),
      });
      return res;
    } catch (e) {
      throw new Error(e);
    }
  }

  async initJSON2DOCXQueue(params: any) {
    try {
      const res = await this.app.curl('http://content-server.hexinedu.com/api/content/open/publish/convertToDocxFromWordId', {
        method: 'POST',
        dataType: 'json',
        headers: { 'x-request-from': 'hexin', 'Content-Type': 'application/json' },
        data: JSON.stringify({
          ...params,
          'queue': true,
          'state': 'word',
          'callback': {
            'method': 'POST',
            'url': 'http://xdoc.open.hexinedu.com/api/open/task/callback',
            'retryCount': 3,
            'returnProps': ['status', 'result', 'reason', 'params'],
          },
          'successStatus': {
            'prop': 'status',
            'value': [0],
          },
        }),
      });
      return res;
    } catch (e) {
      throw new Error(e);
    }
  }

  async callbackRBS({
    task_id,
    task_type,
    task_status,
  }: {
    task_id: string;
    task_type: string;
    task_status: boolean;
  }) {
    try {
      const res = await this.app.curl('http://rbs-founder.hexinedu.com/api/proxy/task/callback', {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          task_id,
          task_type,
          task_status,
        }),
      });
      return res;
    } catch (e) {
      throw new Error(e);
    }
  }
}
