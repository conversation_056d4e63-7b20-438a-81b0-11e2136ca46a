'use strict';

import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, ESourceImageStatus, Instance } from '../model/sourceImage';
import TaskService from './task/base';
import * as sequelize from 'sequelize';
import baseError from '../core/base/baseError';

interface IImageRectInfo {
  x: number;
  y: number;
  w: number;
  h: number;
  i?: number;
}

/*
 * interface ISubmitImageData {
 *   id: number;
 *   updateTime: Date;
 *   createTime: Date;
 *   isDel: number;
 *   imageId: string;
 *   appKey: string;
 *   filename: string;
 *   taskId: number;
 *   taskOrder: number;
 *   bookId: number;
 *   status: number;
 *   result: string;
 *   info: IImageRectInfo;
 * }
 */
interface IImageInfo { // 每个图片的切图结果
  imageId: string;
  result?: IImageRectInfo[];
}

export default class SourceImageService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.SourceImage, defineAttributes);
  }

  public userId: number;

  public async submitPreCrop(taskId: number, data: IImageInfo[]) {
    const { service } = this;
    const statuses: InstanceType<typeof TaskService>['statuses'] = service.task.base.statuses;
    const task = await this.service.task.base.getOne({
      where: { taskId, status: statuses.preCropChecked },
      attributes: ['id'],
    });
    if (!task) {
      return {
        msg: '任务不存在或状态错误',
        type: 0,
      };
    }
    const existImageCount = await service.sourceImage.count({ where: { imageId: data.map((item) => item.imageId), taskId } });
    if (existImageCount !== data.length) {
      return {
        msg: '部分图片不存在',
        type: 1,
      };
    }

    /*
     * 保存图片结果，更新图片状态
     * @todo：这里建议用 bulkCreate 方法重写，优化性能！
     */
    for (let d = 0; d < data.length; d += 1) {
      await service.sourceImage.update(
        { status: ESourceImageStatus.finished, result: data[d].result?.length ? JSON.stringify(data[d].result) : '' },
        { where: { imageId: data[d].imageId, taskId } });
    }
    return {
      msg: '',
      type: 2,
    };
  }

  // 批量通过预切图
  public async batchFinishPreCrop(taskIDArr: number[], userId: number) {
    // const { logger } = this.ctx;
    this.userId = userId;
    const taskList = await this.service.sourceImage.getAll({
      where: { taskId: { [sequelize.Op.in]: taskIDArr } },
      // raw: true,
    });
    const taskImageMap: {
      [key: number]: any[]
    } = {};
    for (let i = 0; i < taskIDArr.length; i += 1) {
      const taskID = taskIDArr[i];
      taskImageMap[taskID] = taskList.filter((item) => item.taskId === taskID).map((item) => ({
        imageId: item.imageId,
        result: item.result ? JSON.parse(item.result) : [],
        info: item.info ? JSON.parse(item.info) : undefined,
      }));
    }
    return await this.batchSubmitPreCrop(taskImageMap as any, taskList);
  }

  public async finishTask(taskId: number) {
    const { service } = this;
    const { statuses } = this.service.task.base;
    const [affectedCount] = await this.service.task.base.update(
      { status: statuses.preCropProcessed },
      { where: { taskId, status: statuses.preCropChecked } }
    );
    if (!affectedCount) {
      return baseError.dataNotExistError('任务不存在或状态错误');
    }
    await service.task.base.pushPreCrop(taskId);
    return 0;
  }

  public async batchSubmitPreCrop(taskMap: {
    [key: number]: any[]
  }, taskList: any) {
    const res: number[] = [];
    for (const taskId in taskMap) {
      const imageInfo = taskMap[taskId];
      const { type } = await this.submitPreCrop(Number(taskId), imageInfo);
      if (type === 2) {
        // 说明该任务图片全部保存
        res.push(Number(taskId));
        // finish掉这个任务
        await this.finishTask(Number(taskId));
      }
      // res.push(Number(taskId));
    }
    return res.map((taskId) => {
      return {
        taskId,
        imageLength: taskMap[taskId].length,
        task: taskList.find((item) => item.taskId === taskId),
      };
    });
  }
}
