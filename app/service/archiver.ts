'use strict';
import { Service } from 'egg';
import * as archiver from 'archiver';

export default class ArchiverService extends Service {
  public archiveByOption(option: any) {
    const { data, compressionType } = option;
    const archive = archiver(compressionType);
    data.forEach((item) => {
      archive.append(item.data, { name: item.fileName });
    });
    archive.finalize();
    return archive;
  }
}
