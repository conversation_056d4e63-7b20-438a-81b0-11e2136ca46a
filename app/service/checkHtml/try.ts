import {
  // IElementNode,
  parseHtml,
  getHtml,
  THtmlNode
} from '../../core/utils/htmlHelper';
import { mergeLabelNear } from '../../core/utils/htmlToJsonV4/helper/cleanupNode';

export function formateHtml(html: string) {
  const [questionHtml, answerHtml] = html.split(/<hr[^>]+answer_separator[\s\S]*?>/i);
  const questionJson = parseJson(questionHtml);
  let transformedHtml = getHtml(questionJson);
  if (answerHtml) {
    transformedHtml = `${transformedHtml}<hr data-label="answer_separator" />${answerHtml}`;
  }
  return transformedHtml;
}

function parseJson(html: string): THtmlNode[] {
  const nodeTree = parseHtml(html);
  mergeLabelNear(nodeTree);

  return nodeTree;
}
