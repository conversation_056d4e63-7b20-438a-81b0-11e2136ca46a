/**
 * @file 书本操作历史
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/bookHistory';

export default class BookHistoryService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.BookHistory, defineAttributes);
  }

  public reviewTypes = {
    apply: 21,
    quit: 22,
    confirm: 23,
  };

}
