/**
 * @file 项目配置信息
 */

'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import MetaService from '../../core/base/metaService';
import { Attributes, defineAttributes, Instance, IProjectMetas, PROJECT_DEFAULT_METAS } from '../../model/projectMeta';
import { IPageCountInfo } from '../../model/taskMeta';

export default class ProjectMetaService
  extends MetaService<'projectId', number, IProjectMetas, Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.ProjectMeta, defineAttributes, PROJECT_DEFAULT_METAS, 'projectId');
  }

  async getMetaInfo(meta: Partial<IProjectMetas>) {
    const id = 'id';
    const data = await this.getMetaInfoDict({ [id]: meta });
    return data[id];
  }

  async getMetaInfoDict(metaDict: { [id: string]: Partial<IProjectMetas> }, isTask?: boolean) {
    const { ctx, config } = this;
    const stageKeys = new Set<string>();
    const subjectKeys = new Set<string>();
    const gradeUids = new Set<string>();
    const editionUids = new Set<string>();
    const ids = Object.keys(metaDict);
    if (!ids.length) {
      return {};
    }
    ids.forEach((id) => {
      const meta = metaDict[id];
      if (meta.stage) stageKeys.add(meta.stage);
      if (meta.subject) subjectKeys.add(meta.subject);
      if (meta.gradeUid) gradeUids.add(meta.gradeUid);
      if (meta.editionUid) editionUids.add(meta.editionUid);
    });
    if (!stageKeys.size && !subjectKeys.size && !gradeUids.size && !editionUids.size) {
      return {};
    }
    const resp = await ctx.curl(`${isTask ? config.content.apiTask :config.content.api}/base/getInfo`, {
      data: {
        stage: [...stageKeys],
        subject: [...subjectKeys],
        gradeUid: [...gradeUids],
        editionUid: [...editionUids],
      },
      dataType: 'json',
    });
    if (!resp.data || !resp.data.data) {
      return {};
    }
    const info = resp.data.data;
    const stageMap = _.keyBy(info.stage, (s) => s.key);
    const subjectMap = _.keyBy(info.subject, (s) => s.key);
    const gradeMap = _.keyBy(info.grade, (s) => s.uid);
    const editionMap = _.keyBy(info.edition, (s) => s.uid);
    const res: { [key: string]: { stage?: string; subject?: string; grade?: string; edition?: string } } = {};
    ids.forEach((id) => {
      const meta = metaDict[id];
      const stage = stageMap[meta.stage!];
      const subject = subjectMap[meta.subject!];
      const grade = gradeMap[meta.gradeUid!];
      const edition = editionMap[meta.editionUid!];
      const data = {
        stage: stage ? stage.name : undefined,
        subject: subject ? subject.name : undefined,
        grade: grade ? grade.name : undefined,
        edition: edition ? edition.name : undefined,
      };
      res[id] = data;
    });
    return res;
  }

  /**
   * 同步元信息到工单系统
   * @param projectId
   * @param ticket_id
   * @param countInfos
   */
  async syncMetaInfoToWorkSheet( ticket_id: string ,countInfos: IPageCountInfo[]) {
    try {

      const ppt_page_info_for_xdoc = countInfos.map((item) => ({
        'md5': item.md5,
        'name': item.name,
        'mode': item.mode,
        'internal_pages': item.internalPages,
        'actual_pages': item.actualPages,
        'page_columns': item.pageColumns,
        'page_size': item.pageSize,
      }));
      const res = await this.app.curl(`${this.config.workOrder.api}/api/open/ticket/v1/handle`, {
        method: 'PUT',
        dataType: 'json',
        data: JSON.stringify({
          ticket_id,
          ppt_page_info_for_xdoc,
        }),
      });
      this.logger.info(`${ticket_id}同步元信息到工单系统成功: ${JSON.stringify(res.data)}`);
    } catch (e) {
      this.logger.info(`同步元信息到工单系统失败：${e.message}`);
    }
  }
}
