/**
 * @file 项目操作历史记录
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/projectHistory';

export default class ProjectHistoryService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.ProjectHistory, defineAttributes);
  }

  public reviewTypes = {
    apply: 21,
    quit: 22,
    confirm: 24,
  };

  public baseTypes = {
    revoke: 31,
    del: 32,
    updatePageCountInfos: 33, // 更新页码信息
  };

}
