/**
 * @file project
 * <AUTHOR>
 */

'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/project';
import superSequelize from '../../../typings/app/core/modelService';
import * as sha1 from 'sha1';
import { IProjectMetas } from '../../model/projectMeta';
import { findAllNodes, iterateNode } from '../../core/utils/treeHelper';
import { Op } from 'sequelize';

/**
 * 支持的文件扩展名类型
 * 中间json: 'json'
 * 输出json: 'official.json'
 * 手工标注过的输出json: 'mark.official.json'
 * 额外信息字段: 'meta.json'
 * docx: 'docx'
 * 每个任务的docx打包: 'docx.zip'
 * 合并后的Html: 'html'
 * 合并后的质检Html: 'diff.html'
 * 多word数据json zip: 'official.json.zip'
 */
type supportExtension = 'json'
  | 'official.json'
  | 'mark.official.json'
  | 'meta.json'
  | 'docx'
  | 'docx.zip'
  | 'html'
  | 'diff.html'
  | 'official.json.zip'
  | 'ai.json'
  | 'ai.flatten.json';

export default class ProjectService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Project, defineAttributes);
  }

  public statuses = {
    processing: 11,
    unreviewed: 12,
    reviewing: 13,
    reviewed: 14,
    publishing: 15,
    error: -1,
  };

  public openStatuses = {
    init: 1,
    processing: 2,
    successful: 3,
    failed: 4,
  };

  private hashProjectId(appKey: string, projectId: number|string) {
    return sha1(`${appKey}:${projectId}`);
  }

  public getUrl(appKey: string, projectId: number|string, extension: supportExtension, timestamp = true, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}${this.getOssKey(appKey, projectId, extension)}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getOssKey(appKey: string, projectId: number|string, extension: supportExtension) {
    return `open/${appKey}/project/${this.hashProjectId(appKey, projectId)}.${extension}`;
  }

  public getOssData(appKey: string, projectId: number|string, extension: supportExtension) {
    const url = this.getUrl(appKey, projectId, extension, true, true);
    return this.service.oss.fetch(url, extension);
  }

  public async setOssData(appKey: string, projectId: number|string, extension: supportExtension, data: any) {
    const key = this.getOssKey(appKey, projectId, extension);
    const url = await this.service.oss.upload(key, data, 'string');
    return url;
  }

  public async setOssMeta(appKey: string, projectId: number|string, meta: Partial<IProjectMetas>) {
    const { service } = this;
    const data = await service.project.meta.getMetaInfo(meta);
    const url = await this.setOssData(appKey, projectId, 'meta.json', {
      ...data,
      ..._.pick(meta, 'bookName'),
    });
    return url;
  }

  public async checkOssData({ appKey, projectId, srcExtension, dstExtension }: { appKey: string; projectId: number|string; srcExtension: supportExtension; dstExtension: supportExtension; }) {
    const { ctx } = this;
    const src_file = this.getUrl(appKey, projectId, srcExtension);
    const dst_file = this.getUrl(appKey, projectId, dstExtension);
    let errorInfo = '';
    try {
      const res = await ctx.curl('http://*************:55556/consist_check', {
        method: 'POST',
        dataType: 'json',
        data: {
          src_file,
          dst_file,
          task_id: projectId,
        },
        timeout: 60 * 1000,
      });
      const { pass_check, diff_char } = res.data;
      if (!pass_check) {
        errorInfo = `内容一致性检测异常【${srcExtension}2${dstExtension}】：${JSON.stringify(diff_char)}`;
      }
      this.logger.info(`内容一致性检测异常【${srcExtension}2${dstExtension}projectId: ${projectId}, appKey: ${appKey}, 返回： ${JSON.stringify(res)}`);
    } catch (e) {
      this.logger.error(`内容一致性检测异常【${srcExtension}2${dstExtension}】：接口报错${e}`);
      errorInfo = `内容一致性检测异常【${srcExtension}2${dstExtension}】：${JSON.stringify(e)}`;
    }
    return errorInfo;
  }

  public async getRelatedList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service, app } = this;
    const { fn, col } = app.model;
    const whereOpt = options.where || {} as any;
    const oneByOne = whereOpt.oneByOne;
    delete whereOpt.oneByOne;

    // 如果需要查询只有一个已完成任务的项目，使用专门的方法处理
    if (oneByOne) {
      return await this.getOneByOneList(options);
    }

    this.formatWhereOpt(whereOpt);
    this.logger.info(whereOpt);
    const projects = await this.getList({
      attributes: [
        'id',
        'userId',
        'subject',
        'projectName',
        'status',
        'errorInfo',
        'appKey',
        'createTime',
        'workOrder',
        'endWithWords',
        'pdfCount',
        'docxUrl'
      ],
      page: options.page,
      pageSize: options.pageSize,
      where: whereOpt,
      order: [['id', 'DESC']],
    });
    if (!projects.length) {
      return [];
    }
    const userIds: number[] = [];
    const appKeys: string[] = [];
    const idMap = {};
    const appMap = {};
    for (const { appKey, userId } of projects) {
      appKeys.push(appKey);
      userIds.push(userId);
    }
    const [users, apps, books] = await Promise.all([
      userIds.length ? // 搜索用户名，Todo：冗余存一下优化性能
        service.user.search(userIds) :
        Promise.resolve([]),
      service.appl.getAllByUc({ // 搜索 app 名，Todo：冗余存一下优化性能
        where: { appKey: appKeys },
        attributes: ['appKey', 'appName'],
      }),
      service.book.getAll({ where: { projectId: projects.map((p) => p.id) } })
    ]);
    const tasks = await service.task.base.getAll({
      where: { bookId: books.map((b) => b.id), mergedTaskId: null },
      attributes: ['bookId', 'status', 'resourceType', [fn('COUNT', col('id')), 'count']],
      group: ['bookId', 'status'],
    }) as any;
    for (const { userId, nickname } of users) {
      idMap[userId] = nickname;
    }
    apps.forEach((item) => {
      appMap[item.appKey] = item.appName;
    });
    // 添加图书和任务相关信息
    const bookMap = {};
    const taskStatuses = service.task.base.statuses;
    const allTypes = service.book.types;
    for (const { id, type, status, projectId } of books) {
      if (!bookMap[id]) {
        bookMap[id] = { type, status, projectId, tasks: [] };
      }
      if (!bookMap[id][type]) {
        bookMap[id][type] = {
          status,
          bookId: id,
          complete: 0,
          processing: 0,
          error: 0,
          count: 0,
        };
      }
    }

    for (const task of tasks) {
      const { bookId, status, count } = task;
      const type = bookMap[bookId].type;
      if (status === taskStatuses.error) {
        bookMap[bookId][type].error += count;
      } else if (status === taskStatuses.reviewed) {
        bookMap[bookId][type].complete += count;
      } else {
        bookMap[bookId][type].processing += count;
      }
      bookMap[bookId][type].count += count;
      bookMap[bookId].tasks.push(task);
    }
    // 处理每个项目，并在oneByOne=true时进行过滤
    const filteredProjects: any[] = [];
    for (const project of (projects as any)) {
      this.formatProject(project);
      project.appName = appMap[project.appKey] || '';
      project.username = idMap[project.userId] || '';
      project.books = {};
      const projectBooks = Object.keys(bookMap)
        .filter((bid) => bookMap[bid].projectId === project.id)
        .map((bid) => bookMap[bid]);
      for (const key in allTypes) {
        const foundBook = projectBooks.find((bookItem) => bookItem[allTypes[key]]);
        if (Boolean(foundBook)) {
          project.books[key] = foundBook[allTypes[key]];
        }
      }
      (project as any).taskResourceType = projectBooks.find((book) => book.tasks.length)?.tasks[0].resourceType;
      filteredProjects.push(project);
    }
    return filteredProjects;
  }

  private formatProject(project) {
    const { statuses } = this;
    project.jsonUrl = [statuses.reviewing, statuses.reviewed].includes(project.status) ?
      this.getUrl(project.appKey, project.id, 'json', true) :
      null;
    project.officialJsonUrl = statuses.reviewed === project.status ?
      this.getUrl(project.appKey, project.id, 'official.json', true) :
      null;
    project.officialJsonZipUrl = statuses.reviewed === project.status ?
      this.getUrl(project.appKey, project.id, 'official.json.zip', true) :
      null;
    // project.docxUrl = project.status === statuses.reviewed ?
    //   this.getUrl(project.appKey, project.id, 'docx', true) :
    //   null;
    project.docxZipUrl = project.status === statuses.reviewed ?
      this.getUrl(project.appKey, project.id, 'docx.zip', true) :
      null;
    project.htmlUrl = statuses.reviewed === project.status ?
      this.getUrl(project.appKey, project.id, 'html', true) :
      null;
    project.createTime = Number(new Date(project.createTime));
  }

  private formatWhereOpt(whereOpt) {
    const { key } = whereOpt;
    if (key) {
      if (/^[0-9]+$/.test(key)) {
        whereOpt.id = Number(key);
      } else {
        whereOpt[Op.or] = [
          { projectName: { $like: `%${key}%` } },
          { workOrder: { $like: `%${key}%` } }
        ];
      }
    }
    delete whereOpt.key;
  }

  private formatWhereOptExact(whereOpt) {
    whereOpt.projectName = whereOpt.key;
    delete whereOpt.key;
  }

  public async relatedCount(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const whereOpt: any = options.where || {};
    const oneByOne = whereOpt.oneByOne;
    delete whereOpt.oneByOne;

    // 如果需要查询只有一个已完成任务的项目，使用专门的方法处理
    if (oneByOne) {
      return await this.oneByOneCount(options);
    }

    this.formatWhereOpt(whereOpt);
    return await this.count({ where: whereOpt });
  }

  /**
   * 使用联表查询获取只有一个已完成任务的项目列表
   * @param options 查询选项
   * @returns 项目列表
   */
  public async getOneByOneList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service, app } = this;
    const taskStatuses = service.task.base.statuses;

    // 格式化查询条件
    const whereOpt = options.where || {} as any;
    this.formatWhereOpt(whereOpt);

    // 构建SQL查询条件
    let sqlWhere = 't.mergedTaskId IS NULL';
    if (whereOpt.id) {
      sqlWhere += ` AND p.id = ${whereOpt.id}`;
    }
    if (whereOpt[Op.or]) {
      const projectNameLike = whereOpt[Op.or][0]?.projectName?.$like?.replace(/%/g, '') || '';
      const workOrderLike = whereOpt[Op.or][1]?.workOrder?.$like?.replace(/%/g, '') || '';
      if (projectNameLike || workOrderLike) {
        sqlWhere += ` AND (p.projectName LIKE '%${projectNameLike}%' OR p.workOrder LIKE '%${workOrderLike}%')`;
      }
    }

    // 使用子查询方式，性能更好
    const subQuery = app.model.query(`
      SELECT b.projectId
      FROM book b
      JOIN task t ON b.id = t.bookId
      JOIN project p ON b.projectId = p.id
      WHERE ${sqlWhere}
      GROUP BY b.projectId
      HAVING COUNT(t.id) = 1 AND SUM(CASE WHEN t.status = ${taskStatuses.reviewed} THEN 1 ELSE 0 END) = 1
    `, { type: app.model.QueryTypes.SELECT }) as Promise<{projectId: number}[]>;

    const projectIds = await subQuery;

    // 如果没有符合条件的项目，直接返回空数组
    if (!projectIds.length) {
      return [];
    }

    // 构建项目ID查询条件，保留原始查询条件中不依赖联表的部分
    const projectWhere: any = {};
    if (whereOpt.status) projectWhere.status = whereOpt.status;
    if (whereOpt.appKey) projectWhere.appKey = whereOpt.appKey;
    if (whereOpt.userId) projectWhere.userId = whereOpt.userId;
    // 添加符合条件的项目ID
    projectWhere.id = { [Op.in]: projectIds.map((p) => p.projectId) };

    // 查询符合条件的项目
    const projects = await this.getList({
      attributes: [
        'id',
        'userId',
        'subject',
        'projectName',
        'status',
        'errorInfo',
        'appKey',
        'createTime',
        'workOrder',
        'endWithWords',
        'pdfCount',
        'docxUrl'
      ],
      page: options.page,
      pageSize: options.pageSize,
      where: projectWhere,
      order: [['id', 'DESC']],
    });

    if (!projects.length) {
      return [];
    }

    // 获取关联数据：用户、应用、书籍
    const userIds: number[] = [];
    const appKeys: string[] = [];
    const idMap = {};
    const appMap = {};

    for (const { appKey, userId } of projects) {
      appKeys.push(appKey);
      userIds.push(userId);
    }

    const [, , books] = await Promise.all([
      userIds.length ?
        service.user.search(userIds) :
        Promise.resolve([]),
      service.appl.getAllByUc({
        where: { appKey: appKeys },
        attributes: ['appKey', 'appName'],
      }),
      service.book.getAll({ where: { projectId: projects.map((p) => p.id) } })
    ]);

    // 直接获取每个项目的任务（保证每个项目只有一个任务）
    const tasks = await service.task.base.getAll({
      where: {
        bookId: books.map((b) => b.id),
        mergedTaskId: null,
        status: taskStatuses.reviewed,
      },
    });

    // 构建书籍与任务的映射关系
    const bookMap = {};
    const allTypes = service.book.types;

    for (const { id, type, status, projectId } of books) {
      if (!bookMap[id]) {
        bookMap[id] = { type, status, projectId, tasks: [] };
      }
      if (!bookMap[id][type]) {
        bookMap[id][type] = {
          status,
          bookId: id,
          complete: 0,
          processing: 0,
          error: 0,
          count: 0,
        };
      }
    }

    // 为每个书籍关联任务信息
    for (const task of tasks) {
      const { bookId } = task;
      if (bookMap[bookId]) {
        const type = bookMap[bookId].type;
        bookMap[bookId][type].complete = 1;
        bookMap[bookId][type].count = 1;
        bookMap[bookId].tasks.push(task);
      }
    }

    // 处理项目信息
    for (const project of (projects as any)) {
      this.formatProject(project);
      project.appName = appMap[project.appKey] || '';
      project.username = idMap[project.userId] || '';
      project.books = {};

      const projectBooks = Object.keys(bookMap)
        .filter((bid) => bookMap[bid].projectId === project.id)
        .map((bid) => bookMap[bid]);

      for (const key in allTypes) {
        const foundBook = projectBooks.find((bookItem) => bookItem[allTypes[key]]);
        if (Boolean(foundBook)) {
          project.books[key] = foundBook[allTypes[key]];
        }
      }

      project.taskResourceType = projectBooks.find((book) => book.tasks.length)?.tasks[0].resourceType;
    }

    return projects;
  }

  /**
   * 获取只有一个已完成任务的项目数量
   * @param options 查询选项
   * @returns 项目数量
   */
  public async oneByOneCount(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service, app } = this;
    const taskStatuses = service.task.base.statuses;

    // 格式化查询条件
    const whereOpt = options.where || {} as any;
    this.formatWhereOpt(whereOpt);

    // 构建SQL查询条件
    let sqlWhere = 't.mergedTaskId IS NULL';
    if (whereOpt.id) {
      sqlWhere += ` AND p.id = ${whereOpt.id}`;
    }
    if (whereOpt.status) {
      sqlWhere += ` AND p.status = ${whereOpt.status}`;
    }
    if (whereOpt.appKey) {
      sqlWhere += ` AND p.appKey = '${whereOpt.appKey}'`;
    }
    if (whereOpt.userId) {
      sqlWhere += ` AND p.userId = ${whereOpt.userId}`;
    }
    if (whereOpt[Op.or]) {
      const projectNameLike = whereOpt[Op.or][0]?.projectName?.$like?.replace(/%/g, '') || '';
      const workOrderLike = whereOpt[Op.or][1]?.workOrder?.$like?.replace(/%/g, '') || '';
      if (projectNameLike || workOrderLike) {
        sqlWhere += ` AND (p.projectName LIKE '%${projectNameLike}%' OR p.workOrder LIKE '%${workOrderLike}%')`;
      }
    }

    // 使用子查询计算符合条件的项目数量
    const result = await app.model.query(`
      SELECT COUNT(*) as count FROM (
        SELECT b.projectId
        FROM book b
        JOIN task t ON b.id = t.bookId
        JOIN project p ON b.projectId = p.id
        WHERE ${sqlWhere}
        GROUP BY b.projectId
        HAVING COUNT(t.id) = 1 AND SUM(CASE WHEN t.status = ${taskStatuses.reviewed} THEN 1 ELSE 0 END) = 1
      ) as temp
    `, { type: app.model.QueryTypes.SELECT }) as {count: string}[];

    return result.length > 0 ? parseInt(result[0].count, 10) : 0;
  }

  public async exactCount(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const whereOpt: any = { ...options.where } || {};
    this.formatWhereOptExact(whereOpt);
    return await this.count({ where: whereOpt });
  }

  // 获取完全一致的项目
  public async getExactList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service } = this;
    const whereOpt: any = { ...options.where } || {} as any;
    this.formatWhereOptExact(whereOpt);
    const projects = await this.getList({
      attributes: ['id', 'userId', 'subject', 'projectName', 'status', 'errorInfo', 'appKey', 'createTime', 'workOrder', 'endWithWords', 'pdfCount'],
      page: options.page,
      pageSize: options.pageSize,
      where: whereOpt,
      order: [['id', 'DESC']],
    });
    if (!projects.length) {
      return [];
    }
    const userIds: number[] = [];
    const appKeys: string[] = [];
    const idMap = {};
    const appMap = {};
    for (const { appKey, userId } of projects) {
      appKeys.push(appKey);
      userIds.push(userId);
    }
    // 搜索用户名
    if (userIds.length) {
      const users = await service.user.search(userIds);
      for (const { userId, nickname } of users) {
        idMap[userId] = nickname;
      }
    }
    const apps = await service.appl.getAllByUc({ where: { appKey: appKeys }, attributes: ['appKey', 'appName'] });
    apps.forEach((item) => {
      appMap[item.appKey] = item.appName;
    });
    projects.forEach((project: any) => {
      this.formatProject(project);
      project.appName = appMap[project.appKey] || '';
      project.username = idMap[project.userId] || '';
    });
    return projects;
  }

  public async getRelateOne(options: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service, app, ctx, statuses } = this;
    const taskStatuses = service.task.base.statuses;
    const bookStatuses = service.book.statuses;
    const { fn, col } = app.model;
    options.attributes = [
      'id', 'userId', 'subject',
      'projectName', 'status', 'errorInfo', 'appKey',
      'createTime', 'reviewUserId', 'workOrder', 'endWithWords', 'pdfCount'
    ];
    const project = await this.getOne(options) as (superSequelize.Attributes<Attributes> & {
      username: string;
      reviewUsername: string;
      appName: string;
      jsonUrl: string | null;
      officialJsonUrl: string | null;
      books: {
        catalog?: any;
        question?: any;
      }
    }) | null;
    if (!project) {
      return null;
    }
    const [users, appl, books] = await Promise.all([
      service.user.search(project.reviewUserId ? [project.userId, project.reviewUserId] : [project.userId]),
      service.appl.getOneByUc({ where: { appKey: project.appKey } }),
      service.book.getAll({ where: { projectId: project.id } })
    ]);
    const allTypes = service.book.types;
    const userMap = {};
    for (const { userId, nickname } of users) {
      userMap[userId] = nickname || '';
    }
    project.username = userMap[project.userId];
    project.reviewUsername = !project.reviewUserId ? '' : userMap[project.reviewUserId];
    if (appl) {
      project.appName = appl.appName;
    }
    const tasks = await service.task.base.getAll({
      where: { bookId: books.map((item) => item.id), mergedTaskId: null },
      attributes: ['bookId', 'status', [fn('COUNT', col('id')), 'count']],
      group: ['bookId', 'status'],
    }) as any;
    const bookTaskMap = {};
    const bookMap = {};
    let hasAllBookReviewed = true;
    for (const { id, type, status } of books) {
      bookMap[id] = { type, status };
      if (status !== bookStatuses.reviewed) {
        hasAllBookReviewed = false;
      }
      if (!bookTaskMap[type]) {
        bookTaskMap[type] = {
          status,
          bookId: id,
          complete: 0,
          processing: 0,
          error: 0,
          count: 0,
        };
      }
    }
    for (const { bookId, status, count } of tasks) {
      const type = bookMap[bookId].type;
      if (status === taskStatuses.error) {
        bookTaskMap[type].error += count;
      } else if (status === taskStatuses.reviewed) {
        bookTaskMap[type].complete += count;
      } else {
        bookTaskMap[type].processing += count;
      }
      bookTaskMap[type].count += count;
    }
    project.books = {};
    for (const key in allTypes) {
      if (bookTaskMap[allTypes[key]]) {
        project.books[key] = bookTaskMap[allTypes[key]];
      }
    }
    this.formatProject(project);
    // 处理下异常的项目状态
    if (hasAllBookReviewed && project.status < statuses.unreviewed) {
      project.status = statuses.unreviewed;
      ctx.runInBackground(async() => {
        await this.update({ status: statuses.unreviewed }, { where: { id: project.id } });
      });
    }
    return project;
  }

  public async relatedDelete(projectId: number) {
    const { service, app } = this;
    const books = await service.book.getAll({ where: { projectId }, attributes: ['id'] });
    if (!books.length) {
      await service.project.base.delete({ where: { id: projectId } });
      return;
    }
    const tasks = await service.task.base.getAll({
      where: { bookId: books.map((item) => item.id) },
      attributes: ['taskId'],
    });
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.project.base.delete({ transaction, where: { id: projectId } }),
        service.book.delete({ transaction, where: { projectId } })
      ]);
      if (tasks.length) {
        const taskIds = tasks.map((item) => item.taskId);
        await Promise.all([
          service.task.base.delete({ transaction, where: { taskId: taskIds } }),
          service.image.delete({ transaction, where: { taskId: taskIds } }),
          service.plan.task.delete({ transaction, where: { taskId: taskIds } })
        ]);
      }
    });
  }

  public async setJsonTags(json: any[]) {
    const { ctx, config, logger } = this;
    const knowledgeNodes = findAllNodes(json, ({ node }) => node.knowledge_uid, { stopFindChildren: false });
    const questionTagNodes = findAllNodes(json, ({ node }) => node.question_tag_uid, { stopFindChildren: false });
    const knowledgeUids = [...new Set(_.flatten(knowledgeNodes.map((node) => node.knowledge_uid)))];
    const questionTagUids = [...new Set(questionTagNodes.map((node) => node.question_tag_uid))];
    if (knowledgeUids.length || questionTagUids.length) {
      const resp = await ctx.curl(`${config.content.api}/base/getInfo`, {
        data: {
          knowledgeUid: knowledgeUids,
          questionTagUid: questionTagUids,
        },
        dataType: 'json',
      });
      const data = resp.data && resp.data.data;
      if (!data) {
        logger.error(['读取内容信息错误', resp.status, resp.data]);
        throw new Error('读取内容信息错误');
      }
      const { knowledge, questionTag } = data as {
        knowledge?: { uid: string; name: string }[][];
        questionTag?: { uid: string; name: string }[][];
      };
      const knowledgeMap: { [uid: string]: string } = {};
      (knowledge || []).forEach((path) => {
        const tag = path[path.length - 1];
        knowledgeMap[tag.uid] = path.map((tag) => tag.name).join('-');
      });
      const questionTagMap: { [uid: string]: string } = {};
      (questionTag || []).forEach((path) => {
        const tag = path[path.length - 1];
        questionTagMap[tag.uid] = path.map((tag) => tag.name).join('-');
      });
      knowledgeNodes.forEach((node) => {
        node.knowledge = node.knowledge_uid.map((uid) => knowledgeMap[uid]).filter((i) => i);
      });
      questionTagNodes.forEach((node) => {
        node.question_tag = questionTagMap[node.question_tag_uid];
      });
    }
    for (const { node } of iterateNode(json)) {
      delete node.knowledge_uid;
      delete node.question_tag_uid;
    }
  }

  public readonly projectQueue = 'xdoc:project:publish:queue';
  public readonly applyProjectQueue = 'xdoc:project:apply:queue';

  public async popFromPublish() {
    const projectId = await this.app.redis.rpop(this.projectQueue);
    return Number(projectId) || 0;
  }

  public async pushToPublish(...projectIds: number[]) {
    await this.app.redis.lpush(this.projectQueue, ...projectIds);
  }

  public async popFromApplyProject() {
    const project = await this.app.redis.rpop(this.applyProjectQueue);
    return JSON.parse(project || '{}');
  }

  public async pushToApplyProject(data: {projectId: number, userId: number, costTime?: number}) {
    await this.app.redis.lpush(this.applyProjectQueue, JSON.stringify(data));
  }

  /**
   * 根据发布时间范围查询项目列表
   * @param {string} startTime - 开始时间
   * @param {string} endTime - 结束时间
   * @return {Promise<array>} 项目列表
   */
  async getListByUpdateTime(startTime: string, endTime: string) {
    // 构建查询条件
    const where = {
      updateTime: {
        [Op.between]: [
          `${startTime}T00:00:00.000Z`,
          `${endTime}T23:59:59.999Z`
        ],
      },
      status: this.statuses.reviewed,
    };

    // 查询数据库
    const projects = await this.getList({
      attributes: ['id', 'subject', 'updateTime', 'appKey'],
      where,
    }, false);

    // 处理OSS URL
    const result = projects.map(async (project) => {
      const htmlUrl = this.getUrl(project.appKey, project.id, 'html', true);
      const jsonUrl = this.getUrl(project.appKey, project.id, 'official.json', true);
      // 查 taskResource
      const book = await this.service.book.getOne({ where: { projectId: project.id } });
      let resourceType;
      if (book) {
        const task = await this.service.task.base.getOne({ where: { bookId: book.id } });
        if (task) {
          const parentTask = await this.service.task.base.getOne({ where: { taskId: task.parentTaskId } });
          if (parentTask) {
            resourceType = parentTask.resourceType;
          }
        }
      }

      return {
        ...project,
        htmlUrl,
        jsonUrl,
        resourceType,
      };
    });

    return await Promise.all(result);
  }
}
