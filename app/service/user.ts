/**
 * @file 用户信息（对接账户系统）
 * <AUTHOR>
 */

'use strict';
import { Service } from 'egg';

interface UserInfo {
  nickname: string;
  userId: number;
  male: 1 | 0;
  headImageUrl: string;
}

export default class UserService extends Service {

  public readonly aiFilterUserId = 88888;

  // 获取用户的登录状态
  public async getLoginStatus(cookie: string): Promise<{ isLogin: boolean, userId: number } | undefined> {
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/getLoginStatus`, {
        dataType: 'json',
        headers: { Cookie: cookie },
        // q: timeout 10s
        timeout: 10000,
      });
      if (!data.status && data.data) {
        return data.data;
      }
    } catch (e) {
      logger.error(e);
    }
  }

  // 获取用户的基本信息
  public async getUserInfo(): Promise<UserInfo | null> {
    const { config, ctx, logger } = this;
    const { cookie } = ctx.data;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/getOwn`, {
        dataType: 'json',
        headers: { Cookie: cookie },
      });
      if (!data.status && data.data) {
        return data.data;
      }
    } catch (e) {
      logger.error(e);
    }
    return null;
  }

  // 获取用户的基本信息
  public async search(userIds: number[]): Promise<UserInfo[]> {
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/search`, {
        method: 'POST',
        dataType: 'json',
        data: { userId: userIds },
      });
      if (!data.status && data.data) {
        return Array.isArray(data.data) ? data.data : [data.data];
      }
    } catch (e) {
      logger.error(e);
    }
    return [];
  }

  // 获取用户的基本信息 - 根据nickname;
  public async searchUserByNickName(postData: { nickname?: string[] }): Promise<UserInfo[]> {
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/searchUserByNickName`, {
        method: 'POST',
        dataType: 'json',
        contentType: 'json',
        data: postData,
      });
      if (!data.status && data.data) {
        return Array.isArray(data.data) ? data.data : [data.data];
      }
    } catch (e) {
      logger.error(e);
    }
    return [];
  }

  public async create(username: string, password: string): Promise<number | null> {
    const { config, ctx, logger } = this;
    const { appKey, appSecret, api } = config.uc;
    try {
      const { data } = await ctx.curl(`${api}/admin/user/bulkCreate`, {
        dataType: 'json',
        method: 'POST',
        data: {
          appKey,
          appSecret,
          data: JSON.stringify([{
            password,
            username,
          }]),
        },
      });
      if (!data.status && data.data) {
        return data.data[0].userId;
      }
    } catch (e) {
      logger.error(e);
    }
    return null;
  }

  public async resetPassword(username: string, password: string) {
    const { config, ctx } = this;
    const { appKey, appSecret, api } = config.uc;
    const { data } = await ctx.curl(`${api}/admin/user/resetPassword`, {
      dataType: 'json',
      method: 'POST',
      data: {
        appKey,
        appSecret,
        username,
        password,
      },
    });
    if (!data || data.status) {
      throw new Error(data && data.statusInfo || '修改密码失败');
    }
  }
}
