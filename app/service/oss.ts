/**
 * @file 阿里云oss
 * <AUTHOR>
 */

'use strict';

import { Service } from 'egg';
import { Wrapper } from 'ali-oss';
import * as moment from 'moment';
import * as crypto from 'crypto';
import * as uuid from 'uuid/v4';
import { convertHtmlToDocx } from '../core/utils/htmlToDocx';
import { iterateNode } from '../core/utils/treeHelper';
import { THtmlNode } from '../core/utils/htmlHelper';

export default class OssService extends Service {

  public createOss(bucket?: string) {
    const ossConfig = this.config.aliOss;
    const { env } = this.app;
    return new Wrapper({
      region: ossConfig.region,
      accessKeyId: ossConfig.accessKeyId,
      accessKeySecret: ossConfig.accessKeySecret,
      bucket: bucket ?? ossConfig.bucket,
      internal: env === 'prod' || env === 'pre',
    });
  }

  private getPolicyBase64(expireSyncpoint: number, key: string, fileSize: number) {
    const expire = moment.utc(moment(expireSyncpoint)).toISOString();
    const policyText = {
      expiration: expire, // 设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
      conditions: [
        ['starts-with', '$key', key],
        ['content-length-range', 0, fileSize * 1024 * 1024] // 设置上传文件的大小限制
      ],
    };
    return Buffer.from(JSON.stringify(policyText)).toString('base64');
  }

  private getSignature(policyBase64: string) {
    const { accessKeySecret } = this.config.aliOss;
    const signature = crypto.createHmac('sha1', accessKeySecret).update(policyBase64, 'utf8').digest('base64');
    return signature;
  }

  public getUploadToken(key: string, fileSize: number | undefined = 100) {
    const { accessKeyId, host } = this.config.aliOss;
    const countdown = 600;
    const expireSyncpoint = Number(new Date()) + countdown * 1000;
    // 获取保险
    const policyBase64 = this.getPolicyBase64(expireSyncpoint, key, fileSize);
    // 获取签名
    const signature = this.getSignature(policyBase64);
    return {
      signature,
      key,
      host,
      accessId: accessKeyId,
      policy: policyBase64,
      expire: expireSyncpoint / 1000,
      countdown,
    };
  }

  // 支持：外部url、string、base64、buffer、json。返回阿里云地址
  public async upload(key: string, data: any, stringType: 'string' | 'base64' = 'base64', reRunTimes = 0) {
    try {
      const client = this.createOss();
      let buff: any = null;
      if (Buffer.isBuffer(data)) {
        buff = data;
      } else if (typeof data === 'string') {
        if (stringType === 'string') {
          buff = Buffer.from(data);
        } else if (/^http/.test(data)) {
          const res = await this.app.curl(data, { method: 'GET', timeout: 10000 });
          if (!res.data) {
            throw new Error('文件下载失败！');
          }
          buff = res.data;
        } else {
          buff = Buffer.from(data, 'base64');
        }
      } else {
        buff = Buffer.from(data ? JSON.stringify(data) : '');
      }
      const result = await client.put(key, buff, {timeout: 600000});
      return result.url as string;
    } catch (e) {
      if (reRunTimes < 3) {
        return await this.upload(key, data, stringType, reRunTimes + 1);
      }
      this.logger.info(`[ossUploadError] ${key}, ${stringType} error:${e}`);
      throw new Error(`[ossUploadError] ${key}, ${stringType} error:${e}`);

    }
  }

  public async fetch(url, extension = '', reRunTimes = 0) {
    const { app, logger } = this;
    const isJson = /json$/i.test(extension);
    const isText = /html$/i.test(extension);
    let dataType: 'json' | 'text' | undefined;
    if (isJson) {
      dataType = 'json';
    } else if (isText) {
      dataType = 'text';
    }
    try {
      const res = await app.curl(url, {
        method: 'GET',
        dataType,
        timeout: 10000,
      });
      if (res.status && !/^([23]).*/.test(`${res.status}`)) {
        return isJson ? null : '';
      }
      return res.data;
    } catch (e) {
      if (reRunTimes < 3) {
        return await this.fetch(url, extension, reRunTimes + 1);
      }
      logger.info(`get ${url}:${extension} oss data error: `, e);
      return isJson ? null : '';

    }
  }

  public async uploadBase64forHtml(key: string, nodes: THtmlNode[]) {
    let hasBase64Img = false;
    await Promise.all([...iterateNode(nodes)].map(async({ node }) => {
      if (node.type !== 'element' || node.tagName !== 'img') {
        return;
      }
      const src = node.attrs.src || '';
      const match = src.match(/data:image\/(.*?);base64,/i);
      if (!match) {
        return;
      }
      hasBase64Img = true;
      let ext = match[1];
      if (ext === 'jpeg') { ext = 'jpg'; }
      const url = await this.uploadBase64(key, src, ext);
      node.attrs.src = url;
    }));
    return hasBase64Img;
  }

  private async uploadBase64(key, dataUrl, ext) {
    const uid = uuid().replace(/-/g, '').substring(0, 20);
    const filename = `${uid}.${ext}`;
    const base64 = dataUrl.split(',')[1];
    const url = await this.service.oss.upload(`${key}/${filename}`, base64);
    return url.replace('-internal', '');
  }

  public async convertHtmlToDocAndUpload(
    key: string,
    html: string,
    errorIgnore = false
  ): Promise<{ buffer: Buffer, html: string, xml1: string, xml2: string }> {
    const { logger, config } = this;
    const client = this.createOss();
    let res: { buffer: Buffer, html: string, xml1: string, xml2: string };
    try {
      res = await convertHtmlToDocx(html.replace(config.aliOss.host, config.aliOss.privateHost), {});
      logger.info(`DOCX 生成 log: 准备上传 DOCX [${key}]`);
      await client.put(key, res.buffer);
      logger.info(`DOCX 生成 log: 上传完成 DOCX [${key}]`);
    } catch (e) {
      logger.error(`DOCX 生成 log: DOCX 生成在 [${key}] 抛出异常: ${e}`);
      if (!errorIgnore) throw e;
    }
    return res!;
  }

}
