/**
 * @file 权限系统
 */

'use strict';
import { Service } from 'egg';
import * as _ from 'lodash';

export default class ThemisService extends Service {

  private async curl(method: 'GET' | 'POST', url, params = {}) {
    const { config, ctx } = this;
    const { appKey, appSecret, api } = config.themis;
    const resp = await ctx.curl(`${api}/${url}`, {
      dataType: 'json',
      method,
      data: {
        ...params,
        appKey,
        appSecret,
      },
    });
    if (![200, 201].includes(resp.status)) {
      throw new Error(`授权服务异常，请重试 ${resp.status}`);
    }
    const data = resp.data;
    if (data.status) {
      throw new Error(`${data.statusInfo || '授权服务异常，请重试'} ${data.status}`);
    }
    return data.data;
  }

  public async grantValidateByUserId(userId: number, name: string) {
    const data = await this.curl('POST', 'grant/validateByUserId', { userId, name });
    return data && data.hasGranted as boolean;
  }

  public async getPermissionsByUserId(userId: number) {
    const data = await this.curl('GET', 'permission/getListByUserId', { userId }) as string[];
    return data || [];
  }

  public async getRolesByUserId(userId: number) {
    const data = await this.curl('GET', 'userRole/getListByUserId', { userId }) as { id: number; name: string }[];
    return data || [];
  }

  public async hasRoleByUserId(userId: number, callback: (params: { id: number; name: string }) => boolean) {
    const roles = await this.getRolesByUserId(userId);
    return roles.some(callback);
  }

  public async getUsersByRole(params: { roleId?: number; roleName?: string; userId?: number }) {
    const data = await this.curl('GET', 'userRole/getAllUsersByRole', params) as {
      id: number;
      updateTime: string;
      createTime: string;
      isActive: number;
      isDel: number;
      userId: number;
      username: string;
      roleId: number;
      nickname: string;
      male: number;
      headImageUrl: string;
    }[];
    const users = _.orderBy(data, ['nickname', 'userId']);
    return users;
  }
}
