/**
 * @file 限时任务人员学科权限配置
 */

'use strict';
import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/timeLimitUserSubject';

export default class TimeLimitUserSubjectService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.TimeLimitUserSubject, defineAttributes);
  }
}
