/**
 * @file 计划中的人员
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/planUser';
import * as sequelize from 'sequelize';
import * as _ from 'lodash';
import { PlanTaskAssignType } from '../../model/planGroup';
import { PlanTaskStatus } from '../../model/planTask';
import { LogicError } from '../../core/base/errors';

export default class PlanUserService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.PlanUser, defineAttributes);
  }

  async applyTaskById(
    { planGroupId, userId, taskId }: { planGroupId: number, userId: number, taskId: number },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { ctx, service } = this;
    const [planGroup, userInGroup, hasTask] = await Promise.all([
      service.plan.group.getOne({
        where: { id: planGroupId },
        attributes: ['assignType'],
      }),
      this.exists({ where: { planGroupId, userId } }),
      service.plan.task.getOne({
        where: {
          userId,
          $or: [
            { status: PlanTaskStatus.ING }, // 存在处理中
            { status: [PlanTaskStatus.REJECTED, PlanTaskStatus.INIT], planGroupId } // 或本组存在未处理
          ],
        } as any,
        attributes: ['taskId', 'status'],
      })
    ]);
    if (!planGroup) {
      throw new LogicError('任务组不存在');
    }
    if (!userInGroup) {
      throw new LogicError('成员不在该任务组中，请切换其他组');
    }
    if (hasTask) {
      throw new LogicError(`存在${service.plan.task.getStatusTitle(hasTask.status)}的任务：${hasTask.taskId}`);
    }
    if (planGroup.assignType === PlanTaskAssignType.FORCE) {
      throw new LogicError('组长分配任务组不能申领任务');
    }
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      throw new LogicError('任务不存在');
    }
    if (![3, 5].includes(task.status)) {
      throw new LogicError('任务不是可处理状态');
    }
    const where = { planGroupId, userId: 0, status: [PlanTaskStatus.INIT, PlanTaskStatus.REJECTED] };

    const planTask = await service.plan.task.getOne({ where: { ...where, taskId, taskType: task.status === 3 ? 1 : 2 } });
    if (!planTask) {
      throw new LogicError('任务不存在或者别人正在处理');
    }
    await service.plan.task.update(
      { userId, status: PlanTaskStatus.ING },
      {
        ...options,
        where: { ...where, id: planTask.id },
      }
    );
    ctx.runInBackground(async() => {
      await service.plan.base.pushByGroupId(planGroupId);
      await service.plan.group.push(planGroupId);
    });
  }

  async applyTask(
    { planGroupId, userId }: { planGroupId: number, userId: number },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { ctx, service } = this;
    const [planGroup, userInGroup, hasTask] = await Promise.all([
      service.plan.group.getOne({
        where: { id: planGroupId },
        attributes: ['assignType'],
      }),
      this.exists({ where: { planGroupId, userId } }),
      service.plan.task.getOne({
        where: {
          userId,
          $or: [
            { status: PlanTaskStatus.ING }, // 存在处理中
            { status: [PlanTaskStatus.REJECTED, PlanTaskStatus.INIT], planGroupId } // 或本组存在未处理
          ],
        } as any,
        attributes: ['taskId', 'status'],
      })
    ]);
    if (!planGroup) {
      throw new LogicError('任务组不存在');
    }
    if (!userInGroup) {
      throw new LogicError('成员不在该任务组中，请切换其他组');
    }
    if (hasTask) {
      throw new LogicError(`存在${service.plan.task.getStatusTitle(hasTask.status)}的任务：${hasTask.taskId}`);
    }
    if (planGroup.assignType === PlanTaskAssignType.FORCE) {
      throw new LogicError('组长分配任务组不能申领任务');
    }

    async function doApply(): Promise<number> {
      const where = { planGroupId, userId: 0, status: [PlanTaskStatus.INIT, PlanTaskStatus.REJECTED] };
      const count = await service.plan.task.count({ where });
      if (!count) {
        throw new LogicError('无待处理任务了');
      }
      const offset = _.random(Math.min(count, 5));
      const [planTask] = await service.plan.task.getAll({
        offset,
        limit: 1,
        where,
        attributes: ['id', 'taskId'],
        order: [['priority', 'desc'], ['id', 'asc']],
      });
      if (!planTask) return await doApply();
      const [affectedCount] = await service.plan.task.update(
        { userId, status: PlanTaskStatus.ING },
        {
          ...options,
          where: { ...where, id: planTask.id },
        }
      );
      if (!affectedCount) return await doApply();
      ctx.runInBackground(async() => {
        await service.plan.base.pushByGroupId(planGroupId);
        await service.plan.group.push(planGroupId);
      });
      return planTask.taskId;
    }

    return await doApply();
  }

  async startTask(
    { planGroupId, userId, taskId }: { planGroupId: number, userId: number, taskId?: number },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service, ctx } = this;
    const [planGroup, userInGroup, hasTask] = await Promise.all([
      service.plan.group.getOne({ where: { id: planGroupId } }),
      this.exists({ where: { planGroupId, userId } }),
      service.plan.task.getOne({
        where: { userId, status: PlanTaskStatus.ING },
        attributes: ['taskId'],
      })
    ]);
    if (!planGroup) {
      throw new LogicError('任务组不存在');
    }
    if (!userInGroup) {
      throw new LogicError('成员不在该任务组中，请切换其他组');
    }
    if (hasTask) {
      throw new LogicError(`存在处理中的任务：${hasTask.taskId}`);
    }
    let targetTaskId = taskId;
    if (!targetTaskId) {
      const planTask = await service.plan.task.getOne({
        attributes: ['taskId'],
        where: {
          planGroupId,
          userId,
          status: [PlanTaskStatus.INIT, PlanTaskStatus.REJECTED],
        },
        order: [['status', 'desc'], ['id', 'asc']],
      });
      targetTaskId = planTask ? planTask.taskId : undefined;
    }
    if (!targetTaskId) {
      throw new LogicError('没有指定任务');
    }
    const [affectedCount] = await service.plan.task.update(
      { status: PlanTaskStatus.ING },
      {
        ...options,
        where: {
          taskId: targetTaskId,
          planGroupId,
          userId,
          status: [PlanTaskStatus.INIT, PlanTaskStatus.REJECTED],
        },
      }
    );
    if (!affectedCount) {
      throw new LogicError('没有待处理任务');
    }
    ctx.runInBackground(async() => {
      await service.plan.base.pushByGroupId(planGroupId);
      await service.plan.group.push(planGroupId);
    });
    return targetTaskId;
  }

}
