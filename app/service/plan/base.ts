/**
 * @file 生产计划
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/plan';
import * as sequelize from 'sequelize';
import * as _ from 'lodash';
import { PlanTaskType } from '../../model/planFlow';

export default class PlanService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Plan, defineAttributes);
  }

  private readonly planTaskCountsPlanIdSet = 'plan:taskCountsPlanId:set';
  private readonly planTaskCountsPlanIdSetIng = 'plan:taskCountsPlanId:set:ing';

  async push(...planIds: number[]) {
    if (!planIds.length) return;
    const { redis } = this.app;
    await redis.sadd(this.planTaskCountsPlanIdSet, ...planIds);
  }

  async pushByFlowId(...planFlowIds: number[]) {
    const { service } = this;
    const { fn, col } = this.app.model;
    const flows = await service.plan.flow.getAll({
      where: { id: planFlowIds },
      attributes: [
        [fn('distinct', col('planId')), 'planId']
      ],
    });
    await service.plan.base.push(...flows.map((f) => f.planId));
  }

  async pushByGroupId(...planGroupIds: number[]) {
    const { service } = this;
    const { fn, col } = this.app.model;
    const tasks = await service.plan.task.getAll({
      where: { planGroupId: planGroupIds },
      attributes: [
        [fn('distinct', col('planId')), 'planId']
      ],
    });
    await service.plan.base.push(...tasks.map((t) => t.planId));
  }

  async pushByTaskId(...taskIds: number[]) {
    const { service } = this;
    const { fn, col } = this.app.model;
    const tasks = await service.plan.task.getAll({
      where: { taskId: taskIds },
      attributes: [
        [fn('distinct', col('planId')), 'planId']
      ],
    });
    await this.push(...tasks.map((t) => t.planId));
  }

  async pop() {
    const { redis } = this.app;
    const planId = await redis.spop(this.planTaskCountsPlanIdSet);
    if (!planId) return;
    await redis.sadd(this.planTaskCountsPlanIdSetIng, planId);
    return Number(planId);
  }

  async finishStat(planIds: number[]) {
    const { redis } = this.app;
    await redis.srem(this.planTaskCountsPlanIdSetIng, ...planIds);
  }

  async statTaskByPlanId(planIds: number[]) {
    const { service } = this;
    const [plans, flows] = await Promise.all([
      service.plan.base.model.findAll({
        where: { id: planIds },
        attributes: ['id'],
      }),
      service.plan.flow.model.findAll({
        where: { planId: planIds },
        attributes: ['id'],
      })
    ]);
    const counts = await service.plan.task.getCounts(
      { planId: plans.map((p) => p.id) },
      ['planId', 'planFlowId']
    );
    const flowDataMap = _.keyBy(counts, (counts) => counts.planFlowId);
    const flowData = flows.map((flow) => {
      const counts = flowDataMap[flow.id] || {};
      return {
        id: flow.id,
        taskCount: counts.taskCount || 0,
        ingCount: counts.ingCount || 0,
        finishedCount: counts.finishedCount || 0,
        assignedCount: counts.assignedCount || 0,
        assignedGroupCount: counts.assignedGroupCount || 0,
      };
    });
    const planDataGroup = _.groupBy(counts, (counts) => counts.planId);
    const planData = plans.map((plan) => {
      const counts = planDataGroup[plan.id] || [];
      return {
        id: plan.id,
        taskCount: _.sumBy(counts, (stat) => stat.taskCount),
        ingCount: _.sumBy(counts, (stat) => stat.ingCount),
        finishedCount: _.sumBy(counts, (stat) => stat.finishedCount),
        assignedCount: _.sumBy(counts, (stat) => stat.assignedCount),
        assignedGroupCount: _.sumBy(counts, (stat) => stat.assignedGroupCount),
      };
    });
    await Promise.all([
      service.plan.flow.bulkCreate(
        flowData as any,
        { updateOnDuplicate: ['taskCount', 'ingCount', 'finishedCount', 'assignedCount', 'assignedGroupCount'] }
      ),
      service.plan.base.bulkCreate(
        planData as any,
        { updateOnDuplicate: ['taskCount', 'ingCount', 'finishedCount', 'assignedCount', 'assignedGroupCount'] }
      )
    ]);
  }

  async createPlan({ name, deadlineTime }, options?: { transaction?: sequelize.Transaction }) {
    const plan = await this.create({ name, deadlineTime }, options);
    const flows = await this.service.plan.flow.bulkCreate(
      [
        { planId: plan.id, taskType: PlanTaskType.MARK },
        { planId: plan.id, taskType: PlanTaskType.REVIEW }
      ],
      options
    );
    plan.flows = _.keyBy(flows, (f) => this.service.plan.flow.getTaskTypeName(f.taskType)) as any;
    return plan;
  }

  async getListByOption(
    option: {
      page: number,
      pageSize: number,
      search?: string,
      isFinished?: boolean,
      isAssigned?: boolean,
      isAssignedGroup?: boolean,
    }
  ) {
    const where: any = {};
    if (option.search) {
      if (/^\d+$/.test(option.search)) {
        where.id = option.search;
      } else {
        where.name = { $like: `%${option.search}%` };
      }
    }
    if (option.isFinished != null) {
      if (option.isFinished) {
        where.taskCount = { $gt: 0 };
        where.finishedCount = { $eq: sequelize.col('taskCount') };
      } else {
        where.$and = where.$and || [];
        where.$and.push({
          $or: [
            { taskCount: 0 },
            { finishedCount: { $lt: sequelize.col('taskCount') } }
          ],
        });
      }
    }
    if (option.isAssigned != null) {
      if (option.isAssigned) {
        where.taskCount = { $gt: 0 };
        where.assignedCount = { $eq: sequelize.col('taskCount') };
      } else {
        where.$and = where.$and || [];
        where.$and.push({
          $or: [
            { taskCount: 0 },
            { assignedCount: { $lt: sequelize.col('taskCount') } }
          ],
        });
      }
    }
    if (option.isAssignedGroup != null) {
      if (option.isAssignedGroup) {
        where.taskCount = { $gt: 0 };
        where.assignedGroupCount = { $eq: sequelize.col('taskCount') };
      } else {
        where.$and = where.$and || [];
        where.$and.push({
          $or: [
            { taskCount: 0 },
            { assignedGroupCount: { $lt: sequelize.col('taskCount') } }
          ],
        });
      }
    }
    return await Promise.all([
      this.getList({
        page: option.page,
        pageSize: option.pageSize,
        where,
        order: [['createTime', 'desc']],
      }),
      this.count({ where })
    ]);
  }
}
