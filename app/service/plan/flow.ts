/**
 * @file 生产计划
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance, PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import * as sequelize from 'sequelize';

export default class PlanFlowService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.PlanFlow, defineAttributes);
  }

  public getTaskTypeName(taskType: PlanTaskType) {
    const taskTypeName = ({ 1: 'mark', 2: 'review' })[taskType] as 'mark' | 'review';
    if (!taskTypeName) {
      throw new Error(`错误的任务类型${taskType}`);
    }
    return taskTypeName;
  }

  public async getApplList(planFlowId) {
    const { app } = this;

    const sql = `
      select appl.appKey,
        appl.appName,
        appl.userId,
        appl.isTest,

        count(plan_task.taskId) as taskCount,
        sum(case when plan_task.status=${PlanTaskStatus.ING} then 1 else 0 end) as ingCount,
        sum(case when plan_task.status=${PlanTaskStatus.FINISHED} then 1 else 0 end) as finishedCount,
        sum(case when plan_task.userId>0 then 1 else 0 end) as assignedCount,
        sum(case when plan_task.planGroupId>0 then 1 else 0 end) as assignedGroupCount

      from application as appl,
        task,
        plan_task

      where appl.appKey=task.appKey
        and plan_task.taskId=task.taskId
        and plan_task.planFlowId=:planFlowId

        and appl.isDel=0
        and task.isDel=0
        and plan_task.isDel=0

      group by appl.id
    `;
    const data = await app.model.query(sql, {
      raw: true,
      type: sequelize.QueryTypes.SELECT,
      replacements: { planFlowId },
    });
    data.forEach((data) => {
      Object.assign(data, {
        taskCount: Number(data.taskCount),
        ingCount: Number(data.ingCount),
        finishedCount: Number(data.finishedCount),
        assignedCount: Number(data.assignedCount),
        assignedGroupCount: Number(data.assignedGroupCount),
      });
    });
    return data;
  }
}
