/**
 * @file 计划内任务组
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/planGroup';
import { PlanUserRoleType } from '../../model/planUser';
import * as sequelize from 'sequelize';
import * as _ from 'lodash';
import { PlanTaskStatus } from '../../model/planTask';
import { PlanTaskType } from '../../model/planFlow';
import { LogicError } from '../../core/base/errors';

export default class PlanGroupService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.PlanGroup, defineAttributes);
  }

  private readonly planGroupTaskCountsGroupIdSet = 'plan:group:taskCountsGroupId:set';
  private readonly planGroupTaskCountsGroupIdSetIng = 'plan:group:taskCountsGroupId:set:ing';

  async push(...groupIds: number[]) {
    if (!groupIds.length) return;
    const { redis } = this.app;
    await redis.sadd(this.planGroupTaskCountsGroupIdSet, ...groupIds);
  }

  async pushByTaskId(
    { planFlowId, taskType }: { planFlowId?: number, taskType?: PlanTaskType },
    ...taskIds: number[]
  ) {
    const { service } = this;
    const { fn, col } = this.app.model;
    const where: any = { taskId: taskIds, planGroupId: { $gt: 0 } };
    if (planFlowId) where.planFlowId = planFlowId;
    if (taskType) where.taskType = taskType;
    const tasks = await service.plan.task.getAll({
      where,
      attributes: [
        [fn('distinct', col('planGroupId')), 'planGroupId']
      ],
    });
    const groupIds = tasks.map((t) => t.planGroupId);
    await this.push(...groupIds);
  }

  async pop() {
    const { redis } = this.app;
    const groupId = await redis.spop(this.planGroupTaskCountsGroupIdSet);
    if (!groupId) return;
    await redis.sadd(this.planGroupTaskCountsGroupIdSetIng, groupId);
    return Number(groupId);
  }

  async finishStat(groupIds: number[]) {
    const { redis } = this.app;
    await redis.srem(this.planGroupTaskCountsGroupIdSetIng, ...groupIds);
  }

  async statTaskByGroupId(groupIds: number[]) {
    const { service } = this;
    const groups = await service.plan.group.getAll({
      where: { id: groupIds },
      attributes: ['id'],
    });
    const counts = await service.plan.task.getCounts(
      { planGroupId: groups.map((p) => p.id) },
      ['planGroupId']
    );
    const countsMap = _.keyBy(counts, (count) => count.planGroupId);
    const groupData = groups.map((group) => {
      const counts = countsMap[group.id] || {};
      return {
        id: group.id,
        taskCount: counts.taskCount || 0,
        ingCount: counts.ingCount || 0,
        finishedCount: counts.finishedCount || 0,
        assignedCount: counts.assignedCount || 0,
      };
    });
    await service.plan.group.bulkCreate(
      groupData as any,
      { updateOnDuplicate: ['taskCount', 'ingCount', 'finishedCount', 'assignedCount'] }
    );
  }

  // 组内新增成员
  public async addUsers(
    planFlowId: number,
    planGroupId: number,
    users: { userId: number, roleType: PlanUserRoleType }[],
    options?: { transaction?: sequelize.Transaction }
  ) {
    const planUsers = users.map(({ userId, roleType }) => ({ planFlowId, planGroupId, userId, roleType }));
    await this.service.plan.user.bulkCreate(
      planUsers,
      { ...options, ignoreDuplicates: true }
    );
  }

  // 移除成员
  public async removeUsers(
    planFlowId: number,
    planGroupId: number,
    userIds: number[],
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { ctx, service } = this;
    const existsTask = await this.service.plan.task.exists({
      ...options,
      where: { planGroupId, userId: userIds, status: [PlanTaskStatus.ING, PlanTaskStatus.FINISHED] },
    });
    if (existsTask) {
      throw new LogicError('存在标注中任务，不可移除');
    }
    const existsManager = await this.service.plan.user.exists({
      ...options,
      where: { planGroupId, userId: userIds, roleType: PlanUserRoleType.MANAGER },
    });
    if (existsManager) {
      throw new LogicError('不可以移除组长');
    }
    await this.service.plan.task.update(
      { userId: 0 },
      {
        ...options,
        where: { planFlowId, planGroupId, userId: userIds },
      }
    );
    await this.service.plan.user.delete({
      ...options,
      where: { planFlowId, planGroupId, userId: userIds },
    });
    ctx.runInBackground(async() => {
      await service.plan.base.pushByFlowId(planFlowId);
      await service.plan.group.push(planGroupId);
    });
  }

  // 修改组管理员
  public async changeManager(
    planFlowId: number,
    planGroupId: number,
    userId: number,
    options?: { transaction?: sequelize.Transaction }
  ) {
    const exists = await this.service.plan.user.exists({
      ...options,
      where: {
        planGroupId,
        userId,
        roleType: PlanUserRoleType.MANAGER,
      },
    });
    if (exists) {
      // 已经是管理员了，不需要修改
      return false;
    }
    await this.service.plan.user.update(
      { roleType: PlanUserRoleType.NORMAL },
      {
        ...options,
        where: { planGroupId, roleType: PlanUserRoleType.MANAGER },
      }
    );
    await this.service.plan.user.bulkCreate(
      [{ planFlowId, planGroupId, userId, roleType: PlanUserRoleType.MANAGER }],
      { ...options, updateOnDuplicate: ['roleType'] }
    );
    return true;
  }

}
