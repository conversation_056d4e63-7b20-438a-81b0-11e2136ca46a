/**
 * @file 计划内任务
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance, PlanTaskStatus } from '../../model/planTask';
import * as sequelize from 'sequelize';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskAssignType } from '../../model/planGroup';
import { LogicError } from '../../core/base/errors';
import { ETaskType } from '../../model/task';

export default class PlanTaskService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.PlanTask, defineAttributes);
  }

  async getListByOption(
    opt: {
      page: number,
      pageSize: number,
      planFlowId?: number,
      search?: string,
      isFinished?: boolean,
      isAssigned?: boolean,
      isAssignedGroup?: boolean,
      planGroupIds?: number[],
      statuses?: PlanTaskStatus[],
      appKeys?: string[],
      taskStatus?: number[],
      userIds?: number[],
      orderBy?: string[],
    }
  ) {
    const { app } = this;

    const querySql = this.getQuerySql(opt);

    const pagedSql = `
    select task.taskName,
      task.subject,
      task.imageCount,
      task.appKey,
      plan_task.planGroupId,
      plan_group.name as planGroupName,
      plan_task.priority,
      plan_task.taskId,
      plan_task.userId,
      plan_task.taskType,
      plan_task.status,
      task.status as taskStatus

    from plan_task
        left join plan_group on plan_group.id=plan_task.planGroupId and plan_group.isDel=0,
      task

    where ${querySql}

    ${opt.orderBy ? `order by ${opt.orderBy.join(',')}` : ''}
    limit :limit offset :offset;
    `;

    const countSql = `
    select count(task.taskId) as count
    from plan_task,
      task
    where ${querySql}
    `;
    const [tasks, count] = await Promise.all([
      app.model.query(pagedSql, {
        raw: true,
        type: sequelize.QueryTypes.SELECT,
        replacements: {
          ...opt,
          limit: opt.pageSize,
          offset: (opt.page - 1) * opt.pageSize,
        },
      }),
      app.model.query(countSql, {
        raw: true,
        type: sequelize.QueryTypes.SELECT,
        replacements: opt,
      })
    ]);
    if (tasks.length) {
      const appKey: string[] = Array.from(new Set(tasks.map((item) => item.appKey)));
      const appls = await this.service.appl.getAllByUc({ where: { appKey } });
      const applMap = {};
      for (const { appKey, appName } of appls) {
        applMap[appKey] = { appName };
      }
      tasks.forEach((item) => {
        Object.assign(item, applMap[item.appKey]);
      });
    }

    return [tasks, count[0].count] as [any[], number];
  }

  async getTaskIdsByOption(
    opt: {
      planFlowId?: number,
      search?: string,
      isFinished?: boolean,
      isAssigned?: boolean,
      isAssignedGroup?: boolean,
      planGroupIds?: number[],
      statuses?: PlanTaskStatus[],
      appKeys?: string[],
      taskStatus?: number[],
      userIds?: number[],
      taskIds?: number[],
      orderBy?: string[],
    }
  ) {
    const { app } = this;
    const querySql = this.getQuerySql(opt);

    const sql = `
    select task.taskId

    from plan_task,
      task

    where ${querySql}

    ${opt.orderBy ? `order by plan_task.${opt.orderBy.join(',')}` : ''}
  `;

    const tasks = await app.model.query(sql, {
      raw: true,
      type: sequelize.QueryTypes.SELECT,
      replacements: opt,
    });

    return tasks.map((item) => item.taskId) as number[];
  }

  private getQuerySql(
    opt: {
      planFlowId?: number,
      search?: string,
      isFinished?: boolean,
      isAssigned?: boolean,
      isAssignedGroup?: boolean,
      planGroupIds?: number[],
      statuses?: PlanTaskStatus[],
      taskStatus?: number[],
      appKeys?: string[],
      userIds?: number[],
      taskIds?: number[],
      assignType?: number
    }
  ) {
    const isSearchId = opt.search && /^\d+$/.test(opt.search);
    if (opt.search && !isSearchId) opt.search = `%${opt.search}%`;

    const sql = ` plan_task.taskId=task.taskId

    ${opt.planFlowId ? 'and plan_task.planFlowId=:planFlowId' : ''}
    ${opt.isFinished != null ? `and plan_task.status${opt.isFinished ? '=' : '!='}${PlanTaskStatus.FINISHED}` : ''}
    ${opt.isAssigned != null ? `and plan_task.userId${opt.isAssigned ? '!=' : '='}0` : ''}
    ${opt.isAssignedGroup != null ? `and plan_task.planGroupId${opt.isAssignedGroup ? '!=' : '='}0` : ''}
    ${opt.planGroupIds ? 'and plan_task.planGroupId in (:planGroupIds)' : ''}
    ${opt.statuses ? 'and plan_task.status in (:statuses)' : ''}
    ${opt.appKeys ? 'and task.appKey in (:appKeys)' : ''}
    ${opt.taskStatus ? 'and task.status in (:taskStatus)' : ''}
    ${opt.userIds && opt.assignType === 3 ? 'and (plan_task.userId in (:userIds) or plan_task.userId=0)' : opt.userIds ? 'and plan_task.userId in (:userIds)' : ''}
    ${opt.taskIds ? 'and plan_task.taskId in (:taskIds)' : ''}

    ${opt.search ? (isSearchId ? 'and task.taskId=:search' : 'and task.taskName like :search') : ''}

    and plan_task.isDel=0
    and task.isDel=0
    `;
    return sql;
  }

  public getStatusTitle(status: PlanTaskStatus) {
    if (status === PlanTaskStatus.PENDING) return '尚未进入流程';
    if (status === PlanTaskStatus.REJECTED) return '异常打回';
    if (status === PlanTaskStatus.INIT) return '等待处理';
    if (status === PlanTaskStatus.ING) return '处理中';
    return '已完成';
  }

  public taskStatusToFlowStatus(taskStatus: number, taskType: PlanTaskType) {
    const { statuses } = this.service.task.base;
    if (taskType === PlanTaskType.MARK) {
      if (taskStatus >= statuses.unreviewed) {
        return PlanTaskStatus.FINISHED;
      }
      if (taskStatus === statuses.marking) {
        return PlanTaskStatus.ING;
      }
      if (taskStatus === statuses.unmarked) {
        return PlanTaskStatus.INIT;
      }
      return PlanTaskStatus.PENDING;
    }
    if (taskStatus === statuses.reviewed) {
      return PlanTaskStatus.FINISHED;
    }
    if (taskStatus === statuses.reviewing) {
      return PlanTaskStatus.ING;
    }
    if (taskStatus === statuses.unreviewed) {
      return PlanTaskStatus.INIT;
    }
    return PlanTaskStatus.PENDING;
  }

  public flowStatusToTaskStatus(status: PlanTaskStatus, taskType: PlanTaskType) {
    const { statuses } = this.service.task.base;
    if (taskType === PlanTaskType.MARK) {
      if (status === PlanTaskStatus.INIT || status === PlanTaskStatus.REJECTED) {
        return statuses.unmarked;
      }
      if (status === PlanTaskStatus.ING) {
        return statuses.marking;
      }
      if (status === PlanTaskStatus.FINISHED) {
        return statuses.unreviewed;
      }
      return statuses.autoProcessing; // 不准确
    }
    if (status === PlanTaskStatus.INIT || status === PlanTaskStatus.REJECTED) {
      return statuses.unreviewed;
    }
    if (status === PlanTaskStatus.ING) {
      return statuses.reviewing;
    }
    if (status === PlanTaskStatus.FINISHED) {
      return statuses.reviewed;
    }
    return statuses.marking; // 不准确
  }

  async getCounts(where: sequelize.WhereOptions<Attributes>, group?: string[]): Promise<(Attributes & {
    taskCount: number,
    ingCount: number,
    finishedCount: number,
    assignedCount: number,
    assignedGroupCount: number;
  })[]> {
    const { col, fn, literal } = this.app.model;
    const counts = await this.getAll({
      where,
      attributes: [
        ...group || [],
        [fn('count', col('taskId')), 'taskCount'],
        [fn('sum', literal(`case when status=${PlanTaskStatus.ING} then 1 else 0 end`)), 'ingCount'],
        [fn('sum', literal(`case when status=${PlanTaskStatus.FINISHED} then 1 else 0 end`)), 'finishedCount'],
        [fn('sum', literal('case when userId>0 then 1 else 0 end')), 'assignedCount'],
        [fn('sum', literal('case when planGroupId>0 then 1 else 0 end')), 'assignedGroupCount']
      ],
      group,
    });
    counts.forEach((data: any) => {
      data.taskCount = Number(data.taskCount) || 0;
      data.ingCount = Number(data.ingCount) || 0;
      data.finishedCount = Number(data.finishedCount) || 0;
      data.assignedCount = Number(data.assignedCount) || 0;
      data.assignedGroupCount = Number(data.assignedGroupCount) || 0;
    });
    return counts as any;
  }

  async addToPlan(
    { planId, taskIds, priority }: { planId: number, taskIds: number[], priority?: number },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service, app } = this;
    const exists = await service.task.base.exists({ where: { taskId: taskIds, taskType: [ETaskType.plan, ETaskType.limit] } });
    if (exists) {
      throw new LogicError('任务已经在计划中或限时池中，请刷新重试');
    }
    const tasks = await service.task.base.getAll({
      where: { taskId: taskIds },
      attributes: ['taskId', 'status'],
    });
    if (tasks.length < taskIds.length) {
      throw new LogicError('任务不存在，请刷新重试');
    }
    const flows = await service.plan.flow.getAll({
      where: { planId },
      attributes: ['id', 'taskType'],
    });
    const data: any[] = [];
    flows.forEach((flow) => {
      tasks.forEach((task) => {
        data.push({
          planId,
          planFlowId: flow.id,
          taskId: task.taskId,
          taskType: flow.taskType,
          priority: priority == null ? 1 : priority,
          status: this.taskStatusToFlowStatus(task.status, flow.taskType),
        });
      });
    });

    await this.bulkCreate(data, options);
    await service.task.base.update({ taskType: ETaskType.plan }, { ...options, where: { taskId: taskIds } });

    app.runInBackground(async() => {
      await service.plan.base.push(planId);
    });
  }

  async moveOut(
    { planId, taskIds }: { planId: number, taskIds: number[] },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { app, service } = this;
    const existsInPlanCount = await this.count({ where: { planId, taskId: taskIds } });
    if (existsInPlanCount < 2 * taskIds.length) {
      throw new LogicError('数据错误，请刷新重试');
    }
    const existsCannotMove = await this.exists({ where: { planId, taskId: taskIds, status: [PlanTaskStatus.ING, PlanTaskStatus.FINISHED] } });
    if (existsCannotMove) {
      throw new LogicError('任务在处理中或已完成');
    }

    await this.delete({ ...options, where: { planId, taskId: taskIds } });
    await service.task.base.update({ taskType: ETaskType.unset }, { ...options, where: { taskId: taskIds } });

    app.runInBackground(async() => {
      await this.service.plan.base.push(planId);
    });
  }

  async move(
    opts: {
      planFlowId: number,
      taskIds: number[],
      planGroupId?: number,
      userId?: number,
      priority?: number,
    },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service, app } = this;
    const { fn, col } = app.model;
    const { planFlowId, taskIds, planGroupId, userId, priority } = opts;
    if (planGroupId == null && userId == null && priority == null) {
      throw new LogicError('参数错误');
    }
    const existsInFlowCount = await this.count({ where: { planFlowId, taskId: taskIds } });
    if (existsInFlowCount < taskIds.length) {
      throw new LogicError('任务不存在，请刷新重试');
    }
    const existsCannotMove = (planGroupId != null || userId != null) && await this.exists({
      where: {
        planFlowId,
        taskId: taskIds,
        userId: { $gt: 0 },
        status: [PlanTaskStatus.ING, PlanTaskStatus.FINISHED],
      },
    });
    if (existsCannotMove) {
      throw new LogicError('任务在处理中或已完成');
    }
    const change: { priority?: number, planGroupId?: number, userId?: number } = {};
    if (priority != null) change.priority = priority;
    if (userId) {
      const taskPlanGroups = await this.getAll({
        where: { planFlowId, taskId: taskIds, planGroupId: { $gt: 0 } },
        attributes: [[fn('distinct', col('planGroupId')), 'planGroupId']],
      });
      if (taskPlanGroups.length !== 1) {
        throw new LogicError('任务不属同一个组，请刷新重试');
      }
      const userExistsInGroup = await service.plan.user.exists({ where: { planFlowId, userId, planGroupId: taskPlanGroups[0].planGroupId } });
      if (!userExistsInGroup) {
        throw new LogicError('成员不存在，请刷新重试');
      }
    } else if (planGroupId != null) {
      change.planGroupId = planGroupId;
    }
    if (userId != null) {
      change.userId = userId;
    }
    await service.plan.group.pushByTaskId({ planFlowId }, ...taskIds);
    await this.update(change, {
      ...options,
      where: { planFlowId, taskId: taskIds },
    });
    this.app.runInBackground(async() => {
      await service.plan.base.pushByFlowId(planFlowId);
      await service.plan.group.pushByTaskId({ planFlowId }, ...taskIds);
    });
  }

  // 在流程中开始任务时，更新任务中的状态和处理人
  async onStartTask(
    { taskType, userId, taskId }: { taskType: PlanTaskType, userId: number, taskId: number },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service } = this;
    const userIdKey = taskType === PlanTaskType.MARK ? 'markUserId' : 'reviewUserId';
    const startTimeKey = taskType === PlanTaskType.MARK ? 'startMarkTime' : 'startReviewTime';
    // 更新任务原始状态
    await service.task.base.update(
      {
        status: service.plan.task.flowStatusToTaskStatus(PlanTaskStatus.ING, taskType),
        [userIdKey]: userId,
        [startTimeKey]: new Date(),
      },
      { ...options, where: { taskId } }
    );
    this.app.runInBackground(async() => {
      await this.service.plan.base.pushByTaskId(taskId);
      await this.service.plan.group.pushByTaskId({ taskType }, taskId);
    });
  }

  // 更新流程中状态
  async onChangeTaskStatus(
    {
      taskId,
      taskType,
      targetStatus,
    }: {
      taskId: number,
      taskType: PlanTaskType,
      targetStatus: PlanTaskStatus,
    },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const task = await this.getOne({
      attributes: ['id', 'planId'],
      where: { taskId, taskType },
    });
    if (!task) {
      return;
    }
    // 更新成完成初始化状态
    await this.update(
      { status: targetStatus },
      { ...options, where: { id: task.id } }
    );
    this.app.runInBackground(async() => {
      await this.service.plan.base.push(task.planId);
      await this.service.plan.group.pushByTaskId({ taskType }, taskId);
    });
  }

  // 放弃处理任务时，更新流程中任务状态（自由分配组会自动回收）
  async onQuitTask(
    { taskId, taskType }: { taskId: number, taskType: PlanTaskType },
    options?: { transaction?: sequelize.Transaction }
  ) {
    const { service } = this;
    const task = await this.getOne({
      attributes: ['id', 'planId', 'planGroupId'],
      where: { taskId, taskType },
    });
    if (!task) {
      return;
    }
    const group = await service.plan.group.getOne({
      attributes: ['assignType'],
      where: { id: task.planGroupId },
    });
    if (!group) {
      return;
    }
    const change: { status: PlanTaskStatus, userId?: number } = { status: PlanTaskStatus.INIT };
    if (group.assignType === PlanTaskAssignType.FREE) {
      change.userId = 0;
    }
    await this.update(
      change,
      { ...options, where: { id: task.id } }
    );
    this.app.runInBackground(async() => {
      await this.service.plan.base.push(task.planId);
      await this.service.plan.group.pushByTaskId({ taskType }, taskId);
    });
  }

  // 删除任务时，更新流程中任务状态
  async onDeleteTask(taskId: number, options?: { transaction?: sequelize.Transaction }) {
    const task = await this.getOne({
      attributes: ['planId'],
      where: { taskId },
    });
    if (!task) {
      return;
    }
    await this.delete({ ...options, where: { taskId } });
    this.app.runInBackground(async() => {
      await this.service.plan.base.push(task.planId);
      await this.service.plan.group.pushByTaskId({}, taskId);
    });
  }

}
