/**
 * @file 飞书机器人通知
 */
'use strict';

import { Service } from 'egg';

export default class StatV2Service extends Service {
  // 通过任务 id 获取 markUser 的工作时间
  public async getAllTimeByTaskIds(taskIds: number[]) {
    const { logger } = this;
    const { mongo } = this.app;
    const whereOpt: any = {};
    whereOpt.taskId = { $in: taskIds };

    logger.info(`getAllTimeByTaskIds begin ${taskIds}`);

    const taskIdWitdhMarkIdList: any = await this.service.task.base.getAll({
      where: whereOpt,
      attributes: ['taskId', 'markUserId'],
    });

    if (!taskIdWitdhMarkIdList || !taskIdWitdhMarkIdList.length) {
      return Array.from(taskIds).fill(0);
    }
    const taskMarkUserMap: any = {};
    taskIdWitdhMarkIdList.map((item) => {
      taskMarkUserMap[item.taskId] = item.markUserId;
    });
    const tasksTime: number[] = [];
    for (const taskId of taskIds) {
      const task = await mongo.db.collection('stat_v2').findOne({ taskId });
      let taskTime = 0;
      if (task && task.costTime) {
        for (const key in task.costTime) {
          if (
            key &&
            Object.prototype.hasOwnProperty.call(task.costTime, key) &&
            taskMarkUserMap &&
            taskMarkUserMap[taskId] &&
            key.startsWith(String(taskMarkUserMap[taskId]))
          ) {
            const timeRecord = task.costTime[key];
            if (timeRecord.startTime && timeRecord.endTime) {
              const startTime = new Date(timeRecord.startTime).getTime();
              const endTime = new Date(timeRecord.endTime).getTime();
              const diffTime = (endTime - startTime) / 1000; // s
              taskTime += diffTime;
            }
          }
        }
      }
      console.log(`${taskId}${taskTime} ------------------${tasksTime}`);
      tasksTime.push(taskTime);
    }
    return tasksTime;
  }

  public async getSplitTimeById(taskId: number) {
    const task = await this.app.mongo.db
      .collection('stat_v2')
      .findOne({ taskId });
    let taskTime = 0;
    if (task && task.costTime) {
      for (const key in task.costTime) {
        if (key && Object.prototype.hasOwnProperty.call(task.costTime, key)) {
          const timeRecord = task.costTime[key];
          if (timeRecord.startTime && timeRecord.endTime) {
            const startTime = new Date(timeRecord.startTime).getTime();
            const endTime = new Date(timeRecord.endTime).getTime();
            const diffTime = (endTime - startTime) / 1000; // s
            taskTime += diffTime;
          }
        }
      }
    }
    return taskTime;
  }

  //   通过 taskids 获取 pdf 页数
  public async getAllPdfsCountByTaskIds(taskIds: number[]) {
    const { service } = this;
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: taskIds } });
    const pdfCount: number[] = [];
    taskIds.forEach((taskId: any) => {
      const bodyCount = service.task.meta.calcPdfCount(
        metaDict[taskId]?.pdfInfo
      );
      const ansCount = service.task.meta.calcPdfAnsCount(
        metaDict[taskId]?.pdfInfo
      );
      pdfCount.push(Number(bodyCount) + Number(ansCount));
    });

    return pdfCount;
  }
}
