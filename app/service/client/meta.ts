/**
 * @file 客户配置信息
 */

'use strict';

import { Context } from 'egg';
import MetaService from '../../core/base/metaService';
import { Attributes, CLIENT_DEFAULT_METAS, defineAttributes, IClientMetas, Instance } from '../../model/clientMeta';

export default class ClientService
  extends MetaService<'userId', number, IClientMetas, Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.ClientMeta, defineAttributes, CLIENT_DEFAULT_METAS, 'userId');
  }
}
