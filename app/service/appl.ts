/**
 * @file 开放平台应用程序管理
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import superSequelize from '../../typings/app/core/modelService';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/application';

export default class ApplService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Application, defineAttributes);
  }

  public hexinApps = ['hexin', 'hexin2', '5d0b7d5c7d35cb32'];

  // 使用新版编辑器和json解析
  public isNewVersion(appKey) {
    return !['5693d0caf3722bc3de550529', 'test_v1'].includes(appKey);
  }

  public async exists(options?: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>>): Promise<boolean> {
    if (!options?.where?.appKey) return false;
    const { appKey } = options.where;
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/orgAppl/getAllByAppKey`, {
        method: 'POST',
        dataType: 'json',
        contentType: 'json',
        data: { appKey: [appKey] },
      });
      if (data?.data?.length) return true;
    } catch (e) {
      logger.error('appl.exists', e);
    }
    return false;
  }

  public async getAllByUc(options?: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>>): Promise<Instance[]> {
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/orgAppl/getAppl`, {
        method: 'POST',
        dataType: 'json',
        contentType: 'json',
        data: {
          method: 'getAll',
          options,
        },
      });
      if (data?.data) return data.data as Instance[];
    } catch (e) {
      logger.error('appl.getAllByUc', e);
    }
    return [];
  }

  public async getOneByUc(options?: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>>): Promise<Instance | null> {
    const { config, ctx, logger } = this;
    try {
      const { data } = await ctx.curl(`${config.uc.api}/orgAppl/getAppl`, {
        method: 'POST',
        dataType: 'json',
        contentType: 'json',
        data: {
          method: 'getOne',
          options,
        },
      });
      if (data?.data) return data.data as Instance;
    } catch (e) {
      logger.error('appl.getOneByUc', e);
    }
    return null;
  }
}
