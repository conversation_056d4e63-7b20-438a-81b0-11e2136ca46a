/**
 * @file column
 * <AUTHOR>
 */

'use strict';

import { Service } from 'egg';
import * as uuid from 'uuid/v4';
import { EImageMarkStatus } from '../model/image';

export default class ColumnService extends Service {

  private async clip({ taskId, imageId, bookId, appKey, filename, columnResult, taskOrder, rerun }: {
    taskId: number;
    imageId: string;
    bookId: number;
    filename: string;
    appKey: string;
    columnResult: any[];
    taskOrder: number;
    rerun: boolean;
  }) {
    const { service, app, ctx } = this;
    const { setFileOrder } = service.image;
    const newImages: any[] = [];
    const uploadingImages: any[] = [];
    let result: string | null;
    result = await app.invokeRpc('imageCrop', {
      uid: imageId,
      url: service.image.getUrl(appKey, imageId, 'jpg', false),
      points: columnResult,
    });
    this.logger.info(`clip failed ${taskId} ${imageId}`);
    if (!result) {
      // 重试
      try {
        result = await app.invokeRpc('imageCropRerun', {
          uid: imageId,
          url: service.image.getUrl(appKey, imageId, 'jpg', false),
          points: columnResult,
        });
      } catch (e) {
        this.logger.error(`clip failed ${taskId} ${imageId}`);
        result = null;
        throw new Error(e);
      }
    }
    if (!result) {
      throw new Error('clip failed');
    }
    for (const { img_str } of JSON.parse(result)[0].crop_img) {
      const newImageId = uuid().replace(/-/g, '');
      newImages.push({
        taskId,
        appKey,
        bookId,
        imageId: newImageId,
        wordCount: 0,
        marked: EImageMarkStatus.init,
        reviewed: false,
        preprocessed: true,
        disabled: false,
        filename: setFileOrder(filename, newImages.length + 1),
        originalId: imageId,
        multiple: false,
        columnResult: JSON.stringify([columnResult[newImages.length]]),
        taskOrder,
        rerun,
      });
      uploadingImages.push({
        imageId: newImageId,
        data: img_str,
      });
    }
    ctx.runInBackground(async() => {
      this.logger.info(`clip success ${taskId} ${imageId}`);
      await Promise.all(uploadingImages.map((item) => {
        return service.oss.upload(`open/${appKey}/image/${item.imageId}.jpg`, item.data);
      }));
    });
    return newImages;
  }

  public async relatedUpdate({ taskId, imageId, bookId, columnResult, latexResult, taskOrder, rerun }: {
    taskId: number;
    imageId: string;
    bookId: number;
    columnResult: any[];
    latexResult: any[];
    taskOrder: number;
    rerun: boolean;
  }) {
    const { service, app } = this;
    const { statuses } = service.task.base;
    const images = await service.image.getAll({
      where: { taskId, disabled: false },
      attributes: ['imageId', 'preprocessed', 'appKey', 'filename', 'rerun'],
    });
    // this.logger.info(`relatedUpdate imageId:${imageId} columnResult:${columnResult}`);
    const { filename, appKey } = images.find((item) => item.imageId === imageId)!;
    const allPreprocessed = images.every((item) => item.imageId === imageId || item.preprocessed);
    const newImages = columnResult.length
      ? await this.clip({
        columnResult,
        appKey,
        filename,
        taskId,
        bookId,
        imageId,
        taskOrder,
        rerun,
      })
      : [];
    const allImages = newImages.length
      ? [...images.filter((item) => item.imageId !== imageId), ...newImages]
      : images;
    await app.model.transaction(async(transaction) => {
      if (allPreprocessed) {
        await service.task.base.update(
          {
            status: statuses.columnProcessed,
            imageCount: allImages.length,
          },
          { transaction, where: { taskId } }
        );
      }
      if (newImages.length) {
        // 自动切图回调成功，创建新图
        await Promise.all([
          service.image.update(
            {
              disabled: true,
              latexResult: latexResult.length ? JSON.stringify(latexResult) : '',
              columnResult: columnResult.length ? JSON.stringify(columnResult) : '',
            },
            { transaction, where: { imageId } }
          ),
          service.image.bulkCreate(newImages, { transaction })
        ]);
      } else {
        await service.image.update(
          {
            latexResult: latexResult.length ? JSON.stringify(latexResult) : '',
            columnResult: columnResult.length ? JSON.stringify(columnResult) : '',
            preprocessed: true,
          },
          { transaction, where: { imageId } }
        );
      }
    });
    if (allPreprocessed) {
      const imageMap = {};
      allImages.forEach((item) => {
        if (!rerun || item.rerun) {
          imageMap[item.imageId] = 0;
        }
      });
      // 划块切图完，开始走机器识别流程，重跑回到这个流程过程中，有地方没有重置 imageStructProcessor redis，集中放到这里重置
      await service.task.base.resetTaskConfig(taskId, 'imageStructProcessor', imageMap);
    }
    return allPreprocessed;
  }
}
