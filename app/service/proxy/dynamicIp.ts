/**
 * @file 动态ip
 * <AUTHOR>
 */
'use strict';

import { Service } from 'egg';

export default class DynamicIpService extends Service {

  private readonly orderId = '976403808128117';
  private readonly apiKey = 'aqp2vviwruperc59k4b2bes823if4t8x';
  private readonly apiHost = 'https://dps.kdlapi.com/api';

  private defaultParams = {
    orderid: this.orderId,
    sign_type: 'simple',
    signature: this.apiKey,
    format: 'json',
  };

  public async getNewIps(num: number | undefined = 1): Promise<string[]> {
    const { app, logger } = this;
    const { data } = await app.curl(`${this.apiHost}/getdps`, {
      method: 'GET',
      dataType: 'json',
      data: { ...this.defaultParams, num },
    });
    if (data.code) {
      logger.error(`get new ip error : ${data.msg}!`);
      return [];
    }
    return data.data.proxy_list;
  }

  public async filterAvailableIps(ips: string[]) {
    const { app, logger } = this;
    const { data } = await app.curl(`${this.apiHost}/checkdpsvalid`, {
      method: 'GET',
      dataType: 'json',
      data: { ...this.defaultParams, proxy: ips.join(',') },
    });
    if (data.code) {
      logger.error(`validate ips error : ${data.msg}!`);
      return [];
    }
    const availableIps: string[] = [];
    for (const ip of ips) {
      if (data.data[ip]) {
        availableIps.push(ip);
      }
    }
    return availableIps;
  }

}
