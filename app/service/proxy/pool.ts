/**
 * @file 动态ip
 * <AUTHOR>
 */
'use strict';

import { Service } from 'egg';

export default class DynamicIpService extends Service {

  // ip过期时间
  private readonly expiredTime = 5 * 60 * 1000; // 5分钟
  // ip池的redis key
  private readonly poolKey = 'dynamic:ip:pool';

  public async getOne(): Promise<string | null> {
    const availableIps = await this.getAll();
    return availableIps.length ? availableIps[Math.floor(Math.random() * availableIps.length)] : null;
  }

  public async invalidate(ips: string[]) {
    await this.app.redis.hdel(this.poolKey, ...ips);
  }

  public async push(ips: string[]) {
    if (!ips.length) return;
    const ipMap = {};
    for (const ip of ips) {
      ipMap[ip] = `${Number(new Date()) + this.expiredTime}`;
    }
    await this.app.redis.hmset(this.poolKey, ipMap);
  }

  public async count() {
    return await this.app.redis.hlen(this.poolKey);
  }

  public async getAll(): Promise<string[]> {
    const { redis } = this.app;
    const allIps = await redis.hgetall(this.poolKey);
    const expiredIps: string[] = [];
    const availableIps: string[] = [];
    for (const ip in allIps) {
      if (allIps[ip] < Number(new Date())) {
        expiredIps.push(ip);
      } else {
        availableIps.push(ip);
      }
    }
    if (expiredIps.length) {
      await this.invalidate(expiredIps);
    }
    return availableIps;
  }

}
