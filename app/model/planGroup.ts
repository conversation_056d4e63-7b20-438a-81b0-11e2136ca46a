/**
 * @file 计划内任务组
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum PlanTaskAssignType {
  FORCE = 1,
  RANDOMFREE = 2,
  FREE = 3
}

interface CusAttributes {
  planFlowId: number;
  name: string;
  assignType: PlanTaskAssignType;
  taskCount?: number;
  ingCount?: number;
  finishedCount?: number;
  assignedCount?: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  planFlowId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '计划流程id',
    allowUpdate: false,
  },
  name: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '组名',
    allowUpdate: true,
  },
  assignType: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务分配模式类型',
    allowUpdate: false,
  },
  taskCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  ingCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '处理中任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  finishedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '完成任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  assignedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '分配任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('plan_group', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['planFlowId'],
      }
    ],
  } as any);
};
