/**
 * @file 项目配置表
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { IMetaAttrs, metaAttributes } from '../core/base/metaService';
import { IPageCountInfo } from './taskMeta';

export enum EBookType {
  NORMAL = 1, // 教辅书
  E_BOOK = 2, // 电子书
}

export enum EBookTextType {
  HANDWRITTEN = 1, // 手写
  PRINTING = 2, // 印刷
}

export enum EBookColumnType {
  SINGLE = 1, // 单栏
  DOUBLE = 2, // 双栏
  MULTI = -1, // 多兰
}

export enum EBookCollectType {
  PICTURE = 1, // 拍照
  SCAN = 2, // 扫描
}

export type IProjectMetas = {
  bookName: string; // 书名
  type: EBookType; // 书籍类型：教辅试卷、电子书
  textType: EBookTextType; // 文本形态（手写、印刷）
  columnType: EBookColumnType; // 页面版式（单栏、双栏、多栏）
  collectType: EBookCollectType; // 图像采集方式（拍照、扫描）

  // 如选择教辅则增加以下内容
  stage?: string; // 学段。
  subject?: string; // 学科。stage 不同时，选择列表不同
  gradeUid?: string; // 年级。stage 不同时，选择列表不同
  editionUid?: string; // 版本。stage 不同时，选择列表不同
  workOrder?: string; // 工单信息

  // 图书类型
  bookType?: string; // 试卷/练习册/讲解/其他

  pageCountInfos?: IPageCountInfo[]; // 页数信息
  isNewFlow: number; // 是否新流程
  imageHtmlVersion?: string; // 图片识别版本（v2 新流程，不走划块、公式校对等）

  isOpen: boolean; // 是否为 open api 的项目
  isAIAuto: boolean; // 自动流程
  isAIEdit: boolean; // 是否为 AI 编辑项目
  isAICheck: boolean; // 是否为 AI 校对项目
  isAutoPublish: boolean; // 是否自动过，默认 true
};

export const PROJECT_DEFAULT_METAS: { [key in keyof IProjectMetas]?: string } = {};

type CusAttributes = IMetaAttrs<'projectId', number, IProjectMetas>;

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  ...metaAttributes,
  projectId: {
    type: Sequelize.BIGINT(11),
    allowNull: false,
    comment: '项目id',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('project_meta', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['projectId', 'key', 'isDel'],
      },
      {
        unique: false,
        fields: ['key'],
      }
    ],
  });
};
