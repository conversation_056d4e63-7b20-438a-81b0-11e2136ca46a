/**
 * @file 任务统计（工作量、工作时间、工资）
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { ETaskResourceType } from './task';

interface CusAttributes {
  taskId: number;
  type: string;
  resourceType: ETaskResourceType;
  date: string;
  imageCount: number;
  pdfCount: number;
  leafQuestionCount: number;
  parentQuestionCount: number;
  costTime: number;
  userId: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的任务id',
    allowUpdate: false,
  },
  resourceType: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务类型',
    defaultValue: 0,
    allowUpdate: false,
  },
  type: {
    type: Sequelize.STRING(16),
    allowNull: false,
    comment: '记录类型',
    defaultValue: '',
    allowUpdate: false,
  },
  imageCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '当前任务的PDF页码数量，PDF页码信息由文件拆分的时候运营人员填入',
    defaultValue: 0,
    allowUpdate: false,
  },
  pdfCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '图片总数,区别于task表中imageCount,这里记录的初始图片数目',
    defaultValue: 0,
    allowUpdate: false,
  },
  date: {
    type: Sequelize.STRING(16),
    allowNull: false,
    comment: '日期，如2019-02-10',
    defaultValue: '',
    allowUpdate: false,
  },
  leafQuestionCount: {
    type: Sequelize.INTEGER(),
    allowNull: false,
    comment: '叶子节点试题数目',
    defaultValue: 0,
    allowUpdate: false,
  },
  parentQuestionCount: {
    type: Sequelize.INTEGER(),
    allowNull: false,
    comment: '父节点试题数目',
    defaultValue: 0,
    allowUpdate: false,
  },
  costTime: {
    type: Sequelize.INTEGER(),
    allowNull: false,
    comment: '当前类型阶段消耗时间',
    defaultValue: 0,
    allowUpdate: false,
  },
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: false,
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('task_stat', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['type', 'taskId', 'isDel'],
      },
      {
        unique: false,
        fields: ['date', 'userId'],
      }
    ],
  });
};
