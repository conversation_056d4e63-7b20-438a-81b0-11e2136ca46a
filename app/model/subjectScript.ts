/**
 * @file 学科脚本
 */
'use strict';

import { Application } from 'egg';
import Sequelize from 'sequelize';

export interface Attributes {
  id: number;
  scriptName: string;
  name: string;
  desc: string;
}

export type Instance = Sequelize.Instance<Attributes>;

export const defineAttributes: any = {
  id: {
    type: Sequelize.INTEGER(11),
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键',
    allowUpdate: false,
  },
  scriptName: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '脚本名称',
    allowUpdate: true,
  },
  name: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '中文名称',
    allowUpdate: true,
  },
  desc: {
    type: Sequelize.TEXT(),
    allowNull: true,
    comment: '描述',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('subject_script', defineAttributes, { freezeTableName: true });
};
