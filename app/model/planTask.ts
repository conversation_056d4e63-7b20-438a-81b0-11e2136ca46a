/**
 * @file 计划内任务
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { PlanTaskType } from './planFlow';

export enum PlanTaskStatus {
  PENDING = -1,
  INIT,
  ING,
  FINISHED,
  REJECTED,
}

interface CusAttributes {
  planId: number;
  planFlowId: number;
  planGroupId: number;
  taskId: number;
  taskType: PlanTaskType;
  userId: number;
  status: PlanTaskStatus;
  priority: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  planId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '计划id',
    allowUpdate: false,
  },
  planFlowId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '计划流程id',
    allowUpdate: false,
  },
  planGroupId: {
    type: Sequelize.BIGINT(16),
    defaultValue: 0,
    allowNull: true,
    comment: '所属组id',
    allowUpdate: true,
  },
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的任务id',
    allowUpdate: false,
  },
  taskType: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务类型',
    allowUpdate: false,
  },
  userId: {
    type: Sequelize.BIGINT(16),
    defaultValue: 0,
    allowNull: true,
    comment: '处理人员id',
    allowUpdate: true,
  },
  status: {
    type: Sequelize.SMALLINT(),
    defaultValue: PlanTaskStatus.INIT,
    allowNull: false,
    comment: '状态',
    allowUpdate: true,
  },
  priority: {
    type: Sequelize.SMALLINT(),
    defaultValue: 1,
    allowNull: false,
    comment: '优先级',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('plan_task', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['planFlowId', 'taskId', 'isDel'],
      },
      {
        unique: false,
        fields: ['planGroupId', 'userId'],
      },
      {
        unique: false,
        fields: ['planId', 'planFlowId'],
      },
      {
        unique: false,
        fields: ['taskId'],
      },
      {
        unique: false,
        fields: ['userId'],
      }
    ],
  } as any);
};
