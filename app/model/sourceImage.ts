/**
 * @file 原图
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum ESourceImageStatus {
  init = 0,
  finished = 1,
}

interface CusAttributes {
  imageId: string;
  appKey: string;
  filename: string;
  taskId: number;
  taskOrder: number;
  bookId: number;
  status: ESourceImageStatus;
  result: string;
  info: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  imageId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '图片id',
    allowUpdate: false,
  },
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    defaultValue: '',
    allowUpdate: false,
  },
  filename: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '前端上传的文件名，会根据此进行排序分组',
    defaultValue: '',
    allowUpdate: false,
  },
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的任务id',
    defaultValue: 0,
    allowUpdate: true,
  },
  taskOrder: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '在任务中顺序',
    allowUpdate: true,
  },
  bookId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    defaultValue: 0,
    comment: '关联的书籍id,针对一些尚未分配任务id的图片,用于之后的分组',
    allowUpdate: true,
  },
  status: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '原图处理状态',
    allowUpdate: true,
  },
  result: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '切分数据json',
    defaultValue: '',
    allowUpdate: true,
  },
  info: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '图片大小信息',
    defaultValue: '',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('source_image', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['imageId'],
      },
      {
        unique: false,
        fields: ['taskId'],
      }
    ],
  });
};
