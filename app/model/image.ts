/**
 * @file 图片详情
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum EImageMarkStatus {
  init = 0,
  // @tips：检查和标注环节合并，checked 字段暂时弃用。
  checked = 2, // 完成 aif 检查
  finished = 1, // 标注完成
}

interface CusAttributes {
  imageId: string;
  appKey: string;
  filename: string;
  wordCount: number;
  taskId: number;
  taskOrder: number;
  bookId: number;
  marked: EImageMarkStatus;
  reviewed: boolean;
  preprocessed: boolean;
  originalId: string;
  sourceId: string;
  multiple: boolean;
  columnResult: string;
  latexResult: string;
  disabled: boolean;
  rerunTimes?: number; // 重跑次数
  rerun?: boolean; // 是否重跑中
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  imageId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '图片id',
    allowUpdate: false,
  },
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    allowUpdate: false,
  },
  filename: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '前端上传的文件名，会根据此进行排序分组',
    defaultValue: '',
    allowUpdate: false,
  },
  wordCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '文档字数',
    allowUpdate: true,
  },
  marked: {
    type: Sequelize.SMALLINT(1),
    allowNull: false,
    defaultValue: false,
    comment: '是否被标注了',
    allowUpdate: true,
  },
  reviewed: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否被审核',
    allowUpdate: true,
  },
  preprocessed: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否被预处理',
    allowUpdate: true,
  },
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的任务id',
    allowUpdate: true,
  },
  taskOrder: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '在任务中顺序',
    allowUpdate: true,
  },
  bookId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    defaultValue: 0,
    comment: '关联的书籍id,针对一些尚未分配任务id的图片,用于之后的分组',
    allowUpdate: true,
  },
  originalId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '原始图片id,分栏前的图片id',
    defaultValue: '',
    allowUpdate: true,
  },
  sourceId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '最初原始图片id,手工切图前的图片id',
    defaultValue: '',
    allowUpdate: true,
  },
  columnResult: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '分栏详情',
    defaultValue: '',
    allowUpdate: true,
  },
  latexResult: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '公式标注详情',
    defaultValue: '',
    allowUpdate: true,
  },
  multiple: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否多栏',
    defaultValue: false,
    allowUpdate: true,
  },
  disabled: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '图片启用状态',
    defaultValue: false,
    allowUpdate: true,
  },
  rerunTimes: {
    type: Sequelize.SMALLINT().UNSIGNED,
    allowNull: false,
    comment: '重跑识别次数',
    defaultValue: 0,
    allowUpdate: true,
  },
  rerun: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否在重跑流程中',
    defaultValue: false,
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('image', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['imageId'],
      },
      {
        unique: false,
        fields: ['taskId'],
      },
      {
        unique: false,
        fields: ['bookId'],
      }
    ],
  });
};
