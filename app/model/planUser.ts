/**
 * @file 计划中的人员
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum PlanUserRoleType {
  MANAGER = 0,
  NORMAL = 1,
}

interface CusAttributes {
  planFlowId: number;
  planGroupId: number;
  userId: number;
  roleType: PlanUserRoleType;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  planFlowId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '计划流程id',
    allowUpdate: false,
  },
  planGroupId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '所属组id',
    allowUpdate: true,
  },
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理人员id',
    defaultValue: 0,
    allowUpdate: false,
  },
  roleType: {
    type: Sequelize.SMALLINT(),
    defaultValue: PlanUserRoleType.NORMAL,
    allowNull: false,
    comment: '角色类型',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('plan_user', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['planFlowId', 'planGroupId', 'userId', 'isDel'],
      },
      {
        unique: false,
        fields: ['planGroupId', 'userId', 'isDel'],
      },
      {
        unique: false,
        fields: ['planFlowId', 'roleType'],
      },
      {
        unique: false,
        fields: ['planGroupId', 'roleType'],
      },
      {
        unique: false,
        fields: ['userId'],
      }
    ],
  } as any);
};
