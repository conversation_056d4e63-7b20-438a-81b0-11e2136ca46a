/**
 * @file 客户账户配置表
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { IMetaAttrs, metaAttributes } from '../core/base/metaService';

export type IClientMetas = {
  exportDocx: boolean;
  exportJson: boolean;
  markJson: boolean;
  api7dayDoc: boolean;
  api7dayDocFlow: boolean;
  api7dayDocSeparate: boolean;
};

export const CLIENT_DEFAULT_METAS: { [key in keyof IClientMetas]: string } = {
  exportDocx: 'true',
  exportJson: 'false',
  markJson: 'false',
  api7dayDoc: 'false',
  api7dayDocFlow: 'false',
  api7dayDocSeparate: 'false',
};

type CusAttributes = IMetaAttrs<'userId', number, IClientMetas>;

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  ...metaAttributes,
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '客户id',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('client_meta', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['userId', 'key', 'isDel'],
      },
      {
        unique: false,
        fields: ['key'],
      }
    ],
  });
};
