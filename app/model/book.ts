/**
 * @file 书本
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  projectId: number;
  appKey: string;
  subject: 'math' | 'chinese' | 'en-math';
  bookName: string;
  status: number;
  type: number;
  taskCount: number;
  startReviewTime?: any;
  endReviewTime?: any;
  reviewUserId: number;
  errorInfo: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  bookName: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '书籍名称',
    allowUpdate: false,
  },
  type: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '书籍类型1.目录 2试题 3答案',
    defaultValue: 0,
    allowUpdate: true,
  },
  taskCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务总和',
    defaultValue: 0,
    allowUpdate: true,
  },
  status: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '书本状态:1.处理中 2.待审核 3.已审核 -1.异常',
    defaultValue: 0,
    allowUpdate: true,
  },
  projectId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '项目id',
    allowUpdate: false,
  },
  startReviewTime: {
    type: Sequelize.DATE(3),
    comment: '开始审核时间（最新一次）',
    allowUpdate: true,
  },
  endReviewTime: {
    type: Sequelize.DATE(3),
    comment: '结束审核时间（最新一次）',
    allowUpdate: true,
  },
  reviewUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    allowUpdate: false,
  },
  subject: {
    type: Sequelize.STRING(32),
    allowNull: false,
    comment: '任务学科',
    allowUpdate: true,
  },
  errorInfo: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '异常原因',
    allowUpdate: true,
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('book', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['projectId', 'type'],
      }
    ],
  });
};
