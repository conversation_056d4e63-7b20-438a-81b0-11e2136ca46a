/**
 * @file 计划流程
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum PlanTaskType {
  MARK = 1,
  REVIEW = 2,
}

interface CusAttributes {
  planId: number;
  taskType: PlanTaskType;
  taskCount?: number;
  ingCount?: number;
  finishedCount?: number;
  assignedCount?: number;
  assignedGroupCount?: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  planId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '计划id',
    allowUpdate: false,
  },
  taskType: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务类型',
    allowUpdate: false,
  },
  taskCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  ingCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '处理中任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  finishedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '完成任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  assignedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '分配任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  assignedGroupCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '分配任务组任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('plan_flow', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['planId', 'taskType'],
      },
      {
        unique: false,
        fields: ['taskType'],
      }
    ],
  } as any);
};
