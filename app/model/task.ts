/**
 * @file 任务详情
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { EMarkJsonStatus } from './project';

export enum ETaskType {
  unset = 0, // 未设置
  limit = 1, // 限时
  plan = 2, // 生成计划
}

export enum ETaskResourceType {
  IMAGE = 0,
  WORD = 1,
  FBD = 2,
  HTML = 3,
  WORDV2 = 4,
  FBDV2 = 5,
  IMAGE2 = 6,
  PDF = 7,
}

interface CusAttributes {
  taskId: number;
  taskName: string;
  bookId: number;
  bookOrder: number;
  subject: 'math' | 'chinese' | 'en-math';
  callbackUrl: string;
  status: number;
  markJsonStatus?: EMarkJsonStatus;
  appKey: string;
  isTest: boolean;
  isCallback: boolean;
  callbackError: boolean;
  errorInfo: string;
  markUserId: number;
  reviewUserId: number;
  preprocessUserId: number;
  splitUserId: number;
  operatAdminUserId: number;
  imageCount: number;
  pdfImageCount?: number;
  catalog: string;
  extra: string;
  startMarkTime?: any;
  endMarkTime?: any;
  startReviewTime?: any;
  endReviewTime?: any;
  callbackTime?: any;
  errorNodeCount?: number;
  rerunTimes?: number; // 重跑次数
  rerun?: boolean; // 是否重跑中
  rerunProcessUserId?: number; // 重新处理人

  taskType?: ETaskType; // 任务类型：生产计划或限时任务

  timeLimit?: number; // 时限。单位分钟
  timeWarning?: number; // 预警阈值比例。默认 0.5
  countdown?: number; // 剩余时间，单位分钟
  warning?: boolean; // 是否预警

  resourceType?: ETaskResourceType; // 任务资源类型：图片、word
  priority?: number; // 任务优先级
  parentTaskId?: number; // 关联Id

  diffCharCount?: number; // 对比machine.html字符串差异
  ticketId?: string // 目前是针对图片转录使用
  mergedTaskId?: number;
  mergedOrder?: number // 该任务在合并后任务中的顺序
  formulaRequestCount?: number // 公式识别 API 调用次数统计
  generateStatus?: number // 生成状态, 1 是生成中，默认 0
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的任务id',
    allowUpdate: false,
  },
  taskName: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '任务名称',
    allowUpdate: true,
  },
  subject: {
    type: Sequelize.STRING(32),
    allowNull: false,
    comment: '任务学科',
    allowUpdate: true,
  },
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    allowUpdate: false,
  },
  callbackUrl: {
    type: Sequelize.STRING(256),
    allowNull: true,
    defaultValue: '',
    comment: '任务处理完毕后回调地址',
    allowUpdate: true,
  },
  isCallback: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否回调',
    allowUpdate: true,
  },
  callbackError: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否回调异常',
    allowUpdate: true,
  },
  callbackTime: {
    type: Sequelize.DATE(3),
    comment: '回调时间',
    allowUpdate: true,
  },
  status: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '状态',
    allowUpdate: true,
  },
  markJsonStatus: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '项目状态: 0初始化 1标注中 2已完成',
    defaultValue: 0,
    allowUpdate: true,
  },
  preprocessUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '预处理用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  operatAdminUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '管理员抽查的用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  splitUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '文件分割用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  markUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  reviewUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  errorInfo: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '异常原因',
    allowUpdate: true,
  },
  isTest: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否是测试任务',
    allowUpdate: true,
  },
  imageCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '图片总数',
    defaultValue: 0,
    allowUpdate: true,
  },
  pdfImageCount: {
    type: Sequelize.FLOAT(),
    allowNull: true,
    comment: '来源于 PDF 的图片总数',
    defaultValue: 0,
    allowUpdate: true,
  },
  startMarkTime: {
    type: Sequelize.DATE(3),
    comment: '开始标注时间（最新一次）',
    allowUpdate: true,
  },
  endMarkTime: {
    type: Sequelize.DATE(3),
    comment: '结束标注时间（最新一次）',
    allowUpdate: true,
  },
  startReviewTime: {
    type: Sequelize.DATE(3),
    comment: '开始审核时间（最新一次）',
    allowUpdate: true,
  },
  endReviewTime: {
    type: Sequelize.DATE(3),
    comment: '结束审核时间（最新一次）',
    allowUpdate: true,
  },
  bookId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '书籍id, 没有关联书的任务此项为空',
    defaultValue: 0,
    allowUpdate: true,
  },
  bookOrder: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    defaultValue: 0,
    comment: '在书本中顺序',
    allowUpdate: true,
  },
  catalog: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '目录：JSON string',
    defaultValue: '',
    allowUpdate: true,
  },
  extra: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '额外参数',
    allowUpdate: false,
  },
  errorNodeCount: {
    type: Sequelize.SMALLINT().UNSIGNED,
    allowNull: false,
    comment: 'json中错误节点数量',
    defaultValue: 0,
    allowUpdate: true,
  },
  rerunTimes: {
    type: Sequelize.SMALLINT().UNSIGNED,
    allowNull: false,
    comment: '重跑识别次数',
    defaultValue: 0,
    allowUpdate: true,
  },
  rerun: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否在重跑流程中',
    defaultValue: false,
    allowUpdate: true,
  },
  rerunProcessUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '重新预处理用户id',
    defaultValue: 0,
    allowUpdate: true,
  },

  taskType: {
    type: Sequelize.SMALLINT().UNSIGNED,
    allowNull: true,
    comment: '任务类型',
    defaultValue: 0,
    allowUpdate: true,
  },
  timeLimit: {
    type: Sequelize.BIGINT(16).UNSIGNED,
    allowNull: true,
    comment: '时限。单位分钟',
    defaultValue: 0,
    allowUpdate: true,
  },
  timeWarning: {
    type: Sequelize.FLOAT(),
    allowNull: true,
    comment: '预警阈值比例，默认 0.5',
    defaultValue: 0,
    allowUpdate: true,
  },
  countdown: {
    type: Sequelize.INTEGER(),
    allowNull: true,
    comment: '剩余时间，单位分钟',
    defaultValue: 0,
    allowUpdate: true,
  },
  warning: {
    type: Sequelize.BOOLEAN,
    allowNull: true,
    comment: '是否预警。剩余时间低于阈值时预警',
    defaultValue: false,
    allowUpdate: true,
  },
  priority: {
    type: Sequelize.SMALLINT(),
    allowNull: true,
    comment: '任务优先级',
    // defaultValue: 0,
    allowUpdate: true,
  },
  parentTaskId: {
    type: Sequelize.BIGINT(16),
    allowNull: true,
    comment: '关联的父任务id',
    allowUpdate: false,
  },
  resourceType: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务资源类型：图片、word',
    defaultValue: 0,
    allowUpdate: true,
  },
  diffCharCount: {
    type: Sequelize.BIGINT(16),
    allowNull: true,
    comment: 'machine.html和发布html字符串差异',
    defaultValue: 0,
    allowUpdate: true,
  },
  ticketId: {
    type: Sequelize.STRING(256),
    allowNull: true,
    comment: '工单 ID',
    defaultValue: '',
    allowUpdate: true,
  },
  mergedTaskId: {
    type: Sequelize.BIGINT(16),
    allowNull: true,
    comment: '合并后的任务id',
    allowUpdate: true,
  },
  mergedOrder: {
    type: Sequelize.BIGINT(16),
    allowNull: true,
    comment: '该任务在合并后任务中的顺序',
  },
  formulaRequestCount: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '公式识别 API 调用次数统计',
    defaultValue: 0,
    allowUpdate: true,
  },
  generateStatus: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '生成状态, 1 是生成中，默认 0, 2 是失败',
    defaultValue: 0,
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('task', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['taskId', 'isDel'],
      },
      {
        unique: false,
        fields: ['markUserId'],
      },
      {
        unique: false,
        fields: ['reviewUserId'],
      },
      {
        unique: false,
        fields: ['appKey'],
      },
      {
        unique: false,
        fields: ['status'],
      },
      {
        unique: false,
        fields: ['bookId'],
      },
      {
        unique: false,
        fields: ['taskType', 'warning', 'countdown'],
      }
    ],
  });
};
