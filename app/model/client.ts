/**
 * @file 客户账户表
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  userId: number;
  isActive: boolean;
  phone: string;
  clientName: string;
  createUserId: number;
  config?: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '客户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  isActive: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否被激活',
    defaultValue: true,
    allowUpdate: true,
  },
  phone: {
    type: Sequelize.STRING(32),
    allowNull: false,
    comment: '客户手机',
    allowUpdate: false,
  },
  clientName: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '客户名',
    allowUpdate: true,
  },
  createUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '创建者id',
    defaultValue: 0,
    allowUpdate: true,
  },
  config: {
    type: Sequelize.TEXT,
    allowNull: false,
    comment: '用户配置信息',
    defaultValue: '{}',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('client', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['userId', 'isDel'],
      }
    ],
  });
};
