/**
 * @file 计划
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import * as planFlow from './planFlow';

interface CusAttributes {
  name: string;
  deadlineTime: any;
  taskCount?: number;
  ingCount?: number;
  finishedCount?: number;
  assignedCount?: number;
  assignedGroupCount?: number;
  flows?: {
    mark: planFlow.Instance,
    review: planFlow.Instance,
  };
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  name: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '计划名称',
    allowUpdate: true,
  },
  deadlineTime: {
    type: Sequelize.DATE(3),
    allowNull: false,
    comment: '截止日期（仅参考）',
    allowUpdate: true,
  },
  taskCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  ingCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '处理中任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  finishedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '完成任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  assignedCount: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '分配任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
  assignedGroupCount:  {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '分配任务组任务总数',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('plan', defineAttributes, {
    freezeTableName: true,
    indexes: [],
  } as any);
};
