/**
 * @file 应用表
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  appKey: string;
  appSecret: string;
  appName: string;
  userId: number;
  isActive: boolean;
  isTest: boolean;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    allowUpdate: false,
  },
  appSecret: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用秘钥',
    allowUpdate: false,
  },
  appName: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用名称',
    allowUpdate: true,
  },
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的客户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  isActive: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否被激活',
    defaultValue: true,
    allowUpdate: true,
  },
  isTest: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否是测试',
    defaultValue: false,
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  // @todo：无法使用app.utils
  return model.define<Instance, Attributes>('application', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['appKey', 'isDel'],
      }
    ],
  });
};
