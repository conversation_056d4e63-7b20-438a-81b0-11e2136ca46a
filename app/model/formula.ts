/**
 * @file 图片公式
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  formulaId: string;
  imageId: string;
  marked: boolean;
  confidence: number;
  reviewed: boolean;
  disabled: boolean;
  coordinate: string;
  latex: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  formulaId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '公式id',
    allowUpdate: false,
  },
  latex: {
    type: Sequelize.STRING(1024),
    allowNull: false,
    comment: '图片id',
    defaultValue: '',
    allowUpdate: true,
  },
  confidence: {
    type: Sequelize.FLOAT(),
    allowNull: false,
    comment: '公式识别信度',
    defaultValue: 0,
    allowUpdate: true,
  },
  marked: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已经被标注',
    allowUpdate: true,
  },
  reviewed: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已经被审核',
    allowUpdate: true,
  },
  disabled: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否需要被编辑',
    allowUpdate: true,
  },
  coordinate: {
    type: Sequelize.STRING(64),
    allowNull: false,
    defaultValue: '',
    comment: '公式在图片的坐标',
    allowUpdate: true,
  },
  imageId: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '图片id',
    allowUpdate: false,
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('formula', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['imageId', 'formulaId', 'isDel'],
      }
    ],
  });
};
