/**
 * @file 任务文件表
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { IFileAttrs, fileAttributes } from '../core/base/fileService';

export type ITaskZips = { id: string; url: string; isDown: boolean; isError?: boolean; fbds: { name: string; able: boolean; isPreHandle: boolean; path: string; type: 'body' | 'answer' }[] }[];
export type ITaskPdfs = { id: string; name: string; url: string; task_id?: string; isFinish?: boolean; type?: string; }[];
export type ITaskFbds = { body: { id: string; name: string; }[], answer: { id: string; name: string; }[] };
export type ITaskWords = { body: { id: string; url: string; name: string; isDown: boolean, isPreHandle: boolean, isError?: boolean, origin_url: string, after_page_num: number }[], answer: { id: string; url: string; name: string; isDown: boolean, isPreHandle: boolean, isError?: boolean, origin_url: string, after_page_num: number }[] };
export type ITaskWorkOrderFbdBasePath = { zip: string; pdf: string; };
export type ITaskWorkOrderWordBasePath = { word: string; };
export type ITaskImgs = { name?: string; page_num?: number; path?: string; compression_path?: string; id: string; info?: string; pdfId?: string; type?: string; splitType?: string; split?: boolean; chunkFromImageId?: string; }[];

export type ITaskFiles = {
  zips: ITaskZips;
  pdfs: ITaskPdfs;
  fbds: ITaskFbds;
  words: ITaskWords;
  workOrderFbdBasePath: ITaskWorkOrderFbdBasePath;
  workOrderWordBasePath: ITaskWorkOrderWordBasePath;
  imgs: ITaskImgs;
  imgs_bak: ITaskImgs;
  imgs_pdf?: ITaskImgs;
};

export const TASK_DEFAULT_FILES: { [key in keyof ITaskFiles]?: string } = {};

type CusAttributes = IFileAttrs<'taskId', number, ITaskFiles>;

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  ...fileAttributes,
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '任务id',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('task_file', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['taskId', 'key', 'isDel'],
      },
      {
        unique: false,
        fields: ['taskId'],
      },
      {
        unique: false,
        fields: ['key'],
      }
    ],
  });
};
