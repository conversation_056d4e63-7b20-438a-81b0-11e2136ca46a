/**
 * @file 项目
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum EMarkJsonStatus {
  INIT = 0,
  ING = 1,
  FINISHED = 2,
}

interface CusAttributes {
  projectName: string;
  userId: number;
  status: number;
  markJsonStatus?: EMarkJsonStatus;
  subject: 'math' | 'chinese' | 'en-math';
  errorInfo: string;
  startReviewTime?: any;
  endReviewTime?: any;
  reviewUserId: number;
  appKey: string;
  workOrder?: string;
  endWithWords?: boolean;
  pdfCount?: number;
  applyStatus?: number;
  docxUrl?: string;
  // 是否需要在项目发布时自动转换 word 并交付
  isResWord?: boolean;
  priority?: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '创建者id',
    defaultValue: 0,
    allowUpdate: false,
  },
  projectName: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '书籍名称',
    allowUpdate: true,
  },
  subject: {
    type: Sequelize.STRING(32),
    allowNull: false,
    comment: '任务学科',
    allowUpdate: true,
  },
  status: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '项目状态:1.处理中 2.待审核 3.已审核 -1.异常',
    defaultValue: 0,
    allowUpdate: true,
  },
  markJsonStatus: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '项目状态: 0初始化 1标注中 2已完成',
    defaultValue: 0,
    allowUpdate: true,
  },
  appKey: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '应用的唯一key',
    allowUpdate: false,
  },
  startReviewTime: {
    type: Sequelize.DATE(3),
    comment: '开始审核时间（最新一次）',
    allowUpdate: true,
  },
  endReviewTime: {
    type: Sequelize.DATE(3),
    comment: '结束审核时间（最新一次）',
    allowUpdate: true,
  },
  reviewUserId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: true,
  },
  errorInfo: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '异常原因',
    allowUpdate: true,
  },
  workOrder: {
    type: Sequelize.STRING(64),
    comment: '项目工单号',
    defaultValue: '',
    allowUpdate: true,
  },
  endWithWords: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    comment: '是否是多Words工单',
    defaultValue: false,
    allowUpdate: true,
  },
  pdfCount: {
    type: Sequelize.BIGINT(16),
    defaultValue: 0,
    comment: 'pdf页数',
  },
  docxUrl: {
    type: Sequelize.STRING(256),
    defaultValue: null,
    comment: 'word URL',
    allowUpdate: true,
  },
  applyStatus: {
    type: Sequelize.BIGINT(16),
    defaultValue: 0,
    comment: '重新生成的状态码，用于队列判断状态',
  },
  isResWord: {
    type: Sequelize.BOOLEAN,
    defaultValue: false,
    comment: '是否需要在项目发布时自动转换 word 并交付',
  },
  priority: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '优先级',
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('project', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['status'],
      }
    ],
  });
};
