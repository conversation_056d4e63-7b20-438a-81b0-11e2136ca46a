/**
 * @file 限时任务人员学科权限配置
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  userId: number;
  subject: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理人员id',
    allowUpdate: false,
  },
  subject: {
    type: Sequelize.STRING(64),
    allowNull: false,
    comment: '学科 如 math',
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('time_limit_user_subject', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'subject', 'isDel'],
      },
      {
        unique: false,
        fields: ['subject'],
      }
    ],
  } as any);
};
