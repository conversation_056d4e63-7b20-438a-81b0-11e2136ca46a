/**
 * @file 学科脚本
 */
'use strict';

import { Application } from 'egg';
import Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

export enum ESubjectScriptConfigTaskType {
  image = 1,
  wordV2 = 2,
  fbdV2 = 3
}
export interface CusAttributes {
  subject: string;
  taskType: ESubjectScriptConfigTaskType;
  script?: string;
}
// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

export const defineAttributes: any = {
  ...ModelBizAttributes,
  subject: {
    type: Sequelize.STRING(128),
    allowNull: false,
    comment: '学科',
    allowUpdate: false,
  },
  taskType: {
    type: Sequelize.SMALLINT,
    allowNull: false,
    comment: '任务类型',
    allowUpdate: false,
  },
  script: {
    type: Sequelize.STRING,
    allowNull: true,
    comment: '脚本',
    allowUpdate: true,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('subject_script_config', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['subject', 'taskType', 'isDel'],
      }
    ],
  });
};
