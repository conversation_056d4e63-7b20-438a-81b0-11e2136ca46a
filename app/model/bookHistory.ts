/**
 * @file 项目历史记录
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  bookId: number;
  type: number;
  data: string;
  costTime: number;
  userId: number;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  bookId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '关联的书本id',
    allowUpdate: false,
  },
  type: {
    type: Sequelize.SMALLINT(),
    allowNull: false,
    comment: '记录类型',
    defaultValue: 0,
    allowUpdate: false,
  },
  costTime: {
    type: Sequelize.INTEGER(),
    allowNull: false,
    comment: '操作消耗时间',
    defaultValue: 0,
    allowUpdate: false,
  },
  data: {
    type: Sequelize.TEXT(),
    allowNull: false,
    comment: '日志内容',
    defaultValue: '',
    allowUpdate: false,
  },
  userId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '处理者的用户id',
    defaultValue: 0,
    allowUpdate: false,
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('book_history', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        unique: false,
        fields: ['bookId', 'type'],
      }
    ],
  });
};
