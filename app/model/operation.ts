/**
 * @file 用户操作记录表
 * <AUTHOR>
 */
'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';

interface CusAttributes {
  userId: string;
  path: string;
  inputData: string;
}

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  userId: {
    type: Sequelize.STRING(32),
    allowNull: false,
    comment: '操作者id',
    allowUpdate: false,
  },
  path: {
    type: Sequelize.STRING(128),
    allowNull: true,
    comment: '请求的api地址',
    defaultValue: '',
    allowUpdate: false,
  },
  inputData: {
    type: Sequelize.TEXT(),
    allowNull: true,
    comment: '请求的参数JSON STRING',
    defaultValue: '',
    allowUpdate: false,
  },
};
export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('user_operation', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: false,
        fields: ['userId'],
      }
    ],
  });
};
