/**
 * @file 分栏标注
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import * as iconv from 'iconv-lite';
import validate from '../../core/decorators/validate';
import { htmlToJsonV2 } from '../../core/utils/htmlToJsonV2';
import { convertHtmlToKatexHtml } from '../../core/utils/htmlToDocx';
import { htmlToJsonV5 as htmlToJson } from '../../core/utils/aiEdit/htmlToJsonAI/index';
import { cleanFilename } from '../../core/utils/helper';
import { html2docx } from '../../core/utils/htmlToDocx/html2docx';
import getDefaultStyles, { TDocxStyleOption } from '../../core/utils/htmlToDocx/docxStyles';
import {
  latex2mathml,
  mathml2docx,
  mathml2omml,
  replaceDocxOmml,
  replaceLatex,
  replaceOmml
} from '../../core/utils/latexConverter';
import baseError from '../../core/base/baseError';

export default class OtherController extends Controller {

  @validate({
    fileString: 'string?',
    encoding: 'string?',
    html: 'string?',
    version: {
      type: 'enum',
      values: ['old', 'new'],
      required: false,
    },
  })
  public async html2json() {
    const { ctx } = this;
    let { fileString, html, encoding, version } = ctx.input;
    try {
      if (encoding) {
        fileString = Buffer.from(iconv.decode(Buffer.from(fileString), encoding)).toString('utf-8');
      }
      if (fileString) {
        html = fileString;
      }
      const json = version === 'old' ? htmlToJsonV2({ html }) : htmlToJson({ html, from: 'open' });
      ctx.body = {
        status: 0,
        data: json,
      };
    } catch (error) {
      this.logger.info(`html2json error: ${JSON.stringify(error)}`);
      ctx.body = baseError.paramsError(error?.message || 'html2json error');
    }
  }

  @validate({ fileString: 'string', encoding: 'string?' })
  public async html2khtml() {
    const { ctx } = this;
    let { fileString, encoding } = ctx.input;
    if (encoding) {
      fileString = Buffer.from(iconv.decode(Buffer.from(fileString), encoding)).toString('utf-8');
    }
    const result = await convertHtmlToKatexHtml(fileString);
    ctx.body = {
      status: 0,
      data: result,
    };
  }

  @validate({ html: 'string', name: 'string?', externalStyles: 'string?' })
  public async html2docx() {
    const { ctx } = this;
    const {
      html,
      name,
      externalStyles,
      styleOptions,
    } = ctx.input as { html: string; name?: string; externalStyles?: string, styleOptions?: TDocxStyleOption };
    const styles = styleOptions ? getDefaultStyles(styleOptions) : undefined;
    const { buffer } = await html2docx({ html, externalStyles, styles });
    ctx.attachment(`${name ? cleanFilename(name) : 'result'}.docx`);
    ctx.body = buffer;
  }

  @validate({
    data: {
      type: 'array',
      itemType: 'string',
      min: 1,
    },
    input: ['latex', 'mathml'],
    output: ['mathml', 'omml', 'docx'],
    name: 'string?',
    separator: 'string?',
    block: 'boolean?',
  })
  public async math() {
    const { ctx } = this;
    const { data, input, output, name, separator, block } = ctx.input as {
      data: string[];
      input: 'latex' | 'mathml';
      output: 'mathml' | 'omml' | 'docx';
      name?: string;
      separator?: string;
      block?: boolean;
    };
    const inputData = input === 'latex' && output !== 'mathml' ? data.map(replaceLatex) : data;
    const hasReplaced = inputData !== data && inputData.some((d, i) => d !== data[i]);
    let result = input === 'latex' ? await latex2mathml(inputData) : data;
    if (output === 'omml') {
      result = await mathml2omml(result);
      if (hasReplaced) {
        result = result.map(replaceOmml);
      }
    } else if (output === 'docx') {
      let buffer = await mathml2docx(result, { separator, block });
      if (!buffer) {
        return ctx.body = baseError.paramsError('没有公式');
      }
      if (hasReplaced) {
        buffer = await replaceDocxOmml(buffer);
      }
      ctx.attachment(`${name ? cleanFilename(name) : 'result'}.docx`);
      return ctx.body = buffer;
    }
    ctx.body = {
      status: 0,
      data: result,
    };
  }
}
