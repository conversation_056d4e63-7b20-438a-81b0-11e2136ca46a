/**
 * @file 分栏标注
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';

export default class TaskColumnController extends Controller {

  @validate({ taskId: 'number' })
  public async getAllProcessedByTaskId() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    this.logger.info(`[getAllProcessedByTaskId]: ${taskId}`);
    const images = await service.image.getAll({
      where: { taskId, originalId: '' },
      attributes: ['imageId', 'multiple', 'columnResult', 'latexResult', 'appKey', 'filename', 'taskOrder'],
    }) as any;
    this.logger.info(`[getAllProcessedByTaskId]: ${taskId} unsortImage ', ${images.length}, ${images}`);
    const sortImage = service.image.sortImageByFile(images);
    this.logger.info(`[getAllProcessedByTaskId]: ${taskId} sortImageList ',${sortImage.length}, ${sortImage}`);
    ctx.body = {
      status: 0,
      data: sortImage.map((item) => {
        item.url = service.image.getUrl(item.appKey, item.imageId, 'jpg', false);
        delete item.filename;
        delete item.taskOrder;
        delete item.appKey;
        return item;
      }),
    };
  }

}
