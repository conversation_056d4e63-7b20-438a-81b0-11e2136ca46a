/**
 * @file 动态ip
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';

export default class DynamicIpController extends Controller {
  public async getOne() {
    const { service, ctx } = this;
    const ip = await service.proxy.pool.getOne();
    if (!ip) {
      return ctx.body = baseError.serverError('无可用的ip');
    }
    ctx.body = {
      status: 0,
      data: { ip },
    };
  }
}
