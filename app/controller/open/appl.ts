'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';

export default class ApplController extends Controller {

  @validate({ userId: 'number' })
  public async create() {
    const { ctx, service } = this;
    const { userId, isTest, appKey, appSecret, appName } = ctx.input;
    const client = await service.client.base.getOne({ where: { userId } });
    if (!client || !client.isActive) {
      return ctx.body = baseError.dataNotExistError('客户不存在或者未激活');
    }
    await service.appl.create({
      appKey,
      appSecret,
      userId,
      appName,
      isTest,
      isActive: true,
    });
    ctx.body = { status: 0 };
  }
}
