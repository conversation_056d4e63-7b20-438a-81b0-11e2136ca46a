'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';

export default class TimeoutExampleController extends Controller {
  
  /**
   * 添加超时监控任务
   * 示例：POST /api/open/timeout/add
   * Body: {
   *   "taskId": "task123",
   *   "type": "ai_edit",
   *   "timeoutMs": 300000,
   *   "title": "AI编辑任务超时",
   *   "content": "AI编辑任务执行超时，请检查任务状态",
   *   "receiveId": 13
   * }
   */
  public async addTimeoutTask() {
    const { ctx, service } = this;
    
    try {
      const { 
        taskId, 
        type, 
        timeoutMs, 
        title, 
        content, 
        receiveId,
        extra 
      } = ctx.input;

      if (!taskId || !type || !timeoutMs || !title || !content) {
        return ctx.body = baseError.paramsError('缺少必要参数');
      }

      await service.timeout.addTimeoutTask(taskId, type, timeoutMs, {
        title,
        content,
        receiveId,
        extra,
      });

      return ctx.body = {
        status: 0,
        message: '超时监控任务添加成功',
        data: {
          taskId,
          type,
          timeoutMs,
          expireTime: new Date(Date.now() + timeoutMs).toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('[addTimeoutTask] 添加超时任务失败:', error);
      return ctx.body = baseError.serverError('添加超时任务失败');
    }
  }

  /**
   * 释放超时监控任务
   * 示例：POST /api/open/timeout/release
   * Body: {
   *   "taskId": "task123",
   *   "type": "ai_edit"
   * }
   */
  public async releaseTimeoutTask() {
    const { ctx, service } = this;
    
    try {
      const { taskId, type } = ctx.input;

      if (!taskId || !type) {
        return ctx.body = baseError.paramsError('缺少必要参数：taskId 或 type');
      }

      const released = await service.timeout.releaseTimeoutTask(taskId, type);

      return ctx.body = {
        status: 0,
        message: released ? '超时监控任务释放成功' : '任务不存在或已释放',
        data: {
          taskId,
          type,
          released,
        },
      };
    } catch (error) {
      this.logger.error('[releaseTimeoutTask] 释放超时任务失败:', error);
      return ctx.body = baseError.serverError('释放超时任务失败');
    }
  }

  /**
   * 查询任务剩余时间
   * 示例：GET /api/open/timeout/remaining?taskId=task123&type=ai_edit
   */
  public async getRemainingTime() {
    const { ctx, service } = this;
    
    try {
      const { taskId, type } = ctx.input;

      if (!taskId || !type) {
        return ctx.body = baseError.paramsError('缺少必要参数：taskId 或 type');
      }

      const remainingTime = await service.timeout.getRemainingTime(taskId, type);

      if (remainingTime === null) {
        return ctx.body = {
          status: 0,
          message: '任务不存在',
          data: null,
        };
      }

      return ctx.body = {
        status: 0,
        message: '查询成功',
        data: {
          taskId,
          type,
          remainingTime,
          remainingTimeFormatted: this.formatTime(remainingTime),
          isExpired: remainingTime <= 0,
        },
      };
    } catch (error) {
      this.logger.error('[getRemainingTime] 查询剩余时间失败:', error);
      return ctx.body = baseError.serverError('查询剩余时间失败');
    }
  }

  /**
   * 获取所有监控中的任务
   * 示例：GET /api/open/timeout/all
   */
  public async getAllMonitoringTasks() {
    const { ctx, service } = this;
    
    try {
      const tasks = await service.timeout.getAllMonitoringTasks();

      const formattedTasks = tasks.map(task => {
        const now = Date.now();
        const remainingTime = Math.max(0, (task.startTime + task.timeout) - now);
        const isExpired = remainingTime <= 0;
        
        return {
          taskId: task.taskId,
          type: task.type,
          timeout: task.timeout,
          startTime: new Date(task.startTime).toISOString(),
          expireTime: new Date(task.startTime + task.timeout).toISOString(),
          remainingTime,
          remainingTimeFormatted: this.formatTime(remainingTime),
          isExpired,
          notifyData: task.notifyData,
        };
      });

      return ctx.body = {
        status: 0,
        message: '查询成功',
        data: {
          total: formattedTasks.length,
          tasks: formattedTasks,
        },
      };
    } catch (error) {
      this.logger.error('[getAllMonitoringTasks] 查询监控任务失败:', error);
      return ctx.body = baseError.serverError('查询监控任务失败');
    }
  }

  /**
   * 清理所有超时任务（谨慎使用）
   * 示例：POST /api/open/timeout/clear-all
   */
  public async clearAllTimeoutTasks() {
    const { ctx, service } = this;
    
    try {
      await service.timeout.clearAllTimeoutTasks();

      return ctx.body = {
        status: 0,
        message: '所有超时监控任务已清理',
      };
    } catch (error) {
      this.logger.error('[clearAllTimeoutTasks] 清理所有超时任务失败:', error);
      return ctx.body = baseError.serverError('清理所有超时任务失败');
    }
  }

  /**
   * 格式化时间显示
   * @param ms 毫秒数
   */
  private formatTime(ms: number): string {
    if (ms <= 0) return '已过期';
    
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时${minutes % 60}分钟`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }
} 