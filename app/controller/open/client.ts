'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
export default class ClientController extends Controller {

  @validate({
    phone: 'string',
    clientName: 'string',
    userId: 'number',
    createUserId: 'number',
  })
  public async create() {
    const { ctx, service } = this;
    const { phone, clientName, userId, createUserId } = ctx.input;
    await service.client.base.create({
      clientName,
      userId,
      phone,
      isActive: true,
      createUserId,
    });
    ctx.body = { status: 0 };
  }

  @validate({path: 'string'})
  public async getOssAuth() {
    const { ctx, service } = this;
    const {path} = ctx.input;
    const auth = service.oss.getUploadToken(path);
    ctx.body = {
      status: 0,
      data: auth,
    };
  }
}
