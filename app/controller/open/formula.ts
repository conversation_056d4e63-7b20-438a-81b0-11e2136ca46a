/**
 * @file 公式处理
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
const katex = require('katex');
export default class FormulaController extends Controller {

  @validate({ url: 'string' })
  public async detect() {
    const { ctx, app, logger } = this;
    const { url } = ctx.input;
    const data = await app.getLatexV2(url);
    if (!data || !data.latex) {
      logger.info(`get mp result error, url : ${url}, data : ${data ? JSON.stringify(data) : null}`);
      return ctx.body = baseError.serverError(data && data.error ? data.error : '服务异常');
    }
    ctx.body = {
      status: 0,
      data: {
        latex: data.latex,
        confidence: Math.floor(data.confidence * 100) / 100,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async getAllByTaskId() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const images = await service.image.getAll({
      where: { taskId, disabled: false },
      attributes: ['imageId', 'appKey', 'marked'],
    });
    if (!images.length) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    const imageMap = {};
    for (const { imageId, appKey } of images) {
      imageMap[imageId] = appKey;
    }
    const formulas = await service.formula.getAll({
      where: { imageId: Object.keys(imageMap) },
      attributes: ['imageId', 'coordinate', 'latex', 'confidence'],
    }) as any;
    ctx.body = {
      status: 0,
      data: formulas.map((formula) => {
        formula.url = service.formula.getUrl(imageMap[formula.imageId], formula.imageId, formula.coordinate);
        delete formula.coordinate;
        return formula;
      }),
    };
  }

  @validate({ formulas: { type: 'array' } })
  public async check() {
    const { ctx } = this;
    const { formulas } = ctx.input;
    const data: { status: 0 | 1, formula: string, res: string }[] = [];
    formulas.forEach((formula: string) => {
      try {
        const html = katex.renderToString(formula);
        data.push({
          status: 0,
          formula,
          res: html,
        });
      } catch (e) {
        data.push({
          status: 1,
          formula,
          res: JSON.stringify(e),
        });
      }
    });
    ctx.body = {
      status: 0,
      data,
    };
  }
}
