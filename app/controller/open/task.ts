/**
 * @file 开放任务控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { IClientMetas } from '../../model/clientMeta';
import { ETaskResourceType, ETaskType } from '../../model/task';
import * as ImageModel from '../../model/image';
import { EImageMarkStatus } from '../../model/image';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import { TaskProcessTypes } from '../../service/task/base';

interface IAvailableImages{
  type: TaskProcessTypes;
  rpcName: string;
  subject: 'math' | 'chinese' | 'en-math';
  uid: string;
  url: string;
  appKey: string;
  taskId: number;
  columnResult: any[];
  rerun?: boolean;
  latexResult?: {
    coordinate?: number[][];
    type?: string;
    key?: number;
  }[];
}
export default class OpenTaskController extends Controller {
  // 获取 rpc 任务
  @validate({ type: ['imageStructProcessor', 'imageColumnProcessor'] })
  public async pop() {
    const { ctx, service } = this;
    const { type } = ctx.input as {
      type: TaskProcessTypes
    };
    const task = await service.task.base.pop(type);
    if (!task) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    this.logger.info(`pop taskGet-> type: ${type} -> taskId: ${task.taskId}`);
    const whereOpt: any = { taskId: task.taskId };
    if (task.rerun) {
      whereOpt.rerun = true;
    }
    const images = await service.image.getAll({
      where: whereOpt,
      attributes: ['imageId', 'disabled', 'latexResult', 'originalId', 'columnResult', 'appKey', 'sourceId', 'filename'],
    });
    const disabledMap = {};
    const disabledMapForSourceId = {};
    const disabledMapForFileName = {};
    let availableImages: IAvailableImages[] = [];
    for (const { imageId, disabled, latexResult, sourceId, filename } of images) {
      if (disabled) {
        disabledMap[imageId] = latexResult ? JSON.parse(latexResult) : [];
        disabledMapForSourceId[imageId] = sourceId;
        disabledMapForFileName[imageId] = filename;
      }
    }
    this.logger.info(`checkSourceId taskId: ${task.taskId} ${JSON.stringify(disabledMapForSourceId)}`);
    // this.logger.info(`sortImagesBegin before taskId: ${task.taskId} images:${JSON.stringify(images)} `);
    for (const image of images) {
      if (!image.disabled) {
        const newImage: IAvailableImages = {
          type,
          rpcName: type,
          subject: task.subject,
          uid: image.imageId,
          url: service.image.getUrl(image.appKey, image.imageId, 'jpg', false),
          appKey: task.appKey,
          taskId: task.taskId,
          columnResult: image.columnResult ? JSON.parse(image.columnResult) : [],
          rerun: task.rerun,
        };
        // 这里提供预切后的地址
        let oImageId = '';
        let cropResult;
        let originImage;
        if (image.originalId) {
          if (disabledMap[image.originalId]) {
            newImage.latexResult = disabledMap[image.originalId];
            oImageId = disabledMapForSourceId[image.originalId];
            const filename = disabledMapForFileName[image.originalId];
            originImage = await service.sourceImage.getOne({
              where: { imageId: oImageId },
              attributes: ['result', 'info'],
            });
            const cropIndex = /\$_(\d+)_\$/.test(filename) ?
              Number(filename.match(/\$_(\d+)_\$/)[1]) - 1 :
              Number(filename.match(/(\d+)\.jpg/)[1]);
            cropResult = originImage?.result?.[cropIndex] ?
              JSON.parse(originImage.result)[cropIndex] :
              { x: 0, y: 0, w: originImage?.info ? JSON.parse(originImage.info).w : 0, h: originImage?.info ? JSON.parse(originImage.info).h : 0 };
          }
        } else {
          newImage.latexResult = image.latexResult ? JSON.parse(image.latexResult) : [];
          oImageId = image.sourceId;
          originImage = await service.sourceImage.getOne({
            where: { imageId: oImageId },
            attributes: ['result', 'info'],
          });
          cropResult = (originImage?.result && JSON.parse(originImage.result)[0]) ||
            { x: 0, y: 0, w: originImage?.info ? JSON.parse(originImage.info).w : 0, h: originImage?.info ? JSON.parse(originImage.info).h : 0 };
        }
        if (!cropResult) {
          cropResult = { x: 0, y: 0, w: originImage?.info ? JSON.parse(originImage.info).w : 0, h: originImage?.info ? JSON.parse(originImage.info).h : 0 };
        }
        newImage.url += '###' + service.image.getUrl(image.appKey, oImageId, 'jpg', false) + '###x_' + (cropResult.x || 0) + 'y_' + (cropResult.y || 0) + 'w_' + (cropResult.w || 0) + 'h_' + (cropResult.h || 0);
        this.logger.info(`checkSourceId taskId: ${task.taskId} imageId: ${image.imageId} sourceId: ${oImageId} padding: ${JSON.stringify(cropResult)}`);
        availableImages.push(newImage);
      }
    }
    // this.logger.info(`availableImages taskId: ${task.taskId} availableImages: ${JSON.stringify(availableImages.map((item) => {
    //   return {
    //     url: item.url,
    //     taskId: item.taskId,
    //     type: item.type,
    //   };
    // }))}`);
    // 若 TaskConfig 已存在，则重新把未完成的图片放进队列即可，不需要冗余做后面的全部处理！
    if (!availableImages.length) return;
    const endImgs: IAvailableImages[] = [];
    this.logger.info(`sortImagesBegin-> type: ${type} -> taskId: ${task.taskId} -> ${availableImages.length}`);
    try {
      const taskCfg = await service.task.base.getTaskConfig(task.taskId, type);
      // this.logger.info(`${task.taskId} taskCfg: ${JSON.stringify(taskCfg)}`);
      if (taskCfg) {
        for (const image of availableImages) {
          const value = taskCfg[image.uid];
          this.logger.info(`${task.taskId} image: ${image.uid} value: ${value}`);
          if (value !== '1') {
            endImgs.push(image);
          }
        }
        availableImages = endImgs;
      }
    } catch (e) {
      this.logger.error(`${task.taskId} getErrorImgIng error: ${e}`);
    }
    // to see logger detail
    const str = availableImages.length ? availableImages.reduce((pre, cur) => {
      return pre + JSON.stringify(cur.uid) + '-----';
    }, '') : '';
    this.logger.info(`pop taskid: ${task?.taskId} --type: ${type} ---images:${str}`);
    this.logger.info(`pop taskid: ${task?.taskId} params: ${JSON.stringify(availableImages)}`);
    ctx.body = {
      status: 0,
      data: availableImages,
    };
  }

  @validate({ type: ['imageStructProcessor', 'imageColumnProcessor'], uid: 'string' })
  public async update() {
    const { ctx, service, logger } = this;
    const imageId: string = ctx.input.uid;
    const type: string = ctx.input.type;
    const result: Record<string, unknown> = !ctx.input.result ? null : JSON.parse(ctx.input.result);
    logger.info(`get update task: ${imageId} ${type}`);
    /*
     * Tips：
     * 图片识别的 RPC 服务是拉取模式，调用 this.pop 函数，拉取的人工划块处理完毕任务是读 db 拿到的，
     * 但是会逐个图片更新状态。
     * 详细参考：https://github.com/Sigma-Research/rpc-balance-client/blob/0fce1395f324da12b6ef099e12fc299c4ada6088/config/config.prod.ts
     */
    const image = await service.image.getOne({
      where: { imageId, disabled: false },
      attributes: ['taskId'],
    });
    if (!image || !image.taskId) {
      logger.info(`update no image exists : ${imageId}`);
      return ctx.body = baseError.dataNotExistError();
    }
    await service.image.pushToQueue(JSON.stringify(Object.assign({ imageId, type, taskId: image.taskId, priority: false }, { result })));
    logger.info(`push image ${imageId}, type: ${type},taskid: ${image.taskId} to queue`);
    ctx.body = { status: 0 };
  }

  public async updateV2() {
    const { ctx } = this;
    const { data } = ctx.input;
    const { result: imageHtmls, status , task_id: taskId } = data;
    if (!taskId || !status) {
      await this.service.task.base.update({
        status: this.service.task.base.statuses.error,
        errorInfo: JSON.stringify(taskId) || '跑 html 失败',
      }, { where: { taskId } });
      this.logger.info(`updateV2 callback task error: ${taskId} ${JSON.stringify(imageHtmls)}`);
      return ctx.body = { status: 0 };
    }
    const task = await this.service.task.base.getOne({ where: { taskId: Number(taskId) } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    this.logger.info(`callback task status: ${task.status} 、${task.ticketId}`);
    // 兼容学科网的自动流程
    if (task.status !== this.service.task.base.statuses.ocrProcessing && !task.ticketId) {
      return ctx.body = baseError.dataAlreadyExistError('任务状态异常');
    }
    this.logger.info(`callback task: ${taskId} ${JSON.stringify(imageHtmls)}`);
    // imageHtmls: { imageId: html }
    ctx.runInBackground(async() => {
      // 上传 html
      const urls = await Promise.all(Object.entries(imageHtmls).map(async(item) => {
        this.logger.info(`updateV2 callback taskId: ${taskId} ${item[0]}`);
        const imageId = item[0];
        if (!imageId) {
          return ctx.body = baseError.dataNotExistError('imageId不存在');
        }
        return this.service.image.setOssData(task.appKey, imageId, 'html', item[1] as string);
      }));
      this.logger.info(`updateV2 callback taskId: ${taskId} ${JSON.stringify(urls)}`);
      // 更新任务状态到标注中
      await this.service.task.base.update({ status: task.markUserId ? this.service.task.base.statuses.marking : this.service.task.base.statuses.unmarked }, { where: { taskId } });
      await this.service.plan.task.onChangeTaskStatus({
        taskId,
        taskType: PlanTaskType.MARK,
        targetStatus: task.rerun && task.markUserId ? PlanTaskStatus.ING : PlanTaskStatus.INIT,
      });
      await this.service.plan.task.onChangeTaskStatus({
        taskId,
        taskType: PlanTaskType.REVIEW,
        targetStatus: PlanTaskStatus.PENDING,
      });
    });
    ctx.body = { status: 0 };
  }

  // word预处理完成。更新结果
  @validate({
    taskId: 'number',
    status: 'number',
    imageIds: {
      type: 'array',
      required: false,
      itemType: 'string',
      min: 1,
    },
    reason: 'string?',
  })
  public async updateTaskResult() {
    const { ctx, service, app, logger } = this;
    if (ctx.params.type !== 'word' && ctx.params.type !== 'fbd') {
      return ctx.body = baseError.paramsError('任务类型错误');
    }
    const { taskId, status, imageIds, reason } = ctx.input as {
      taskId: number;
      status: 0 | 1;
      imageIds?: string[];
      reason?: string;
    };
    if (status === 0 && (!imageIds || !imageIds.length)) {
      return ctx.body = baseError.paramsError('缺少imageIds');
    }
    const type = ctx.params.type as 'word' | 'fbd';
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId, resourceType: type === 'word' ? ETaskResourceType.WORD : ETaskResourceType.FBD } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status > statuses.unmarked) {
      return ctx.body = baseError.dataAlreadyExistError('任务已进入后续流程');
    }
    if (status === 1) {
      await service.task.base.update({
        status: statuses.error,
        errorInfo: reason || `${type}预处理异常`,
      }, { where: { taskId } });
      return ctx.body = { status: 0 };
    }
    const imgIds = imageIds!;
    await service.task.base.update({ status: statuses.updating }, { where: { taskId } });
    ctx.runInBackground(async() => {
      try {
        const images = imgIds.map((imageId, i) => {
          return {
            taskId,
            imageId,
            appKey: task.appKey,
            wordCount: 0,
            marked: EImageMarkStatus.init,
            reviewed: false,
            preprocessed: true,
            disabled: false,
            filename: `${i + 1}.jpg`,
            bookId: task.bookId,
            taskOrder: i + 1,
            originalId: '',
            sourceId: '',
            multiple: false,
            columnResult: '',
            latexResult: '',
          } as ImageModel.Attributes;
        });
        await app.model.transaction(async(transaction) => {
          await service.image.delete({ where: { taskId }, transaction });
          await service.image.bulkCreate(images, { transaction });
          await service.task.base.update(
            { status: statuses.unmarked, imageCount: images.length },
            { where: { taskId }, transaction }
          );
          await service.plan.task.onChangeTaskStatus({
            taskId,
            taskType: PlanTaskType.MARK,
            targetStatus: PlanTaskStatus.INIT,
          }, { transaction });
          await service.plan.task.onChangeTaskStatus({
            taskId,
            taskType: PlanTaskType.REVIEW,
            targetStatus: PlanTaskStatus.PENDING,
          }, { transaction });
        });
        const html = await service.task.stat.stashMachineHtml(task.appKey, taskId, imgIds);
        await service.task.stat.newStat({
          html,
          taskId,
          resourceType: task.resourceType!,
          type: 'machine',
          appKey: task.appKey,
          subject: task.subject,
          imageCount: images.length,
        });
      } catch (e) {
        logger.error(e);
        await service.task.base.update({
          status: statuses.error,
          errorInfo: (e as any).message,
        }, { where: { taskId } });
      }
    });
    ctx.body = { status: 0 };
  }

  // 查看详情（http://open.hexinedu.com/#/parse/task，仅仅是一个页面，客户不再使用）
  @validate({ taskId: 'number' })
  public async getOneById() {
    const { ctx, service } = this;
    const { appKey } = ctx.data;
    const { taskId } = ctx.input;
    const { statuses, openStatuses } = service.task.base;
    const task = await service.task.base.getOne({
      where: {
        appKey,
        taskId,
      },
      attributes: ['appKey', 'status', 'isTest', 'taskName'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if ((task.status !== statuses.reviewed && !task.isTest) ||
      (task.isTest && [
        statuses.init,
        statuses.columnQueue,
        statuses.columnProcessing,
        statuses.columnProcessed,
        statuses.columnAutoProcessed,
        statuses.autoProcessing
      ].includes(task.status))) {
      return ctx.body = {
        status: 0,
        data: {
          taskName: task.taskName,
          status: task.status === statuses.error ? openStatuses.failed : openStatuses.processing,
        },
      };
    }
    // 如果是非测试任务，返回人工标注后的数据
    if (!task.isTest) {
      return ctx.body = {
        status: 0,
        data: {
          taskName: task.taskName,
          status: openStatuses.successful,
          docx: service.task.base.getUrl(task.appKey, taskId, 'docx', true),
        },
      };
    }
    const images = await service.image.getAll({ where: { taskId, disabled: false } });
    const docxUrls = images.map((item) => service.image.getUrl(item.appKey, item.imageId, 'docx', true));
    ctx.body = {
      status: 0,
      data: {
        taskName: task.taskName,
        status: openStatuses.successful,
        docx: docxUrls.length ? docxUrls[0] : '',
      },
    };
  }

  // 查看详情
  @validate({ taskId: 'number', appKey: 'string?' })
  public async getInfo() {
    const { ctx, service } = this;
    const appKey = ctx.data?.appKey || ctx.input?.appKey || '';
    if (!appKey) {
      return ctx.body = baseError.paramsError('缺少appKey');
    }
    const { taskId } = ctx.input;
    const { statuses, openStatuses } = service.task.base;
    const [task, imageCount] = await Promise.all([
      service.task.base.getOne({
        where: {
          appKey,
          taskId,
        },
        attributes: ['taskId', 'appKey', 'status', 'isCallback', 'callbackError', 'errorInfo', 'taskName', 'extra', 'ticketId', 'endReviewTime'],
      }),
      service.image.count({ where: { taskId, disabled: false } })
    ]);
    this.logger.info('open getInfo', taskId, appKey);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    // 只有当回调完成（成功或者失败后）才可以被查询
    if (!task.ticketId) {
      if (task.status === statuses.error && task.isCallback) {
        return ctx.body = {
          status: 0,
          data: {
            taskName: task.taskName,
            status: openStatuses.failed,
            errorInfo: task.errorInfo,
            extra: task.extra,
          },
        };
      }
      if (!task.isCallback) {
        return ctx.body = {
          status: 0,
          data: {
            taskName: task.taskName,
            status: openStatuses.processing,
            extra: task.extra,
          },
        };
      }
    }

    let clientMetas: Partial<IClientMetas> | undefined;
    const appl = await service.appl.getOneByUc({
      where: { appKey },
      attributes: ['userId'],
    });
    if (appl && appl.userId) {
      clientMetas = await service.client.meta.getMetas({
        userId: appl.userId,
        key: ['exportJson', 'exportDocx'],
      });
    }
    const config = service.client.meta.setDefaultMetas(clientMetas || {});
    const metaInfo = await service.task.base.getOssData(task.appKey, task.taskId, 'meta.json');
    let charStat: any;
    let status = this.service.task.base.judgeOpenTaskStatus(task.status);
    if (task.status === statuses.reviewed) {
      const stat = await this.service.task.stat.getDesignatedHTMLStatData(task.appKey, taskId, 'p');
      if (stat) {
        this.logger.info('stat', JSON.stringify(stat));
        charStat = {
          charCount: stat.char === undefined ? -1 : stat.char,
          cnCharCount: stat.cn_char === undefined ? -1 :stat.cn_char,
          puncCharCount: stat.punct_char === undefined ? -1 : stat.punct_char,
          enNumCharCount: stat.en_char === undefined ? -1 : stat.en_char,
          numCharCount: stat.num_char === undefined ? -1 : stat.num_char,
        };
      } else {
        // 如果没有统计数据，当前任务状态被认为是失败
        status = openStatuses.failed;
      }
    }
    ctx.body = {
      status: 0,
      data: {
        taskId,
        taskName: task.taskName,
        extra: task.extra,
        status: task.ticketId ? status : openStatuses.successful,
        isCallback: task.isCallback,
        info: task.ticketId ? task.status === statuses.reviewed ? undefined : task.errorInfo : undefined,
        endTime: task.ticketId ? task.endReviewTime : undefined,
        callbackError: task.callbackError,
        docx: task.ticketId ? config.exportDocx ?
          service.task.base.getUrl(task.appKey, taskId, 'formatted.docx', false) :
          undefined : undefined,
        json: config.exportJson ?
          service.task.base.getUrl(task.appKey, taskId, 'formatted.json', false) :
          undefined,
        meta: metaInfo,
        metaJson: metaInfo ? service.task.base.getUrl(task.appKey, task.taskId, 'meta.json', false) : undefined,
        charStat,
        pageCount: task.ticketId ? imageCount : undefined,
      },
    };
  }

  @validate({
    subject: ['chinese', 'math', 'en-math'],
    urls: {
      type: 'array',
      itemType: 'string',
      min: 1,
      max: 30,
    },
    callbackUrl: {
      type: 'string',
      required: false,
      max: 256,
    },
    taskName: {
      type: 'string',
      required: false,
      max: 64,
    },
    extra: {
      type: 'string',
      required: false,
      max: 128,
    },
    timeLimit: {
      type: 'number',
      min: 12 * 60,
      required: false,
    },
    timeWarning: 'number?',
    ticketId: 'string?',
    priority: 'number?',
    appKey: 'string?',
  })
  public async create() {
    const { ctx, service } = this;
    // type暂时支持imageStructProcessor,只支持异步任务
    const { callbackUrl, subject, urls, isTest, taskName, extra, timeLimit, timeWarning = 0.5, priority } = ctx.input as {
      callbackUrl: string;
      subject: 'math' | 'chinese' | 'en-math';
      urls: string[];
      isTest?: boolean | number;
      taskName: string;
      extra: string;
      timeLimit?: number;
      timeWarning?: number;
      priority?: number;
      ticketId?: string;
      appKey?: string;
    };
    if (!ctx.data?.appl?.appKey && !ctx.input?.appKey) {
      return ctx.body = baseError.paramsError('缺少appKey');
    }
    const taskType = !ctx.data?.appl.isTest && timeLimit ? ETaskType.limit : ETaskType.unset;
    const taskId = await service.task.base.relatedCreate({
      appKey: ctx.data?.appl?.appKey || ctx.input.appKey || '',
      subject,
      callbackUrl,
      images: urls,
      open: true,
      extra: extra || '',
      isTest: isTest == null || Boolean(isTest),
      taskName: taskName || '',
      taskType,
      timeLimit: taskType === ETaskType.limit ? timeLimit : 0,
      timeWarning: taskType === ETaskType.limit ? timeWarning : 0,
      priority,
      ticketId: ctx.input.ticketId,
    },
    // 学科网应该用的这个接口
    {});
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  @validate({ taskId: 'number', appKey: 'string' })
  public async revoke() {
    const { ctx, service, logger, app } = this;
    const { statuses } = service.task.base;
    const { taskId, appKey } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    logger.info(`revoke task: ${taskId}`);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    // 创建时间大于 24 小时， 允许撤销
    const { createTime } = task;
    const createTimeDate: any = new Date(createTime);
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    let isOverTimeLimit = false;
    if (now - createTimeDate > oneDay) {
      isOverTimeLimit = true;
    }

    const { columnProcessing, columnProcessed, autoProcessing, unmarked, marking, unreviewed, reviewing, dataCleaning, dataCleanfailed, jsonPreProcessing, reviewed, closed } = statuses;
    if ([columnProcessing,
      columnProcessed,
      autoProcessing,
      unmarked,
      marking,
      unreviewed,
      reviewing,
      dataCleaning,
      dataCleanfailed,
      jsonPreProcessing,
      reviewed,
      closed].includes(task.status) && !isOverTimeLimit) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      service.task.base.update(
        { status: statuses.closed },
        { transaction, where: { taskId } }
      );
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId: appKey,
        type: service.task.history.otherTypes.revoked.id,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  public async getUploadToken() {
    const { service, ctx } = this;
    const { appKey } = ctx.data;
    ctx.body = {
      status: 0,
      data: service.oss.getUploadToken(`open/${appKey}/image/`),
    };
  }

  // @validate({ taskId: 'number', wordUrl: 'string' })
  public async callback() {
    const { ctx } = this;
    const { params, result, status } = ctx.input;
    this.logger.info(`callback task: ${JSON.stringify(ctx.input)}`);
    const task_id = params.taskId;
    if (!result || status !== 2) {
      await this.service.robot.sendRobotMessageWhenCallbackError(task_id, result || 'render 失败');
      return ctx.body = { status: 0 };
    }
    const task = await this.service.task.base.getOne({ where: { taskId: Number(task_id) } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const taskJSONPath = this.service.task.base.getUrl(task.appKey, task.taskId, 'json', false);
    this.logger.info(`callback task: ${task_id} ${taskJSONPath}`);
    ctx.runInBackground(async() => {
      // 发布工单
      await this.service.workSheet.publishWorkSheet({
        business_project_id: `${task_id}`,
        ticket_id: task.ticketId,
        system_id: 0,
        delivery: {
          word_url: result,
          json_url: taskJSONPath,
        },
      });
    });
    ctx.body = { status: 0 };
  }

  public async getImageHtmlUrl() {
    const { ctx, service } = this;
    const { imageId } = ctx.input;
    const image = await service.image.getOne({
      where: { imageId, disabled: false },
      attributes: ['imageId', 'marked', 'reviewed', 'taskId', 'appKey', 'originalId', 'sourceId'],
    }) as any;
    if (!image) {
      return ctx.body = baseError.dataNotExistError('图片不存在');
    }
    const url = service.image.getUrl(image.appKey, imageId, 'html', true, true);
    ctx.body = {
      status: 0,
      data: url,
    };
  }

    @validate({ taskId: 'number' })
  public async prioritizeQueue() {
    const { ctx, service, logger } = this;
    const { taskId } = ctx.input;
    const { statuses } = service.task.base;
    
    logger.info(`[prioritizeQueue] 开始处理任务 ${taskId} 的队列置顶请求`);
    
    // 检查任务是否存在且状态为 -100 (columnAutoProcessing)
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['taskId', 'status', 'appKey'],
    });
    
    if (!task) {
      logger.warn(`[prioritizeQueue] 任务 ${taskId} 不存在`);
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    
    logger.info(`[prioritizeQueue] 任务 ${taskId} 当前状态: ${task.status}, appKey: ${task.appKey}`);
    
    if (task.status !== statuses.columnAutoProcessing) {
      logger.warn(`[prioritizeQueue] 任务 ${taskId} 状态不符合要求，当前状态: ${task.status}, 需要状态: ${statuses.columnAutoProcessing}`);
      return ctx.body = baseError.paramsError('只有状态为机器划块识别中(-100)的任务才能进行队列置顶操作');
    }
    
    logger.info(`[prioritizeQueue] 任务 ${taskId} 状态验证通过，开始执行队列置顶操作`);
    
    // 执行置顶操作
    const result = await service.image.prioritizeTaskInQueue(taskId);
    
    logger.info(`[prioritizeQueue] 任务 ${taskId} 优先级设置完成，修改项目数: ${result.modified}, 队列总长度: ${result.total}`);
    
    const responseMessage = result.modified > 0 ?
      `成功将任务 ${taskId} 的 ${result.modified} 个队列项设置为高优先级` :
      '队列中未找到该任务的相关项目';
    
    logger.info(`[prioritizeQueue] 任务 ${taskId} 处理结果: ${responseMessage}`);
    
    ctx.body = {
      status: 0,
      data: {
        taskId,
        modified: result.modified,
        total: result.total,
        message: responseMessage,
      },
    };
  }
}
