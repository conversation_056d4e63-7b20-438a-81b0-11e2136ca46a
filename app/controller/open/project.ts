'use strict';

import { Controller } from 'egg';
import { Op } from 'sequelize';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { IProjectMetas } from '../../model/projectMeta';
import { ITaskFiles, ITaskPdfs, ITaskWords } from '../../model/taskFile';
import { ETaskResourceType } from '../../model/task';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import { removeCommonExtensions } from '../../core/utils';
import * as _ from 'lodash';

export default class OpenProjectController extends Controller {

  @validate({
    projectIds: {
      type: 'array?',
      itemType: 'number',
    },
    workOrder: 'string?',
  })
  public async getProject() {
    const { ctx, service } = this;
    const { projectIds, workOrder } = ctx.input as {
      projectIds?: number[];
      workOrder?: string;
    };
    const where: any = {};
    if (projectIds) {
      where[Op.or] = projectIds.map((id) => ({ id }));
    }
    if (workOrder) {
      where.workOrder = workOrder;
    }
    const projects = await service.project.base.getAll({ where });
    const metaDict = await service.project.meta.getMetasDict({ projectId: { $in: projects.map((p) => p.id) } });
    projects.forEach((project: any) => {
      const meta = metaDict[project.id];
      project.meta = meta || {};
      if (meta) {
        project.metaJsonUrl = service.project.base.getUrl(project.appKey, project.id, 'meta.json');
      }
      project.deliverable = {
        json: `/${service.project.base.getOssKey(project.appKey, project.id, 'json')}`,
        zip: `/${service.project.base.getOssKey(project.appKey, project.id, 'docx.zip')}`,
        docx: `/${service.project.base.getOssKey(project.appKey, project.id, 'docx')}`,
        html: `/${service.project.base.getOssKey(project.appKey, project.id, 'html')}`,
      };
    });
    ctx.body = {
      status: 0,
      data: {
        count: projects.length,
        projects,
      },
    };
  }

  public async updateProject() {
    const { service, ctx } = this;
    const { projectId, workOrder } = ctx.input;
    const project = await service.project.base.getOne({ where: { id: projectId } });
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    await service.project.base.update(
      { workOrder: workOrder || '' },
      { where: { id: projectId } }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    projectName: 'string',
    subject: 'string?',
  })
  public async updateProjectName() {
    const { service, ctx } = this;
    const { projectId, projectName } = ctx.input;
    const project = await service.project.base.getOne({ where: { id: projectId } });
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    await service.project.base.update(
      { projectName },
      { where: { id: projectId } }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    projectId: 'number',
    file: {
      type: 'object',
      rule: {
        pdfs: 'array?',
        fbds: 'object?',
      },
    },
  })
  public async setFiles() {
    const { service, ctx } = this;
    const { projectId, file } = ctx.input as {
      projectId: number;
      file: Partial<ITaskFiles>;
    };
    const [project, books] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['appKey', 'projectName'],
      }),
      service.book.getAll({
        where: { projectId },
        attributes: ['id'],
      })
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const tasks = await service.task.base.getAll({ where: { bookId: books.map((b) => b.id) } });
    this.logger.info(`[open.setFiles]: projectId-${projectId}, file - ${JSON.stringify(file)}, tasks - ${JSON.stringify(tasks)}`);
    if (tasks.length) {
      const taskIds = Array.from(new Set(
        [
          ...Array.from(new Set(tasks.filter((item) => (item as any).parentTaskId).map((item) => (item as any).parentTaskId))),
          ...tasks.map((t) => t.taskId)
        ]
      ));
      ctx.runInBackground(async() => {
        await service.task.taskV2.pdf2image(file.pdfs!);
        await service.task.file.setFiles(taskIds, file);
      });
    }
    ctx.body = { status: 0 };
  }

  public async createImageProject() {
    const { service, ctx, app } = this;
    const { appKey, projectName, hasCatalog, meta, workOrder, priority, tasks } = ctx.input as {
      appKey: string;
      projectName: string;
      hasCatalog: boolean;
      meta: Partial<IProjectMetas>;
      workOrder: string;
      priority?: number;
      tasks: { taskName: string; images: string[] }[];
    };
    // 转一下
    meta.imageHtmlVersion = (meta as any).imagehtml_version || '';
    const inputSbuject: string = ctx.input.subject;
    const subject: 'math' | 'chinese'
      = ['chemistry', '化学', 'biology', '生物', 'physics', '物理', 'math',
        '数学', 'science', '科学', 'li', '理综', 'computer_science', '信息技术', 'math_li', 'math2', 'math3', 'math1', '理数', '数学一', '数学二', '数学三']
        .includes(inputSbuject) ? 'math' : 'chinese';
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }
    const res = await app.model.transaction(async(transaction) => {
      const project = await service.project.base.create(
        {
          userId: 0,
          projectName,
          appKey,
          subject,
          errorInfo: '',
          workOrder: workOrder || '',
          status: service.project.base.statuses.processing,
          reviewUserId: 0,
          startReviewTime: null,
          endReviewTime: null,
          markJsonStatus: 0,
        },
        { transaction }
      );
      if (workOrder) meta.workOrder = workOrder;
      await service.project.meta.setMetas([project.id], meta, { transaction });
      const books = [{
        appKey,
        subject,
        bookName: `${projectName}-试题`,
        projectId: project.id,
        status: service.book.statuses.processing,
        errorInfo: '',
        type: service.book.types.question,
        reviewUserId: 0,
        taskCount: tasks.length,
      }];
      if (hasCatalog) {
        books.unshift({
          appKey,
          subject,
          bookName: `${projectName}-目录`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.catalog,
          reviewUserId: 0,
          taskCount: 0,
        });
      }
      const resBooks = await service.book.bulkCreate(books, { transaction });
      // 更新项目 oss meta.json
      ctx.runInBackground(async() => {
        await service.project.base.setOssMeta(appKey, project.id, meta);
      });
      return { project, resBooks };
    });

    this.logger.info(`[createProject] workOrder:Image ${workOrder} ${JSON.stringify(ctx.input)}`);

    const book = res.resBooks.find((book) => book.type === service.book.types.question)!;
    const taskIds = await Promise.all(tasks.map(({ taskName, images }, index) => {
      return service.task.base.relatedCreate({
        appKey: book.appKey,
        subject: book.subject,
        images,
        open: false,
        callbackUrl: '',
        extra: '',
        isTest: false,
        taskName: taskName?.includes(projectName) ? taskName : `${projectName}-${taskName}`,
        bookId: book.id,
        bookOrder: index + 1,
        priority,
      }, meta);
    }));

    ctx.runInBackground(async() => {
      taskIds.forEach(async(taskId) => {
        if (taskId) {
          await service.task.base.setOssMeta(appKey, taskId, meta);
          await service.task.meta.setMetas([taskId], meta);
        }
      });
    });

    ctx.body = {
      status: 0,
      data: { projectId: res.project.id },
    };
  }

  // 传入projectId 则重新生成任务
  public async createProject() {
    const { service, ctx, app } = this;
    let { appKey, projectName, hasCatalog, meta, workOrder, priority, file, is_word_json_o2o, projectId, is_res_word } = ctx.input as {
      appKey: string;
      projectName: string;
      hasCatalog: boolean;
      meta: Partial<IProjectMetas>;
      workOrder: string;
      priority?: number;
      file: Partial<ITaskFiles>;
      is_word_json_o2o?: boolean;
      projectId?: number;
      is_res_word: boolean;
    };
    // 转一下
    meta.imageHtmlVersion = (meta as any).imageHtmlVersion || '';

    const inputSbuject: string = ctx.input.subject;
    let subject: 'math' | 'chinese' = ['chemistry', '化学', 'biology', 'math_wen' , '生物', 'physics', '物理', 'math', '数学', 'science', '科学', 'li', '理综', 'computer_science', '信息技术', 'math_li', '理数']
      .includes(inputSbuject) ? 'math' : 'chinese';
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }

    let project:any;
    let res: any;
    let resBooks: any;

    if (!projectId) {
      res = await app.model.transaction(async(transaction) => {
        const project = await service.project.base.create(
          {
            userId: 0,
            projectName,
            appKey,
            subject,
            errorInfo: '',
            workOrder: workOrder || '',
            status: service.project.base.statuses.processing,
            reviewUserId: 0,
            startReviewTime: null,
            endReviewTime: null,
            markJsonStatus: 0,
            endWithWords: is_word_json_o2o,
            isResWord: is_res_word || false,
            priority: priority || 0,
          },
          { transaction }
        );
        await service.project.meta.setMetas([project.id], meta, { transaction });
        const books = [{
          appKey,
          subject,
          bookName: `${projectName}-试题`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.question,
          reviewUserId: 0,
          taskCount: 0,
        }];
        if (hasCatalog) {
          books.unshift({
            appKey,
            subject,
            bookName: `${projectName}-目录`,
            projectId: project.id,
            status: service.book.statuses.processing,
            errorInfo: '',
            type: service.book.types.catalog,
            reviewUserId: 0,
            taskCount: 0,
          });
        }
        const resBooks = await service.book.bulkCreate(books, { transaction });
        // 更新项目 oss meta.json
        ctx.runInBackground(async() => {
          await service.project.base.setOssMeta(appKey, project.id, meta);
        });
        return { project, resBooks };
      });
      project = res.project;
      resBooks = res.resBooks;
      this.logger.info(`[createProject] workOrder: ${workOrder} ${res.project.id} ${JSON.stringify(ctx.input)}`);
    } else {
      // 重新生成任务
      project = await service.project.base.getOne({ where: { id: projectId } });
      resBooks = await service.book.getAll({ where: { projectId } });
      is_word_json_o2o = project.endWithWords;
      subject = project.subject;
    }

    if (workOrder) meta.workOrder = workOrder;

    // 将项目的 oss meta.json 同步到每个任务
    ctx.runInBackground(async() => {
      const book = resBooks.find((book) => book.type === service.book.types.question)!;
      const { zips, pdfs, words, fbds, workOrderFbdBasePath, workOrderWordBasePath } = file;
      let taskId = 0;

      // pdf任务
      if (pdfs && !zips && !fbds && !workOrderFbdBasePath && !words && !workOrderWordBasePath) {
        taskId = await service.task.taskV2.pdfCreate({
          appKey: book.appKey,
          subject: book.subject,
          priority,
          open: false,
          callbackUrl: '',
          extra: '',
          isTest: false,
          bookId: book.id,
          bookOrder: 1,
          taskName: `${projectName}-拆分任务`,
          meta,
          file,
        });
      }

      if (zips && pdfs && fbds && workOrderFbdBasePath) {
        taskId = await service.task.taskV2.fbdCreate({
          appKey: book.appKey,
          subject: book.subject,
          open: false,
          callbackUrl: '',
          extra: '',
          isTest: false,
          bookId: book.id,
          bookOrder: 1,
          taskName: `${projectName}-拆分任务`,
          meta,
          file,
          priority,
          projectId: project.id,
        });
      }

      if (words && workOrderWordBasePath && !is_word_json_o2o) {
        taskId = await service.task.taskV2.wordCreate({
          appKey: book.appKey,
          subject: book.subject,
          open: false,
          callbackUrl: '',
          extra: '',
          isTest: false,
          bookId: book.id,
          bookOrder: 1,
          taskName: `${projectName}-拆分任务`,
          file,
          meta,
          priority,
          projectId: project.id,
        });
      }

      if (words && workOrderWordBasePath && is_word_json_o2o) {
        // 1. 拿到bodylist -> item
        for (const [index, item] of words.body.entries()) {
          // console.log(index, item);
          const fileItem = { words: { body: [item], answer: [] }, workOrderWordBasePath };
          const wordName = item.name ? removeCommonExtensions(item.name) : '';
          taskId = await service.task.taskV2.wordCreate({
            appKey: book.appKey,
            subject: book.subject,
            open: false,
            callbackUrl: '',
            extra: '',
            isTest: false,
            bookId: book.id,
            bookOrder: index + 1,
            taskName: `${wordName ? `${projectName}-${wordName.replace(/-/g, '_')}` : `${projectName}-多Word拆分任务-${index + 1}`}`,
            file: fileItem,
            meta,
            priority,
            projectId: project.id,
          });
          try {
            await Promise.all([
              service.task.base.setOssMeta(appKey, taskId, meta),
              service.task.meta.setMetas([taskId], meta),
              service.task.file.setFiles([taskId], fileItem)
            ]);
          } catch (e) {
            const { statuses } = service.task.base;
            service.task.base.update(
              {
                errorInfo:`创建任务失败，请联系维护人员，${e}`,
                status: statuses.error,
              },
              { where: { taskId } }
            );
            this.logger.info(`[open.createProject]: set metas or files error ${taskId}`, e);
          }
        }
      }

      if (taskId && !Number(is_word_json_o2o)) {
        try {
          await Promise.all([
            service.task.base.setOssMeta(appKey, taskId, meta),
            service.task.meta.setMetas([taskId], meta),
            service.task.file.setFiles([taskId], file)
          ]);
        } catch (e) {
          const { statuses } = service.task.base;
          service.task.base.update(
            {
              errorInfo:`创建任务失败，请联系维护人员，${e}`,
              status: statuses.error,
            },
            { where: { taskId } }
          );
          this.logger.info(`[open.createProject]: set metas or files error ${taskId}`, e);
        }
      }
    });

    ctx.body = {
      status: 0,
      data: { projectId: res?.project.id || projectId },
    };
  }

  public async copyProject() {
    const { service, ctx, app } = this;
    const { appKey, hasCatalog, projectId } = ctx.input as {
      appKey: string;
      hasCatalog: boolean;
      projectId: number;
    };
    this.logger.info(`[open.copyProject]: ${JSON.stringify(ctx.input)}`);
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }

    // 重新生成任务
    const originProject = await service.project.base.getOne({ where: { id: projectId } });
    const originBooks = await service.book.getAll({ where: { projectId } });

    if (!originProject) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (!originBooks || !originBooks.length) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }

    const subject = originProject.subject;
    const projectName = originProject.projectName + '-复制';
    const isWordJsonO2O = originProject.endWithWords;
    const workOrder = originProject.workOrder;
    const isResWord = originProject.isResWord;
    let meta;
    const res = await app.model.transaction(async(transaction) => {
      const project = await service.project.base.create(
        {
          userId: 0,
          projectName,
          appKey,
          subject,
          errorInfo: '',
          workOrder: workOrder || '',
          status: service.project.base.statuses.processing,
          reviewUserId: 0,
          startReviewTime: null,
          endReviewTime: null,
          markJsonStatus: 0,
          endWithWords: isWordJsonO2O,
          isResWord: isResWord || false,
        },
        { transaction }
      );
      meta = await service.project.meta.getMetas({ projectId });
      this.logger.info(`[open.copyProject]: projectId: ${projectId} meta: ${JSON.stringify(meta)}`);
      await service.project.meta.setMetas([project.id], meta, { transaction });
      const books = [{
        appKey,
        subject: originProject.subject,
        bookName: `${projectName}-试题`,
        projectId: project.id,
        status: service.book.statuses.processing,
        errorInfo: '',
        type: service.book.types.question,
        reviewUserId: 0,
        taskCount: 0,
      }];
      if (hasCatalog) {
        books.unshift({
          appKey,
          subject,
          bookName: `${projectName}-目录`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.catalog,
          reviewUserId: 0,
          taskCount: 0,
        });
      }
      const resBooks = await service.book.bulkCreate(books, { transaction });
      // 更新项目 oss meta.json
      ctx.runInBackground(async() => {
        await service.project.base.setOssMeta(appKey, project.id, meta);
      });
      return { project, resBooks };
    });
    const project = res.project;
    const resBooks = res.resBooks;

    if (workOrder) meta.workOrder = workOrder;

    // 将项目的 oss meta.json 同步到每个任务
    const book = resBooks.find((book) => book.type === service.book.types.question)!;
    const tasks = await service.task.base.getAll({ where: { bookId: originBooks[0].id } });
    this.logger.info(`[open.copyProject]: projectId-${projectId}, tasks - ${tasks.map((v) => v.taskId)}`);
    const canCopy = tasks.some((task) => task.parentTaskId);
    if (!canCopy) {
      this.logger.info(`[open.copyProject]: 项目${projectId} 无法复制`);
      return ctx.body = baseError.dataNotExistError('项目无子任务，无法复制');
    }

    // 复制
    try {
      await Promise.all(tasks.map(async(task) => service.task.base.copyTask(task, book.id)));
    } catch (e) {
      this.logger.error(`[open.copyProject]: 复制任务失败 ${e}`);
    }

    ctx.body = {
      status: 0,
      data: { projectId: project.id },
    };
  }

  @validate({
    projectIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  public async exportStat() {
    const { service, ctx, app } = this;
    const input = ctx.input as {
      taskIds?: number[];
      projectIds?: number[];
      ticketIds?: string[];
      userIds?: number[];
      types?: string[];
      appKeys?: string[];
      subjects?: string[];
      startTime?: number;
      endTime?: number;
      columns: { title: string; prop: string }[];
      sort: {
        item: string;
        order: 'asc' | 'desc';
      }
    };
    const match: any = {};
    const project = { _id: 0 };
    const taskAttrs: string[] = [];
    const projectAttrs: string[] = [];
    const columns = [
      {
        title: '大题数',
        prop: 'json.question.level_1',
      },
      {
        title: '英文字母数',
        prop: 'html.p.en_char',
      },
      {
        title: '汉字数',
        prop: 'html.p.cn_char',
      },
      {
        title: '标点符号数',
        prop: 'html.p.punct_char',
      },
      {
        title: '公式内字符数',
        prop: 'html.latex.char',
      }
    ];
    columns.forEach((item) => {
      // 统计属性
      project[item.prop] = 1;
    });
    if (taskAttrs.length || projectAttrs.length || input.projectIds) {
      project['task_id'] = 1;
      projectAttrs.push('id');
      taskAttrs.push('taskId');
    }
    if (projectAttrs.length || input.projectIds) {
      taskAttrs.push('bookId');
      projectAttrs.push('id');
    }
    type PromiseValue<T> = T extends PromiseLike<infer U> ? U : never;
    let books: PromiseValue<ReturnType<typeof service.book.getAll>> | undefined;
    let tasks: PromiseValue<ReturnType<typeof service.task.base.getAll>> | undefined;
    books = await service.book.getAll({ where: { projectId: input.projectIds }, attributes: ['id', 'projectId'] });
    const query: any = { bookId: books.map((b) => b.id) };
    tasks = await service.task.base.getAll({ where: { ...query, mergedTaskId: null }, attributes: taskAttrs });
    match.task_id = { $in: tasks.map((task) => task.taskId) };
    match.type = { $in: ['review'] };
    const stats = await app.mongo.db.collection('stat').find(match, project).toArray();
    const taskIds = [...new Set<number>(stats.map((item) => item.task_id).filter((i) => i))];
    const _bookMap: any = {};
    const _projectMap: any = {};
    const _taskMap: any = {};
    const _statsMap: any = {};
    if (!tasks) {
      tasks = taskAttrs.length && taskIds.length ? await service.task.base.getAll({
        where: { taskId: taskIds, mergedTaskId: null },
        attributes: taskAttrs,
      }) : [];
      // 没输入 taskId 的情况下
    }
    for (const task of tasks) {
      // 获取这个任务的总字符 html.p.char
      let book;
      let _project;
      let _tasks;
      let _stats;
      if (!_bookMap[task.bookId]) {
        // 处理过就别再查询一次了, 没有 book就不需要再往下走
        book = await service.book.getOne({ where: { id: task.bookId } });
        _bookMap[task.bookId] = book;
      } else {
        book = _bookMap[task.bookId];
      }
      if (!book) continue;
      if (!_projectMap[book.projectId]) {
        _project = await service.project.base.getOne({ where: { id: book.projectId } });
        _projectMap[book.projectId] = _project;
      } else {
        _project = _projectMap[book.projectId];
      }
      if (_taskMap[_project.id]) {
        _tasks = _taskMap[_project.id];
      } else {
        _tasks = await service.task.base.getAll({ where: { bookId: task.bookId, mergedTaskId: null } });
        _taskMap[_project.id] = _tasks;
      }
      if (_statsMap[_project.id]) {
        _stats = _statsMap[_project.id];
      } else {
        _stats = await app.mongo.db.collection('stat').find({ ...match, task_id:  { $in: _tasks.map((t) => t.taskId) } }).toArray();
        _statsMap[_project.id] = _stats;
      }
    }
    if (!books) {
      const bookIds = projectAttrs.length ? [...new Set(tasks.map((item) => item.bookId))] : [];
      books = bookIds.length ?
        await service.book.getAll({ where: { id: bookIds }, attributes: ['id', 'projectId'] }) :
        [];
    }
    const projectIds = [...new Set(books.map((b) => b.projectId))];
    const projects = projectIds.length && projectAttrs.length ?
      await service.project.base.getAll({ where: { id: projectIds }, attributes: projectAttrs }) :
      [];
    const taskMap = _.keyBy(tasks, (basic) => basic.taskId);
    const bookMap = _.keyBy(books, (book) => book.id);
    const projectMap = _.keyBy(projects, (project) => project.id);
    // input.columns.push({ title: '个数', prop: 'taskCount' });
    const _stats: typeof stats = [];
    const rows: any[] = [];
    for (const stat of stats) {
      // 项目 ID
      const task = { ...taskMap[stat.task_id], startTime: '', endTime: '' };
      const book = { ...bookMap[task.bookId] };
      const _project = { ...projectMap[book.projectId] };
      stat.projectId = _project.id;
      _stats.push({ ...stat });
      rows.push(columns.map((item) => {
        return { [item.prop]: _.get(stat, item.prop) || 0 , projectId: stat.projectId };
      }));
    }

    const transformed = Object.entries(rows).map(([, array]) => {
    // 合并每个数组为单个对象
      return array.reduce((acc, item) => {
        const projectId = item.projectId;
        if (!acc.projectId) acc.projectId = projectId;
        for (const [k, v] of Object.entries(item)) {
          if (k !== 'projectId') {
            acc[k] = v;
          }
        }
        return acc;
      }, {});
    });

    const merged = Object.values(
      transformed.reduce((acc, item) => {
        const projectId = item.projectId;
        if (!acc[projectId]) {
          acc[projectId] = { ...item };
        } else {
          for (const [key, value] of Object.entries(item)) {
            if (key !== 'projectId') {
              acc[projectId][key] = (acc[projectId][key] || 0) + value;
            }
          }
        }
        return acc;
      }, {})
    );

    ctx.body = { status: 0, data: merged };
  }

  @validate({
    projectIds: {
      type: 'array',
      itemType: 'number',
    },
    subject: 'string?',
    stage: 'string?',
  })
  public async changeSubject() {
    const { service, ctx, app, logger } = this;
    let { projectIds, subject, stage } = ctx.input as {
      projectIds: number[];
      subject?: string;
      stage?: string;
    };
    const ids = projectIds.filter(async(id) => await service.project.base.exists({ where: { id } }));
    if (!ids.length || ids.length !== projectIds.length) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    logger.info(`[open.changeSubject]: ${JSON.stringify(ctx.input)}`);
    ctx.runInBackground(async() => {
      await app.model.transaction(async(transaction) => {
        let isChangeSubject = true;
        if (!subject) {
          const projects = await service.project.base.getAll({ where: { id: ids } });
          subject = projects[0].subject;
          isChangeSubject = false;
        }
        if (!stage) {
          const metaDict = await service.project.meta.getMetasDict({ projectId: ids[0] });
          stage = metaDict[ids[0]].stage;
        }
        const _subject: 'math' | 'chinese' = ['chemistry', '化学', 'biology', 'math_wen' , '生物', 'physics', '物理', 'math', '数学', 'science', '科学', 'li', '理综', 'computer_science', '信息技术', 'math_li', '理数']
          .includes(subject) ? 'math' : 'chinese';
        await Promise.all(ids.map(async(id) =>
          Promise.all([
            service.project.base.update({ subject: _subject }, {
              where: { id },
              transaction,
            }),
            service.book.update({ subject: _subject }, {
              where: { projectId: id },
              transaction,
            })
          ])
        ));
        // 更新 meta, 不存在的话就不修改
        await service.project.meta.setMetas(ids, {
          subject,
          stage,
        }, { transaction });

        // 更新 项目的任务
        const books = await service.book.getAll({ where: { projectId: ids } });
        const tasks = await service.task.base.getAll({ where: { bookId: books.map((b) => b.id) } });
        const { statuses } = service.task.base;
        if (tasks.some((task) =>
          ![statuses.init,
            statuses.pending,
            statuses.updating,
            statuses.split,
            statuses.spliting].includes(task.status))) {
          this.logger.info('任务状态不允许修改学科');
          return;
        }
        if (isChangeSubject) {
          // 重跑
          const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
          for (const task of tasks) {
            const taskId = task.taskId;
            const file = fileDict[taskId];
            task.subject = _subject;
            if (task.resourceType === ETaskResourceType.WORDV2) {
              const resFile = await service.task.file.getFiles({ taskId });
              file.pdfs = resFile.pdfs || [];
              const words: ITaskWords | undefined = fileDict[taskId].words;
              words?.body.forEach((item) => {
                if (item.isPreHandle) {
                  const curPdf = (fileDict[taskId].pdfs as ITaskPdfs).find((pdf) => pdf.name === item.name);
                  if (curPdf) {
                    file.pdfs?.splice(file.pdfs.indexOf(curPdf), 1);
                  }
                }
              });
            }
            const meta = await service.task.meta.getMetas({ taskId });
            meta.bookType = '试卷';
            await Promise.all([
              service.task.taskV2.restart(task as any, file, meta, true, false,{ transaction }),
              service.plan.task.onChangeTaskStatus({
                taskId,
                taskType: PlanTaskType.MARK,
                targetStatus: PlanTaskStatus.PENDING,
              }, { transaction }),
              service.task.file.setFiles([taskId], file, { transaction }),
              service.plan.task.onChangeTaskStatus({
                taskId,
                taskType: PlanTaskType.REVIEW,
                targetStatus: PlanTaskStatus.PENDING,
              }, { transaction })
            ]);
          }

          await Promise.all(tasks.map(async(task) => {
            return service.task.base.update({ subject: _subject }, {
              where: { taskId: task.taskId },
              transaction,
            });
          }));
        }
        // 更新 meta
        await service.task.meta.setMetas(tasks.map((t) => t.taskId), {
          subject,
          stage,
        }, { transaction });
        await Promise.all(tasks.map(async(task) => {
          return service.task.history.create({
            taskId: task.taskId,
            userId: 0,
            type: service.task.history.otherTypes.changeSubject.id,
            data: `修改项目学科为${subject}，学段为${stage}`,
            costTime: 0,
          }, { transaction });
        }));
        // 记录操作日志
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    projectIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  public async delete() {
    const { service, ctx } = this;
    const { projectIds } = ctx.input as {
      projectIds: number[];
    };
    await Promise.all(projectIds.map(async(id) => {
      const exists = await service.project.base.exists({ where: { id } });
      if (exists) return service.project.base.relatedDelete(id);
    }));
    this.logger.info(`[open.delete]: ${JSON.stringify(projectIds)}`);
    ctx.body = { status: 0 };
  }

  public async getPdfPages() {
    const { service, ctx } = this;
    const { projectId } = ctx.input as {
      projectId: string;
    };
    const whereOpt = {} as any;
    if (!projectId) return ctx.body = baseError.paramsError('缺少xdoc项目id');
    whereOpt.id = projectId;
    const project = await service.project.base.getRelateOne({ where: whereOpt });
    return ctx.body = {
      status: 0,
      pdfCount: project?.pdfCount,
    };
  }

  public async callback() {
    const { ctx } = this;
    const { result, status, projectId, isResWord } = ctx.input;
    this.logger.info(`callback project: ${JSON.stringify(ctx.input)}`);
    if (!result || status !== 2) {
      await this.service.robot.sendRobotMessageWhenCallbackError(projectId, result || 'render 失败');
      return ctx.body = { status: 0 };
    }
    const project = await this.service.project.base.getOne({ where: { id: projectId } });
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (isResWord) {
      try {
        await this.service.project.base.update(
          { status: this.service.project.base.statuses.reviewed },
          { where: { id: projectId } });
        const { data } = await this.service.rbs.callbackRBS({
          task_id: projectId,
          task_type: 'html_docx',
          task_status: true,
        });
        // 交付
        const projectMeta = await this.service.project.meta.getMetas({ projectId });
        let pdfCount = project?.pdfCount;
        if (!pdfCount && projectMeta?.pageCountInfos?.length) {
          pdfCount = projectMeta?.pageCountInfos[0].internalPages;
        }
        await this.app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            business_project_id: `${projectId}`,
            ticket_id: project.workOrder,
            system_id: 0,
            xdoc_pdf_page_count: pdfCount || 0,
            delivery: {
              task_name: project.projectName,
              // word_url 一定要遵循这个命名格式（bucket name）：https://sigma-stable-doc.oss-cn-shanghai.aliyuncs.com
              word_url: result,
            },
          }),
        });

        this.logger.info(`callback project: ${JSON.stringify(data)}`);
      } catch (e) {
        await this.service.rbs.callbackRBS({
          task_id: projectId,
          task_type: 'html_docx',
          task_status: false,
        });
        this.logger.error(`callback project: ${JSON.stringify(e)}`);
      }
    }
    try {
      await this.service.project.base.update({ docxUrl: result }, { where: { id: projectId } });
      const { data } = await this.service.rbs.callbackRBS({
        task_id: projectId,
        task_type: 'html_docx',
        task_status: true,
      });
      this.logger.info(`callback project: ${JSON.stringify(data)}`);
    } catch (e) {
      await this.service.rbs.callbackRBS({
        task_id: projectId,
        task_type: 'html_docx',
        task_status: false,
      });
      this.logger.error(`callback project: ${JSON.stringify(e)}`);
    }

    ctx.body = { status: 0 };
  }

  /**
 * 根据发布时间范围查询项目列表
 * @param {object} ctx - 上下文对象
 */
  async getListByUpdateTime() {
    const { ctx, service } = this;
    const { start_time, end_time } = ctx.input;
    // 如果不是 xxxx-xx-xx 格式的时间，返回错误
    if (!/^\d{4}-\d{2}-\d{2}$/.test(start_time) || !/^\d{4}-\d{2}-\d{2}$/.test(end_time)) {
      return ctx.body = baseError.paramsError('时间格式错误，请使用 yyyy-mm-dd 格式');
    }

    try {
      const projectList = await service.project.base.getListByUpdateTime(start_time, end_time);
      const count = projectList.length;
      // 构建响应数据
      const result = projectList.map((project) => ({
        project_id: project.id,
        resource_type: project.resourceType,
        subject: project.subject,
        html_url: project.htmlUrl || '',
        json_url: project.jsonUrl || '',
        publish_time: project.updateTime,
      }));

      ctx.body = {
        status: 0,
        data: result,
        count,
      };
    } catch (error) {
      ctx.body = baseError.dataNotExistError(`查询项目失败: ${error.message}`);
    }
  }
}
