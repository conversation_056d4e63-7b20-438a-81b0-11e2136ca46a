/**
 * @file 应用控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import * as sha1 from 'sha1';

export default class ApplicationController extends Controller {

  private randomStr(len: number) {
    const str1 = Math.random().toString(36).substr(2);
    const str2 = Math.random().toString(36).substr(2);
    return sha1(`${str1}:${Number(new Date())}:${str2}`).substr(0, len);
  }

  public async getList() {
    const { ctx, service } = this;
    const { key } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {} as any;
    if (key) {
      whereOpt.appName = { $like: `%${key}%` };
    }
    const [count, applications] = await Promise.all([
      service.appl.count({ where: whereOpt }),
      service.appl.getList({ page, pageSize, where: whereOpt })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        applications,
      },
    };
  }

  @validate({ userId: 'number' })
  public async create() {
    const { ctx, service } = this;
    const { userId, isTest } = ctx.input;
    const client = await service.client.base.getOne({ where: { userId } });
    if (!client || !client.isActive) {
      return ctx.body = baseError.dataNotExistError('客户不存在或者未激活');
    }
    const appKey = this.randomStr(24);
    const appSecret = this.randomStr(40);
    const appName = `${client.clientName}-${isTest ? '测试' : '正式'}`;
    await service.appl.create({
      appKey,
      appSecret,
      userId,
      appName,
      isTest,
      isActive: true,
    });
    ctx.body = {
      status: 0,
      data: {
        userId,
        appKey,
        appSecret,
        appName,
      },
    };
  }

  @validate({ appKey: 'string' })
  public async disable() {
    const { ctx, service } = this;
    const { appKey } = ctx.input;
    const application = await service.appl.getOneByUc({ where: { appKey } });
    if (!application) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.appl.update({ isActive: false }, { where: { appKey } });
    ctx.body = { status: 0 };
  }

  @validate({ appKey: 'string' })
  public async enable() {
    const { ctx, service } = this;
    const { appKey } = ctx.input;
    const application = await service.appl.getOneByUc({ where: { appKey } });
    if (!application) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.appl.update({ isActive: true }, { where: { appKey } });
    ctx.body = { status: 0 };
  }

  @validate({ appKey: 'string' })
  public async delete() {
    const { ctx, service } = this;
    const { appKey } = ctx.input;
    const application = await service.appl.getOneByUc({ where: { appKey } });
    if (!application) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.appl.delete({ where: { appKey } });
    ctx.body = { status: 0 };
  }

  @validate({ userId: 'number' })
  public async getAllByClient() {
    const { ctx, service } = this;
    const { userId } = ctx.input;
    const applications = await service.appl.getAllByUc({
      where: { userId },
      attributes: ['appKey', 'isTest', 'isActive', 'appName'],
    });
    ctx.body = {
      status: 0,
      data: applications,
    };
  }
}
