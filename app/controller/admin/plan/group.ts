/**
 * @file 生产计划任务组控制
 */

'use strict';

import { Controller } from 'egg';

import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';
import { PlanUserRoleType } from '../../../model/planUser';
import { PlanTaskAssignType } from '../../../model/planGroup';
import * as _ from 'lodash';
import { PlanTaskType } from '../../../model/planFlow';

export default class PlanGroupController extends Controller {
  @validate({
    planFlowId: 'number',
    name: 'string',
    assignType: 'number',
    userId: 'number?',
    userIds: {
      type: 'array',
      required: false,
      itemType: 'number',
      min: 0,
    },
  })
  public async create() {
    const { ctx, service, app } = this;
    const { planFlowId, name, assignType, userId, userIds } = ctx.input;
    if (assignType === PlanTaskAssignType.FORCE && !userId) {
      return ctx.body = baseError.dataAlreadyExistError('分配制任务组缺少组长');
    }
    if (assignType !== PlanTaskAssignType.FORCE && userId) {
      return ctx.body = baseError.dataAlreadyExistError('自由领用任务组不能有组长');
    }
    const users: { userId: number, roleType: PlanUserRoleType }[] = (userIds || []).map((userId: number) => {
      return { userId, roleType: PlanUserRoleType.NORMAL };
    });
    if (userId) {
      users.unshift({ userId, roleType: PlanUserRoleType.MANAGER });
    }
    const group = await app.model.transaction(async(transaction) => {
      const group = await service.plan.group.create(
        { planFlowId, name, assignType },
        { transaction }
      );
      if (users.length) {
        await service.plan.group.addUsers(planFlowId, group.id, users, { transaction });
      }
      return group;
    });
    ctx.body = {
      status: 0,
      data: group.id,
    };
  }

  @validate({
    planGroupId: 'number',
    name: 'string',
  })
  public async update() {
    const { ctx, service } = this;
    const { planGroupId, name } = ctx.input;
    const change: { name?: string } = {};
    const groupName = name.trim();
    if (groupName) change.name = groupName;
    if (!Object.keys(change).length) {
      return ctx.body = baseError.paramsError('缺少需要更新的字段');
    }
    const groupExists = await service.plan.group.exists({ where: { id: planGroupId } });
    if (!groupExists) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    await service.plan.group.update(change, { where: { id: planGroupId } });

    ctx.body = { status: 0 };
  }

  @validate({ planGroupId: 'number' })
  public async getOneById() {
    const { ctx, service } = this;
    const { planGroupId } = ctx.input;
    const [group, [counts]] = await Promise.all([
      service.plan.group.getOne({ where: { id: planGroupId } }),
      service.plan.task.getCounts({ planGroupId })
    ]);
    if (!group) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    Object.assign(group, counts || {
      taskCount: 0,
      finishedCount: 0,
      ingCount: 0,
      assignedCount: 0,
      assignedGroupCount: 0,
    });
    if (group.assignType === PlanTaskAssignType.FORCE) {
      const groupUser = await service.plan.user.getOne({
        where: { planGroupId, roleType: PlanUserRoleType.MANAGER },
        attributes: ['userId'],
      });
      if (groupUser) {
        (group as any).managerUserId = groupUser.userId;
        const [user] = await service.user.search([groupUser.userId]);
        if (user) {
          (group as any).managerUsername = user.nickname;
        }
      }
    }

    ctx.body = {
      status: 0,
      data: group,
    };
  }

  @validate({
    planGroupId: 'number',
    userIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async addUsers() {
    const { ctx, service } = this;
    const { planGroupId, userIds } = ctx.input;
    const group = await service.plan.group.getOne({
      where: { id: planGroupId },
      attributes: ['planFlowId'],
    });
    if (!group) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    const users: { userId: number, roleType: PlanUserRoleType }[] = userIds.map((userId: number) => {
      return { userId, roleType: PlanUserRoleType.NORMAL };
    });
    await service.plan.group.addUsers(group.planFlowId, planGroupId, users);
    ctx.body = { status: 0 };
  }

  @validate({
    planGroupId: 'number',
    userIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async removeUsers() {
    const { ctx, service, app } = this;
    const { planGroupId, userIds } = ctx.input;
    const group = await service.plan.group.getOne({
      where: { id: planGroupId },
      attributes: ['planFlowId'],
    });
    if (!group) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    await app.model.transaction(async(transaction) => {
      await service.plan.group.removeUsers(group.planFlowId, planGroupId, userIds, { transaction });
    });
    ctx.body = { status: 0 };
  }

  @validate({ planGroupId: 'number', userId: 'number' })
  public async changeManager() {
    const { ctx, service, app } = this;
    const { planGroupId, userId } = ctx.input;
    const group = await service.plan.group.getOne({
      where: { id: planGroupId },
      attributes: ['planFlowId', 'assignType'],
    });
    if (!group) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    if (group.assignType === PlanTaskAssignType.FREE) {
      return ctx.body = baseError.dataAlreadyExistError('自由领用任务组不能有组长');
    }
    await app.model.transaction(async(transaction) => {
      await service.plan.group.changeManager(group.planFlowId, planGroupId, userId, { transaction });
    });
    ctx.body = { status: 0 };
  }

  @validate({ planGroupId: 'number' })
  public async getUsers() {
    const { ctx, service } = this;
    const { planGroupId } = ctx.input;
    const groupExists = service.plan.group.exists({ where: { id: planGroupId } });
    if (!groupExists) {
      return ctx.body = baseError.dataNotExistError('任务组不存在或已经删除');
    }
    let users: any[] = await service.plan.user.getAll({
      where: { planGroupId },
      attributes: ['userId', 'roleType'],
    });
    if (!users) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    const userIds = users.map((user) => user.userId);
    const userInfos = await service.user.search(userIds);
    const userInfoMap = _.keyBy(userInfos, (userInfo) => userInfo.userId);
    users.forEach((user) => {
      Object.assign(user, userInfoMap[user.userId]);
    });

    users = _.orderBy(users, ['roleType', 'nickname', 'userId']);

    ctx.body = {
      status: 0,
      data: users,
    };
  }

  @validate({ planGroupId: 'number' })
  public async getUserStat() {
    const { ctx, service, app } = this;
    const { mongo } = app;

    const { planGroupId } = ctx.input as { planGroupId: number };

    const group = await service.plan.group.getOne({ where: { id: planGroupId }, attributes: ['planFlowId'] });
    if (!group) {
      return ctx.body = baseError.dataNotExistError('任务组不存在');
    }
    const [flow, planTasks] = await Promise.all([
      service.plan.flow.getOne({ where: { id: group.planFlowId }, attributes: ['taskType'] }),
      service.plan.task.getAll({ where: { planGroupId, userId: { $ne: 0 } }, attributes: ['taskId', 'userId'] })
    ]);
    if (!flow) {
      return ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    if (!planTasks.length) {
      return ctx.body = { status: 0, data: [] };
    }
    const userPlanTaskMap = _.groupBy(planTasks, (t) => t.userId);
    const userIds = Object.keys(userPlanTaskMap).map((userId) => Number(userId));
    const taskIds = planTasks.map((t) => t.taskId);
    const [stats, tasks] = await Promise.all([
      mongo.db.collection('stat').find(
        { task_id: { $in: taskIds }, type: flow.taskType === PlanTaskType.MARK ? 'machine' : 'mark' },
        {
          'html.p.en_char': 1,
          'html.p.cn_char': 1,
          'html.p.punct_char': 1,
          'html.latex.count': 1,
          'html.img.count': 1,
          'html.table.count': 1,
          'html.td.count': 1,
          'html.question.count': 1,
          task_id: 1,
          _id: 0,
        }
      ).toArray(),
      service.task.base.getAll({ where: { taskId: { $in: taskIds } }, attributes: ['imageCount', 'taskId'] })
    ]);
    const statMap = _.keyBy(stats, (s) => s.task_id);
    const taskMap = _.keyBy(tasks, (t) => t.taskId);
    const data = userIds.map((userId) => {
      const userPlanTaskIds = userPlanTaskMap[userId] || [];
      const tasks = userPlanTaskIds.map((t) => taskMap[t.taskId]).filter((i) => i);
      const stats = tasks.map((t) => statMap[t.taskId]).filter((i) => i);
      const enChar = _.sumBy(stats, (stat) => _.get(stat, 'html.p.en_char')) || 0;
      const cnChar = _.sumBy(stats, (stat) => _.get(stat, 'html.p.cn_char')) || 0;
      const punctChar = _.sumBy(stats, (stat) => _.get(stat, 'html.p.punct_char')) || 0;
      const latexCount = _.sumBy(stats, (stat) => _.get(stat, 'html.latex.count')) || 0;
      const imgCount = _.sumBy(stats, (stat) => _.get(stat, 'html.img.count')) || 0;
      const tableCount = _.sumBy(stats, (stat) => _.get(stat, 'html.table.count')) || 0;
      const tdCount = _.sumBy(stats, (stat) => _.get(stat, 'html.td.count')) || 0;
      const questionCount = _.sumBy(stats, (stat) => _.get(stat, 'json.question.count')) || 0;
      const taskCount = tasks.length;
      const imageCount = _.sumBy(tasks, (t) => t.imageCount) || 0;
      return {
        userId,
        enChar,
        cnChar,
        punctChar,
        latexCount,
        imgCount,
        tableCount,
        tdCount,
        questionCount,
        taskCount,
        imageCount,
      };
    });
    ctx.body = {
      status: 0,
      data,
    };
  }

}
