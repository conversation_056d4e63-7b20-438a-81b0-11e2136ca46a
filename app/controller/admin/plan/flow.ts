/**
 * @file 生产计划流程控制
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../../core/decorators/validate';
import baseError from '../../../core/base/baseError';
import * as _ from 'lodash';

export default class PlanFlowController extends Controller {

  @validate({ planFlowId: 'number' })
  public async getGroupList() {
    const { ctx, service } = this;
    const { planFlowId } = ctx.input;
    const flowExists = await service.plan.flow.exists({ where: { id: planFlowId } });
    if (!flowExists) {
      ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    const planGroups = await service.plan.group.getAll({ where: { planFlowId } });
    if (!planGroups.length) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    const planGroupIds = planGroups.map((group) => group.id);
    const taskCounts = await service.plan.task.getCounts({ planGroupId: planGroupIds }, ['planGroupId']);
    const taskCountsMap = _.keyBy(taskCounts, (s) => s.planGroupId);
    planGroups.forEach((planGroup) => {
      Object.assign(planGroup, taskCountsMap[planGroup.id]);
      ['taskCount', 'ingCount', 'finishedCount', 'assignedCount', 'assignedGroupCount'].forEach((key) => {
        planGroup[key] = planGroup[key] || 0;
      });
    });

    ctx.body = {
      status: 0,
      data: planGroups,
    };
  }

  @validate({ planFlowId: 'number' })
  public async getApplList() {
    const { ctx, service } = this;
    const { planFlowId } = ctx.input;
    const flowExists = await service.plan.flow.exists({ where: { id: planFlowId } });
    if (!flowExists) {
      ctx.body = baseError.dataNotExistError('计划流程不存在');
    }

    const data = await service.plan.flow.getApplList(planFlowId);

    ctx.body = {
      status: 0,
      data,
    };
  }

}
