/**
 * @file 生产计划控制
 */

'use strict';

import { Controller } from 'egg';

import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';
import * as _ from 'lodash';
import { PlanTaskType } from '../../../model/planFlow';

export default class PlanController extends Controller {

  @validate({
    name: 'string',
    deadlineTime: 'string',
  })
  public async create() {
    const { ctx, service, app } = this;
    const { name, deadlineTime } = ctx.input;
    const plan = await app.model.transaction(async(transaction) => {
      const plan = await service.plan.base.createPlan({ name, deadlineTime }, { transaction });
      return plan;
    });

    ctx.body = {
      status: 0,
      data: plan.id,
    };
  }

  @validate({
    planId: 'number',
    name: 'string?',
    deadlineTime: 'string?',
  })
  public async update() {
    const { ctx, service } = this;
    const { planId, name, deadlineTime } = ctx.input;
    const change: { name?: string, deadlineTime?: string } = {};
    const planName = name.trim();
    if (planName) change.name = planName;
    if (deadlineTime) change.deadlineTime = deadlineTime;
    if (!Object.keys(change).length) {
      return ctx.body = baseError.paramsError('缺少需要更新的字段');
    }

    const planExists = await service.plan.base.exists({ where: { id: planId } });
    if (!planExists) {
      return ctx.body = baseError.dataNotExistError('生产计划不存在或已经删除');
    }
    await service.plan.base.update(change, { where: { id: planId } });

    ctx.body = {
      status: 0,
      data: planId,
    };
  }

  @validate({ planId: 'number' })
  public async getOneById() {
    const { ctx, service } = this;
    const { planId } = ctx.input;
    const [plan, flows] = await Promise.all([
      service.plan.base.getOne({ where: { id: planId } }),
      service.plan.flow.getAll({ where: { planId } })
    ]);
    if (!plan) {
      return ctx.body = baseError.dataNotExistError('生产计划不存在或已经删除');
    }
    if (flows.length !== 2) {
      return ctx.body = baseError.dataAlreadyExistError('生产计划下必须包含标注和审核两个流程');
    }
    plan.flows = _.keyBy(flows, (f) => this.service.plan.flow.getTaskTypeName(f.taskType)) as any;

    ctx.body = {
      status: 0,
      data: plan,
    };
  }

  @validate({
    page: 'number',
    pageSize: 'number',
    // search support number or string
    isFinished: 'boolean?',
    isAssigned: 'boolean?',
    isAssignedGroup: 'boolean?',
  })
  public async getListPage() {
    const { ctx, service } = this;
    const [plans, count] = await service.plan.base.getListByOption(ctx.input);

    const flows = await service.plan.flow.getAll({ where: { planId: plans.map((plan) => plan.id) } });
    const planMap = _.keyBy(plans, (plan) => plan.id);
    flows.forEach((flow) => {
      const plan: any = planMap[flow.planId];
      if (!plan) return;
      plan.flows = plan.flows || {};
      plan.flows[service.plan.flow.getTaskTypeName(flow.taskType)] = flow;
    });

    ctx.body = {
      status: 0,
      data: {
        count,
        list: plans,
      },
    };
  }

  @validate({ planId: 'number' })
  public async getApplList() {
    const { ctx, service } = this;
    const { planId } = ctx.input;
    let flows = await service.plan.flow.getAll({ where: { planId } });
    if (flows.length !== 2) {
      ctx.body = baseError.dataNotExistError('计划必须包含两个流程');
    }
    flows = _.orderBy(flows, (f) => f.taskType);

    let [markData, reviewData] = await Promise.all(flows.map(async(flow) => {
      return await service.plan.flow.getApplList(flow.id);
    }));

    markData = _.orderBy(markData, (d) => d.appKey);
    reviewData = _.orderBy(reviewData, (d) => d.appKey);

    const data = markData;
    data.forEach((d, index) => {
      const countProps = ['taskCount', 'ingCount', 'finishedCount', 'assignedCount', 'assignedGroupCount'];
      const mark = _.pick(markData[index], countProps);
      const review = _.pick(reviewData[index], countProps);
      Object.assign(d, { mark, review });
    });

    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({
    page: 'number',
    pageSize: 'number',
    planId: 'number',
    /*
     * search?: string | number,
     * status?: number[],
     */
    appKeys: 'string?', // string[]
  })
  public async getTaskListPage() {
    const { ctx, service } = this;
    const toNumberArray = (d) => (typeof d === 'number' ? [d] : (d ? d.split(',').map((id) => Number(id)) : null));
    const toStringArray = (d) => (d ? d.split(',') : null);
    if (ctx.input.appKeys != null) ctx.input.appKeys = toStringArray(ctx.input.appKeys);
    if (ctx.input.status != null) ctx.input.status = toNumberArray(ctx.input.status);
    const { page, pageSize, planId, search, appKeys, status } = ctx.input;

    const flow = await service.plan.flow.getOne({ where: { planId } });

    const taskIds = await service.plan.task.getTaskIdsByOption({
      planFlowId: flow!.id,
      search,
      appKeys,
      taskStatus: status,
      orderBy: ['priority desc'],
    });
    const [tasks, count] = taskIds.length ? await Promise.all([
      await service.task.base.getRelatedList({
        page,
        pageSize,
        where: { taskId: taskIds },
        order: [['id', 'desc']],
      }),
      await service.task.base.relatedCount({ where: { taskId: taskIds } })
    ]) : [[], 0];
    const planTasks = tasks.length ? await service.plan.task.getAll({
      where: { taskId: tasks.map((t) => t.taskId) },
      attributes: ['taskId', 'planGroupId', 'taskType', 'userId', 'status', 'priority'],
    }) : [];
    const planGroupIds = planTasks.map((t) => t.planGroupId).filter((i) => Boolean(i));
    const planGroups = planGroupIds.length ? await service.plan.group.getAll({
      attributes: ['id', 'name'],
      where: { id: planGroupIds },
    }) : [];
    const planGroupMap = _.keyBy(planGroups, (g) => g.id);
    const taskMap = _.keyBy(tasks, (t) => t.taskId);
    planTasks.forEach((t: any) => {
      const task: any = taskMap[t.taskId];
      if (!task) return;
      t.startTime = t.taskType === PlanTaskType.MARK ? task.startMarkTime : task.startReviewTime;
      t.endTime = t.taskType === PlanTaskType.MARK ? task.endMarkTime : task.endReviewTime;
      t.username = t.taskType === PlanTaskType.MARK ? task.markUsername : task.reviewUsername;
      t.planGroupName = planGroupMap[t.planGroupId] && planGroupMap[t.planGroupId].name;
      const taskTypeName = service.plan.flow.getTaskTypeName(t.taskType);
      task[taskTypeName] = t;
      delete t.taskId;
      delete t.taskType;
    });

    ctx.body = {
      status: 0,
      data: {
        count,
        list: _.orderBy(tasks, [(task: any) => task.mark & task.mark.priority], ['desc']),
      },
    };
  }

}
