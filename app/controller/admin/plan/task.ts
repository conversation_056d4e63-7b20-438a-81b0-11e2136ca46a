/**
 * @file 生产计划任务控制
 */

'use strict';

import { Controller } from 'egg';
import * as _ from 'lodash';
import validate from '../../../core/decorators/validate';
import baseError from '../../../core/base/baseError';
import { PlanTaskStatus } from '../../../model/planTask';
import { PlanTaskType } from '../../../model/planFlow';

export default class PlanTaskController extends Controller {
  @validate({
    page: 'number',
    pageSize: 'number',
    planFlowId: 'number?',
    isFinished: 'boolean?',
    isAssigned: 'boolean?',
    isAssignedGroup: 'boolean?',
    /*
     * search?: string | number,
     * planGroupIds?: number[],
     * statuses?: number[],
     * userIds?: number[],
     */
    appKeys: 'string?', // string[]
    // taskStatus?: number[],
    orderBy: 'string?', // string[]
    assignType: 'number?',
    markerName: 'string?',
  })
  public async getListPage() {
    const { ctx, service } = this;

    const toNumberArray = (d) => (typeof d === 'number' ? [d] : (d ? d.split(',').map((id) => Number(id)) : null));
    const toStringArray = (d) => (d ? d.split(',') : null);

    const option = ctx.input;
    if (option.planGroupIds != null) option.planGroupIds = toNumberArray(option.planGroupIds);
    if (option.statuses != null) option.statuses = toNumberArray(option.statuses);
    if (option.userIds != null) option.userIds = toNumberArray(option.userIds);
    if (option.appKeys != null) option.appKeys = toStringArray(option.appKeys);
    if (option.orderBy != null) {
      option.orderBy = toStringArray(option.orderBy);
    } else {
      option.orderBy = ['userId desc', 'priority desc'];
    }
    if (option.taskStatus != null) option.taskStatus = toNumberArray(option.taskStatus);

    // 处理标注人名字的模糊查询
    if (option.markerName) {
      const users = await service.user.searchUserByNickName({ nickname: [option.markerName] });
      if (users.length) {
        const markUserIds = users.map(user => user.userId);
        // 标记任务ID列表，用于在task.base.getAll中过滤
        option.markUserIds = markUserIds;
        
        // 由于service.plan.task.getListByOption方法中需要联表查询
        // 需要获取符合条件的taskId列表先
        const taskWithMarkers = await service.task.base.getAll({
          where: { markUserId: markUserIds },
          attributes: ['taskId']
        });
        
        // 如果找到匹配的任务，则添加taskId过滤
        if (taskWithMarkers.length) {
          option.taskIds = taskWithMarkers.map(task => task.taskId);
        } else {
          // 如果没有匹配的标注人，直接返回空结果
          ctx.body = {
            status: 0,
            data: { count: 0, list: [] },
          };
          return;
        }
      }
    }

    const [planTasks, count] = await service.plan.task.getListByOption(option);
    const taskIds = planTasks.map((task) => task.taskId);
    const tasks = taskIds.length ? await service.task.base.getAll({
      where: {
        taskId: taskIds,
      },
      attributes: [
        'taskId',
        'status',
        'createTime',
        'startMarkTime',
        'endMarkTime',
        'markUserId',
        'startReviewTime',
        'endReviewTime',
        'reviewUserId',
        'priority',
        'resourceType',
        'preprocessUserId', // 划块人
        'rerun' // 任务是否重跑
      ],
    }) : [];
    const userIds = [...new Set(planTasks.map((task) => task.userId)
      .concat(tasks.map((t) => t.markUserId))
      .concat(tasks.map((t) => t.reviewUserId))
      .concat(tasks.map((t) => t.preprocessUserId))
      .filter((i) => i))];
    const users = userIds.length ? await service.user.search(userIds) : [];
    const userMap = _.keyBy(users, (user) => user.userId);
    const taskMap = _.keyBy(tasks, (task) => task.taskId);
    planTasks.forEach((planTask) => {
      const user = userMap[planTask.userId] || {};
      const task = taskMap[planTask.taskId] || {};
      planTask.resourceType = task.resourceType;
      const mark = {
        startTime: task.startMarkTime,
        endTime: task.endMarkTime,
        userId: task.markUserId,
        username: userMap[task.markUserId] && userMap[task.markUserId].nickname,
      };
      const review = {
        startTime: task.startReviewTime,
        endTime: task.endReviewTime,
        userId: task.reviewUserId,
        username: userMap[task.reviewUserId] && userMap[task.reviewUserId].nickname,
      };
      const preprocess = {
        userId: task.preprocessUserId,
        username: userMap[task.preprocessUserId] && userMap[task.preprocessUserId].nickname,
      };
      Object.assign(planTask, {
        username: user.nickname,
        taskCreateTime: task.createTime,
        ..._.pick(planTask.taskType === PlanTaskType.MARK ? mark : review, 'startTime', 'endTime'),
        taskStatus: task.status,
        rerun: task.rerun,
        mark,
        review,
        preprocess,
      });
    });
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: planTasks.map((t) => t.taskId) } });
    planTasks.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      task.meta = meta || {};
    });
    ctx.body = {
      status: 0,
      data: { count, list: planTasks },
    };
  }

  @validate({
    planFlowId: 'number?',
    isFinished: 'boolean?',
    isAssigned: 'boolean?',
    isAssignedGroup: 'boolean?',
    /*
     * planGroupIds?: number[],
     * statuses?: number[],
     * userIds?: number[],
     * taskIds?: number[],
     */
    appKeys: 'string?', // string[]
    type: {
      type: 'enum',
      values: ['machine', 'mark', 'review'],
    },
  })
  public async getStat() {
    const { ctx, service } = this;

    const toNumberArray = (d) => (typeof d === 'number' ? [d] : (d ? d.split(',').map((id) => Number(id)) : null));
    const toStringArray = (d) => (d ? d.split(',') : null);

    const option = ctx.input;
    if (option.planGroupIds != null) option.planGroupIds = toNumberArray(option.planGroupIds);
    if (option.statuses != null) option.statuses = toNumberArray(option.statuses);
    if (option.userIds != null) option.userIds = toNumberArray(option.userIds);
    if (option.taskIds != null) option.userIds = toNumberArray(option.taskIds);
    if (option.appKeys != null) option.appKeys = toStringArray(option.appKeys);

    const taskIds = await service.plan.task.getTaskIdsByOption(option);
    const data = await service.task.stat.aggStat(taskIds, option.type);

    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({
    planFlowId: 'number',
    taskIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async stop() {
    const { ctx, service, app } = this;
    const { planFlowId, taskIds } = ctx.input;
    const existInvalid = await service.plan.task.exists({
      where: {
        planFlowId,
        taskId: taskIds,
        status: { $ne: PlanTaskStatus.ING },
      },
    });
    if (existInvalid) {
      return ctx.body = baseError.dataAlreadyExistError('只能停止处理中的任务');
    }
    const flow = await service.plan.flow.getOne({
      where: { id: planFlowId },
      attributes: ['taskType'],
    });
    if (!flow) {
      return ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    await app.model.transaction(async(transaction) => {
      await service.plan.task.update(
        { status: PlanTaskStatus.INIT },
        {
          transaction,
          where: {
            planFlowId,
            taskId: taskIds,
          },
        }
      );
      await service.task.base.update(
        { status: service.plan.task.flowStatusToTaskStatus(PlanTaskStatus.INIT, flow.taskType) },
        {
          transaction,
          where: { taskId: taskIds },
        }
      );
    });
    ctx.runInBackground(async() => {
      await service.plan.base.pushByFlowId(planFlowId);
      await service.plan.group.pushByTaskId({ planFlowId }, ...taskIds);
    });
    ctx.body = { status: 0 };
  }

  @validate({
    planId: 'number',
    taskIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
    priority: 'number?',
  })
  public async addToPlan() {
    const { ctx, service, app } = this;
    const { planId, taskIds, priority } = ctx.input;
    const planExists = await service.plan.base.exists({ where: { id: planId } });
    if (!planExists) {
      return ctx.body = baseError.dataNotExistError('计划不存在');
    }

    await app.model.transaction(async(transaction) => {
      await service.plan.task.addToPlan({ planId, taskIds, priority }, { transaction });
    });

    ctx.body = { status: 0 };
  }

  @validate({
    planId: 'number',
    taskIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async moveOut() {
    const { ctx, service, app } = this;
    const option = ctx.input;
    const planExists = await service.plan.flow.exists({ where: { id: option.planId } });
    if (!planExists) {
      return ctx.body = baseError.dataNotExistError('计划不存在');
    }

    await app.model.transaction(async(transaction) => {
      await service.plan.task.moveOut(option, { transaction });
    });

    ctx.body = { status: 0 };
  }

  @validate({
    planFlowId: 'number',
    taskIds: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
    planGroupId: 'number?',
    userId: 'number?',
    priority: 'number?',
  })
  public async move() {
    const { ctx, service } = this;
    const option = ctx.input;
    const planFlowExists = await service.plan.flow.exists({ where: { id: option.planFlowId } });
    if (!planFlowExists) {
      return ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    await service.plan.task.move(option);
    ctx.body = { status: 0 };
  }

  @validate({
    planFlowId: 'number?',
    /*
     * planGroupIds?: number[],
     * userIds?: number[],
     */
    group: 'string?',
  })
  public async getCounts() {
    const { ctx, service } = this;
    if (typeof ctx.input.planGroupIds === 'string') {
      ctx.input.planGroupIds = ctx.input.planGroupIds.split(',').map((id) => Number(id));
    }
    if (typeof ctx.input.userIds === 'string') {
      ctx.input.userIds = ctx.input.userIds.split(',').map((id) => Number(id));
    }
    if (ctx.input.group) {
      ctx.input.group = ctx.input.group.split(',');
    }
    const { planFlowId, planGroupIds, userIds, group } = ctx.input;
    if (!planFlowId && !planGroupIds) {
      return ctx.body = baseError.paramsError('缺少参数：planFlowId planGroupIds 至少有一个');
    }
    const where: any = {};
    if (planFlowId) {
      where.planFlowId = planFlowId;
    }
    if (planGroupIds) {
      where.planGroupId = planGroupIds;
    }
    if (userIds) {
      where.userId = userIds;
    }
    const data = await service.plan.task.getCounts(where, group);
    ctx.body = {
      status: 0,
      data,
    };
  }

}
