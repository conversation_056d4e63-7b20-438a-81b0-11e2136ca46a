/**
 * @file 生产计划成员控制
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../../core/decorators/validate';
import * as _ from 'lodash';
import { PlanUserRoleType } from '../../../model/planUser';
import baseError from '../../../core/base/baseError';
import { PlanTaskType } from '../../../model/planFlow';
import { PlanTaskAssignType } from '../../../model/planGroup';

export default class PlanUserController extends Controller {
  @validate({
    userId: 'number?',
    isAllFinished: 'number?',
    limitTime: 'number?', // 限制时间，单位：day
  })
  public async getGroupList() {
    const { ctx, service, app } = this;
    let { userId, isAllFinished, limitTime } = ctx.input;
    userId = userId || ctx.data.userId;

    // 根据 limitTime , 默认不再限制 deadline
    let sql = `
        SELECT plan_group.*,
               plan_flow.id                 as flow_id,
               plan_flow.taskType           as flow_taskType,
               plan_flow.taskCount          as flow_taskCount,
               plan_flow.finishedCount      as flow_finishedCount,
               plan_flow.ingCount           as flow_ingCount,
               plan_flow.assignedCount      as flow_assignedCount,
               plan_flow.assignedGroupCount as flow_assignedGroupCount,

               plan.id                      as plan_id,
               plan.name                    as plan_name,
               plan.deadlineTime            as plan_deadlineTime,
               plan.taskCount               as plan_taskCount,
               plan.finishedCount           as plan_finishedCount,
               plan.ingCount                as plan_ingCount,
               plan.assignedCount           as plan_assignedCount,
               plan.assignedGroupCount      as plan_assignedGroupCount,
               MAX(plan_task.updateTime)    as last_updateTime

        FROM plan
                 JOIN plan_flow ON plan.id = plan_flow.planId
                 JOIN plan_group ON plan_flow.id = plan_group.planFlowId
                 JOIN plan_user ON plan_group.id = plan_user.planGroupId
                 LEFT JOIN plan_task ON plan_group.id = plan_task.planGroupId

        WHERE plan_user.userId = :userId
          AND plan_task.updateTime >= NOW() - INTERVAL ${limitTime || 0} DAY
          AND plan.isDel = 0
          AND plan_flow.isDel = 0
          AND plan_group.isDel = 0
          AND plan_user.isDel = 0

        GROUP BY plan_group.id;
    `;
    if (isAllFinished) {
      sql = `
      select plan_group.*,
        plan_flow.id as flow_id,
        plan_flow.taskType as flow_taskType,
        plan_flow.taskCount as flow_taskCount,
        plan_flow.finishedCount as flow_finishedCount,
        plan_flow.ingCount as flow_ingCount,
        plan_flow.assignedCount as flow_assignedCount,
        plan_flow.assignedGroupCount as flow_assignedGroupCount,

        plan.id as plan_id,
        plan.name as plan_name,
        plan.deadlineTime as plan_deadlineTime,
        plan.taskCount as plan_taskCount,
        plan.finishedCount as plan_finishedCount,
        plan.ingCount as plan_ingCount,
        plan.assignedCount as plan_assignedCount,
        plan.assignedGroupCount as plan_assignedGroupCount

      from plan,
        plan_flow,
        plan_group,
        plan_user

      where plan.id=plan_flow.planId
        and plan_flow.id=plan_group.planFlowId
        and plan_group.id=plan_user.planGroupId
        and plan_user.userId=:userId

--         and (
--           plan_group.taskCount=0 or
--           plan_group.finishedCount=plan_group.taskCount or
--           plan.deadlineTime>=:date
--         )

        and plan.isDel=0
        and plan_flow.isDel=0
        and plan_group.isDel=0
        and plan_user.isDel=0
    `;
    }
    const date = new Date();
    date.setHours(0, 0, 0, 0);
    date.setDate(date.getDate() - 3);

    const groups = await app.model.query(
      sql,
      { raw: true, type: app.model.QueryTypes.SELECT, replacements: { userId, date } }
    );

    if (!groups.length) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }

    groups.forEach((g) => {
      const plan = {};
      const flow = {};
      for (const key in g) {
        if (!g.hasOwnProperty(key)) continue;
        if (key.startsWith('plan_')) {
          plan[key.substring('plan_'.length)] = g[key];
          delete g[key];
        } else if (key.startsWith('flow_')) {
          flow[key.substring('flow_'.length)] = g[key];
          delete g[key];
        }
      }
      g.plan = plan;
      g.flow = flow;
    });

    const groupIds = groups.map((g) => g.id);
    const [userTaskCounts, managers] = await Promise.all([
      service.plan.task.getCounts(
        { planGroupId: groupIds, userId },
        ['planGroupId']
      ),
      service.plan.user.getAll({
        where: {
          planGroupId: groups.map((group) => group.id),
          roleType: PlanUserRoleType.MANAGER,
        },
        attributes: ['planGroupId', 'userId'],
      })
    ]);

    const managerMap = _.keyBy(managers, (m) => m.planGroupId);
    const userTaskCountsMap = _.keyBy(userTaskCounts, (c) => c.planGroupId);

    groups.forEach((group: any) => {
      group.managerUserId = group.assignType === PlanTaskAssignType.FORCE ?
        managerMap[group.id] && managerMap[group.id].userId :
        undefined;
      group.userTaskCounts = userTaskCountsMap[group.id] || { taskCount: 0, finishedCount: 0, ingCount: 0 };
    });

    ctx.body = {
      status: 0,
      data: _.orderBy(
        groups,
        [(g) => g.createTime, (g) => g.plan.deadlineTime],
        ['desc', 'asc']
      ),
    };
  }

  @validate({
    planGroupId: 'number',
    userId: 'number?',
    taskId: 'number?',
  })
  public async applyTask() {
    const { ctx, service } = this;
    let { planGroupId, userId, taskId: id } = ctx.input;
    userId = userId || ctx.data.userId;
    const planGroup = await service.plan.group.getOne({
      where: { id: planGroupId },
      attributes: ['planFlowId'],
    });
    if (!planGroup) {
      return ctx.body = baseError.dataNotExistError('任务组不存在');
    }
    const planFlow = await service.plan.flow.getOne({
      where: { id: planGroup.planFlowId },
      attributes: ['taskType'],
    });
    if (!planFlow) {
      return ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    let taskId = id;
    if (!taskId) {
      taskId = await service.plan.user.applyTask({ planGroupId, userId });
    } else {
      await service.plan.user.applyTaskById({ planGroupId, userId, taskId });
    }
    await service.plan.task.onStartTask(
      { taskType: planFlow.taskType, userId, taskId }
    );
    // 记录操作日志
    ctx.runInBackground(async() => {
      const type = planFlow.taskType === PlanTaskType.REVIEW ?
        service.task.history.reviewTypes.apply.id :
        service.task.history.markTypes.apply.id;
      await service.task.history.create({
        taskId,
        userId,
        type,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = {
      status: 0,
      data: taskId,
    };
  }

  @validate({
    planGroupId: 'number',
    taskId: 'number?',
    userId: 'number?',
  })
  public async startTask() {
    const { ctx, service, app } = this;
    let { planGroupId, userId } = ctx.input;
    userId = userId || ctx.data.userId;
    const targetTaskId = ctx.input.taskId;
    const planGroup = await service.plan.group.getOne({
      where: { id: planGroupId },
      attributes: ['planFlowId'],
    });
    if (!planGroup) {
      return ctx.body = baseError.dataNotExistError('任务组不存在');
    }
    const planFlow = await service.plan.flow.getOne({
      where: { id: planGroup.planFlowId },
      attributes: ['taskType'],
    });
    if (!planFlow) {
      return ctx.body = baseError.dataNotExistError('计划流程不存在');
    }
    const taskId = await app.model.transaction(async(transaction) => {
      const taskId = await service.plan.user.startTask(
        { planGroupId, userId, taskId: targetTaskId },
        { transaction }
      );
      await service.plan.task.onStartTask(
        { taskType: planFlow.taskType, userId, taskId },
        { transaction }
      );
      return taskId;
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      const type = planFlow.taskType === PlanTaskType.REVIEW ?
        service.task.history.reviewTypes.apply.id :
        service.task.history.markTypes.apply.id;
      await service.task.history.create({
        taskId,
        userId,
        type,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = {
      status: 0,
      data: taskId,
    };
  }

}
