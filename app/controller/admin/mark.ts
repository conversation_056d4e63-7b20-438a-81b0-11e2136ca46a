/**
 * @file 标注控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import * as _ from 'lodash';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import { EImageMarkStatus } from '../../model/image';

export default class TaskMarkController extends Controller {

  @validate({ taskId: 'number' })
  public async revoke() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (![statuses.marking, statuses.unreviewed].includes(task.status)) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unmarked,
            markUserId: 0,
            startMarkTime: null,
            endMarkTime: null,
          },
          { transaction, where: { taskId } }
        ),
        service.image.update(
          { marked: EImageMarkStatus.init, reviewed: false },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.REJECTED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.markTypes.revoke.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async quit() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    // @todo: 这里新流程重跑出来的可能会导致无法放弃，任务状态是标注中的。
    if (task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unmarked,
            markUserId: 0,
            startMarkTime: null,
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onQuitTask({ taskId, taskType: PlanTaskType.MARK }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.markTypes.quit.id,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', errorInfo: 'string' })
  public async returnError() {
    const { ctx, service, app } = this;
    const { taskId, errorInfo } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.marking || task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            errorInfo,
            status: statuses.unreviewed,
            endMarkTime: new Date(),
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.INIT,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.markTypes.returnBack.id,
        data: JSON.stringify({ errorInfo }),
        costTime: 0,
      });
      if (task.ticketId) {
        // 关闭工单
        await service.workSheet.submitError({
          ticketId: task.ticketId,
          errorInfo,
        });
        this.logger.info(`close ticket ${task.ticketId} success`);
      }

    });
    ctx.body = { status: 0 };
  }

  public async getOwnList() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { key } = ctx.input;
    const { statuses } = service.task.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const completed = !['false', '0'].includes(ctx.input.completed) && Boolean(ctx.input.completed);
    const whereOpt = {
      key,
      status: !completed ? statuses.marking : { $gt: statuses.marking },
      markUserId: userId,
    } as any;
    const [count, tasks] = await Promise.all([
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  public async getPendingList() {
    const { ctx, service } = this;
    const { key, appKey, notest } = ctx.input;
    const { hexinApps } = service.appl;
    const { statuses } = service.task.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {
      key,
      status: statuses.unmarked,
      markUserId: 0,
    } as any;
    if (appKey) {
      whereOpt.appKey = appKey;
    } else if (notest) {
      whereOpt.appKey = { $not: hexinApps };
    }
    const [count, tasks] = await Promise.all([
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  public async getPendingCount() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    ctx.body = {
      status: 0,
      data: { count: await service.task.base.count({ where: { status: statuses.unmarked, markUserId: 0 } }) },
    };
  }

  public async apply() {
    const { ctx } = this;
    ctx.body = baseError.dataAlreadyExistError('该功能已关闭');
  }

  @validate({
    taskId: 'number',
    imageId: 'string',
    formulaId: 'string',
    latex: 'string',
    confidence: { required: false, type: 'number' },
  })
  public async saveFormula() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, formulaId, latex, confidence } = ctx.input;
    const { userId } = ctx.data;
    const [task, image, formula] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getOne({ where: { imageId, disabled: false } }),
      service.formula.getOne({ where: { imageId, formulaId } })
    ]);
    if (!task || !image || !formula || imageId !== formula.imageId || image.taskId !== Number(taskId)) {
      return ctx.body = baseError.dataNotExistError('公式任务不存在');
    }
    if (task.status !== statuses.marking) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    if (task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('当前不是相关标注者，无权限提交');
    }
    await service.formula.update({
      latex,
      confidence: confidence ? confidence : undefined,
      marked: true,
    }, { where: { imageId, formulaId } });
    // 同步到html中
    await service.image.syncFormulaHtml(task.appKey, imageId, formulaId, latex);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.markTypes.save.id,
        data: JSON.stringify({
          imageId,
          formulaId,
          latex,
        }),
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    imageId: 'string',
    // html: { type: 'string', allowEmpty: true },
    htmlSize: 'number',
    status: 'number?',
    autoSave: 'boolean?',
  })
  public async saveImage() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, htmlSize, status, autoSave } = ctx.input as {
      taskId: number;
      imageId: string;
      // html: string;
      htmlSize: number;
      status?: EImageMarkStatus;
      autoSave?: boolean;
    };
    const { userId } = ctx.data;
    const [task, image] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getOne({ where: { imageId, disabled: false } })
    ]);
    if (!task || !image || image.taskId !== Number(taskId)) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    if (task.status !== statuses.marking) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    if (task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('当前不是相关标注者，无权限提交');
    }
    // await service.image.setOssData(image.appKey, imageId, 'html', html);
    if (status != null) {
      await service.image.update({ marked: status }, { where: { imageId } });
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.markTypes.save.id,
        costTime: 0,
        data: JSON.stringify({
          imageId,
          autoSave,
          htmlSize,
        }),
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', costTime: 'number?' })
  public async confirm() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { taskId, costTime } = ctx.input;
    const { userId } = ctx.data;
    const [task, images] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, disabled: false },
        attributes: ['marked', 'imageId', 'filename', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task || !images || !images.length) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    if (task.status !== statuses.marking) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    if (task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('当前不是相关标注者，无权限提交');
    }
    const allImageIds: string[] = [];
    for (const { marked, imageId } of service.image.sortImageByFile(images)) {
      if (marked !== EImageMarkStatus.finished) {
        return ctx.body = baseError.dataNotExistError('无法提交的任务状态, 部分图片未标注完成');
      }
      allImageIds.push(imageId);
    }

    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unreviewed,
            endMarkTime: new Date(),
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.INIT,
        }, { transaction })
      ]);
    });
    ctx.runInBackground(async() => {
      const html = await service.task.base.combineAndUploadByImageIds(task.appKey, taskId, allImageIds);

      /*
       * const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
       * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
       */

      // 记录操作日志
      await service.task.history.create({
        taskId,
        userId,
        costTime: costTime || 0,
        type: service.task.history.markTypes.confirm.id,
        data: '',
      });
      await service.task.stat.stashMarkHtml(task.appKey, taskId);
      await service.task.stat.newStat({
        html,
        taskId,
        type: 'mark',
        appKey: task.appKey,
        resourceType: task.resourceType!,
        subject: task.subject,
        imageCount: images.length,
        cost: costTime,
        userId,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    imageIds: {
      type: 'array',
      itemType: 'string',
      min: 1,
    },
  })
  public async rerunImages() {
    const { ctx, service, app } = this;
    const { literal } = app.model;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const { taskId, imageIds } = ctx.input as { taskId: number; imageIds: string[] };
    const [task, images] = await Promise.all([
      service.task.base.getOne({
        where: { taskId },
        attributes: ['taskId', 'status', 'rerun', 'rerunTimes', 'markUserId'],
      }),
      service.image.getAll({
        where: { taskId, imageId: imageIds },
        attributes: ['imageId', 'originalId'],
      })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status !== statuses.marking) {
      return ctx.body = baseError.dataAlreadyExistError('任务状态错误，只有标注中任务才可以重跑图片');
    }
    if (task.rerun) {
      return ctx.body = baseError.dataAlreadyExistError('任务已经在重跑中了');
    }
    if (task.rerunTimes! >= 2) {
      return ctx.body = baseError.dataAlreadyExistError('重跑图片限制最多两次');
    }
    if (userId !== task.markUserId) {
      return ctx.body = baseError.dataAlreadyExistError('必须为标注人');
    }
    if (images.length < imageIds.length) {
      return ctx.body = baseError.dataNotExistError('部分图片不存在');
    }

    const originalImageIds = _.uniq(images.map((item) => item.originalId || item.imageId));

    const imageMap = {};
    originalImageIds.forEach((imageId) => {
      imageMap[imageId] = 0;
    });

    await app.model.transaction(async(transaction) => {
      await Promise.all([
        // 更新任务状态 "等待进入队里"
        service.task.base.update(
          {
            status: statuses.columnQueue,
            rerunProcessUserId: 0,
            rerun: true,
            rerunTimes: literal('rerunTimes + 1'),
          },
          {
            transaction,
            where: { taskId },
          }
        ),
        // 更新图片为待处理
        service.image.update(
          {
            preprocessed: false,
            marked: EImageMarkStatus.init,
            disabled: false,
            rerun: true,
            rerunTimes: literal('rerunTimes + 1'),
          },
          {
            transaction,
            where: { taskId, imageId: originalImageIds },
          }
        ),
        // 更新其他图片为未标注
        service.image.update(
          { marked: EImageMarkStatus.init },
          {
            transaction,
            where: { taskId, imageId: { $notIn: originalImageIds } },
          }
        ),
        // 删除分栏后图片
        service.image.delete(
          {
            transaction,
            where: { taskId, originalId: originalImageIds },
          }
        ),
        // 如果任务在计划中，更新计划流程中任务状态
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction })
      ]);
      await service.task.base.initTaskConfig(taskId, 'imageColumnProcessor', imageMap);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.otherTypes.rerunImage.id,
        costTime: 0,
        data: JSON.stringify(originalImageIds),
      });
    });

    ctx.body = { status: 0 };
  }
}
