'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import { Op } from 'sequelize';
import { ESubjectScriptConfigTaskType } from '../../model/subjectScriptConfig';
import config from './xkw_word_cfg';
import * as urllib from 'urllib';

export default class SbujectScriptController extends Controller {
  async getSubjectScriptList() {
    const { ctx, service } = this;
    const data = await service.subjectScript.getCustomAll({ attributes: ['name', 'scriptName', 'desc'] });
    ctx.body = {
      status: 0,
      data,
    };
  }

  async getSubjectScriptConfigList() {
    const { ctx, service } = this;
    const { taskType } = ctx.input;
    const type = ESubjectScriptConfigTaskType[taskType];
    if (!type) {
      return ctx.body = baseError.paramsError('任务类型有问题');
    }
    const data = await service.subjectScriptConfig.getAll({
      where: { taskType: type },
      attributes: ['subject', 'script', 'createTime', 'updateTime'],
    });
    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({ subject: 'string' })
  async addSubjectScriptConfig() {
    const { ctx, service } = this;
    const { subject, taskType } = ctx.input as { subject: string, taskType: string };
    const type = ESubjectScriptConfigTaskType[taskType];
    if (!type || typeof type !== 'number') {
      return ctx.body = baseError.paramsError('任务类型不对');
    }
    await service.subjectScriptConfig.create({ subject, taskType: type });
    ctx.body = { status: 0 };
  }

  @validate({
    subject: 'string',
    script: 'string?',
  })
  async updateSubjectScriptConfig() {
    const { ctx, service } = this;
    const { subject, script, taskType } = ctx.input as { subject: string, script?: string, taskType: string };
    const type = ESubjectScriptConfigTaskType[taskType];
    if (!type || typeof type !== 'number') {
      return ctx.body = baseError.paramsError('任务类型不对');
    }
    await service.subjectScriptConfig.update({ script }, { where: { subject, taskType: type } });
    ctx.body = { status: 0 };
  }

  @validate({ subject: 'string' })
  async delSubjectScriptConfig() {
    const { ctx, service } = this;
    const { subject, taskType } = ctx.input as { subject: string, taskType: string };
    const type = ESubjectScriptConfigTaskType[taskType];
    if (!type || typeof type !== 'number') {
      return ctx.body = baseError.paramsError('任务类型不对');
    }
    if (subject === 'default') {
      return ctx.body = baseError.serverError('不能删除默认配置');
    }
    await service.subjectScriptConfig.forceDelete({ where: { subject, taskType: type } });
    ctx.body = { status: 0 };
  }

  @validate({
    taskIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  async runSubjectScript() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskIds } = ctx.input as {
      taskIds: number[];
    };
    const taskList = await service.task.base.getAll({ where: { [Op.or]: taskIds.map((taskId) => ({ taskId })) } });
    const isV3 = taskList.some((task) => task.mergedTaskId);
    const filterTaskList = taskList.filter(
      (task) => task.status === statuses.dataCleanfailed ||
      task.status === statuses.reviewed ||
      task.status === statuses.dataCleaning ||
      task.status === statuses.jsonPreProcessing ||
        isV3).map((task) => task.taskId);
    if (!filterTaskList.length) {
      return ctx.body = baseError.dataNotExistError('不存在需要重跑的任务');
    }

    await service.task.base.update(
      { status: statuses.dataCleaning },
      { where: { [Op.or]: filterTaskList.map((taskId) => ({ taskId })) } }
    );
    this.logger.info('数据清洗清洗的任务', JSON.stringify(filterTaskList));
    this.logger.info('数据清洗清洗的任务的长度', filterTaskList.length);
    ctx.runInBackground(async() => {
      await Promise.all(filterTaskList.map(async(taskId) => {
        return await service.subjectScript.runSubjectScript(taskId);
      }));
    });

    ctx.body = { status: 0 };
  }

  @validate({
    status: 'number',
    taskId: 'number',
    isNeedCallback: 'boolean',
  })
  async subjectScriptCb() {
    const { ctx, service, app } = this;
    const { status, taskId, isNeedCallback } = ctx.input as { status: number, taskId: number, isNeedCallback: boolean };
    const { statuses } = service.task.base;

    const runningTime = Number(new Date());
    this.logger.info(`[数据清洗回调]taskId: ${taskId}进入数据清洗回调阶段, status: ${status === 0 ? '成功' : '失败'}`);

    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      this.logger.info(`[数据清洗回调]taskId: ${taskId}没找到任务`);
      return ctx.body = baseError.dataNotExistError('任务信息不存在');
    }

    enum cbStatus { success, failed }
    if (status === cbStatus.success) {
      const html = await service.task.base.getOssData(task.appKey, taskId, 'formatted.html', true, false);
      const json = await service.task.base.convert2Json(task.appKey, taskId, html);
      let errorInfo: string | undefined = undefined;
      if (!html) {
        errorInfo = '[数据清洗异常]没有找到对应html';
      }
      errorInfo = await service.task.base.checkHtml(html);
      this.logger.info(`subjectScriptCb 数据清洗后公式异常 taskId${taskId} ${errorInfo}`);
      if (errorInfo) {
        await service.task.base.update(
          {
            status: statuses.dataCleanfailed,
            errorInfo,
          },
          { where: { taskId } }
        );
        return ctx.body = { status: 0 };
      }

      this.logger.info(`[数据清洗回调]taskId: ${taskId}清洗成功`);
      await service.task.base.setOssData(task.appKey, taskId, 'formatted.internal.json', json);
      await service.task.base.setOssData(task.appKey, taskId, 'formatted.json', cleanJsonNodes(json));

      ctx.runInBackground(async() => {
        try {
          const cleanHtml = await service.task.taskV2.cleanHtml(html);
          await service.task.base.setOssData(task.appKey, taskId, 'clean.formatted.html', cleanHtml);
          /*
           * const docKey = service.task.base.getOssKey(task.appKey, taskId, 'formatted.docx');
           * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
           */
          await service.task.base.formateAndUploadDiffFormattedFile(task.appKey, taskId, html);
          let errorInfo = '';
          errorInfo = await service.task.base.checkOssData({
            appKey: task.appKey,
            taskId,
            srcExtension: 'diff.json.formatted.html',
            dstExtension: 'diff.formatted.json',
          });
          if (!errorInfo) { errorInfo = await service.task.base.checkOssData({
            appKey: task.appKey,
            taskId,
            srcExtension:
            'diff.docx.formatted.html',
            dstExtension: 'formatted.docx',
          });
          }

          this.logger.info(`[数据清洗回调] setOss Success taskId: ${taskId}`);

          /*
           * 数据清洗成功，把task更新到 json 预处理状态。同时开始json预处理
           * @todo：抽离到service
           */
          this.logger.info(`[数据清洗回调] taskId=${taskId} jsonPreprocessUrl : ${app.config.jsonPreprocess.api}/api/json-preprocess/task/preprocessing bucket:${app.config.aliOss.bucket} json_path:${service.task.base.getOssKey(task.appKey, taskId, 'formatted.internal.json')}`);
          await service.task.base.update(
            { status: statuses.jsonPreProcessing },
            { where: { taskId } }
          );

          const { subject, stage } = await service.task.meta.getMetas({ taskId });

          const logData = {
            task_id: taskId,
            bucket_name: app.config.aliOss.bucket,
            json_path: service.task.base.getOssKey(task.appKey, taskId, 'formatted.internal.json'),
            upload_path: service.task.base.getOssKey(task.appKey, taskId, 'preprocessed.internal.json'),
            subject,
            stage,
            is_run_subject_qtype: false,
          };
          this.logger.info(`[数据清洗回调] taskId=${taskId} ${JSON.stringify(logData)}`);

          const res = await ctx.curl(`${app.config.jsonPreprocess.api}/api/json-preprocess/json/pre`, {
            method: 'POST',
            contentType: 'json',
            data: {
              task_id: taskId,
              bucket_name: app.config.aliOss.bucket,
              json_path: service.task.base.getOssKey(task.appKey, taskId, 'formatted.internal.json'),
              upload_path: service.task.base.getOssKey(task.appKey, taskId, 'preprocessed.internal.json'),
              subject,
              stage,
              app_key: task.appKey,
              is_run_subject_qtype: false,
            },
          });
          if (res.status !== 200) {
            throw new Error(`[runSubjectScript] error json-preprocess taskId=${taskId} 请求 jsonPreprocess 状态: ${res?.status}, statusMessage=${res?.res?.statusMessage}`);
          }

        } catch (e) {
          this.logger.info(`[数据清洗回调] setOss Error taskId: ${taskId} error: ${e}`);
          await service.task.base.update(
            {
              status: statuses.dataCleanfailed,
              errorInfo: (e as any).message,
            },
            { where: { taskId } }
          );
        }

        /*
         * if (errorInfo) {
         *   return await service.task.base.update(
         *     {
         *       errorInfo,
         *       status: statuses.contentError,
         *     },
         *     { where: { taskId } },
         *   );
         * }
         */

        if (isNeedCallback) {
          // 延迟两个小时回调
          await service.task.callback.callback(Object.assign(task, { status: statuses.reviewed }), 3, 2 * 60 * 60 * 1000);
        }
      });
    }
    if (status === cbStatus.failed) {
      this.logger.info(`[数据清洗回调]taskId: ${taskId}清洗失败`);
      await service.task.base.update(
        {
          status: statuses.dataCleanfailed,
          errorInfo: '[数据清洗异常]清洗返回失败',
        },
        { where: { taskId } }
      );
    }

    this.app.logRunningTime(runningTime, `/script/preProcessingStatus 数据清洗回调 task:${JSON.stringify(taskId)}`);

    return ctx.body = { status: 0 };
  }

  @validate({
    status: 'number',
    taskId: 'number',
  })
  async jsonPreProcessedCb() {
    /**
     * 1. 获取任务 （json预处理中的任务）
     * 2. oss拿到json
     * 3. 转化 preprocessed.internal.json -> preprocessed.json
     * 4. 更新任务状态到已发布
     */
    const { ctx, service } = this;
    const { status, taskId } = ctx.input as { status: number, taskId: number, isNeedCallback: boolean };
    const { statuses } = service.task.base;

    this.logger.info(`[Json回调] begin taskId: ${taskId} ${status}`);

    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      this.logger.info(`[Json回调]taskId: ${taskId}没找到任务`);
      return ctx.body = baseError.dataNotExistError('任务信息不存在');
    }
    if (task.status !== statuses.jsonPreProcessing) {
      this.logger.info(`[Json回调]taskId: ${taskId} 当前task不处于json预处理状态`);
      return ctx.body = baseError.dataNotExistError('当前task不处于json预处理状态');
    }

    enum cbStatus { success, failed } // 0 成功 1失败
    try {
      if (status === cbStatus.success) {
        const preJson = await service.task.base.getOssData(task.appKey, taskId, 'preprocessed.internal.json', true, false);
        let errorInfo: string | undefined = undefined;
        if (!preJson) {
          errorInfo = `[Json回调] 回调异常 taskId: ${taskId} 没有找到对应Json`;
        }
        if (errorInfo) {
          await service.task.base.update(
            {
              status: statuses.dataCleanfailed,
              errorInfo,
            },
            { where: { taskId } }
          );
          return ctx.body = { status: 0 };
        }

        await service.task.base.setOssData(task.appKey, taskId, 'preprocessed.json', cleanJsonNodes(preJson));

        const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
        const interJson = await service.task.base.convert2Json(task.appKey, task.taskId, html);
        await service.task.base.setOssData(task.appKey, taskId, 'internal.json', interJson);

        this.logger.info(`[Json回调] 回调成功 end taskId: ${taskId}`);
        await service.task.base.update(
          { status: statuses.reviewed },
          { where: { taskId } }
        );
        if (task.ticketId) {
          this.logger.info(`[PublishTask] 任务 ${taskId} 开始发布`);
          const pStat = await this.service.task.stat.getDesignatedHTMLStatData(task.appKey, taskId, 'p');
          if (!pStat || Object.keys(pStat).length === 0) {
            errorInfo = '任务信息统计失败， 请重新发布';
            await service.task.base.update(
              {
                status: statuses.dataCleanfailed,
                errorInfo,
              },
              { where: { taskId } }
            );
            return ctx.body = { status: 0 };
          }
        }

        ctx.runInBackground(async() => {
          // 发布成功才转 word 回调
          if (task.ticketId) {
            // 获取 任务的 JSON
            this.logger.info(`[PublishTask] 任务 ${taskId} 开始发布`);
            // 用 json 去转 WORD
            // 从 oss 拿到 json
            let docxConfig;
            try {
              const { data: _config } = await urllib.request('https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/files/xkw_json_word_config.json', {
                method: 'GET',
                timeout: 10 * 1000,
                followRedirect: true,
              });
              // 获取到的是 buffer
              docxConfig = JSON.parse(_config.toString());
              this.logger.info(`[PublishTask] 任务 ${taskId} 转换 WORD---${JSON.stringify(docxConfig.header)}`);
            } catch (e) {
              this.logger.info(`[PublishTask] 任务 ${taskId} 转换 WORD---配置获取失败，使用本地配置}`);
              docxConfig = config;
            }
            const res = await service.rbs.initJSON2DOCXQueue({
              projectId: '0',
              taskId: task.taskId.toString(),
              originJson: interJson,
              config: docxConfig,
              output: 'url',
              fileName: task.taskName,
              exportType: 1,
              sourceFrom: 'xkw',
            });
            this.logger.info(`[PublishTask] 任务 ${taskId} 转换 WORD---${JSON.stringify(res)}`);
            this.logger.info(`reviewConfirm taskId :${task.taskId} pushToPublish end`);
          }
          // 状态更新到审核完成后，机器人通知
          await service.robot.sendRobotMessageWhenTaskReviewed(taskId, task.bookId);
        });
      }
      if (status === cbStatus.failed) {
        throw new Error('json预处理失败');
      }
    } catch (e) {
      this.logger.info(`[Json回调] 回调异常 taskId: ${taskId} error: ${e}`);
      await service.task.base.update(
        {
          status: statuses.dataCleanfailed,
          errorInfo: `[Json回调] json预处理返回失败${(e as any).message}`,
        },
        { where: { taskId } }
      );
    }
    return ctx.body = { status: 0 };
  }
}
