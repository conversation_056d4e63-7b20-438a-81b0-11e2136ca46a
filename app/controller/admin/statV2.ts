/**
 * @file 工时统计V2
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import * as uuid from 'uuid';
import baseError from '../../core/base/baseError';
import * as dateformat from 'dateformat';
import { Op } from 'sequelize';

function getSum(data) {
  return data.length
    ? data.reduce((pre, cur) => {
      return pre + cur;
    }, 0)
    : 0;
}

export default class StatV2Controller extends Controller {
  @validate({
    taskId: 'number',
    nickName: 'string',
    userId: 'number',
  })
  public async begin() {
    const { ctx, app } = this;
    const { mongo } = app;

    const { taskId, nickName, userId, type } = ctx.input as {
      taskId: number;
      nickName: string;
      type?: string;
      userId: number;
    };
    const startTime = dateformat(new Date(), 'yyyy-mm-dd HH:MM:ss');
    console.log(startTime, taskId, nickName, userId);

    const task = await mongo.db
      .collection('stat_v2')
      .find({ taskId })
      .toArray();
    const whereOpt: any = {};

    // 判断是不是标注人
    whereOpt.taskId = { $in: [taskId] };
    const taskUserInfo = await this.service.task.base.getOne({
      where: whereOpt,
      attributes: ['taskId', 'markUserId'],
    });
    this.logger.info(`begin user info --> ${JSON.stringify(taskUserInfo)}`);

    const workTimeId = userId + '_' + uuid().replace(/-/g, '').substring(0, 8);
    console.log(task);
    if (!task.length) {
      console.log('create');
      await mongo.db.collection('stat_v2').insertOne({
        taskId,
        nickName:
          taskUserInfo && taskUserInfo.markUserId === Number(userId) ? nickName : '',
        userId,
        costTime: { [workTimeId]: { startTime } },
        type: type ? type : 'mark',
      });
    } else {
      console.log('update');
      const { costTime } = task[0];
      if (!costTime) return;
      costTime[workTimeId] = { startTime };

      await mongo.db.collection('stat_v2').updateOne(
        { taskId },
        {
          $set: {
            costTime,
            nickName:
              taskUserInfo && taskUserInfo.markUserId === Number(userId)
                ? nickName
                : '',
          },
        }
      );
    }

    this.ctx.body = {
      workTimeId,
      status: 0,
    };
  }

  @validate({
    taskId: 'number',
    nickName: 'string',
    userId: 'number',
    workTimeId: 'string',
  })
  public async end() {
    const { ctx, app } = this;
    const { mongo } = app;

    const { taskId, nickName, userId, workTimeId } = ctx.input as {
      taskId: number;
      nickName: string;
      userId: number;
      workTimeId: string;
    };
    const endTime = dateformat(new Date(), 'yyyy-mm-dd HH:MM:ss');
    console.log(endTime, taskId, nickName, userId, workTimeId);

    const task = await mongo.db
      .collection('stat_v2')
      .find({ taskId })
      .toArray();

    if (
      !task ||
      !task.length ||
      !task[0].costTime ||
      !task[0].costTime[workTimeId]
    ) {
      return (ctx.body = baseError.dataNotExistError(
        workTimeId + ':数据不存在'
      ));
    }

    const costTime = task[0].costTime;
    costTime[workTimeId].endTime = endTime;
    await mongo.db.collection('stat_v2').updateOne(
      { taskId },
      { $set: { costTime } }
    );
    ctx.body = {
      workTimeId,
      status: 0,
    };
  }

  @validate({ taskIds: 'array' })
  public async getTasksStat() {
    const { ctx } = this;
    const { taskIds } = this.ctx.input as {
      taskIds: number[];
    };
    this.logger.info(`getTasksStat begin taskIds: ${taskIds}`);

    // 获取时间
    const tasksTime = await this.service.statV2.getAllTimeByTaskIds(taskIds);
    const timeSum = getSum(tasksTime);

    // 获取pdf数
    const pdfsCount = await this.service.statV2.getAllPdfsCountByTaskIds(
      taskIds
    );
    const pdfSum = getSum(pdfsCount);

    const whereOpt: any = {};
    whereOpt.taskId = { $in: taskIds };
    const taskInfoList = await this.service.task.base.getAll({
      where: whereOpt,
      attributes: ['taskId', 'markUserId', 'taskName', 'createTime', 'diffCharCount'],
    });

    const markUserIds = taskInfoList.map((item) => item.markUserId);
    const nickNameList = taskInfoList.length
      ? await this.service.user.search(markUserIds)
      : [];
    const nickNameMap: any = {};
    nickNameList.map((item) => {
      nickNameMap[item.userId] = item.nickname;
    });

    const data = taskIds.map((_, index) => {
      return {
        pdfsCount: pdfsCount[index],
        tasksTime: tasksTime[index],
        taskId: taskIds[index],
        diffCharCount: taskInfoList[index].diffCharCount || 0,
        markUserId:
          taskInfoList.length && taskInfoList[index]
            ? taskInfoList[index].markUserId
            : '',
        nickNameInfo:
          nickNameMap[taskInfoList.length && taskInfoList[index]
            ? taskInfoList[index].markUserId
            : ''
] ?? '-',
        createTime:
          taskInfoList.length && taskInfoList[index]
            ? taskInfoList[index].createTime
            : '',
        taskName:
          taskInfoList.length && taskInfoList[index]
            ? taskInfoList[index].taskName
            : '',
      };
    });
    ctx.body = {
      status: 0,
      pdfSum,
      data,
      timeSum,
    };
  }

  @validate({
    projectIds: 'array?',
    startTime: 'string?',
    endTime: 'string?',
    page: 'number?',
    pageSize: 'number?',
  })
  public async getProjectsStat() {
    const { ctx, service } = this;
    const { projectIds, startTime, endTime } = ctx.input as {
      projectIds?: number[];
      startTime?: string;
      endTime?: string;
      page?: number;
      pageSize?: number;
    };
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;

    this.logger.info(`${startTime} ${endTime}`);

    // 获取projectList
    let whereOpt = {} as any;
    if (startTime || endTime) {
      whereOpt.endReviewTime = {};
    }
    if (startTime) {
      whereOpt.endReviewTime.$gte = startTime;
    }
    if (endTime) {
      whereOpt.endReviewTime.$lte = endTime;
    }
    whereOpt.endReviewTime = { ...whereOpt.endReviewTime, [Op.ne]: null };

    let projectList: any[];
    let count: number;
    if (!projectIds || !projectIds.length) {
      [projectList, count] = await Promise.all([
        service.project.base.getRelatedList({
          page,
          pageSize,
          where: whereOpt,
        }),
        service.project.base.count({ where: whereOpt })
      ]);
    } else {
      projectList = projectIds.map((p) => {
        return { id: Number(p) };
      });
      count = projectList.length;
    }

    whereOpt = {};
    const projectListRes: any[] = [];
    await Promise.all(
      projectList.map(async(item) => {
        const project = await service.project.base.getRelateOne({ where: { id: Number(item.id) } });
        const bookId = project?.books?.question?.bookId;
        if (project && bookId) {
          whereOpt.bookId = bookId;
          const tasks = await service.task.base.getRelatedList(
            {
              where: whereOpt,
              order: whereOpt.bookId
                ? [
                  ['bookOrder', 'asc'],
                  ['id', 'desc']
                ]
                : [['id', 'desc']],
            },
            false
          );
          const tasksTime = await this.service.statV2.getAllTimeByTaskIds(
            tasks.map((task) => task.taskId)
          );

          const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
          const path = metaDict[tasks[0].taskId]?.path;

          // 获取split info
          const splitId = tasks[0].parentTaskId ?? 0;
          const splitTime = await service.statV2.getSplitTimeById(splitId);

          projectListRes.push({
            taskIds: tasks.map((task) => task.taskId).length
              ? tasks.map((task) => task.taskId)
              : [],
            pdfCount: project?.pdfCount,
            projectId: item.id,
            tasksTime: getSum(tasksTime),
            projectName: project?.projectName,
            subject: project?.subject,
            createTime: dateformat(project?.createTime, 'yyyy-mm-dd'),
            splitTime,
            path,
          });
        }
      })
    );

    const metaDict = await service.project.meta.getMetasDict({ projectId: { $in: projectListRes.map((p) => p.projectId) } });
    projectListRes.forEach((project: any) => {
      const meta = metaDict[project.projectId];
      project.meta = meta || {};
    });

    ctx.body = {
      status: 0,
      count,
      projectListRes,
    };
  }

  @validate({
    projectId: 'number',
    pdfCount: 'number',
  })
  public async setProjectPdfCount() {
    const { ctx } = this;
    const { projectId, pdfCount } = ctx.input as {
      projectId: number;
      pdfCount: number;
    };
    await this.service.project.base.update(
      { pdfCount },
      { where: { id: projectId } }
    );

    ctx.body = { status: 0 };
  }

  /*
   * @validate({
   *   projectId: 'number',
   *   pdfCount: 'number',
   * })
   * public async getUserStat() {
   *   const { ctx } = this;
   *   const { projectId, pdfCount } = ctx.input as {
   *     projectId: number;
   *     pdfCount: number;
   *   };
   *   await this.service.project.base.update(
   *     { pdfCount },
   *     { where: { id: projectId } }
   *   );
   */

  /*
   *   ctx.body = {
   *     status: 0,
   *   };
   * }
   */
}
