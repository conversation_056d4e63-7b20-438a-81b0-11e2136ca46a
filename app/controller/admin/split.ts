'use strict';

import { Controller } from 'egg';
import { Op } from 'sequelize';
import * as sequelize from 'sequelize';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import { IPageCountInfo, IPdfInfo } from '../../model/taskMeta';
import { preCleanHtml } from '../../core/utils/htmlToJsonV4/helper/cleanupNode';
import { ETaskResourceType } from '../../model/task';
import { ITaskImgs } from '../../model/taskFile';
import { hasErrorColOrTrTable } from '../../core/utils/htmlToJsonV5/htmlHelper';
import axios from 'axios';

export default class TaskSplitController extends Controller {

  public async getOwnListBySplit() {
    const { ctx, service } = this;
    const { key, isOutAdmin, filterPriority7 } = ctx.input;
    const { userId } = ctx.data;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const { statuses } = service.task.base;

    const keyFilter = {} as any;
    if (/^[0-9]+$/.test(key)) {
      keyFilter.taskId = Number(key);
    } else if (key) {
      keyFilter.taskName = { $like: `%${key}%` };
    }

    const whereOptForTasksAndTotalCount = {
      status: statuses.spliting,
      splitUserId: userId,
      ...keyFilter,
    } as any;

    if (filterPriority7) {
      whereOptForTasksAndTotalCount.priority = 7;
    }

    const whereOptForPriorityCount = {
      status: statuses.spliting,
      splitUserId: userId,
      priority: 7,
      ...keyFilter,
    } as any;

    const promises: any[] = [];

    promises.push(
      !isOutAdmin
        ? service.task.base.relatedCount({ where: whereOptForTasksAndTotalCount })
        : service.task.base.exactCount({ where: whereOptForTasksAndTotalCount })
    );

    console.log(whereOptForTasksAndTotalCount);
    promises.push(
      !isOutAdmin
        ? service.task.base.getRelatedList({
          page,
          pageSize,
          where: whereOptForTasksAndTotalCount,
          order: [['id', 'desc']],
        })
        : service.task.base.getExactList({
          page,
          pageSize,
          where: whereOptForTasksAndTotalCount,
          order: [['id', 'desc']],
        })
    );

    promises.push(
      !isOutAdmin
        ? service.task.base.relatedCount({ where: whereOptForPriorityCount })
        : service.task.base.exactCount({ where: whereOptForPriorityCount })
    );

    const [count, tasks, priorityCount] = await Promise.all(promises);

    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      const file = fileDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      if (meta) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json');
      }
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        priorityCount,
        tasks,
      },
    };
  }

  public async getPendingListBySplit() {
    const { ctx, service } = this;
    const { key, notest, isOutAdmin, filterPriority7 } = ctx.input;
    const { hexinApps } = service.appl;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const { statuses } = service.task.base;

    const baseWhereOpt = { status: statuses.split } as any;
    if (/^[0-9]+$/.test(key)) {
      baseWhereOpt.taskId = Number(key);
    } else if (key && !isOutAdmin) {
      baseWhereOpt.taskName = { $like: `%${key}%` };
    }
    if (notest) {
      baseWhereOpt.appKey = { $not: hexinApps };
    }
    if (isOutAdmin) { baseWhereOpt.key = key; }

    const whereOptForTasksAndTotalCount = { ...baseWhereOpt };
    if (filterPriority7) {
      whereOptForTasksAndTotalCount.priority = 7;
    }

    const whereOptForPriorityCount = {
      ...baseWhereOpt,
      priority: 7, // Always count priority 7 tasks for this field
    };

    this.logger.info('getPendingListBySplit whereOptForTasksAndTotalCount', whereOptForTasksAndTotalCount);
    this.logger.info('getPendingListBySplit whereOptForPriorityCount', whereOptForPriorityCount);

    const promises: any[] = [];

    // Promise for total count
    promises.push(
      !isOutAdmin
        ? service.task.base.relatedCount({ where: whereOptForTasksAndTotalCount })
        : service.task.base.exactCount({ where: whereOptForTasksAndTotalCount })
    );

    // Promise for tasks list
    promises.push(
      !isOutAdmin
        ? service.task.base.getRelatedList({
            page,
            pageSize,
            where: whereOptForTasksAndTotalCount,
            order: [['id', 'desc']],
          })
        : service.task.base.getExactList({
            page,
            pageSize,
            where: whereOptForTasksAndTotalCount,
            order: [['id', 'desc']],
          })
    );

    // Promise for priorityCount
    promises.push(
      !isOutAdmin
        ? service.task.base.relatedCount({ where: whereOptForPriorityCount })
        : service.task.base.exactCount({ where: whereOptForPriorityCount })
    );

    const [count, tasks, priorityCount] = await Promise.all(promises);

    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      const file = fileDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      if (meta) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json');
      }
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        priorityCount,
        tasks,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async apply() {
    const { service, ctx, logger } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const whereOpt: any = {
      taskId,
      [Op.or]: [
        { status: statuses.split },
        { status: statuses.splitFailed }
      ],
    };
    const task = await service.task.base.getOne({
      where: whereOpt,
      attributes: ['taskId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('暂无可申领的任务');
    }
    const appliedTaskId = task.taskId;
    const [affectedRows] = await service.task.base.update(
      {
        splitUserId: userId,
        status: statuses.spliting,
      },
      {
        where: {
          taskId: appliedTaskId,
          status: statuses.split,
          splitUserId: 0,
        },
      }
    );
    if (!affectedRows) {
      return ctx.body = baseError.dataAlreadyExistError('任务已被申领');
    }
    logger.info(`task ${taskId} status changed : ${statuses.spliting}`);
    const { status } = await service.task.base.getOne({ where: { taskId: appliedTaskId } }) as any;
    logger.info(`after affectedRows : ${affectedRows}, appliedTaskId : ${appliedTaskId}, status : ${status}`);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId: appliedTaskId,
        type: service.task.history.taskV2Types.splitApply.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0, data: { taskId: appliedTaskId } };
  }

  @validate({ taskId: 'number' })
  public async quit() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'splitUserId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.spliting) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    if (task.splitUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人分割，无权限');
    }
    await service.task.base.update({
      splitUserId: 0,
      status: statuses.split,
    }, { where: { taskId } });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.splitQuit.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async reduct() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'splitUserId', 'appKey', 'taskId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.spliting) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    if (task.splitUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人分割，无权限');
    }
    try {
      await service.task.base.copyOssData(task.appKey, task.taskId, 'machine.html', 'html');
    } catch (e) {
      this.logger.error(`controller.admin.split.reduct: ${e}`);
      return ctx.body = baseError.serverError('文件还原失败');
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.splitReduct.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async revoke() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'splitUserId', 'appKey', 'taskId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.splited && task.status !== statuses.splitFailed) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    try {
      await service.task.base.update(
        { status: statuses.split, splitUserId: 0 },
        { where: { taskId } }
      );
    } catch (e) {
      this.logger.error(`controller.admin.split.revoke: ${e}`);
      await service.task.base.update({
        status: statuses.error,
        errorInfo: (e as any).message,
      }, { where: { taskId } });
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.splitRevoke.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async jump() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const isAdmin = await service.themis.hasRoleByUserId(userId, ({ name }) => name === '管理员');
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'appKey', 'taskId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.split) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    if (!isAdmin) {
      return ctx.body = baseError.dataNotExistError('请联系管理员操作');
    }
    try {
      await app.model.transaction(async(transaction) => {
        await service.task.base.update(
          { status: statuses.unmarked },
          { where: { taskId }, transaction }
        );
        await service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.INIT,
        }, { transaction });
        await service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction });
        const meta = await service.task.meta.getMetas({ taskId });
        if (meta.isOpen) {
          // 如果是九学王,path 得是 2
          meta.path = '2';
          await service.task.meta.setMetas([taskId], meta, { transaction });
        }
      });
      // @todo: 先关掉，如果点了修复 HTML，应该用修复后的，而不是 machine.html
      // await service.task.base.copyOssData(task.appKey, task.taskId, 'machine.html', 'html');
    } catch (e) {
      this.logger.error(`controller.admin.split.jump: ${e}`);
      await service.task.base.update({
        status: statuses.error,
        errorInfo: (e as any).message,
      }, { where: { taskId } });
    }
    await service.task.history.create({
      userId,
      taskId,
      type: service.task.history.taskV2Types.splitJump.id,
      costTime: 0,
      data: '',
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    projectId: 'number?',
    workOrder: 'string?',
    reConfirm: 'boolean?',
    taskNames: {
      type: 'array?',
      itemType: 'string',
    },
    pageCountInfos: 'array?',
  })
  public async confirm() {
    const { ctx, service, app, logger } = this;
    const { statuses } = service.task.base;
    const { userId } = ctx.data;
    const { taskId, projectId, reConfirm, workOrder, taskMetas, pageCountInfos, taskNames, paths, taskElementChildrenLength } = ctx.input as {
      taskId: number;
      projectId?: number;
      reConfirm?: boolean;
      workOrder?: string;
      taskMetas?: IPdfInfo[];
      taskNames?: string[];
      paths?: string[];
      pageCountInfos?: IPageCountInfo[]
      taskElementChildrenLength?: any[]
    };
    const task = await service.task.taskV2.getOneById(taskId);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.spliting && task.status !== statuses.splitFailed || userId !== task.splitUserId) {
      return ctx.body = baseError.dataNotExistError('任务不能处理');
    }
    const { appKey, taskName, subject, resourceType } = task;
    let project;
    let meta;
    let bookId;
    let newProjectId;
    let tickedId = '';
    if (projectId) {
      newProjectId = projectId;
      [project, meta] = await Promise.all([
        service.project.base.getRelateOne({ where: { id: projectId } }),
        service.project.meta.getMetas({ projectId })
      ]);
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      bookId = project.books && project.books.question.bookId;
      tickedId = project.workOrder;
    } else {
      meta = await service.task.meta.getMetas({ taskId });
      delete meta.childrenPdfInfo;
      meta.bookName = taskName;
      await app.model.transaction(async(transaction) => {
        project = await service.project.base.create(
          {
            userId,
            projectName: taskName,
            appKey,
            subject,
            errorInfo: '',
            workOrder: workOrder || '',
            status: service.project.base.statuses.processing,
            reviewUserId: 0,
            startReviewTime: null,
            endReviewTime: null,
            markJsonStatus: 0,
          },
          { transaction }
        );
        tickedId = workOrder || '';
        newProjectId = project.id;
        await service.project.meta.setMetas([project.id], meta, { transaction });
        const book = await service.book.create({
          appKey,
          subject,
          bookName: `${taskName}-试题`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.question,
          reviewUserId: 0,
          taskCount: 0,
        }, { transaction });
        bookId = book.id;

        await service.task.taskV2.update(
          { status: statuses.sysSpliting },
          { transaction, where: { taskId } }
        );
        // 更新项目 oss meta.json
        ctx.runInBackground(async() => {
          try {
            await service.project.base.setOssMeta(task.appKey, project.id, meta);
          } catch (e) {
            logger.info(`split.confirm set project oss data error !!!!${e}`);
          }
        });
        return project;
      });
    }

    // 把样例文件创建一份软连接, 名字是bookId
    // 需要先判断一下是否存在过样例文件
    try {
      const sampleUrl = service.task.base.getSampleUrl(appKey, taskId, 'sample.html');
      const data = await this.app.curl(sampleUrl, { method: 'GET' ,timeout: 10000 });
      if (data.status === 200) {
        const originSamplePath = service.task.base.getSampleOssKey(appKey, taskId, 'sample.html');
        const targetSamplePath = originSamplePath.replace(`${taskId}`, `${bookId}-${taskId}`);
        const ossClient = this.service.oss.createOss();
        await ossClient.copy(targetSamplePath, originSamplePath);
      }
    } catch (e) {
      this.logger.info(`split - confirm 分割确认 - taskId: ${taskId} - 不存在样例文件`);
    }
    logger.info(`split.confirm.runInBackground begin!!! taskId: ${taskId}`);
    ctx.runInBackground(async() => {
      try {
        const file = await service.task.file.getFiles({ taskId });
        let html = await service.task.base.getOssData(appKey, taskId, 'html');
        // 检查脏表格
        // 收集 table 字符串，然后用 ###\d+### 替换
        const tables = html.match(/<table[\s\S]*?<\/table>/g);
        let processedHtml = html;
        // @todo: 太慢了，暂时关掉
        if (tables && !1) {
          for (const table of tables) {
            const index = tables.indexOf(table);
            try {
              if (hasErrorColOrTrTable(table)) {
                this.logger.info(`task:${taskId} 检查到错误的表格:第${index}个`);
                const _table = table.replace(/\$\$/g, '#34#');
                const fixedTable = await this.service.task.base.convertErrorTable(_table);
                if (!fixedTable) {
                  continue;
                }
                processedHtml = processedHtml.replace(table, fixedTable.replace(/#34#/g, '$$$$$'));
              }
            } catch (e) {
              this.logger.error(`task:${taskId} 检查到错误的表格:第${index}个, 处理失败`);
            }
          }
        }
        html = processedHtml;
        const tasksHtml = service.task.taskV2.splitTaskHtml(html).map((html) => `<hr data-label="mergeStart" />${html}<hr data-label="mergeEnd" />`);

        // if ([ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(task.resourceType)) {
        // todo：把图片信息都存进去。
        // }
        await app.model.transaction(async(transaction) => {
          const bookStatuses = service.book.statuses;
          const [book, bookOrder] = await Promise.all([
            service.book.getOne({ where: { id: bookId } }),
            service.task.base.model.max('bookOrder', { where: { bookId } })
          ]);
          const taskIds = await service.task.taskV2.splitCreate({
            parentTaskId: taskId,
            appKey: book!.appKey,
            subject: book!.subject,
            open: false,
            callbackUrl: '',
            extra: '',
            isTest: false,
            taskName,
            taskNames,
            resourceType: [ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(task.resourceType) ? ETaskResourceType.IMAGE2 : resourceType,
            bookId,
            priority: task.priority,
            bookOrder: bookOrder || 0,
            length: tasksHtml.length,
          }, { transaction });
          await service.task.meta.setMetas(taskIds, meta, { transaction });
          await service.book.update({
            status: bookStatuses.processing,
            taskCount: sequelize.literal(`taskCount + ${tasksHtml.length}`),
          }, { transaction, where: { id: bookId } });
          await service.task.file.setFiles(taskIds, file, { transaction });
          if (taskMetas) {
            await Promise.all(taskIds.map((taskId, index) =>
              service.task.meta.setMetas(
                [taskId],
                { pdfInfo: taskMetas[index], splitPdfInfo: taskMetas[index], path: paths ? paths[index] : '1' },
                { transaction })));
          }
          const newTaskIds: number[] = [];
          if (taskNames && paths && taskElementChildrenLength && taskElementChildrenLength.length) {
            // 把 v1, v2任务分组
            const v1TaskIds: any = [];
            const v2TaskIds: any = [];
            // const order = bookOrder || 0;
            taskIds.forEach((_task: number, index) => {
              if (paths[index] === '1') {
                v1TaskIds.push({
                  taskId: _task,
                  path: paths[index],
                  html: tasksHtml[index],
                  length: taskElementChildrenLength[index].length,
                  name: taskNames[index],
                  meta: taskMetas ? { pdfInfo: taskMetas[index], splitPdfInfo: taskMetas[index] } : {},
                  bookOrder: index,
                });
              } else {
                v2TaskIds.push({
                  taskId: _task,
                  path: paths[index],
                  html: tasksHtml[index],
                  length: taskElementChildrenLength[index].length,
                  name: taskNames[index],
                  meta: taskMetas ? { pdfInfo: taskMetas[index], splitPdfInfo: taskMetas[index] } : {},
                  bookOrder: index,
                });
              }
            });
            // 然后再根据两个任务的长度进行合并
            let _bookOrder = bookOrder || 0;
            const v1Group = service.task.taskV2.groupTasks(v1TaskIds);
            const v2Group = service.task.taskV2.groupTasks(v2TaskIds);
            // 打印一下分组的结果
            logger.info(`v1Group: ${v1Group.map((item: any) => item.map((i: any) => i.taskId))}`);
            logger.info(`v2Group: ${v2Group.map((item: any) => item.map((i: any) => i.taskId))}`);
            const _resourceType = [ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(task.resourceType) ? ETaskResourceType.IMAGE2 : resourceType;
            for (const tasks of v1Group) {
              _bookOrder = await service.task.base.createMergedTask(_bookOrder, tasks, service, transaction, newTaskIds, taskId, appKey, subject, task, statuses.unmarked, bookId, _resourceType, logger, book, file, '1');
            }
            this.logger.info(`split.confirm.runInBackground begin set oss taskIdV1: ${taskId} taskIds ${taskIds} done`);

            for (const tasks of v2Group) {
              _bookOrder = await service.task.base.createMergedTask(_bookOrder, tasks, service, transaction, newTaskIds, taskId, appKey, subject, task, statuses.unmarked, bookId, _resourceType, logger, book, file, '2');
            }
            this.logger.info(`split.confirm.runInBackground begin set oss taskIdV2: ${taskId} taskIds ${taskIds} done`);

            await service.book.update({
              status: bookStatuses.processing,
              taskCount: v1Group.length + v2Group.length,
            }, { transaction, where: { id: bookId } });
          }

          if (newProjectId && pageCountInfos) {
            // 更新项目的pageCountInfo
            await service.project.meta.setMetas([newProjectId], { pageCountInfos }, { transaction });
            if (taskNames && paths && taskElementChildrenLength && taskElementChildrenLength.length) {
              await service.project.meta.setMetas([newProjectId], { isNewFlow: 1 }, { transaction });
            } else {
              // 重新分割的话， 如果不加可能会有问题
              await service.project.meta.setMetas([newProjectId], { isNewFlow: 0 }, { transaction });
            }
            this.logger.info(`split - confirm 分割确认 - tickedId: ${tickedId}`);
            // 同步到工单
            if (tickedId) {
              await service.project.meta.syncMetaInfoToWorkSheet(tickedId || '', pageCountInfos);
            }
          }
          for (const childTaskId of taskIds) {
            const index = taskIds.indexOf(childTaskId);
            const ossClient = this.service.oss.createOss();
            const sourceKey = service.project.base.getOssKey(book!.appKey, book!.projectId, 'meta.json');
            const key = service.task.base.getOssKey(book!.appKey, childTaskId, 'meta.json');
            try {
              await ossClient.copy(key, sourceKey);
            } catch (e) {
              logger.info('split.confirm.runInBackground set child step2 setmeta error ');
            }
            await service.task.base.setOssData(book!.appKey, childTaskId, 'html', tasksHtml[index]);
            await service.task.base.copyOssData(book!.appKey, childTaskId, 'html', 'machine.html');
            if (![ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(task.resourceType)) {
              try {
                if (file.pdfs) {
                  const pdfs = file.pdfs;
                  await Promise.all(pdfs.map(async(pdf: { id: string; name: string; }) => {
                    return await ossClient.copy(service.task.base.getOssKey(book!.appKey, `${childTaskId}.${pdf.id}`, 'pdf'), service.task.base.getOssKey(appKey, `${taskId}.${pdf.id}`, 'pdf'));
                  }));
                } else {
                  await ossClient.copy(service.task.base.getOssKey(book!.appKey, childTaskId, 'pdf'), service.task.base.getOssKey(appKey, taskId, 'pdf'));
                }
              } catch (e) {
                this.logger.info('文件 copy 失败', JSON.stringify(e));
              }
            }
          }
          logger.info(`split.confirm.runInBackground end set oss taskId: ${taskId} taskIds ${taskIds}`);
          if (task.bookId) {
            await service.book.update({
              status: bookStatuses.processing,
              taskCount: sequelize.literal('taskCount - 1'),
            }, { transaction, where: { id: task.bookId } });
          }
          await service.task.taskV2.update(
            { status: statuses.splited, bookId: 0, bookOrder: 0 },
            { transaction, where: { taskId } }
          );
        });
        await service.task.history.create({
          userId,
          taskId,
          type: service.task.history.taskV2Types[reConfirm ? 'splitReConfirm' : 'splitConfirm'].id,
          costTime: 0,
          data: '',
        });
      } catch (e) {
        logger.error(`controller.admin.split.confirm.ctx.runInBackground.catch: ${e}`);
        await service.task.base.update({ status: statuses.splitFailed }, { where: { taskId } });
        if (!projectId) {
          await service.project.base.relatedDelete(project.id);
        }
        // throw e;
      }
    });
    ctx.body = {
      status: 0,
      task,
      project,
      meta,
    };
  }

  /**
   * @func
   * @desc 分割 pdf 任务为 img 任务
   * @param { number } taskId - 任务ID
   * @param {array } splitArr - 分割数组的下标
   * @param { number } projectId - 项目id
   * @param { number } workOrder - 工单号
   */
  @validate({
    taskId: 'number',
    body: 'array',
    answer: 'array',
    projectId: 'number?',
    workOrder: 'number?',
    pageCountInfos: 'array?',
  })
  public async splitPdf() {
    const { ctx, service, app, logger } = this;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const { taskId, body, answer, projectId, workOrder, pageCountInfos } = ctx.input as {
      taskId: number;
      body: (string[])[];
      answer: (string[] | undefined)[];
      projectId?: number;
      workOrder?: string;
      pageCountInfos?: IPageCountInfo[]
    };

    // 0. 拿到当前 task 信息和 file 信息
    const task = await service.task.taskV2.getOneById(taskId);
    const { appKey, taskName, subject } = task;
    // 0.1 创建图书/项目
    let meta;
    let project;
    let bookId;
    let newProjcetId = 0;
    let newTicketId = '';
    if (projectId) {
      newProjcetId = projectId;
      [project, meta] = await Promise.all([
        service.project.base.getRelateOne({ where: { id: projectId } }),
        service.project.meta.getMetas({ projectId })
      ]);
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      newTicketId = project.workOrder;
      bookId = project.books && project.books.question.bookId;
    } else {
      // 一个图书对应一个项目、对应多个任务
      meta = await service.task.meta.getMetas({ taskId });
      delete meta.childrenPdfInfo;
      meta.bookName = taskName;
      await app.model.transaction(async(transaction) => {
        project = await service.project.base.create(
          {
            userId,
            projectName: taskName,
            appKey,
            subject,
            errorInfo: '',
            workOrder: workOrder || '',
            status: service.project.base.statuses.processing,
            reviewUserId: 0,
            startReviewTime: null,
            endReviewTime: null,
            markJsonStatus: 0,
          },
          { transaction }
        );
        newProjcetId = project.id;
        newTicketId = project.workOrder;
        await service.project.meta.setMetas([project.id], meta, { transaction });
        const book = await service.book.create({
          appKey,
          subject,
          bookName: `${taskName}-图书册`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.question,
          reviewUserId: 0,
          taskCount: 0,
        }, { transaction });
        bookId = book.id;
        // 机器分割任务中
        await service.task.taskV2.update(
          { status: statuses.sysSpliting },
          { transaction, where: { taskId } }
        );
        // 更新项目 oss meta.json
        ctx.runInBackground(async() => {
          await service.project.base.setOssMeta(task.appKey, project.id, meta);
        });
        return project;
      });
    }

    ctx.runInBackground(async() => {
      try {
        const bookStatuses = service.book.statuses;
        let [book, bookOrder, file] = await Promise.all([
          service.book.getOne({ where: { id: bookId } }),
          service.task.base.model.max('bookOrder', { where: { bookId } }),
          service.task.file.getFiles({ taskId })
        ]);
        if (!bookOrder) bookOrder = 0;
        // 1. 拿到 imglist
        let imageIdsList: string[][] = [];
        if (body.length) {
          imageIdsList = body.map((item, index) => {
            if (answer && answer[index]) {
              return item?.concat(answer[index] as string[]);
            }
            return item;
          });
        }
        // 2. 拆分 imgList
        let taskIds;
        await app.model.transaction(async(transaction) => {
          taskIds = await Promise.all(imageIdsList.map(async(imageIds, index) => {
            /*
             * Tips：
             * 在这里计算&记录一下，任务图片是来自于哪些 PDF 图片，
             * 用于更准确的统计外包的成本。
             */
            const images = imageIds.map((imgId) => {
              const img$: any = file.imgs?.find((img) => img.id === imgId);
              if (!img$) return null;
              if (img$.chunkFromImageId) img$.chunkFromImage = file.imgs_pdf?.find((img) => img.id === img$.chunkFromImageId);
              return img$;
            }).filter((i) => Boolean(i));
            let pdfImageCount = imageIds.length;
            logger.info(`计算来源 PDF 的图片数量, imageIds.length=${imageIds.length}, images.length=${images.length}`);
            if (images.length === imageIds.length) {
              // a3 - 乘以 2
              pdfImageCount = images.reduce((a: number, b: any) => {
                let count = 1;
                if (b.info && b.chunkFromImage?.info) { // 分割过的图片，用面积进行计算
                  const rect1 = JSON.parse(b.info);
                  const rect2 = JSON.parse(b.chunkFromImage.info);
                  logger.info(`计算来源 PDF 的图片数量, value is ${parseFloat((rect1.w * rect1.h / (rect2.w * rect2.h)).toFixed(2))}`);
                  count = parseFloat((rect1.w * rect1.h / (rect2.w * rect2.h)).toFixed(2));
                  if (b.paperSize === 'a3') {
                    count = parseFloat((count * 2).toFixed(2));
                  }
                  return a + count;
                }
                if (b.paperSize === 'a3') {
                  count = 2;
                }
                return a + count;
              }, 0);
            }
            return await service.task.taskV2.splitPdfCreate({
              parentTaskId: taskId,
              appKey: book!.appKey || appKey,
              subject: book!.subject || subject,
              callbackUrl: '',
              extra: '',
              isTest: false,
              taskName: taskName + '-' + (index + 1),
              open: false,
              bookId,
              bookOrder: bookOrder + index + 1,
              priority: task.priority,
              images: imageIds,
              pdfImageCount,
              meta,
            }, { transaction });
          }));
          await service.task.meta.setMetas(taskIds, meta, { transaction });
          await service.book.update({
            status: bookStatuses.processing,
            taskCount: sequelize.literal(`taskCount + ${imageIdsList.length}`),
          }, { transaction, where: { id: bookId } });
          // task 分割完不和原来的 book 相关联了
          await service.task.taskV2.update(
            { status: statuses.splited, bookId: 0, bookOrder: 0 },
            { transaction, where: { taskId } }
          );
          if (newProjcetId && pageCountInfos) {
            // 更新项目的pageCountInfo
            await service.project.meta.setMetas([newProjcetId], { pageCountInfos }, { transaction });
            // 同步到工单
            if (newTicketId) {
              this.logger.info(`splitPdf - confirm 分割确认 - newTicketId: ${newTicketId}`);
              await service.project.meta.syncMetaInfoToWorkSheet(newTicketId || '', pageCountInfos);
            }
          }

        });
      } catch (e) {
        logger.info(`controller.admin.split.confirm.ctx.runInBackground.catch: ${e}`);
        await service.task.base.update({
          status: statuses.splitFailed,
          errorInfo: e as any,
        }, { where: { taskId } });
      }
    });

    ctx.body = {
      status: 0,
      taskId,
    };
  }

  @validate({ imageId: 'string', angle: 'number', appKey: 'string' })
  public async rotatePdfImage() {
    const { ctx, service } = this;
    const { imageId, angle, appKey } = ctx.input as { imageId: string, angle: 90 | -90 | 180, appKey: string };
    try {
      await service.task.base.rotatePdfImage(imageId, angle, appKey);
    } catch (e) {
      this.logger.info(`rotatePdfImage appKey:${appKey} imageId:${imageId} error: ${e}`);
      return ctx.body = baseError.serverError('旋转图片失败');
    }
    ctx.body = {
      status:0,
      imageId,
    };
  }

  @validate({ taskId: 'number', ans_page: 'number' })
  public async setAnswer() {
    /**
     * 切分pdf为答案
     */
    const { ctx, service } = this;
    const data = ctx.input as { taskId: number; ans_page: number; };
    const { taskId, ans_page } = data;

    const file = await service.task.file.getFiles({ taskId });
    if (!file.imgs?.length) {
      return ctx.body = baseError.dataNotExistError('未查询到图片信息');
    }
    try {
      const imgs_bak = JSON.parse(JSON.stringify(file.imgs));

      file.imgs = file.imgs.map((img, index) => {
        if (index >= ans_page) {
          img.type = 'answer';
        }
        return img;
      });
      file.imgs_bak = imgs_bak;
      await service.task.file.setFiles([taskId], file);

      ctx.body = {
        status:0,
        length: file.imgs.length,
      };
    } catch (e) {
      this.logger.info(`split setAns Error ${taskId}:${e}`);
      return ctx.body = baseError.serverError('分割图片错误');
    }
  }

  @validate({ taskId: 'number' })
  public async saveEdit() {
    const { ctx, service } = this;
    let { taskId, bodyArr, answerArr, file } = ctx.input;
    if (!file?.imgs?.length) {
      file = await service.task.file.getFiles({ taskId });
    }
    if (!file.imgs?.length) {
      return ctx.body = baseError.dataNotExistError('未查询到图片信息');
    }
    try {
      const imgs_bak = JSON.parse(JSON.stringify(file.imgs));
      file.imgs.map((item) => {
        item.split = false;
        item.splitType = '';
      });
      if (bodyArr?.length) {
        bodyArr.map((item) => {
          const cur = file.imgs?.find((cur) => cur.id === item);
          if (cur) {
            cur.split = true;
            cur.splitType = 'body';
          }
        });
      }
      if (answerArr?.length) {
        answerArr.map((item) => {
          const cur = file.imgs?.find((cur) => cur.id === item);
          if (cur) {
            cur.split = true;
            cur.splitType = 'answer';
          }
        });
      }
      file.imgs_bak = imgs_bak;
      await service.task.file.setFiles([taskId], file);
      ctx.body = { status:0 };
    } catch (e) {
      this.logger.info(`split setAns Error ${taskId}:${e}`);
      return ctx.body = baseError.serverError('保存失败');
    }
  }

  @validate({ taskId: 'number' })
  public async splitPdfImageMove() {
    /**
     * 切分pdf为答案
     */
    const { ctx, service } = this;
    const data = ctx.input as { taskId: number; imageListId: string[]; };
    const { taskId, imageListId } = data;

    const file = await service.task.file.getFiles({ taskId });
    if (!file.imgs?.length) {
      return ctx.body = baseError.dataNotExistError('未查询到图片信息');
    }
    try {
      const imgs_bak = JSON.parse(JSON.stringify(file.imgs));

      const pre = file.imgs.findIndex((item) => item.id == imageListId[0]);
      const cur = file.imgs.findIndex((item) => item.id == imageListId[1]);
      const temp = file.imgs[pre];
      file.imgs[pre] = file.imgs[cur];
      file.imgs[cur] = temp;
      file.imgs[pre].split = false;
      file.imgs[cur].split = false;

      file.imgs_bak = imgs_bak;
      await service.task.file.setFiles([taskId], file);
      ctx.body = { status:0 };
    } catch (e) {
      this.logger.info(`split setAns Error ${taskId}:${e}`);
      return ctx.body = baseError.serverError('移动失败');
    }
  }

  @validate({ imageId: 'string', appKey: 'string', taskId: 'number', result: 'array' })
  public async chunkImage() {
    /**
     * 分割图片
     * 1. 拿到切割信息
     * 2. 分割图片到 xdoc oss
     * 3. 更新 xdoc file 信息
     */
    const { ctx, service } = this;
    const data = ctx.input as { imageId: string, appKey: string, result?: { x: number, y: number, w: number, h: number, i?: number }[], taskId: number, pdfId: string };
    const { appKey, imageId, result, taskId, pdfId } = data;
    if (!result?.length) {
      return ctx.body = baseError.dataNotExistError('请传入切割信息');
    }

    const file = await service.task.file.getFiles({ taskId });
    if (!file.imgs?.length) {
      return ctx.body = baseError.dataNotExistError('未查询到图片信息');
    }
    try {
      const currentImg = file.imgs.find((item) => imageId === item.id);
      const addImgsIds = await service.task.base.uploadSplitImg(appKey, imageId, result);
      this.logger.info(`addImgsIds taskId :${taskId} chunkImgs: ${addImgsIds} `);
      const addImgsList = addImgsIds?.map((item) => {
        return {
          id: item.newImageId,
          info: JSON.stringify(item.info),
          pdfId,
          type: currentImg?.type,
          chunkFromImageId: currentImg?.chunkFromImageId || imageId, // 来源图片可能是经过裁剪的图片
        };
      }) as ITaskImgs;
      const imgs_bak = JSON.parse(JSON.stringify(file.imgs));
      const addIndex = file.imgs.findIndex((item) => item.id === imageId);
      file.imgs.splice(addIndex, 1, ...addImgsList);
      let bodyCount = 0;
      let answerCount = 0;
      file.imgs = file.imgs.map((item) => {
        const index = item.type === 'body' ? bodyCount++ : answerCount++;
        return { ...item, page_num: index + 1 }; // @todo：typo 问题，应该是 pageNum？！！！
      });
      file.imgs_bak = imgs_bak;
      if (!file.imgs_pdf) {
        file.imgs_pdf = imgs_bak; // 若需要分割图片，则记录一下 PDF 原始的图片列表
      }
      await service.task.file.setFiles([taskId], file);

      ctx.body = {
        status:0,
        length: file.imgs.length,
      };
    } catch (e) {
      this.logger.info(`chunkImage error: taskId:${taskId} appKey:${appKey} imageId:${imageId} error: ${e}`);
      return ctx.body = baseError.serverError('分割图片错误');
    }
  }

  @validate({ imageId: 'string', appKey: 'string', taskId: 'number' })
  public async rotateImageV2() {
    /**
     * 分割图片
     * 1. 拿到切割信息
     * 2. 分割图片到 xdoc oss
     * 3. 更新 xdoc file 信息
     */
    const { ctx, service, config } = this;
    const data = ctx.input as { imageId: string, appKey: string, taskId: number, pdfId: string };
    const { appKey, imageId,taskId } = data;
    const file = await service.task.file.getFiles({ taskId });
    if (!file.imgs?.length) {
      return ctx.body = baseError.dataNotExistError('未查询到图片信息');
    }
    try {
      // 旋转图片
      const imgUrl = `${config.aliOss.host}open/${appKey}/image/${imageId}.jpg?x-oss-process=image/rotate,90`;
      // 覆盖上传
      await service.task.base.uploadImage(appKey, imgUrl, imageId );
      this.logger.info(`rotateImage taskId :${taskId}`);
      // 图片的宽高互换
      file.imgs.forEach((img) => {
        if (img.id === imageId) {
          const { w, h } = JSON.parse(img?.info || '{}');
          if (w && h) {
            img.info = JSON.stringify({ w: h, h: w });
          }
        }
      });
      const imgs_bak = JSON.parse(JSON.stringify(file.imgs));

      if (!file.imgs_pdf) {
        file.imgs_pdf = imgs_bak;
      }
      await service.task.file.setFiles([taskId], file);

      ctx.body = { status:0 };
    } catch (e) {
      this.logger.info(`chunkImage error: taskId:${taskId} appKey:${appKey} imageId:${imageId} error: ${e}`);
      return ctx.body = baseError.serverError('分割图片错误');
    }
  }

  @validate({ taskId: 'number' })
  public async rovokeChunkPdfImgs() {
    const { ctx, service } = this;
    const { taskId } = ctx.input as { taskId: number };
    try {
      const file = await service.task.file.getFiles({ taskId });
      if (!file?.imgs_bak?.length) return ctx.body = baseError.dataNotExistError('不存在上一次保存记录');
      file.imgs = file?.imgs_bak;
      delete file.imgs_bak;
      await service.task.file.setFiles([taskId], file);
      ctx.body = { status:0 };
    } catch (e) {
      this.logger.info(`rovokeChunkPdfImgs error: taskId:${taskId} error: ${e}`);
      return ctx.body = baseError.serverError('分割图片错误');
    }
  }

  @validate({ taskId: 'number' })
  public async imageTaskSplit() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId } = ctx.input as {
      taskId: number;
    };
    const [task, images] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, disabled: false },
        attributes: ['reviewed', 'imageId', 'filename', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    const allImageIds: string[] = [];
    for (const { imageId } of service.image.sortImageByFile(images || [])) {
      allImageIds.push(imageId);
    }
    let html = await service.task.base.combineByImageIds(task.appKey, allImageIds, taskId);
    html = preCleanHtml(html);
    await service.task.base.setOssData(task.appKey, taskId, 'html', html);
    await service.task.base.copyOssData(task.appKey, taskId, 'html', 'machine.html');
    await service.task.base.update({ status: statuses.split }, { where: { taskId } });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    projectId: 'number?',
    workOrder: 'string?',
    taskList: {
      type: 'array',
      itemType: 'object',
      rule: {
        start: 'number',
        end: 'number',
        taskName: 'string',
        images: {
          type: 'array',
          itemType: 'object',
          rule: {
            imageId: 'string',
            url: 'string',
            html: 'string',
          },
        },
      },
    },
  })
  public async imageConfirm() {
    const { ctx, service, app, logger } = this;
    const { statuses } = service.task.base;
    const { userId } = ctx.data;
    const { taskId, projectId, workOrder, taskList } = ctx.input as {
      taskId: number;
      projectId?: number;
      workOrder?: string;
      taskList: { start: number, end: number, taskName: string, images: { imageId: string, url: string, html: string }[] }[];
    };
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.unmarked) {
      return ctx.body = baseError.dataNotExistError('任务不能处理');
    }
    const { appKey, taskName, subject, resourceType } = task;
    if (![ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(resourceType!)) {
      return ctx.body = baseError.paramsError('不是可以处理的任务类型');
    }
    let project;
    let meta;
    let bookId;
    if (projectId) {
      [project, meta] = await Promise.all([
        service.project.base.getRelateOne({ where: { id: projectId } }),
        service.project.meta.getMetas({ projectId })
      ]);
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      bookId = project.books && project.books.question.bookId;
    } else {
      meta = await service.task.meta.getMetas({ taskId });
      delete meta.childrenPdfInfo;
      meta.bookName = taskName;
      await app.model.transaction(async(transaction) => {
        project = await service.project.base.create(
          {
            userId,
            projectName: taskName,
            appKey,
            subject,
            errorInfo: '',
            workOrder: workOrder || '',
            status: service.project.base.statuses.processing,
            reviewUserId: 0,
            startReviewTime: null,
            endReviewTime: null,
            markJsonStatus: 0,
          },
          { transaction }
        );
        await service.project.meta.setMetas([project.id], meta, { transaction });
        const book = await service.book.create({
          appKey,
          subject,
          bookName: `${taskName}-试题`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.question,
          reviewUserId: 0,
          taskCount: 0,
        }, { transaction });
        bookId = book.id;
        // 更新项目 oss meta.json
        ctx.runInBackground(async() => {
          await service.project.base.setOssMeta(task.appKey, project.id, meta);
        });
        return project;
      });
    }
    /*
     * await service.task.taskV2.update(
     *   { status: statuses.sysSpliting },
     *   { where: { taskId } }
     * );
     */
    ctx.runInBackground(async() => {
      try {
        const bookOrder = await service.task.base.model.max('bookOrder', { where: { bookId } });
        for (let i = 0; i < taskList.length; i = i + 1) {
          const item = taskList[i];
          await Promise.all(item.images.map(async(item) => {
            await service.image.setOssData(appKey, item.imageId, 'html', item.html);
            return await service.task.base.uploadImage(appKey, item.url, item.imageId);
          }));
          await service.task.base.htmlCreate({
            appKey,
            subject,
            imageIds: item.images.map((item) => item.imageId),
            bookId,
            bookOrder: (bookOrder || 0) + i,
            open: false,
            callbackUrl: '',
            extra: '',
            isTest: false,
            taskName: item.taskName || '',
          }, ETaskResourceType.IMAGE);
        }
        await service.task.meta.setMetas([taskId], meta);
        await service.task.base.setOssMeta(appKey, taskId, meta);
        await service.task.history.create({
          userId,
          taskId,
          type: service.task.history.taskV2Types.splitConfirm.id,
          costTime: 0,
          data: '',
        });
        /*
         * await service.task.base.update({
         *   status: statuses.splited,
         * }, { where: { taskId } });
         */
      } catch (e) {
        logger.error(`controller.admin.split.confirm.ctx.runInBackground.catch: ${e}`);
        await service.task.base.update({ status: statuses.splitFailed }, { where: { taskId } });
        if (!projectId) {
          await service.project.base.relatedDelete(project.id);
        }
      }
    });
    ctx.body = {
      status: 0,
      task,
      project,
      meta,
    };
  }

  @validate({ taskId: 'number?', bookId: 'number?', parentTaskId: 'number?' })
  public async saveSample() {
    const { ctx, service } = this;
    const { taskId, bookId, html, parentTaskId } = ctx.input;
    // const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    let key: string | number;
    let task: any;
    if (taskId) {
      task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['status', 'bookId', 'appkey'],
      });
      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
      }
    }

    if (!bookId) {
      key = taskId;
    } else {
      key = bookId;
    }

    if (!key) {
      return ctx.body = baseError.dataNotExistError('任务错误, 意外退出, 请联系技术人员');
    }

    try {
      if (!bookId) {
        // 分割文件时保存
        await service.task.base.setSampleOssData(task['appkey'], key, 'sample.html', html );
      } else {
        // 标注审核时保存
        await service.task.base.setSampleOssData(task['appkey'], parentTaskId, 'sample.html', html);
        await service.task.base.setSampleOssData(task['appkey'], `${bookId}-${parentTaskId}`, 'sample.html', html);
      }

    } catch (e) {
      this.logger.info(`task: ${taskId} 保存样例文件失败`);
      return ctx.body = baseError.dataNotExistError('保存失败, 请联系技术人员!');
    }
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number?', bookId: 'number?' , parentTaskId: 'number?' })
  public async getSample() {
    const { ctx, service } = this;
    const { taskId,bookId , parentTaskId } = ctx.input;
    // const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    let key: string | number;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'bookId', 'appkey'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    this.logger.info('获取样例', 'taskId: ', taskId, 'task: ',task );

    if (!bookId) {
      key = taskId;
    } else {
      key = `${bookId}-${parentTaskId}`;
    }

    if (!key) {
      return ctx.body = baseError.dataNotExistError('任务错误, 意外退出, 请联系技术人员');
    }

    try {
      let url = service.task.base.getSampleUrl(task['appkey'], key, 'sample.html', true, false);
      try {
        await axios.get(url);
        return ctx.body = { status: 0, data: { url } };
      } catch (e) {
        url = service.task.base.getSampleUrl(task['appkey'], parentTaskId, 'sample.html', true, false);
        return ctx.body = { status: 0, data: { url } };
      }
    } catch (e) {
      this.logger.info(`${key}获取样例文件失败`);
      return ctx.body = { status: -1, data: { } };
    }

  }
}
