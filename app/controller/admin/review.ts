/**
 * @file 审核控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { convertHtmlToKatexHtml } from '../../core/utils/htmlToDocx';
import { errorNodeStat } from '../../core/utils/jsonHelper';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';
import { EImageMarkStatus } from '../../model/image';
import { preCleanHtml } from '../../core/utils/htmlToJsonV4/helper/cleanupNode';

export default class TaskReviewController extends Controller {

  private async checkProject(userId: number, bookId: number) {
    const { service } = this;
    const { statuses } = service.project.base;
    if (!bookId) return;
    const book = await service.book.getOne({ where: { id: bookId } });
    if (!book) return;
    const project = await service.project.base.getOne({ where: { id: book.projectId } });
    if (!project) return;
    if (project.status === statuses.reviewing && userId !== project.reviewUserId) {
      const res = await service.user.search([project.reviewUserId]);
      return `${res?.[0]?.nickname || project.reviewUserId}正在审核${project.projectName}，不能修改这个任务`;
    }
    if (project.status === statuses.reviewed) {
      await service.project.base.update({
        status: statuses.unreviewed,
        startReviewTime: null,
        endReviewTime: null,
        reviewUserId: 0,
      }, { where: { id: project.id } });
    }
  }

  private async isProjectUser(userId: number, bookId: number) {
    const { service } = this;
    if (!bookId) {
      return false;
    }
    const book = await service.book.getOne({
      where: { id: bookId },
      attributes: ['reviewUserId', 'projectId'],
    });
    if (!book) {
      return false;
    }
    if (book.reviewUserId === userId) {
      return true;
    }
    if (!book.projectId) {
      return false;
    }
    const project = await service.project.base.getOne({
      where: { id: book.projectId },
      attributes: ['reviewUserId'],
    });
    return Boolean(project) && project?.reviewUserId === userId;
  }

  private async isAdminUser(userId: number) {
    const { service } = this;
    return await service.themis.hasRoleByUserId(userId, ({ name }) => name === '管理员');
  }

  @validate({ taskId: 'number' })
  public async revoke() {
    const { ctx, service, logger, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    // // 判断如果任务是 v3 任务，则 task 是标注任务， 然后再判断
    // const isV3 = await service.task.base.isV3(task.taskId);
    // if (isV3) {
    //   const mergedTask = await service.task.base.getOne({ where: { taskId: task.mergedTaskId } });
    //   if (mergedTask) {
    //     task = mergedTask;
    //   } else {
    //     return ctx.body = baseError.dataNotExistError('v3 标注任务不存在或已经删除');
    //   }
    // }

    if (![statuses.reviewing, statuses.reviewed, statuses.dataCleanfailed, statuses.contentError].includes(task.status)) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unreviewed,
            reviewUserId: 0,
            startReviewTime: null,
            endReviewTime: null,
          },
          { transaction, where: { taskId } }
        ),
        service.image.update({ reviewed: false }, { transaction, where: { taskId } }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.REJECTED,
        }, { transaction })
      ]);
      if ([statuses.error, statuses.reviewed].includes(task!.status)) {
        logger.info(`after task revoked, task-callbacking : ${task?.taskId} will be canceled!`);
        // 从待统计列表中删除
        await service.task.stat.del(taskId);
      }
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.revoke.id,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async remark() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.unreviewed) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          { status: statuses.marking, endMarkTime: null },
          { transaction, where: { taskId } }
        ),
        service.image.update(
          { marked: EImageMarkStatus.init },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.REJECTED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.remark.id,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  public async quit() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    if (!taskId) {
      return ctx.body = baseError.paramsError();
    }
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.reviewing || task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unreviewed,
            reviewUserId: 0,
            startReviewTime: null,
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onQuitTask({
          taskId,
          taskType: PlanTaskType.REVIEW,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.quit.id,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', errorInfo: 'string' })
  public async returnError() {
    const { ctx, service, app } = this;
    const { taskId, errorInfo } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.reviewing || task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            errorInfo,
            status: statuses.error,
            endReviewTime: new Date(),
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.returnBack.id,
        data: JSON.stringify({ errorInfo }),
        costTime: 0,
      });
      // 延迟两个小时回调
      await service.task.callback.callback(Object.assign(task, {
        errorInfo,
        status: statuses.error,
      }), 3, 2 * 60 * 60 * 1000);
      if (task.ticketId) {
        // 关闭工单
        await service.workSheet.submitError({
          ticketId: task.ticketId,
          errorInfo,
        });
        this.logger.info(`close ticket ${task.ticketId} success`);
      }
    });
    ctx.body = { status: 0 };
  }

  public async getOwnList() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { key, completed, isOutAdmin } = ctx.input;
    const { statuses } = service.task.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {
      key,
      status: completed ? statuses.reviewed : statuses.reviewing,
      reviewUserId: userId,
    } as any;
    const [count, tasks] = await Promise.all(!isOutAdmin ? [
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ] : [
      service.task.base.exactCount({ where: whereOpt }),
      service.task.base.getExactList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  public async getPendingList() {
    const { ctx, service } = this;
    const { key, appKey, notest } = ctx.input;
    const { statuses } = service.task.base;
    const { hexinApps } = service.appl;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {
      key,
      status: statuses.unreviewed,
      reviewUserId: 0,
    } as any;
    if (appKey) {
      whereOpt.appKey = appKey;
    } else if (notest) {
      whereOpt.appKey = { $not: hexinApps };
    }
    const [count, tasks] = await Promise.all([
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  public async getPendingCount() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    ctx.body = {
      status: 0,
      data: { count: await service.task.base.count({ where: { status: statuses.unreviewed, reviewUserId: 0 } }) },
    };
  }

  public async apply() {
    const { ctx } = this;
    ctx.body = baseError.dataAlreadyExistError('该功能已关闭');
  }

  @validate({
    taskId: 'number',
    imageId: 'string',
    formulaId: 'string',
    latex: 'string',
    confidence: { required: false, type: 'number' },
  })
  public async saveFormula() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, formulaId, latex, confidence } = ctx.input;
    const { userId } = ctx.data;
    const [task, image, formula] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getOne({ where: { imageId, disabled: false } }),
      service.formula.getOne({ where: { imageId, formulaId } })
    ]);
    if (!task || !image || !formula || imageId !== formula.imageId || image.taskId !== taskId) {
      return ctx.body = baseError.dataNotExistError('公式任务不存在');
    }
    if (task.status !== statuses.reviewing) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    if (task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('当前不是相关审核者，无权限提交');
    }
    await service.formula.update({
      latex,
      confidence: confidence ? confidence : undefined,
      reviewed: true,
    }, { where: { formulaId, imageId } });
    // 同步到html中
    await service.image.syncFormulaHtml(task.appKey, imageId, formulaId, latex);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.save.id,
        costTime: 0,
        data: JSON.stringify({
          imageId,
          formulaId,
          latex,
        }),
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    imageId: 'string',
    // html: { type: 'string', allowEmpty: true },
    htmlSize: 'number',
    status: 'number?',
    autoSave: 'boolean?',
    specialOperator: {
      type: 'enum',
      values: ['project_check', 'admin_check'],
      required: false,
    },
  })
  public async saveImage() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, htmlSize, status, autoSave, specialOperator } = ctx.input as {
      taskId: number;
      imageId: string;
      htmlSize: number;
      status?: number;
      autoSave?: boolean;
      specialOperator?: 'project_check' | 'admin_check';
    };
    const { userId } = ctx.data;
    const [task, image] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getOne({ where: { imageId, disabled: false } })
    ]);
    if (!task || !image || image.taskId !== Number(taskId)) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    if (task.status === statuses.reviewing && task.reviewUserId !== userId || task.status === statuses.marking && task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId);
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }
    // await service.image.setOssData(image.appKey, imageId, 'html', html);
    if (status != null) {
      await service.image.update({ reviewed: Boolean(status) }, { where: { imageId } });
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.reviewTypes.save.id,
        costTime: 0,
        data: JSON.stringify({
          autoSave,
          specialOperator,
          imageId,
          htmlSize,
        }),
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    costTime: 'number?',
    specialOperator: {
      type: 'enum',
      values: ['project_check', 'admin_check'],
      required: false,
    },
    authCode: 'string?',
  })
  public async confirm() {
    const { ctx, service, app } = this;
    const { mongo } = app;
    const { statuses } = service.task.base;
    const { taskId, costTime, specialOperator, authCode } = ctx.input as {
      taskId: number;
      costTime?: number;
      specialOperator?: 'project_check' | 'admin_check',
      authCode?: string;
    };
    let adminUserId: number | undefined;
    if (authCode) {
      adminUserId = await service.authCode.authCode(authCode);
      if (!adminUserId) {
        return ctx.body = baseError.permissionError(`授权码${authCode}无效`);
      }
    }
    const { userId } = ctx.data;
    const [task, images] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, disabled: false },
        attributes: ['reviewed', 'imageId', 'filename', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    this.logger.info(`reviewConfirm taskId :${task.taskId} begin `);
    const isSpecial = specialOperator && (
      specialOperator === 'project_check' ?
        await this.isProjectUser(userId, task.bookId) :
        await this.isAdminUser(userId)
    );
    if (task.status === statuses.reviewing && task.reviewUserId !== userId || (task.status === statuses.reviewed || task.status === statuses.operatAdmin) && !isSpecial) {
      return ctx.body = baseError.permissionError('无权限操作');
    }
    if (task.status === statuses.operatAdmin && task.operatAdminUserId !== userId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }

    const allImageIds: string[] = [];
    for (const { reviewed, imageId } of service.image.sortImageByFile(images || [])) {
      if (!reviewed) {
        return ctx.body = baseError.dataNotExistError('无法提交的任务状态, 部分图片未审核完成');
      }
      allImageIds.push(imageId);
    }
    this.logger.info(`reviewConfirm taskId :${task.taskId} allImageIds: ${JSON.stringify(allImageIds)} `);

    // @todo：这里也建议放到队列中进行处理
    let html = await service.task.base.combineByImageIds(task.appKey, allImageIds, taskId);
    this.logger.info(`reviewConfirm taskId :${task.taskId} html: ${html?.length ?? '没有html'} `);
    const json = await service.task.base.convert2Json(task.appKey, taskId, html);
    this.logger.info(`reviewConfirm taskId :${task.taskId} generate Json `);
    const catalog = service.task.base.getCatalog(task.appKey, taskId, json);
    const errorStat = errorNodeStat(json);
    if (errorStat.count && !authCode) {
      return ctx.body = baseError.permissionError('存在节点错误时，请使用授权码');
    }
    html = preCleanHtml(html);
    this.logger.info(`reviewConfirm taskId :${task.taskId} preCleanHtml `);
    await service.task.base.setOssData(task.appKey, taskId, 'html', html);
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await service.task.base.formateAndUploadDiffFile(task.appKey, taskId, html);
    this.logger.info(`reviewConfirm taskId :${task.taskId} preCleanHtml end `);
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.dataCleaning,
            endReviewTime: new Date(),
            errorNodeCount: errorStat.count,
            catalog: catalog.length ? JSON.stringify(catalog) : '',
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction })
      ]);
    });
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        costTime: costTime || 0,
        type: service.task.history.reviewTypes.confirm.id,
        data: JSON.stringify({
          specialOperator,
          adminUserId,
        }),
      });
      await service.task.stat.push(task.taskId);
      await service.task.stat.newStat({
        html,
        taskId,
        type: specialOperator || 'review',
        appKey: task.appKey,
        resourceType: task.resourceType!,
        subject: task.subject,
        imageCount: images.length,
        cost: costTime,
        userId,
      });
      this.logger.info(`reviewConfirm taskId :${task.taskId} runInBackground step2 pushIntoStat`);
      await mongo.db.collection('error_node_stat').insertOne({
        taskId,
        userId,
        action: 'confirm',
        timestamp: new Date().getTime(),
        ...errorStat,
      });
      this.logger.info(`reviewConfirm taskId :${task.taskId} runInBackground end`);
    });
    await service.task.base.pushToPublish(taskId);
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', force: 'boolean?' })
  public async getKatexHtml() {
    const { ctx, service } = this;
    const { taskId, force } = ctx.input;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (!force && task.status !== statuses.reviewed) {
      return ctx.body = baseError.dataNotExistError('任务未审核');
    }
    const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
    const khtml = await convertHtmlToKatexHtml(html);
    ctx.body = {
      status: 0,
      data: khtml,
    };
  }

  @validate({ taskId: 'number', force: 'boolean?' })
  public async downloadKatexHtml() {
    const { ctx, service } = this;
    const { taskId, force } = ctx.input;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (!force && task.status !== statuses.reviewed) {
      return ctx.body = baseError.dataNotExistError('任务未审核');
    }
    const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
    const khtml = await convertHtmlToKatexHtml(html);
    ctx.attachment(`${taskId}.katex.html`);
    ctx.body = khtml;
  }

  @validate({
    taskId: 'number',
    // 当前task内标题的索引
    index: 'number',
    body: 'string?',
    level: 'number?',
  })
  /**
   * @func
   * @desc 修改JSON标题，根据任务内的索引查找到所有图片对应HTML内的标题，然后再转成JSON。
   * @param { number } taskId - 任务ID
   * @param { number } index - 标题在对应任务内的索引
   * @param { ?string } body - 修改对应标题的内容
   * @param { ?number } level - 修改对应标题的内容层级
   */
  public async changeChapter() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { userId } = ctx.data;
    const { taskId, specialOperator } = ctx.input as {
      taskId: number;
      index: number;
      body?: string;
      level?: number;
      specialOperator?: 'project_check' | 'admin_check';
    };

    const task= await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status === statuses.reviewing && task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId);
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }
    await service.task.base.update({ generateStatus: 0 }, { where: { taskId } });
    await service.task.base.pushToGenerateImages({
      ...ctx.input,
      userId,
      type: 'changeChapter',
    });
    // const allImageIds: string[] = [];
    // for (const { imageId } of service.image.sortImageByFile(images as any[])) {
    //   allImageIds.push(imageId);
    // }
    //
    // const { appKey } = task;
    // const { html, imageId } = await service.task.base.changeChapter({
    //   allImageIds,
    //   appKey,
    //   index,
    //   body,
    //   level,
    // });
    // if (!html || !imageId) {
    //   return ctx.body = baseError.dataNotExistError('html不存在');
    // }
    // await service.image.setOssData(appKey, imageId, 'html', html);
    //
    // const htmls = await service.task.base.combineAndUploadByImageIds(task.appKey, task.taskId, allImageIds);
    // const json = await service.task.base.convert2Json(task.appKey, task.taskId, htmls);
    // const errorStat = errorNodeStat(json);
    // await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    // await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    // await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });
    //
    // ctx.runInBackground(async() => {
    //   const { mongo } = app;
    //   await mongo.db.collection('error_node_stat').insertOne({
    //     taskId,
    //     userId,
    //     action: 'regenerate',
    //     timestamp: new Date().getTime(),
    //     ...errorStat,
    //   });
    //   if (task.status === statuses.reviewed) {
    //     const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
    //     const res1 = await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
    //     const res2 = await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
    //     if (res1.html.length !== res2.html.length || res1.xml1.length !== res2.xml1.length || res1.xml2.length !== res2.xml2.length) {
    //       service.task.base.update(
    //         {
    //           errorInfo: res1.html.length !== res2.html.length && 'html长度不相等' || res1.xml1.length !== res2.xml1.length && 'xml1长度不相等' || res1.xml2.length !== res2.xml2.length && 'xml2长度不相等' || '',
    //           status: statuses.error,
    //         },
    //         { where: { taskId } }
    //       );
    //     }
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}111`), 'html', res1.html);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}112`), 'html', res1.xml1);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}113`), 'html', res1.xml2);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}211`), 'html', res2.html);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}212`), 'html', res2.xml1);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}213`), 'html', res2.xml2);
    //
    //     let formattedHtml = await service.task.base.getOssData(task.appKey, taskId, 'formatted.html');
    //     formattedHtml = await service.task.base.changeHtmlChapter(formattedHtml, index, body, level);
    //
    //     const json2 = await service.task.base.convert2Json(task.appKey, taskId, formattedHtml);
    //
    //     await service.task.base.setOssData(task.appKey, taskId, 'formatted.html', formattedHtml);
    //     await service.task.base.setOssData(task.appKey, taskId, 'formatted.internal.json', json2);
    //     await service.task.base.setOssData(task.appKey, taskId, 'formatted.json', cleanJsonNodes(json2));
    //
    //     /*
    //      * const formattedDocKey = service.task.base.getOssKey(task.appKey, taskId, 'formatted.docx');
    //      * await service.oss.convertHtmlToDocAndUpload(formattedDocKey, formattedHtml, true);
    //      */
    //   }
    // });

    ctx.body = {
      status: 0,
      data: {},
    };
  }

  @validate({ taskId: 'number' })
  public async refreshChapter() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { userId } = ctx.data;
    const { taskId, specialOperator } = ctx.input as {
      taskId: number;
      specialOperator?: 'project_check' | 'admin_check';
    };

    const task = await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status === statuses.reviewing && task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId);
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }
    await service.task.base.update({ generateStatus: 0 }, { where: { taskId } });
    await service.task.base.pushToGenerateImages({
      ...ctx.input,
      userId,
      type: 'refreshChapter',
    });
    // const allImageIds: string[] = [];
    // for (const { imageId } of service.image.sortImageByFile(images as any[])) {
    //   allImageIds.push(imageId);
    // }
    //
    // const { appKey } = task;
    //
    // const htmlArr = await service.task.base.refreshChapter({
    //   allImageIds,
    //   appKey,
    // });
    // if (!htmlArr) {
    //   return ctx.body = baseError.dataNotExistError('处理失败');
    // }
    // await Promise.all(allImageIds.map(async(imageId, i) => {
    //   const html = htmlArr[i];
    //   if (html) {
    //     await service.image.setOssData(appKey, imageId, 'html', html);
    //   }
    // }));
    //
    // const htmls = await service.task.base.combineAndUploadByImageIds(task.appKey, task.taskId, allImageIds);
    // const json = await service.task.base.convert2Json(task.appKey, task.taskId, htmls);
    // const errorStat = errorNodeStat(json);
    // await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    // await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    // await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });
    //
    // const { mongo } = app;
    // ctx.runInBackground(async() => {
    //   await mongo.db.collection('error_node_stat').insertOne({
    //     taskId,
    //     userId,
    //     action: 'regenerate',
    //     timestamp: new Date().getTime(),
    //     ...errorStat,
    //   });
    //   if (task.status === statuses.reviewed) {
    //     const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
    //     const res1 = await service.oss.convertHtmlToDocAndUpload(docKey, htmls, true);
    //     const res2 = await service.oss.convertHtmlToDocAndUpload(docKey, htmls, true);
    //     if (res1.html.length !== res2.html.length || res1.xml1.length !== res2.xml1.length || res1.xml2.length !== res2.xml2.length) {
    //       service.task.base.update(
    //         {
    //           errorInfo: res1.html.length !== res2.html.length && 'html长度不相等' || res1.xml1.length !== res2.xml1.length && 'xml1长度不相等' || res1.xml2.length !== res2.xml2.length && 'xml2长度不相等' || '',
    //           status: statuses.error,
    //         },
    //         { where: { taskId } }
    //       );
    //     }
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}111`), 'html', res1.html);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}112`), 'html', res1.xml1);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}113`), 'html', res1.xml2);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}211`), 'html', res2.html);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}212`), 'html', res2.xml1);
    //     await service.task.base.setOssData(task.appKey, Number(`${taskId}213`), 'html', res2.xml2);
    //   }
    // });

    ctx.body = {
      status: 0,
      data: {},
    };
  }

  @validate({ taskId: 'number' })
  public async getChapterStatus() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId }, attributes: ['appKey', 'generateStatus'] });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const json = await service.task.base.getOssData(task.appKey, taskId, 'internal.json');
    const errorNodeData = errorNodeStat(json);

    ctx.body = {
      status: 0,
      data: { status: task.generateStatus,errorNodeCount: errorNodeData.count },
    };
  }

  @validate({
    taskId: 'number',
    specialOperator: {
      type: 'enum',
      values: ['project_check', 'admin_check'],
      required: false,
    },
  })
  public async regenerate() {
    const { ctx, service, app } = this;
    const { mongo } = app;
    const { statuses } = service.task.base;
    const { taskId, specialOperator } = ctx.input as {
      taskId: number;
      specialOperator?: 'project_check' | 'admin_check';
    };
    const { userId } = ctx.data;
    const [task, images] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, disabled: false },
        attributes: ['reviewed', 'imageId', 'filename', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const allImageIds: string[] = [];
    for (const { reviewed, imageId } of service.image.sortImageByFile(images as any[])) {
      if (!reviewed) {
        return ctx.body = baseError.dataNotExistError('无法提交的任务状态, 部分图片未审核完成');
      }
      allImageIds.push(imageId);
    }
    const isSpecial = specialOperator && (
      specialOperator === 'project_check' ?
        await this.isProjectUser(userId, task.bookId) :
        await this.isAdminUser(userId)
    );

    if (task.status === statuses.reviewing && task.reviewUserId !== userId || (task.status === statuses.reviewed || task.status === statuses.operatAdmin) && !isSpecial) {
      return ctx.body = baseError.permissionError('无权限操作');
    }
    if (task.status === statuses.operatAdmin && task.operatAdminUserId !== userId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }
    const html = await service.task.base.combineAndUploadByImageIds(task.appKey, task.taskId, allImageIds);
    const json = await service.task.base.convert2Json(task.appKey, task.taskId, html);
    const errorStat = errorNodeStat(json);

    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'json');

    await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });

    ctx.runInBackground(async() => {
      await mongo.db.collection('error_node_stat').insertOne({
        taskId,
        userId,
        action: 'regenerate',
        timestamp: new Date().getTime(),
        ...errorStat,
      });
    });

    ctx.body = {
      status: 0,
      data: {
        json,
        errorNodeCount: errorStat.count,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async getJson() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const json = await service.task.base.getOssData(task.appKey, task.taskId, 'json');
    ctx.body = {
      status: 0,
      data: json,
    };
  }

  public async getJsonUrls() {
    const { ctx, service } = this;
    const { taskId, bookId } = ctx.input as { taskId: string; bookId: number };
    const { statuses } = this.service.task.base;
    if (!taskId && !bookId) {
      ctx.body = baseError.paramsError('参数 taskId、bookId 二选一');
      return;
    }
    const whereOpt: any = {};
    if (taskId) {
      const taskIds = typeof taskId === 'number' ? [taskId] : taskId.split(',');
      whereOpt.taskId = taskIds.length > 1 ? { $in: taskIds } : taskIds[0];
    }
    let isNewFlow = false;
    this.logger.info(`getJsonUrls bookId: ${bookId} taskId: ${taskId}`);
    if (bookId) {
      whereOpt.bookId = bookId;
      const book = await this.service.book.getOne({ where: { id: bookId } });
      if (book) {
        const projectMeta = await this.service.project.meta.getMetas({ projectId: book.projectId });
        if (projectMeta && projectMeta.isNewFlow) {
          isNewFlow = true;
          this.logger.info(`getJsonUrls bookId: ${bookId} isNewFlow: ${isNewFlow}`);
        }
      }
    }
    let tasks = await this.service.task.base.getAll({
      where: whereOpt,
      attributes: ['taskId', 'appKey', 'status', 'taskName', 'errorNodeCount', 'reviewUserId', 'resourceType', 'operatAdminUserId', 'mergedTaskId'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    let mergedTasks;
    let isV3;
    let originTask;
    // 只展示原始任务数据
    if (isNewFlow) {
      tasks = tasks.filter((task) => task.mergedTaskId);
      mergedTasks = tasks.filter((task) => !task.mergedTaskId);
      this.logger.info(`getJsonUrls bookId: ${bookId} mergedTasks: ${mergedTasks.length}`);
    }
    if (!isNewFlow && !bookId && taskId) {
      isV3 = await service.task.base.isV3(Number(taskId));
      // 如果 task 没有 mergedTaskId，说明是标注任务，需要返回标注任务的 task
      // console.log('isV3', isV3, '说明是标注任务', tasks[0].mergedTaskId);
      if (isV3 && !tasks[0].mergedTaskId) {
        tasks = await service.task.base.getAll({ where: { mergedTaskId: taskId }, order: [['mergedOrder', 'ASC']] });
        originTask = await service.task.base.getOne({ where: { taskId } });
      }
    }
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const hasJson = [statuses.reviewing, statuses.reviewed, statuses.operatAdmin].includes(task.status);
      if (hasJson ||
        (isNewFlow && mergedTasks && mergedTasks.every((t) => [statuses.reviewing, statuses.reviewed, statuses.operatAdmin].includes(t.status))) ||
        (isV3 && originTask && [statuses.reviewing, statuses.reviewed, statuses.operatAdmin].includes(originTask.status))) {
        task.jsonUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'json', true);
        task.internalJsonUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'internal.json', true);
        task.htmlUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'html', true);
      }
      task.baseUrl = service.task.base.getOssBaseUrl(task.appKey, task.taskId);
      const meta = metaDict[task.taskId];
      const file = fileDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      if (meta) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json');
      }
    });
    ctx.body = {
      status: 0,
      data: tasks,
    };
  }
}
