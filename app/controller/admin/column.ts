/**
 * @file 分栏标注
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';

export default class TaskColumnController extends Controller {

  @validate({ imageId: 'string' })
  public async getOneById() {
    const { service, ctx } = this;
    const { imageId } = ctx.input;
    const image = await service.image.getOne({ where: { imageId, disabled: false } });
    if (!image) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    const book = await service.book.getOne({ where: { id: image.bookId } });
    if (!book) {
      return ctx.body = baseError.dataNotExistError('数据异常');
    }
    const resData = {
      userId: 0,
      username: '',
      url: service.image.getUrl(image.appKey, imageId, 'jpg', false),
      multiple: Boolean(image.multiple), // 是否多栏
      result: image.columnResult ? JSON.stringify(image.columnResult) : null,
    };
    if (image.taskId) {
      const task = await service.task.base.getOne({
        where: { taskId: image.taskId },
        attributes: ['rerun', 'rerunProcessUserId', 'preprocessUserId'],
      });
      if (!task) {
        return ctx.body = baseError.dataNotExistError('数据异常');
      }
      const processUserIdKey = task.rerun ? 'rerunProcessUserId' : 'preprocessUserId';
      const [{ nickname }] = await service.user.search([task[processUserIdKey]!]);
      resData.userId = task[processUserIdKey]!;
      resData.username = nickname;
    }
    ctx.body = {
      status: 0,
      data: resData,
    };
  }

  @validate({ taskId: 'number' })
  public async restore() {
    // 重新人工划块
    const { service, ctx } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId }, attributes: ['status', 'rerun'] });
    if (!task || task.status >= statuses.unmarked) {
      return ctx.body = baseError.dataNotExistError('任务不存在或处于无法重置的状态');
    }
    await service.task.base.restoreColumn(taskId, task.rerun);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.columnTypes.restore.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async restart() {
    // 重跑机器划块
    const { service, ctx, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId }, attributes: ['status', 'rerun'] });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (![
      statuses.columnAutoProcessed,
      statuses.columnProcessed,
      statuses.columnProcessing,
      statuses.unmarked,
      statuses.ocrProcessing
    ].includes(task.status)) {
      return ctx.body = baseError.dataNotExistError('任务处于无法重置的状态');
    }
    await app.model.transaction(async(transaction) => {
      await service.task.base.update(
        { status: statuses.columnQueue, [task.rerun ? 'rerunProcessUserId' : 'preprocessUserId']: 0 },
        { transaction, where: { taskId } }
      );
      await service.image.update(
        { preprocessed: false, disabled: false },
        {
          transaction,
          where: task.rerun
            ? { taskId, rerun: task.rerun }
            : { taskId },
        }
      );
      await service.image.delete(
        {
          transaction,
          where: task.rerun
            ? { taskId, originalId: { $not: '' }, rerun: true }
            : { taskId, originalId: { $not: '' } },
        }
      );
      if (statuses.unmarked === task.status) {
        await service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction });
      }
    });
    try {
      // 重跑划块 1. 重置redis
      await service.task.base.resetTaskConfig(taskId, 'imageColumnProcessor');
    } catch (e) {
      this.logger.info(`column.restart taskId:${taskId} 重置 imageColumnProcessor redis失败 e:${e}`);
    }

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.columnTypes.restart.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', afterColumn: { type: 'boolean', required: false, default: false } })
  public async skipColumn() {
    // 跳过划块流程 -> 直接到标注，这里走新算法、新流程
    // 修改 task meta 的 imageHtmlVersion 为 'v2'
    const { service, ctx, logger } = this;
    const { taskId, afterColumn } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const meta = await service.task.meta.getMetas({ taskId });
    if (!meta) {
      return ctx.body = baseError.dataNotExistError('任务 meta 不存在');
    }
    await service.task.meta.setMetas([taskId], {
      ...meta,
      imageHtmlVersion: 'v2',
    });
    logger.info(`skipColumn taskId:${taskId} afterColumn:${afterColumn}`);
    // 获取所有 image
    ctx.runInBackground(async() => {
      try {
        let images = await service.image.getAll({where: {taskId}});
        // 先把图片分为原图和分栏后的图
        const originalImages = images.filter((image) => !image.originalId);
        const columnImages = images.filter((image) => image.originalId);
        if (columnImages.length && afterColumn) {
          // 找到 originalImages 里的对应的图，删掉，把 columnImages 里的图加到 originalImages 里
          for (const columnImage of columnImages) {
            const originalImage = originalImages.find((image) => image.imageId === columnImage.originalId);
            if (originalImage) {
              originalImages.splice(originalImages.indexOf(originalImage), 1);
            }
            originalImages.push(columnImage);
          }
          images = service.image.sortImageByFile(originalImages);
        } else {
          images = images.filter((image) => !image.originalId);
          // 更新图片，把 disabled 设置为 false, columnImages 的 disabled 设置为 true
          await Promise.all(originalImages.map((image) =>
            service.image.update({ disabled: false }, { where: { imageId: image.imageId } })
          ));
          await Promise.all(columnImages.map((image) =>
            service.image.update({ disabled: true }, { where: { imageId: image.imageId } })
          ));
        }
        const imageUrls = images.map((item) => this.service.image.getUrl(task.appKey, item.imageId, 'jpg', false, false));
        await this.service.rbs.initRBSQueue({
          task_id: task!.taskId.toString(),
          task_type: 'image2html',
          task_info: {
            task_id: task!.taskId.toString(),
            subject: task!.subject,
            image_urls: imageUrls,
            get_task_parameters: [
              'task_id',
              'subject',
              'image_urls'
            ],
            callback_extras: [
              'task_id',
              'subject',
              'image_urls'
            ],
            run_type: 'common',
            callback_url: 'http://xdoc.open.hexinedu.com/api/open/task/updateV2',
            // callback_url: 'https://3489-223-70-85-96.ngrok-free.app/api/open/task/updateV2',
            push_time: new Date().getTime(),
            timestamp: new Date().getTime(),
          },
        });
        await service.task.base.update({ status: service.task.base.statuses.ocrProcessing }, { where: { taskId } });
      } catch (e) {
        logger.info(`skipColumn taskId:${taskId} error:${e}`);
        await service.task.base.update({ status: service.task.base.statuses.error }, { where: { taskId } });
      }
    });

    return ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async revoke() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId }, attributes: ['status', 'rerun'] });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.columnProcessing) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    const processUserIdKey = task.rerun ? 'rerunProcessUserId' : 'preprocessUserId';
    await Promise.all([
      service.task.base.update({ status: statuses.columnAutoProcessed, [processUserIdKey]: 0 }, { where: { taskId } }),
      service.image.update(
        { preprocessed: false },
        {
          where: task.rerun
            ? { taskId, rerun: task.rerun }
            : { taskId },
        }
      )
    ]);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.columnTypes.revoke.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: { type: 'number', required: false },
    bookId: { type: 'number', required: false },
    rerun: { type: 'boolean', required: false, default: false },
    taskType: {
      type: 'array',
      itemType: 'number',
      required: false,
    },
  })
  public async apply() {
    const { service, ctx, logger } = this;
    const { taskId, bookId, rerun, taskType } = ctx.input;
    if (!taskId && !bookId) {
      return ctx.body = baseError.paramsError('taskId, bookId 二选一');
    }
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    let task;
    if (taskId) {
      const whereOpt: any = { taskId, status: statuses.columnAutoProcessed, rerun: Boolean(rerun) };
      if (taskType) {
        whereOpt.taskType = taskType;
      }
      task = await service.task.base.getOne({
        where: whereOpt,
        attributes: ['taskId', 'rerun'],
      });
    } else {
      const whereOpt: any = { bookId, status: statuses.columnAutoProcessed, rerun: false };
      if (taskType) {
        whereOpt.taskType = taskType;
      }
      const tasks = await service.task.base.getList({
        where: whereOpt,
        page: 1,
        pageSize: 20,
        attributes: ['taskId', 'rerun'],
      });
      if (tasks.length) {
        task = tasks[Math.floor(Math.random() * tasks.length)];
      }
    }
    if (!task) {
      return ctx.body = baseError.dataNotExistError('暂无可申领的任务');
    }
    const appliedTaskId = task.taskId;
    const processUserIdKey = task.rerun ? 'rerunProcessUserId' : 'preprocessUserId';
    const [affectedRows] = await service.task.base.update(
      {
        [processUserIdKey]: userId,
        status: statuses.columnProcessing,
      },
      {
        where: {
          taskId: appliedTaskId,
          status: statuses.columnAutoProcessed,
          [processUserIdKey]: 0,
        },
      }
    );
    if (!affectedRows) {
      return ctx.body = baseError.dataAlreadyExistError('任务已被申领');
    }
    logger.info(`task ${taskId} status changed : ${statuses.columnProcessing}`);
    const { status } = await service.task.base.getOne({ where: { taskId: appliedTaskId } }) as any;
    logger.info(`after affectedRows : ${affectedRows}, appliedTaskId : ${appliedTaskId}, status : ${status}`);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId: appliedTaskId,
        type: service.task.history.columnTypes.apply.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0, data: { taskId: appliedTaskId } };
  }

  @validate({ taskId: 'number' })
  public async quit() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['status', 'rerun', 'rerunProcessUserId', 'preprocessUserId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.columnProcessing) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    const processUserIdKey = task.rerun ? 'rerunProcessUserId' : 'preprocessUserId';
    if (task[processUserIdKey] !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人标注，无权限');
    }
    await service.task.base.update({
      [processUserIdKey]: 0,
      status: statuses.columnAutoProcessed,
    }, { where: { taskId } });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.columnTypes.quit.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async getAllByTaskId() {
    const { ctx, service } = this;
    const { taskId, showAll } = ctx.input;
    const whereOpt = { taskId } as any;
    if (!showAll) {
      whereOpt.preprocessed = false;
    }
    const task = await service.task.base.getOne({ where: { taskId }, attributes: ['rerun'] });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.rerun) {
      whereOpt.rerun = true;
    }
    const images = await service.image.getAll({
      where: whereOpt,
      attributes: ['imageId', 'multiple', 'columnResult', 'latexResult', 'appKey', 'filename', 'taskOrder', 'sourceId'],
    });
    ctx.body = {
      status: 0,
      data: service.image.sortImageByFile(images!).map((item: any) => {
        item.multiple = Boolean(item.multiple);
        item.result = (item.columnResult ? JSON.parse(item.columnResult) : []).concat(item.latexResult ? JSON.parse(item.latexResult) : []);
        item.url = service.image.getUrl(item.appKey, item.imageId, 'jpg', false);
        delete item.columnResult;
        delete item.latexResult;
        return item;
      }),
    };
  }

  @validate({
    imageId: 'string',
    multiple: 'boolean',
    data: 'string',
    costTime: 'number?',
  })
  public async save() {
    const { ctx, service, logger } = this;
    const { imageId, costTime } = ctx.input;
    const { userId } = ctx.data;
    let { data } = ctx.input;
    const { statuses } = service.task.base;
    logger.info(`get ${imageId} columnResult : ${data}`);
    try {
      data = JSON.parse(data);
    } catch (e) {
      return ctx.body = baseError.paramsError('无效的标注结果');
    }
    const columnResult: any[] = [];
    const latexResult: any[] = [];
    data.forEach((item) => {
      if (!item || !item.type) {
        return;
      }
      if (!['paragraph', '$par', '$pac', 'heading'].includes(item.type)) {
        latexResult.push(item);
      } else {
        columnResult.push(item);
      }
    });
    const image = await service.image.getOne({ where: { imageId, disabled: false } });
    if (!image || !image.taskId) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const task = await service.task.base.getOne({ where: { taskId: image.taskId } });
    if (!task || task.status !== statuses.columnProcessing) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    const processUserIdKey = task.rerun ? 'rerunProcessUserId' : 'preprocessUserId';
    if (task[processUserIdKey] !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人标注，无权限');
    }
    await service.column.relatedUpdate({
      columnResult,
      latexResult,
      imageId,
      taskId: task.taskId,
      bookId: task.bookId,
      taskOrder: image.taskOrder,
      rerun: task.rerun!,
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        costTime: costTime || 0,
        taskId: image.taskId,
        type: service.task.history.columnTypes.save.id,
        data: JSON.stringify({ imageId }),
      });
    });
    ctx.body = { status: 0 };
  }

  public async getPendingBooks() {
    const { ctx, service } = this;
    const { key, appKey, notest } = ctx.input;
    const { hexinApps } = service.appl;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const pendingBookIds = await service.book.getPreprocessingBooks();
    const whereOpt = { id: pendingBookIds } as any;
    if (key) {
      whereOpt.bookName = { $like: `%${key}%` };
    }
    if (appKey) {
      whereOpt.appKey = appKey;
    } else if (notest) {
      whereOpt.appKey = { $not: hexinApps };
    }
    const [books, count] = await Promise.all([
      service.book.getList({
        page,
        pageSize,
        where: whereOpt,
        attributes: ['id', 'type', 'bookName', 'taskCount'],
      }),
      service.book.count({ where: whereOpt })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        books,
      },
    };
  }

  public async getPendingTasks() {
    const { ctx, service } = this;
    const { key, appKey, notest, rerun, taskType, isOutAdmin } = ctx.input;
    const { hexinApps } = service.appl;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const { statuses } = service.task.base;
    const whereOpt = { status: statuses.columnAutoProcessed } as any;
    if (/^[0-9]+$/.test(key)) {
      whereOpt.taskId = Number(key);
    } else if (key && !isOutAdmin) {
      whereOpt.taskName = { $like: `%${key}%` };
    }
    if (appKey) {
      whereOpt.appKey = appKey;
    } else if (notest) {
      whereOpt.appKey = { $not: hexinApps };
    }
    if (isOutAdmin) { whereOpt.key = key; }
    whereOpt.rerun = Boolean(rerun);
    if (taskType) {
      whereOpt.taskType = typeof taskType === 'number' ? taskType : taskType.split(',');
    }
    const [count, tasks] = await Promise.all(!isOutAdmin ? [
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ] : [
      service.task.base.exactCount({ where: whereOpt }),
      service.task.base.getExactList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  public async getOwnTasks() {
    const { ctx, service } = this;
    const { key, rerun, taskType, isOutAdmin } = ctx.input;
    const { userId } = ctx.data;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const { statuses } = service.task.base;
    const processUserIdKey = rerun ? 'rerunProcessUserId' : 'preprocessUserId';
    const whereOpt = { status: statuses.columnProcessing, [processUserIdKey]: userId } as any;
    if (/^[0-9]+$/.test(key)) {
      whereOpt.taskId = Number(key);
    } else if (key) {
      whereOpt.taskName = { $like: `%${key}%` };
    }
    if (taskType) {
      whereOpt.taskType = typeof taskType === 'number' ? taskType : taskType.split(',');
    }
    const [count, tasks] = await Promise.all(!isOutAdmin ? [
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ] : [
      service.task.base.exactCount({ where: whereOpt }),
      service.task.base.getExactList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }
}
