/**
 * @file 客户控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import * as sha1 from 'sha1';
import { IClientMetas } from '../../model/clientMeta';
import { Instance as TClient } from '../../model/client';

type TClientItem = TClient & {
  importantPriorityTime: number;
  normalPriorityTime: number;
};

interface IClientConfig {
  importantPriorityTime?: number;
  normalPriorityTime?: number;
}

export default class ClientController extends Controller {

  private randomStr(len: number) {
    const str1 = Math.random().toString(36).substr(2);
    const str2 = Math.random().toString(36).substr(2);
    return sha1(`${str1}:${Number(new Date())}:${str2}`).substr(0, len);
  }

  @validate({
    phone: {
      type: 'string',
      max: 32,
    },
    clientName: {
      type: 'string',
      max: 64,
    },
  })
  public async create() {
    const { ctx, service } = this;
    const { phone, clientName } = ctx.input;
    const password = this.randomStr(6);
    const userId = await service.user.create(phone, password);
    if (!userId) {
      return ctx.body = baseError.dataAlreadyExistError('用户已经存在');
    }
    await service.client.base.create({
      clientName,
      userId,
      phone,
      isActive: true,
      createUserId: ctx.data.userId,
    });
    ctx.body = {
      status: 0,
      data: {
        userId,
        phone,
        clientName,
        password,
      },
    };
  }

  public async getList() {
    const { ctx, service } = this;
    const { key } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {} as any;
    if (key) {
      whereOpt.clientName = { $like: `%${key}%` };
    }
    const [count, clients] = await Promise.all([
      service.client.base.count({ where: whereOpt }),
      service.client.base.getList({ page, pageSize, where: whereOpt })
    ]);
    const rclients: TClientItem[] = clients.map((item: TClient) => {
      const { config } = item;
      const rconfig: IClientConfig = JSON.parse(config || '{}');
      return {
        ...item,
        importantPriorityTime: rconfig.importantPriorityTime ?? 24,
        normalPriorityTime: rconfig.normalPriorityTime ?? 48,
      };
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        clients: rclients,
      },
    };
  }

  @validate({ userId: 'number' })
  public async disable() {
    const { ctx, service } = this;
    const { userId } = ctx.input;
    const client = await service.client.base.getOne({ where: { userId } });
    if (!client) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.client.base.update({ isActive: false }, { where: { userId } });
    ctx.body = { status: 0 };
  }

  @validate({ userId: 'number' })
  public async enable() {
    const { ctx, service } = this;
    const { userId } = ctx.input;
    const client = await service.client.base.getOne({ where: { userId } });
    if (!client) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.client.base.update({ isActive: true }, { where: { userId } });
    ctx.body = { status: 0 };
  }

  @validate({ userId: 'number' })
  public async delete() {
    const { ctx, service } = this;
    const { userId } = ctx.input;
    const client = await service.client.base.getOne({ where: { userId } });
    if (!client) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    await service.client.base.delete({ where: { userId } });
    ctx.body = { status: 0 };
  }

  @validate({
    userId: 'number',
    password: {
      type: 'string',
      min: 6,
      max: 32,
      required: false,
    },
    clientName: {
      type: 'string',
      max: 64,
      required: false,
    },
  })
  public async update() {
    const { ctx, service } = this;
    const { userId, password, clientName } = ctx.input;
    const client = await service.client.base.getOne({
      where: { userId },
      attributes: ['phone'],
    });
    if (!password && !clientName) {
      return ctx.body = baseError.paramsError('缺少参数');
    }
    if (!client) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }
    if (clientName) {
      await service.client.base.update({ clientName }, { where: { userId } });
    }
    if (password) {
      await service.user.resetPassword(client.phone, password);
    }
    ctx.body = { status: 0 };
  }

  metaRules: { [key in keyof IClientMetas]: object | string } = {
    exportDocx: 'boolean?',
    exportJson: 'boolean?',
    markJson: 'boolean?',
    api7dayDoc: 'boolean?',
    api7dayDocFlow: 'boolean?',
    api7dayDocSeparate: 'boolean?',
  };

  @validate({
    userId: 'number',
    data: {
      type: 'object',
      rule: this.metaRules,
    },
    // @todo： 字段待修改；
    importantPriorityTime: 'number?',
    normalPriorityTime: 'number?',
  })
  public async updateConfig() {
    const { ctx, service } = this;
    const { userId, data, importantPriorityTime, normalPriorityTime } = ctx.input;

    const exists = await service.client.base.exists({ where: { userId } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }

    const client = await service.client.base.getOne({ where: { userId } });
    const config = JSON.parse(client?.config || '{}');
    config.importantPriorityTime = importantPriorityTime;
    config.normalPriorityTime = normalPriorityTime;

    await service.client.meta.setMetas([userId], data);
    await service.client.base.update({ config: JSON.stringify(config) }, { where: { userId } });

    ctx.body = { status: 0 };
  }

  @validate({ userId: 'number' })
  public async getConfig() {
    const { ctx, service } = this;
    const { userId } = ctx.input;

    const exists = await service.client.base.exists({ where: { userId } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户不存在');
    }

    const metas = await service.client.meta.getMetas({ userId });
    const data = service.client.meta.setDefaultMetas(metas);

    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({path: 'string'})
  public async getOssAuth() {
    const { ctx, service } = this;
    const {path} = ctx.input;
    const auth = service.oss.getUploadToken(path);
    ctx.body = {
      status: 0,
      data: auth,
    };
  }
}
