/**
 * @file 书本控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import * as sequelize from 'sequelize';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class BookController extends Controller {

  @validate({ bookId: 'number' })
  public async getOneById() {
    const { service, ctx } = this;
    const { bookId } = ctx.input;
    const book = await service.book.getRelateOne({ where: { id: bookId } });
    if (!book) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    ctx.body = {
      status: 0,
      data: book,
    };
  }

  @validate({ appKey: 'string', baseDir: 'string?' })
  public async getUploadToken() {
    const { service, ctx } = this;
    const { appKey, baseDir = 'image' } = ctx.input;
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }
    ctx.body = {
      status: 0,
      data: service.oss.getUploadToken(`open/${appKey}/${baseDir}/`),
    };
  }

  @validate({ imageId: 'string', bookId: 'number', filename: 'string' })
  public async upload() {
    const { service, ctx } = this;
    const { imageId, bookId, filename } = ctx.input;
    if (isNaN(service.image.getFileOrder(filename))) {
      return ctx.body = baseError.paramsError('文件名必须是带数字，可排序的jpg or png 图片');
    }
    const [book, images] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.image.getAll({
        where: { bookId },
        attributes: ['disabled', 'taskId', 'filename'],
      })
    ]);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('书本任务不存在');
    }
    for (const image of images!) {
      if (image.taskId > 0 && !image.disabled) {
        return ctx.body = baseError.dataAlreadyExistError('分组已存在，不能继续传图');
      }
      if (image.filename === filename) {
        return ctx.body = baseError.dataAlreadyExistError('文件已存在，请勿重复上传');
      }
    }
    await service.sourceImage.create({
      imageId,
      bookId,
      filename,
      taskId: 0,
      taskOrder: 0,
      appKey: book.appKey,
      status: 0,
      result: '',
      info: '',
    });
    ctx.body = { status: 0 };
  }

  @validate({ bookId: 'number' })
  public async completeUpload() {
    const { service, ctx, app } = this;
    const { bookId } = ctx.input;
    const { statuses } = service.task.base;
    let taskImageCount = 10;
    const [book, images] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.sourceImage.getAll({
        where: { bookId },
        attributes: ['taskId', 'filename', 'id', 'imageId', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!book || !images || !images.length) {
      return ctx.body = baseError.dataNotExistError('书本任务不存在');
    }
    // 如果是目录书，所有的图片放在一个任务里面
    if (book.type === service.book.types.catalog) {
      taskImageCount = images.length;
    }
    if (images[0] && images[0].taskId > 0) {
      return ctx.body = baseError.dataAlreadyExistError('分组已存在，不能重复确认');
    }
    const idSize = Math.floor(images.length / taskImageCount) + (images.length % taskImageCount ? 1 : 0);
    const taskIds = await service.task.base.getBatchIds(idSize);
    if (!taskIds.length) {
      return ctx.body = baseError.serverError('系统异常，任务创建失败');
    }
    // 为了更新 taskId
    const newImages = service.image.sortImageByFile(images).map((item, index) => {
      const taskId = taskIds[Math.floor(index / taskImageCount)];
      return { taskId, imageId: item.imageId as string, appKey: book.appKey };
    });
    const tasks = taskIds.map((taskId, index) => {
      let imageCount;
      if (book.type === service.book.types.catalog || index !== taskIds.length - 1) {
        imageCount = taskImageCount;
      } else {
        imageCount = images.length % taskImageCount;
      }
      return {
        taskId,
        bookId,
        imageCount,
        bookOrder: index + 1,
        appKey: book.appKey,
        subject: book.subject,
        callbackUrl: '',
        extra: '',
        taskName: `${book.bookName}-${index + 1}`,
        isCallback: false,
        callbackError: false,
        status: statuses.preCropCheck,
        errorInfo: '',
        markUserId: 0,
        reviewUserId: 0,
        splitUserId: 0,
        preprocessUserId: 0,
        operatAdminUserId: 0,
        catalog: '',
        isTest: false,
      };
    });

    const projectMeta = await service.project.meta.getMetas({ projectId: book.projectId });

    await app.model.transaction(async(transaction) => {
      if (book.type === service.book.types.catalog) {
        await service.image.update({ taskId: taskIds[0] }, { where: { bookId } });
      } else {
        await service.sourceImage.bulkCreate(newImages as any, { updateOnDuplicate: ['taskId'] });
      }
      await service.task.base.bulkCreate(tasks, { transaction });
      await service.task.meta.setMetas(tasks.map((t) => t.taskId), projectMeta, { transaction });
      await service.book.update({ taskCount: taskIds.length }, { where: { id: bookId } });
    });

    await service.task.base.pushPreCropCheck(tasks, { type: projectMeta.imageHtmlVersion || 'v1' });

    // 将项目的 oss meta.json 同步到每个任务
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      const sourceKey = service.project.base.getOssKey(book.appKey, book.projectId, 'meta.json');
      for (const task of tasks) {
        const key = service.task.base.getOssKey(task.appKey, task.taskId, 'meta.json');
        await ossClient.copy(key, sourceKey);
      }
    });

    ctx.body = { status: 0 };
  }

  @validate({
    bookId: 'number',
    taskName: {
      type: 'string',
      max: 64,
    },
    images: {
      type: 'array',
      itemType: 'object',
      rule: {
        id: 'string',
        name: 'string',
      },
      min: 1,
      max: 250,
    },
  })
  public async subTaskCreate() {
    const { service, ctx } = this;
    const { bookId, taskName, images } = ctx.input as {
      bookId: number;
      taskName: string;
      images: { id: string, name: string }[];
    };
    const bookStatuses = service.book.statuses;
    const [book, bookOrder] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.task.base.model.max('bookOrder', { where: { bookId } })
    ]);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('图书不存在');
    }
    const projectMeta = await service.project.meta.getMetas({ projectId: book.projectId });
    const taskId = await service.task.base.relatedCreate({
      appKey: book.appKey,
      subject: book.subject,
      images,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName,
      bookId,
      bookOrder: (bookOrder || 0) + 1,
    }, projectMeta);
    await service.task.meta.setMetas([taskId], projectMeta);
    await service.book.update({
      status: bookStatuses.processing,
      taskCount: sequelize.literal('taskCount + 1'),
    }, { where: { id: bookId } });

    // 将项目的 oss meta.json 同步到每个任务
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      const sourceKey = service.project.base.getOssKey(book.appKey, book.projectId, 'meta.json');
      const key = service.task.base.getOssKey(book.appKey, taskId, 'meta.json');
      await ossClient.copy(key, sourceKey);
    });

    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  @validate({ bookId: 'number' })
  public async getCatalog() {
    const { service, ctx } = this;
    const { bookId } = ctx.input;
    const book = await service.book.getOne({ where: { id: bookId } });
    if (!book) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    const bookCatalog = await service.book.getCatalog(bookId);
    if (!bookCatalog.length) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    ctx.body = {
      status: 0,
      data: bookCatalog,
    };
  }

  @validate({ bookId: 'number' })
  public async getAllImages() {
    const { service, ctx } = this;
    const { bookId } = ctx.input;
    const [book, imgaes] = await Promise.all([
      service.book.getOne({ where: { id: bookId }, attributes: ['appKey'] }),
      service.sourceImage.getAll({
        where: { bookId },
        attributes: ['imageId', 'filename', 'appKey'],
      })
    ]);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    ctx.body = {
      status: 0,
      data: imgaes!.map((item) => {
        const url = service.image.getUrl(item.appKey, item.imageId, 'jpg', true);
        Object.assign(item, { url });
        return item;
      }),
    };
  }

}
