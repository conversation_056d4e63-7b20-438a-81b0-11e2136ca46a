/**
 * @file 管理员授权码
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';

export default class AuthCodeController extends Controller {
  @validate({
    refresh: 'boolean?',
    renew: 'boolean?',
    expire: {
      type: 'number',
      required: false,
      min: 30,
      max: 2 * 60 * 60, // 最长 2 小时
    },
  })
  public async getCode() {
    const { ctx, service } = this;
    const { refresh, renew, expire } = ctx.input as { refresh?: boolean; renew?: boolean; expire?: number };
    const { userId } = ctx.data;

    let data: { code: string; ttl: number } | undefined;
    if (refresh) {
      data = await service.authCode.generateCode(userId, expire);
    } else {
      data = await service.authCode.getCode(userId);
      if (!data && renew) {
        data = await service.authCode.generateCode(userId, expire);
      }
    }

    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({ code: 'string' })
  public async authCode() {
    const { ctx, service } = this;
    const { code } = ctx.input as { code: string };
    const userId = await service.authCode.authCode(code);
    ctx.body = {
      status: 0,
      data: userId,
    };
  }
}
