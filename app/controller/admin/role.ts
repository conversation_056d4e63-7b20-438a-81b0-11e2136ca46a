/**
 * @file 角色控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';

export default class RoleController extends Controller {

  public async getAll() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    this.logger.info('[test] loggger time1');
    const data = await service.themis.getRolesByUserId(userId);
    this.logger.info('[test] loggger time2');
    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({
    // roleId: roleId[],
    roleName: 'string?', // roleName: string[]
    // userId: number[]
  })
  public async getAllUsersByRole() {
    const { ctx, service } = this;
    const { roleId, roleName, userId } = ctx.input;
    if (roleId && roleName || !roleId && !roleName) {
      return ctx.body = baseError.paramsError('参数 roleId, roleName 二选一');
    }
    const users = await service.themis.getUsersByRole({ roleId, roleName, userId });
    ctx.body = {
      status: 0,
      data: users,
    };

  }
}
