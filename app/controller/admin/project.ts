/**
 * @file 项目控制
 * <AUTHOR>
 */

'use strict';

import {Controller} from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import {separateJsonByChapter} from '../../core/utils/jsonHelper';
import * as _ from 'lodash';
import {cleanFilename} from '../../core/utils/helper';
import {IProjectMetas} from '../../model/projectMeta';
import {ETaskType} from '../../model/task';

export default class ProjectController extends Controller {

  @validate({
    projectName: 'string',
    appKey: 'string',
    subject: ['math', 'chinese', 'en-math'],
    hasCatalog: 'boolean',
    workOrder: 'string?',
    meta: {
      type: 'object',
      rule: {
        bookName: 'string',
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
  })
  public async create() {
    const {
      service,
      ctx,
      app,
    } = this;
    const {userId} = ctx.data;
    const {
      appKey,
      projectName,
      hasCatalog,
      subject,
      meta,
      workOrder,
    } = ctx.input;
    const exists = await service.appl.exists({where: {appKey}});
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }

    const project = await app.model.transaction(async(transaction) => {
      const project = await service.project.base.create(
        {
          userId,
          projectName,
          appKey,
          subject,
          errorInfo: '',
          workOrder: workOrder || '',
          status: service.project.base.statuses.processing,
          reviewUserId: 0,
          startReviewTime: null,
          endReviewTime: null,
          markJsonStatus: 0,
        },
        {transaction}
      );
      if (workOrder) meta.workOrder = workOrder;
      await service.project.meta.setMetas([project.id], meta, {transaction});
      const books = [{
        appKey,
        subject,
        bookName: `${projectName}-试题`,
        projectId: project.id,
        status: service.book.statuses.processing,
        errorInfo: '',
        type: service.book.types.question,
        reviewUserId: 0,
        taskCount: 0,
      }];
      if (hasCatalog) {
        books.unshift({
          appKey,
          subject,
          bookName: `${projectName}-目录`,
          projectId: project.id,
          status: service.book.statuses.processing,
          errorInfo: '',
          type: service.book.types.catalog,
          reviewUserId: 0,
          taskCount: 0,
        });
      }
      await service.book.bulkCreate(books, {transaction});
      // 更新项目 oss meta.json
      ctx.runInBackground(async() => {
        await service.project.base.setOssMeta(appKey, project.id, meta);
      });
      return project;
    });

    ctx.body = {
      status: 0,
      data: {projectId: project.id},
    };
  }

  @validate({
    projectName: 'string?',
    projectId: 'number',
    workOrder: 'string?',
    meta: {
      type: 'object',
      rule: {
        bookName: 'string?',
        type: 'number?',
        textType: 'number?',
        columnType: 'number?',
        collectType: 'number?',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
  })
  public async update() {
    const {
      service,
      ctx,
      app,
    } = this;
    const {statuses} = service.task.base;
    const {userId} = ctx.data;
    let {
      projectId,
      projectName,
      workOrder,
      meta,
    } = ctx.input as {
      workOrder?: string;
      projectId: number;
      projectName?: string;
      meta: Partial<IProjectMetas>;
    };
    if (workOrder) meta.workOrder = workOrder;
    projectName = projectName && projectName.trim();
    const [project, existMeta, books] = await Promise.all([
      service.project.base.getOne({
        where: {id: projectId},
        attributes: ['appKey', 'projectName'],
      }),
      service.project.meta.getMetas({projectId}),
      service.book.getAll({
        where: {projectId},
        attributes: ['id'],
      })
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    await service.project.base.update(
      {
        projectName,
        workOrder: workOrder || '',
      },
      {where: {id: projectId}}
    );

    const tasks = await service.task.base.getAll({
      where: {bookId: books.map((b) => b.id)},
      attributes: ['taskId', 'appKey'],
    });
    const taskIds = tasks.map((t) => t.taskId);

    meta = {...existMeta, ...meta};

    let quitTaskIds: number[] | undefined = undefined;
    if (existMeta.subject !== meta.subject) {
      const quitTasks = await service.task.base.getAll(
        {
          where: {
            taskId: taskIds,
            taskType: ETaskType.limit,
            status: {$lte: statuses.marking},
            markUserId: {$gt: 0},
          },
          attributes: ['taskId'],
        }
      );
      quitTaskIds = quitTasks.map((t) => t.taskId);
    }

    await app.model.transaction(async(transaction) => {
      await service.project.meta.setMetas([projectId], meta, {transaction});
      await service.task.meta.setMetas(taskIds, meta, {transaction});
      if (existMeta.subject !== meta.subject) {
        await service.task.base.update(
          {
            markUserId: 0,
            startMarkTime: null,
          },
          {
            transaction,
            where: {
              taskId: taskIds,
              taskType: ETaskType.limit,
              status: {$lte: statuses.marking},
              markUserId: {$gt: 0},
            },
          }
        );
        await service.task.base.update(
          {status: statuses.unmarked},
          {
            transaction,
            where: {
              taskId: taskIds,
              taskType: ETaskType.limit,
              status: statuses.marking,
            },
          }
        );
      }
    });

    // 更新项目和每个任务的 oss meta.json
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      await service.project.base.setOssMeta(project.appKey, projectId, meta);
      const sourceKey = service.project.base.getOssKey(project.appKey, projectId, 'meta.json');
      for (const task of tasks) {
        const key = service.task.base.getOssKey(task.appKey, task.taskId, 'meta.json');
        await ossClient.copy(key, sourceKey);
      }
      if (quitTaskIds && quitTaskIds.length) {
        await service.task.history.bulkCreate(quitTaskIds.map((taskId) => {
          return {
            taskId,
            userId,
            type: service.task.history.markTypes.quitOnChangeSubject.id,
            data: `${existMeta.subject}>${meta.subject}`,
            costTime: 0,
          };
        }));
      }
    });

    ctx.body = {status: 0};
  }

  public async getListByAdmin() {
    const {
      ctx,
      service,
    } = this;
    const {
      key,
      status,
      isOutAdmin,
      oneByOne,
      filterPriority7,
    } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;

    const baseFilter = {} as any;
    if (status >= -1) {
      baseFilter.status = status;
    }
    baseFilter.key = key;

    const whereOptForListAndItsCount = { ...baseFilter };
    if (filterPriority7) {
      whereOptForListAndItsCount.priority = 7;
    }

    const whereOptForPriority7Count = {
      ...baseFilter,
      priority: 7,
    };

    const promises: any[] = [];

    promises.push(
      !isOutAdmin
        ? service.project.base.relatedCount({ where: whereOptForListAndItsCount })
        : service.project.base.exactCount({ where: whereOptForListAndItsCount })
    );

    let listWhereClause;
    if (!isOutAdmin) {
      listWhereClause = { ...whereOptForListAndItsCount, oneByOne };
    } else {
      listWhereClause = whereOptForListAndItsCount;
    }

    promises.push(
      !isOutAdmin
        ? service.project.base.getRelatedList({
            page,
            pageSize,
            where: listWhereClause,
          })
        : service.project.base.getExactList({
            page,
            pageSize,
            where: listWhereClause,
          })
    );

    promises.push(
      !isOutAdmin
        ? service.project.base.relatedCount({ where: whereOptForPriority7Count })
        : service.project.base.exactCount({ where: whereOptForPriority7Count })
    );

    const [count, projects, priorityCount] = await Promise.all(promises);

    const metaDict = await service.project.meta.getMetasDict({ projectId: { $in: projects.map((p) => p.id) } });
    projects.forEach((project: any) => {
      const meta = metaDict[project.id];
      project.meta = meta || {};
      if (meta) {
        project.metaJsonUrl = service.project.base.getUrl(project.appKey, project.id, 'meta.json');
      }
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        priorityCount,
        projects,
      },
    };
  }

  @validate({
    ids: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async getUrls() {
    const {
      service,
      ctx,
    } = this;
    const {ids} = ctx.input as { ids: number[] };
    const projects = await service.project.base.getList({
      where: {
        id: ids,
        status: service.project.base.statuses.reviewed,
      },
      attributes: ['id', 'appKey', 'projectName'],
    });
    const data = projects.map((project) => {
      return {
        id: project.id,
        name: project.projectName,
        json: service.project.base.getUrl(project.appKey, project.id, 'json', true),
        docx: service.project.base.getUrl(project.appKey, project.id, 'docx', true),
        docxZip: service.project.base.getUrl(project.appKey, project.id, 'docx.zip', true),
      };
    });
    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({projectId: 'number'})
  public async getOneById() {
    const {
      service,
      ctx,
    } = this;
    const {projectId} = ctx.input;
    const [project, meta] = await Promise.all([
      service.project.base.getRelateOne({where: {id: projectId}}),
      service.project.meta.getMetas({projectId})
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    (project as any).meta = meta;
    if (Object.keys(meta).length) {
      (project as any).metaJsonUrl = service.project.base.getUrl(project.appKey, projectId, 'meta.json');
    }
    ctx.body = {
      status: 0,
      data: project,
    };
  }

  @validate({taskId: 'number'})
  public async getOneByTaskId() {
    const {
      service,
      ctx,
    } = this;
    const {taskId} = ctx.input;
    const task = await service.task.base.getOne({where: {taskId}});
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const bookId = task.bookId;
    if (!bookId) {
      return ctx.body = baseError.dataNotExistError('任务不在任何图书内');
    }
    const book = await service.book.getOne({where: {id: bookId}});
    if (!book) {
      return ctx.body = baseError.dataNotExistError('任务对应的图书不存在');
    }
    const projectId = book.projectId;
    if (!projectId) {
      return ctx.body = baseError.dataNotExistError('图书不在任何项目内');
    }
    const project = await service.project.base.getOne({where: {id: projectId}});
    if (!project) {
      return ctx.body = baseError.dataNotExistError('任务对应的项目不存在');
    }
    return ctx.body = {
      status: 0,
      data: project,
    };
  }

  @validate({
    projectId: 'number',
    level: 'number',
    type: {
      type: 'enum',
      values: ['official', 'mark.official'],
      required: false,
    },
  })
  public async separate() {
    const {
      service,
      ctx,
    } = this;
    const {
      projectId,
      level,
      type = 'official',
    } = ctx.input;
    if (level < 0) {
      return ctx.body = baseError.paramsError(`projectId: ${projectId}错误；level:${level}; `);
    }
    const project: any = await service.project.base.getOne({where: {id: projectId}});
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const projectJson = await service.project.base.getOssData(
      project.appKey,
      project.id,
      type === 'official' ? 'official.json' : 'mark.official.json'
    );
    if (!projectJson) {
      return ctx.body = baseError.dataNotExistError('json文件不存在');
    }

    const separateResult = separateJsonByChapter(projectJson, level);
    if (_.isEmpty(separateResult)) {
      return ctx.body = baseError.dataNotExistError(`不存在${level}级目录`);
    }
    // 移除文件名中的无效字符
    const data = separateResult.map((item, index) => {
      const path = item.path.map(cleanFilename);
      return {
        fileName: `./${project.projectName}/${path.join('/')}_${index + 1}.json`,
        data: JSON.stringify([item.node]),
      };
    });
    ctx.attachment(`${cleanFilename(project.projectName)}.zip`);
    ctx.set('Content-Type', 'application/octet-stream');
    const archive = await service.archiver.archiveByOption({
      data,
      compressionType: 'zip',
    });
    ctx.body = archive;
  }

  @validate({projectId: 'number'})
  public async delete() {
    const {
      service,
      ctx,
    } = this;
    const {userId} = ctx.data;
    const {projectId} = ctx.input;
    const exists = await service.project.base.exists({where: {id: projectId}});
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    await service.project.base.relatedDelete(projectId);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.baseTypes.del,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = {status: 0};
  }

  @validate({projectId: 'number'})
  public async revoke() {
    const {
      service,
      ctx,
    } = this;
    const {projectId} = ctx.input;
    const {userId} = ctx.data;
    const {statuses} = service.project.base;
    const project = await service.project.base.getOne({
      where: {id: projectId},
      attributes: ['status'],
    });
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (![statuses.reviewing, statuses.reviewed, statuses.publishing].includes(project.status)) {
      return ctx.body = baseError.dataNotExistError('无法撤回的项目状态');
    }
    await service.project.base.update({
      status: statuses.unreviewed,
      startReviewTime: null,
      endReviewTime: null,
      reviewUserId: 0,
    }, {where: {id: projectId}});
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.baseTypes.revoke,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = {status: 0};
  }

  @validate({
    projectId: 'number',
    pageCountInfos: 'array',
  })
  public async updatePageCountInfos() {
    // 只有项目审核人可以修改， 并且项目是发布状态
    const {
      service,
      ctx,
    } = this;
    const {
      projectId,
      pageCountInfos,
    } = ctx.input;
    const {userId} = ctx.data;
    const {statuses} = service.project.base;
    const project = await service.project.base.getOne({where: {id: projectId}});
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    this.logger.info('updatePageCountInfos', projectId, project.status, project.reviewUserId);
    if (![statuses.reviewing, statuses.reviewed, statuses.unreviewed, statuses.publishing].includes(project.status)) {
      return ctx.body = baseError.dataNotExistError('项目不是发布状态');
    }
    if (!project.reviewUserId || project.reviewUserId !== userId) {
      this.logger.info('updatePageCountInfos: ' + project.reviewUserId + userId);
      return ctx.body = baseError.dataNotExistError('你没有修改权限！');
    }
    this.logger.info('updatePageCountInfos', projectId, pageCountInfos);
    await service.project.meta.setMetas([projectId], {pageCountInfos});
    // 记录操作日志
    ctx.runInBackground(async() => {
      // 同步到工单
      if (project.workOrder) {
        await service.project.meta.syncMetaInfoToWorkSheet(project.workOrder, pageCountInfos);
        this.logger.info('syncMetaInfoToWorkSheet', project.id, project.workOrder, pageCountInfos);
      }

      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.baseTypes.updatePageCountInfos,
        data: JSON.stringify(pageCountInfos),
        costTime: 0,
      });
    });
    ctx.body = {status: 0};
  }

  async getCatalogBookProjects() {
    const {
      ctx,
      service,
    } = this;
    const {key} = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    let projectIds: number[] | undefined;
    if (key) {
      if (typeof key === 'number' || /^\d+$/.test(key)) {
        projectIds = [Number(key)];
      } else {
        const projects = await service.project.base.getAll({
          where: {projectName: {$like: `%${key}%`}},
          attributes: ['id'],
        });
        if (!projects.length) {
          return ctx.body = {
            status: 0,
            data: {
              list: [],
              count: 0,
              page,
              pageSize,
            },
          };
        }
        projectIds = projects.map((p) => p.id);
      }
    }
    const whereOpt = {
      type: service.book.types.catalog,
      status: service.book.statuses.reviewed,
    } as Record<string, any>;
    if (projectIds) whereOpt.projectId = projectIds;
    const [count, books] = await Promise.all([
      service.book.count({where: whereOpt}),
      service.book.getList({
        page,
        pageSize,
        where: whereOpt,
        attributes: ['id', 'projectId'],
      })
    ]);
    if (!count) {
      return ctx.body = {
        status: 0,
        data: {
          list: [],
          count: 0,
          page,
          pageSize,
        },
      };
    }
    const projects = await service.project.base.getAll({
      where: {id: books.map((b) => b.projectId)},
      attributes: ['id', 'projectName'],
    });
    const map = new Map(projects.map((p) => [p.id, p.projectName]));
    const list = books.map((book) => {
      return {
        ...book,
        id: undefined,
        bookId: book.id,
        projectName: map.get(book.projectId),
      };
    });
    ctx.body = {
      status: 0,
      data: {
        list,
        count,
        page,
        pageSize,
      },
    };
  }

  @validate({projectId: 'number'})
  async getProjectStat() {
    const {
      ctx,
      service,
    } = this;
    const {projectId} = ctx.input;
    const project = await service.project.base.getOne({where: {id: projectId}});
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const book = await service.book.getOne({where: {projectId}});
    if (!book) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const tasks = await service.task.base.getAll({where: {bookId: [book.id]}});
    if (!tasks.length) {
      return ctx.body = {
        status: 0,
        data: {},
      };
    }
    const taskMap = tasks.map((task) => ({
      taskId: task.taskId,
      taskName: task.taskName,
      appKey: task.appKey,
      char: 0,
      taskType: 'v1',
      charPercent: 0,
    }));
    for (const task of taskMap) {
      // 获取总字符数
      const stat = await this.service.task.stat.getDesignatedHTMLStatData(task.appKey, task.taskId, 'p');
      // 练习 -> v2 , 讲解 -> v1
      const char = stat.char;
      // 根据 meta 的 path 来判断
      const taskType = task.taskName.includes('练习') ? 'v2' : 'v1';
      task.char = taskType === 'v2' ? 1.5 * char : char;
      task.taskType = taskType;
      task.charPercent = 0;
    }
    // 算出各自 v1, v2总的
    const taskMapGroup: any = _.groupBy(taskMap, (task) => task.taskType);
    for (const taskType of Object.keys(taskMapGroup)) {
      const _tasks = taskMapGroup[taskType];
      const char = _tasks.reduce((sum, task) => sum + task.char, 0);
      taskMapGroup[taskType] = {
        char,
        tasks: taskMap.filter((task) => task.taskType === taskType).map((v) => {
          // 算出占比
          v.charPercent = v.char / char;
          return v;
        }),
      };
    }

    ctx.body = {
      status: 0,
      data: {taskMap},
    };
  }

  @validate({projectId: 'number'})
  async html2word() {
    const {
      ctx,
      service,
      config,
    } = this;
    const {projectId} = ctx.input;
    const project = await service.project.base.getOne({where: {id: projectId}});
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const book = await service.book.getOne({where: {projectId}});
    if (!book) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const projectHtml = await service.project.base.getOssData(project.appKey, projectId, 'html');
    if (!projectHtml) {
      return ctx.body = baseError.dataNotExistError('项目html不存在');
    }
    try {
      // 清空原 url
      await service.project.base.update({docxUrl: ''}, {where: {id: projectId}});
      const {data} = await service.rbs.initRBSQueue({
        task_id: project.id.toString(),
        task_type: 'html_docx',
        task_info: {
          projectId: project.id,
          jobName: 'generateDocxFromHtml',
          queue: true,
          params: {
            html: projectHtml,
            file_name: project.projectName + '.docx',
            config: {disable_choice_layout: false},
            taskId: project.id,
            output: 'url',
          },
          callback: {
            // url: 'http://qnsizsyi51t6.ngrok.xiaomiqiu123.top/api/open/project/word/callback',
            url: `${config.ngrok.callbackUrl}/api/open/project/word/callback`,
            method: 'POST',
            returnProps: ['projectId', 'status', 'result', 'reason'],
            successStatus: {
              'prop': 'status',
              'value': [0],
            },
          },
          push_time: new Date().getTime(),
          timestamp: new Date().getTime(),
        },
      });
      this.logger.info('html2word', data);
      ctx.body = {status: 0};
    } catch (e) {
      ctx.body = baseError.dataNotExistError('项目html转换失败');
    }
  }

}
