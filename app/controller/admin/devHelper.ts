/**
 * @file 审核控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { convertHtmlToDocx, convertHtmlToKatexHtml } from '../../core/utils/htmlToDocx';
import { htmlToJson } from '../../core/utils/htmlToJsonV4';
import { htmlToJsonV2 } from '../../core/utils/htmlToJsonV2';
import { statHtml, statJson } from '../../core/utils/taskStat';
import { json2docx7day, json2docxZip7day } from '../../core/utils/json2docx7day';
import axios from 'axios';

export default class TaskDevHelperController extends Controller {

  @validate({
    taskId: 'number?',
    projectId: 'number?',
  })
  public async generateDoc() {
    // html 转 docx。仅生成并下载docx，方便查看docx效果
    const { ctx, service } = this;
    const { taskId, projectId } = ctx.input as {
      taskId?: number;
      projectId?: number;
    };
    if (!taskId && !projectId || taskId && projectId) {
      return ctx.body = baseError.paramsError('taskId, projectId 二选一');
    }
    let html: string;
    if (taskId) {
      const [task, images] = await Promise.all([
        service.task.base.getOne({
          where: { taskId },
          attributes: ['appKey'],
        }),
        service.image.getAll({
          where: { taskId, disabled: false },
          attributes: ['imageId', 'filename', 'taskOrder'],
          order: [['id', 'ASC']],
        })
      ]);
      if (!task) {
        return ctx.body = baseError.dataNotExistError('图片任务不存在');
      }
      const allImageIds: string[] = service.image.sortImageByFile(images || []).map(({ imageId }) => imageId);
      html = await service.task.base.combineByImageIds(task.appKey, allImageIds, taskId);
    } else if (projectId) {
      const { types } = service.book;
      const [project, book] = await Promise.all([
        service.project.base.getOne({
          where: { id: projectId },
          attributes: ['id'],
        }),
        service.book.getOne({
          where: { projectId, type: types.question },
          attributes: ['id'],
        })
      ]);
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      if (!book) {
        return ctx.body = baseError.dataNotExistError('试题图书不存在');
      }
      const tasks = await service.task.base.getAll({
        where: { bookId: book.id },
        attributes: ['taskId', 'appKey'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      });
      const htmls = await Promise.all(tasks.map(async(task) => {
        try {
          const html = await service.task.base.getOssData(task.appKey, task.taskId, 'html');
          return html;
        } catch (e) {
          return '';
        }
      }));
      html = htmls.join('');
    }
    const { buffer } = await convertHtmlToDocx(html!);
    ctx.attachment(`${taskId || projectId}.docx`);
    ctx.body = buffer;
  }

  @validate({
    taskId: 'number?',
    projectId: 'number?',
    mode: {
      type: 'enum',
      values: ['flow', 'flow_body_answer', 'separate'],
      required: false,
      default: 'flow_body_answer',
    },
  })
  public async generateDoc7day() {
    // 七天客户 json转docx。仅生成并下载docx，方便查看docx效果
    const { ctx, service } = this;
    const { taskId, projectId, mode } = ctx.input as {
      taskId?: number;
      projectId?: number;
      mode: 'flow' | 'flow_body_answer' | 'separate'
    };
    if (!taskId && !projectId || taskId && projectId) {
      return ctx.body = baseError.paramsError('taskId, projectId 二选一');
    }
    let json;
    if (taskId) {
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['appKey'],
      });
      if (!task) {
        return ctx.body = baseError.dataNotExistError('图片任务不存在');
      }
      json = await service.task.base.getOssData(task.appKey, taskId, 'json');
    } else if (projectId) {
      const project = await service.project.base.getOne({
        where: { id: projectId },
        attributes: ['appKey'],
      });
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      json = await service.project.base.getOssData(project.appKey, projectId, 'official.json');
    }
    if (!json) {
      return ctx.body = baseError.dataNotExistError('尚未生成json');
    }
    const buffer = mode === 'separate' ? await json2docxZip7day(json) : await json2docx7day(json, mode);
    ctx.attachment(`${taskId || projectId}.${mode === 'separate' ? 'zip' : 'docx'}`);
    ctx.body = buffer;
  }

  @validate({
    taskId: 'number',
    type: ['machine', 'mark', 'review'], // 统计类型
    stash: 'boolean?', // 是否将现有html暂存，默认 false
  })
  public async triggerStat() {
    const { ctx, service } = this;
    const { taskId, type, stash } = ctx.input;
    const [task, imageCount] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.count({ where: { taskId, disabled: false } })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    const ext = ({
      machine: 'machine.html',
      mark: 'mark.html',
      review: 'html',
    })[type];
    let html = await service.task.base.getOssData(task.appKey, taskId, ext);
    if (!html && stash) {
      if (type === 'machine') {
        const images = await service.image.getAll({
          where: { taskId, disabled: false },
          attributes: ['imageId', 'filename', 'taskOrder'],
          order: [['id', 'ASC']],
        });
        const imageIds: string[] = service.image.sortImageByFile(images).map(({ imageId }) => {
          return imageId;
        });
        html = await service.task.stat.stashMachineHtml(task.appKey, taskId, imageIds);
      } else if (type === 'mark') {
        await service.task.stat.stashMarkHtml(task.appKey, taskId);
        html = await service.task.base.getOssData(task.appKey, taskId, ext);
      }
    }
    if (!html) {
      return ctx.body = baseError.dataNotExistError('html不存在');
    }
    let userId;
    if (type === 'mark') {
      userId = task.markUserId;
    } else if (type === 'review') {
      userId = task.reviewUserId;
    }
    let costTime;
    if (userId) {
      const history = await service.task.history.getOne({
        where: { taskId, userId },
        attributes: ['costTime'],
        order: [['id', 'DESC']],
      });
      if (history) {
        costTime = history.costTime;
      }
    }
    await service.task.stat.newStat({
      html,
      taskId,
      type,
      appKey: task.appKey,
      subject: task.subject,
      resourceType: task.resourceType!,
      imageCount,
      cost: costTime,
      userId,
    }, /data-label=/.test(html));
    ctx.body = { status: 0 };
  }

  @validate({ html: 'string' })
  public async htmlToKatexHtml() {
    const { ctx } = this;
    const { html } = ctx.input;
    const khtml = await convertHtmlToKatexHtml(html);
    ctx.body = {
      status: 0,
      data: khtml,
    };
  }

  @validate({
    html: 'string',
    version: {
      type: 'enum',
      values: ['old', 'new'],
      required: false,
    },
    isOfficial: 'boolean?',
  })
  public async htmlToJson() {
    const { ctx } = this;
    if (ctx.input.isOfficial == null) ctx.input.isOfficial = true;
    const { html, version, isOfficial } = ctx.input;
    const json = version === 'old' ? htmlToJsonV2({ html, isOfficial }) : htmlToJson({ html, isOfficial });
    ctx.body = {
      status: 0,
      data: json,
    };
  }

  @validate({
    html: 'string',
    version: {
      type: 'enum',
      values: ['old', 'new'],
      required: false,
    },
  })
  public async statHtml() {
    const { ctx } = this;
    const { html, version } = ctx.input;
    const json = version === 'old' ? htmlToJsonV2({ html }) : htmlToJson({ html });
    const stat = {
      html: statHtml(json),
      json: statJson(json),
    };
    ctx.body = {
      status: 0,
      data: stat,
    };
  }

  /**
   * LaTeX字符串解析
   * 将字符串数组发送到LaTeX解析服务，并返回解析结果
   */
  public async latexParser() {
    const { ctx } = this;
    const latexs = ctx.input.latexArray;

    // 检查输入数据是否为数组
    if (!Array.isArray(latexs)) {
      return ctx.body = baseError.paramsError('输入数据必须是字符串数组');
    }

    try {
      // 调用外部LaTeX解析服务
      const response = await axios.post(`${this.config.content.api}/open/formatLatex`, { latexs });

      // 返回解析结果
      ctx.body = response.data;
    } catch (error) {
      // 处理错误情况
      ctx.logger.error('LaTeX解析服务调用失败', error);
      return ctx.body = baseError.serverError('LaTeX解析服务调用失败');
    }
  }
}
