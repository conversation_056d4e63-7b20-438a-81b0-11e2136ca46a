/**
 * @file 限时任务人员学科权限配置
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';

export default class TimeLimitUserSubjectController extends Controller {

  @validate({
    data: {
      type: 'array',
      itemType: 'object',
      rule: {
        userId: 'number',
        subject: {
          type: 'string',
          allowEmpty: true,
        },
      },
      min: 1,
    },
  })
  public async create() {
    const { ctx, service } = this;
    const { data } = ctx.input as {
      data: { userId: number; subject: string }[];
    };

    await service.timeLimitUserSubject.bulkCreate(
      data,
      { ignoreDuplicates: true }
    );

    ctx.body = { status: 0 };
  }

  @validate({
    id: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async remove() {
    const { ctx, service } = this;
    const { id } = ctx.input as {
      id: number[];
    };

    const [affectedCount] = await service.timeLimitUserSubject.delete({ where: { id } });

    ctx.body = {
      data: affectedCount,
      status: 0,
    };
  }

  @validate({
    // userId?: number[];
    subject: { // subject?: string[]
      type: 'string',
      required: false,
      allowEmpty: true,
    },
    page: 'number?',
    pageSize: 'number?',
  })
  public async getList() {
    const { ctx, service } = this;
    const { userId, subject, page, pageSize } = ctx.input as {
      userId?: string | number;
      subject?: string;
      page?: number;
      pageSize?: number;
    };

    const userIds = userId ? (typeof userId === 'number' ? [userId] : userId.split(',').map((i) => Number(i))) : undefined;
    const subjects = subject == null ? undefined : subject.split(',');

    const whereOpt: any = {};
    if (userIds) {
      whereOpt.userId = userIds;
    }
    if (subjects) {
      whereOpt.subject = subjects;
    }

    let [list, count] = await Promise.all([
      service.timeLimitUserSubject.getAll({
        where: whereOpt,
        limit: pageSize,
        offset: page && pageSize ? (page - 1) * pageSize : undefined,
        attributes: ['id', 'userId', 'subject'],
      }),
      pageSize ? service.timeLimitUserSubject.count({ where: whereOpt }) : 0
    ]);
    count = count || list.length;

    ctx.body = {
      data: { list, count },
      status: 0,
    };
  }
}
