/**
 * @file fbd 任务
 */

import { Controller } from 'egg';
import * as sequelize from 'sequelize';

import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';
import { ITaskMetas } from '../../../model/taskMeta';
import { ETaskType } from '../../../model/task';

export default class TaskFbdController extends Controller {
  @validate({
    subject: ['chinese', 'math', 'en-math'],
    taskNames: {
      type: 'array',
      itemType: 'string',
      rule: { max: 64 },
      min: 1,
    },
    appKey: 'string',
    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    taskType: {
      type: 'enum',
      values: [0, 1],
      required: false,
    },
    timeLimit: 'number?',
    timeWarning: 'number?',
  })
  public async create() {
    const { ctx, service } = this;
    const { subject, taskNames, priority, appKey, meta, taskType, timeLimit, timeWarning } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskNames: string[];
      priority?: number;
      appKey: string;
      wordIds: string[];
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }
    const taskIds = await service.task.base.fbdCreate({
      appKey,
      subject,
      open: false,
      priority,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskNames,
      taskType,
      timeLimit,
      timeWarning,
    });
    await service.task.meta.setMetas(taskIds, meta);
    ctx.runInBackground(async() => {
      await Promise.all(
        taskIds.map(async(taskId) => {
          await service.task.base.setOssMeta(appKey, taskId, meta);
        })
      );
    });
    ctx.body = {
      status: 0,
      data: { taskIds },
    };
  }

  @validate({
    bookId: 'number',
    taskNames: {
      type: 'array',
      itemType: 'string',
      rule: { max: 64 },
      min: 1,
    },
  })
  public async subTaskCreate() {
    const { service, ctx } = this;
    const { bookId, taskNames } = ctx.input as {
      bookId: number;
      taskNames: string[];
    };
    const bookStatuses = service.book.statuses;
    const [book, bookOrder] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.task.base.model.max('bookOrder', { where: { bookId } })
    ]);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('图书不存在');
    }
    const projectMeta = await service.project.meta.getMetas({ projectId: book.projectId });
    const taskIds = await service.task.base.fbdCreate({
      appKey: book.appKey,
      subject: book.subject,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskNames,
      bookId,
      bookOrder: (bookOrder || 0) + 1,
    });
    await service.task.meta.setMetas(taskIds, projectMeta);
    await service.book.update({
      status: bookStatuses.processing,
      taskCount: sequelize.literal(`taskCount + ${taskIds.length}`),
    }, { where: { id: bookId } });

    // 将项目的 oss meta.json 同步到每个任务
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      const sourceKey = service.project.base.getOssKey(book.appKey, book.projectId, 'meta.json');
      await Promise.all(taskIds.map(async(taskId) => {
        const key = service.task.base.getOssKey(book.appKey, taskId, 'meta.json');
        await ossClient.copy(key, sourceKey);
      }));
    });

    ctx.body = {
      status: 0,
      data: { taskIds },
    };
  }

}
