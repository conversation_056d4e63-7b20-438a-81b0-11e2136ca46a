/**
 * @file 任务控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../../core/decorators/validate';
import { ETaskType } from '../../../model/task';
import baseError from '../../../core/base/baseError';

export default class TaskLimitController extends Controller {

  @validate({
    taskId: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
    timeLimit: {
      type: 'number',
      min: 12 * 60,
      required: false,
    },
    timeWarning: 'number?',
  })
  public async setLimit() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, timeLimit = 24 * 60, timeWarning = 0.5 } = ctx.input as {
      taskId: number[];
      timeLimit?: number;
      timeWarning?: number;
    };
    const { userId } = ctx.data;

    const [affectedCount] = await service.task.base.update(
      { taskType: ETaskType.limit, timeLimit, timeWarning },
      { where: { taskId, taskType: ETaskType.unset, status: { $ne: statuses.reviewed } } }
    );

    ctx.runInBackground(async() => {
      // 记录操作日志
      await service.task.history.bulkCreate(taskId.map((taskId) => {
        return {
          taskId,
          userId,
          type: service.task.history.otherTypes.setTaskType.id,
          data: JSON.stringify({ taskType: ETaskType.limit, timeLimit, timeWarning }),
          costTime: 0,
        };
      }));

      // 更新任务倒计时和预警
      await service.task.base.updateTaskCountdown({ taskId });
    });

    ctx.body = {
      data: affectedCount,
      status: 0,
    };
  }

  @validate({
    taskId: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async unsetLimit() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId } = ctx.input as {
      taskId: number[];
    };
    const { userId } = ctx.data;

    const [affectedCount] = await service.task.base.update(
      { taskType: ETaskType.unset },
      { where: { taskId, taskType: ETaskType.limit, status: { $ne: statuses.reviewed } } }
    );

    ctx.runInBackground(async() => {
      // 记录操作日志
      await service.task.history.bulkCreate(taskId.map((taskId) => {
        return {
          taskId,
          userId,
          type: service.task.history.otherTypes.setTaskType.id,
          data: JSON.stringify({ taskType: ETaskType.unset }),
          costTime: 0,
        };
      }));
    });

    ctx.body = {
      data: affectedCount,
      status: 0,
    };
  }

  async getSubjectList() {
    const { ctx, app, service } = this;
    const { statuses } = service.task.base;
    const sql = `
      select task_meta.value as subject,
        count(task.taskId) as count,
        task.warning

      from task
       left join task_meta
        on task_meta.taskId=task.taskId and task_meta.isDel=0 and task_meta.key='subject'

      where task.isDel=0
       and task.status!=${statuses.reviewed}
       and task.taskType=${ETaskType.limit}

      group by task_meta.value, task.warning

      order by task.countdown
    `;
    const res = await app.model.query(sql, {
      raw: true,
      type: app.model.QueryTypes.SELECT,
    }) as { subject?: string; warning: number; count: number }[];
    const data = [] as { subject: string; total: number; warning: number }[];
    const dataMap = {} as { [subject: string]: { subject: string; total: number; warning: number } };
    res.forEach((re) => {
      const subject: string = re.subject ? JSON.parse(re.subject) : '';
      let item = dataMap[subject];
      if (!item) {
        item = { subject, total: 0, warning: 0 };
        dataMap[subject] = item;
        data.push(item);
      }
      item.total += re.count;
      if (re.warning) {
        item.warning += re.count;
      }
    });

    ctx.body = {
      data,
      status: 0,
    };
  }

  @validate({ subject: 'string?' })
  async getApplList() {
    const { ctx, app, service } = this;
    const { subject } = ctx.input as {
      subject?: string;
    };
    const { statuses } = service.task.base;

    const joinSubjectSql = subject == null
      ? ''
      : ` left join task_meta
      on task_meta.taskId=task.taskId
      and task_meta.isDel=0
      and task_meta.key='subject'
      `;

    const querySubjectSql = subject == null
      ? ''
      : (subject === '' ? ' and (task_meta.value is null or task_meta.value=\'""\')' : ' and task_meta.value=:subject');

    const sql = `
      select
        appl.appName,
        task.appKey,
        task.warning,
        task.rerun,
        count(task.taskId) as count

      from task ${joinSubjectSql},
        application as appl

      where appl.appKey=task.appKey
       and task.isDel=0
       and task.status!=${statuses.reviewed}
       and task.taskType=${ETaskType.limit}
       ${querySubjectSql}

      group by task.appKey, task.warning, task.rerun

      order by task.countdown
    `;

    const res = await app.model.query(sql, {
      raw: true,
      type: app.model.QueryTypes.SELECT,
      replacements: { subject: subject ? JSON.stringify(subject) : undefined },
    }) as {
      appName: string;
      appKey: string;
      warning: boolean;
      rerun: boolean;
      count: number;
    }[];
    const data = [] as {
      appName: string;
      appKey: string;
      warning: number;
      rerun: number;
      total: number;
    }[];
    const dataMap = {} as {
      [appKey: string]: {
        appName: string;
        appKey: string;
        warning: number;
        rerun: number;
        total: number;
      };
    };
    res.forEach((re) => {
      const appKey = re.appKey;
      let item = dataMap[appKey];
      if (!item) {
        item = { appKey, appName: re.appName, warning: 0, rerun: 0, total: 0 };
        dataMap[appKey] = item;
        data.push(item);
      }
      if (re.warning) {
        item.warning += re.count;
      }
      if (re.rerun) {
        item.rerun += re.count;
      }
      item.total += re.count;
    });

    ctx.body = {
      data,
      status: 0,
    };

  }

  @validate({
    subject: 'string?',
    appKey: 'string?',
    page: 'number?',
    pageSize: 'number?',
  })
  async getTaskList() {
    const { ctx, app, service } = this;
    const { subject, appKey, page, pageSize, key } = ctx.input as {
      subject?: string;
      appKey?: string;
      page?: number;
      pageSize?: number;
      key?: string | number;
    };
    const { statuses } = service.task.base;

    const querySubjectSql = subject == null
      ? ''
      : (subject === '' ? ' and (task_meta.value is null or task_meta.value=\'""\')' : ' and task_meta.value=:subject');

    const sql = `
    from task
      left join task_meta
        on task_meta.taskId=task.taskId
        and task_meta.isDel=0
        and task_meta.key='subject'

    where task.isDel=0
     ${key ?
    typeof key === 'string'
      ? 'and task.taskName like :searchString'
      : 'and task.taskId=:searchNumber'
    : ''}
     and task.status!=${statuses.reviewed}
     and task.taskType=${ETaskType.limit}
     ${querySubjectSql}
     ${appKey ? 'and task.appKey=:appKey' : ''}

    order by task.countdown
    `;

    const replacements = {
      searchString: `%${key}%`,
      searchNumber: key,
      subject: subject ? JSON.stringify(subject) : undefined,
      appKey,
    };
    let list: any[];
    let count: number;
    if (pageSize) {
      let countData;
      [list, countData] = await Promise.all([
        app.model.query(
          `select task.*, task.subject as recSubject, task_meta.value as subject
           ${sql}
           limit :limit
           ${page ? 'offset :offset' : ''}
           `,
          {
            raw: true,
            type: app.model.QueryTypes.SELECT,
            replacements: {
              limit: pageSize,
              offset: page && pageSize ? (page - 1) * pageSize : undefined,
              ...replacements,
            },
          }
        ),
        app.model.query(
          `select count(task.id) as count ${sql}`,
          {
            raw: true,
            type: app.model.QueryTypes.SELECT,
            replacements,
          }
        )
      ]);
      count = countData[0].count;
    } else {
      list = await app.model.query(
        `select task.*, task.subject as recSubject, task_meta.value as subject
         ${sql}`,
        {
          raw: true,
          type: app.model.QueryTypes.SELECT,
          replacements,
        }
      );
      count = list.length;
    }

    list.forEach((item) => {
      item.subject = item.subject ? JSON.parse(item.subject) : '';
      this.formatTask(item);
    });

    // 获取用户名
    const userIds = list.map((item) => item.stepUserId).filter((i) => i);
    const userMap = {} as { [userId: string]: string };
    if (userIds.length) {
      const users = await service.user.search(userIds);
      users.forEach(({ userId, nickname }) => {
        userMap[userId] = nickname;
      });
    }

    list.forEach((item) => {
      item.stepUserName = userMap[item.stepUserId];
    });

    if (list.length) {
      const apps = await service.appl.getAllByUc({
        where: { appKey: list.map((item) => item.appKey) },
        attributes: ['appKey', 'appName'],
      });
      const appMap = {} as { [appKey: string]: string };
      apps.forEach(({ appKey, appName }) => {
        appMap[appKey] = appName;
      });
      list.forEach((item) => {
        item.appName = appMap[item.appKey];
      });
    }

    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: list.map((t) => t.taskId) } });
    list.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      task.meta = meta || {};
    });

    ctx.body = {
      data: { list, count },
      status: 0,
    };
  }

  private formatTask(item: any) {
    const { service } = this;
    const { statuses } = service.task.base;
    let stepUserId: number;
    let stepStartTime;
    let stepEndTime;
    let step;
    if (item.rerun) {
      stepUserId = item.rerunProcessUserId;
      step = 'rerun';
    } else if (item.status < statuses.unmarked) {
      stepUserId = item.preprocessUserId;
      step = 'column';
    } else if (item.status < statuses.unreviewed) {
      stepUserId = item.markUserId;
      stepStartTime = item.startMarkTime;
      stepEndTime = item.endMarkTime;
      step = 'mark';
    } else {
      stepUserId = item.reviewUserId;
      stepStartTime = item.startReviewTime;
      stepEndTime = item.endReviewTime;
      step = 'review';
    }
    Object.assign(item, {
      stepUserId: stepUserId === 0 ? undefined : stepUserId,
      stepStartTime,
      stepEndTime,
      step,
    });
    item.taskCode = service.task.base.getTaskCode(item.createTime.getTime(), item.taskId);
  }

  @validate({
    type: {
      type: 'enum',
      values: ['mark', 'review'],
      required: true,
    },
  })
  async getOwnPendingTaskSubjectList() {
    const { ctx, app, service } = this;
    const { statuses } = service.task.base;
    const { type } = ctx.input as { type: 'mark' | 'review' };
    const { userId } = ctx.data as { userId: number };
    const res = await service.timeLimitUserSubject.getAll({
      where: { userId },
      attributes: ['subject'],
    });
    if (!res.length) {
      return ctx.body = { data: [], status: 0 };
    }
    const allSubjects = res.map((re) => re.subject);
    const subjects = allSubjects.filter((i) => Boolean(i)).map((i) => JSON.stringify(i));
    const emptySubject = allSubjects.some((i) => !i);
    const sql = `
      select task_meta.value as subject,
        count(task.taskId) as count

      from task
       left join task_meta
        on task_meta.taskId=task.taskId and task_meta.isDel=0 and task_meta.key='subject'

      where task.isDel=0
       and task.status=${type === 'mark' ? statuses.unmarked : statuses.unreviewed}
       and task.taskType=${ETaskType.limit}
       and (
        ${subjects.length ? 'task_meta.value in (:subjects)' : ''}
        ${subjects.length && emptySubject ? ' or ' : ''}
        ${emptySubject ? 'task_meta.value is null or task_meta.value=\'""\'' : ''}
       )

      group by task_meta.value

      order by task.countdown
    `;
    const data = await app.model.query(sql, {
      raw: true,
      type: app.model.QueryTypes.SELECT,
      replacements: { subjects },
    }) as { subject: string; count: number }[];
    data.forEach((item) => {
      if (!item.subject) {
        item.subject = '';
      }
      item.subject = item.subject ? JSON.parse(item.subject) : '';
    });

    ctx.body = {
      data,
      status: 0,
    };
  }

  @validate({
    type: {
      type: 'enum',
      values: ['mark', 'review'],
      required: true,
    },
    status: {
      type: 'enum',
      values: ['ing', 'finished'],
      required: false,
    },
    page: 'number?',
    pageSize: 'number?',
  })
  async getOwnTaskList() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { type, status = 'ing', page = 1, pageSize = 10 } = ctx.input as {
      type: 'mark' | 'review';
      status?: 'ing' | 'finished';
      page?: number;
      pageSize?: number;
    };
    const { userId } = ctx.data as { userId: number };
    const targetStatus = type === 'mark' ? statuses.unreviewed : statuses.reviewed;
    const where = {
      [type === 'mark' ? 'markUserId' : 'reviewUserId']: userId,
      status: status === 'ing' ? { $lt: targetStatus } : targetStatus,
      taskType: ETaskType.limit,
    };
    const [list, count] = await Promise.all([
      service.task.base.getList({
        where,
        attributes: [
          'taskId', 'createTime', 'taskName', 'imageCount', 'status',
          'startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime',
          'timeLimit', 'timeWarning', 'countdown', 'warning', 'priority', 'resourceType'
        ],
        order: [['countdown', 'asc'], ['taskId', 'desc']],
        page,
        pageSize,
      }),
      service.task.base.count({ where })
    ]);

    const metaMap = await service.task.meta.getMetasDict({
      taskId: list.map((task) => task.taskId),
      key: 'subject',
    });

    list.forEach((task) => {
      const meta = metaMap[task.taskId];
      (task as any).subject = meta && meta.subject || '';
    });

    list.forEach((task) => {
      this.formatTask(task);
    });

    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: list.map((t) => t.taskId) } });
    list.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      task.meta = meta || {};
    });

    ctx.body = {
      data: { list, count },
      status: 0,
    };

  }

  @validate({
    type: {
      type: 'enum',
      values: ['mark', 'review'],
      required: true,
    },
    subject: 'string?',
    taskCode: 'string?',
    taskId: 'number?',
  })
  async apply() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { type, subject, taskCode, taskId } = ctx.input as {
      type: 'mark' | 'review';
      subject?: string;
      taskCode?: string;
      taskId?: number;
    };
    if (subject == null && !taskCode && !taskId || subject != null && taskCode && taskId) {
      return ctx.body = baseError.paramsError('subject, taskCode 参数二选一');
    }
    const { userId } = ctx.data as { userId: number };

    const userIdKey = type === 'mark' ? 'markUserId' : 'reviewUserId';
    const startTimeKey = type === 'mark' ? 'startMarkTime' : 'startReviewTime';
    const status = type === 'mark' ? statuses.unmarked : statuses.unreviewed;
    const updateStatus = type === 'mark' ? statuses.marking : statuses.reviewing;

    const existsTask = await service.task.base.getOne({
      where: {
        status: updateStatus,
        [userIdKey]: userId,
      },
      attributes: ['createTime', 'taskId', 'taskType'],
    });
    if (existsTask) {
      const createTime = existsTask.createTime as any as Date;
      const code = service.task.base.getTaskCode(createTime.getTime(), existsTask.taskId);
      const typeName = ({
        [ETaskType.unset]: '未入池',
        [ETaskType.limit]: '限时',
        [ETaskType.plan]: '生产计划',
      })[existsTask.taskType!];
      return ctx.body = baseError.dataAlreadyExistError(`已经有正在处理中的${typeName}任务 ${code}`);
    }

    const whereOpt = {
      [userIdKey]: 0,
      status,
      taskType: ETaskType.limit,
    };

    let targetTaskId;
    if (subject != null) {
      const sql = `
        select task.taskId

        from task
          left join task_meta
            on task_meta.taskId=task.taskId and task_meta.isDel=0 and task_meta.key='subject'

        where task.isDel=0
          and task.${userIdKey}=0
          and task.status=${status}
          and task.taskType=${ETaskType.limit}
          ${subject ? 'and task_meta.value=:subject' : 'and (task_meta.value is null or task_meta.value=\'""\')'}

        order by task.countdown

        limit 20
      `;
      const tasks = await app.model.query(sql, {
        raw: true,
        type: app.model.QueryTypes.SELECT,
        replacements: { subject: subject ? JSON.stringify(subject) : undefined },
      });
      const task = tasks[Math.floor(Math.random() * tasks.length)];
      if (task) {
        targetTaskId = task.taskId;
      }
    } else if (taskCode) {
      const task = await service.task.base.getTaskByCode(
        taskCode,
        {
          where: whereOpt,
          attributes: ['taskId'],
        }
      );
      if (task) {
        targetTaskId = task.taskId;
      }
    } else if (taskId) {
      targetTaskId = taskId;
    }

    if (!targetTaskId) {
      return ctx.body = baseError.dataNotExistError('暂无可申领的任务');
    }

    const [affectedRow] = await service.task.base.update(
      {
        status: updateStatus,
        [userIdKey]: userId,
        [startTimeKey]: new Date(),
      },
      { where: { ...whereOpt, taskId: targetTaskId } }
    );
    if (!affectedRow) {
      return ctx.body = baseError.dataAlreadyExistError('任务无法被申领');
    }

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId: targetTaskId,
        userId,
        type: service.task.history[type === 'mark' ? 'markTypes' : 'reviewTypes'].apply.id,
        data: '',
        costTime: 0,
      });
    });

    ctx.body = {
      data: targetTaskId,
      status: 0,
    };

  }

}
