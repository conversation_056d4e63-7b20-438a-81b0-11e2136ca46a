'use strict';

import { Controller } from 'egg';
import validate from '../../../core/decorators/validate';
import baseError from '../../../core/base/baseError';
import { ITaskMetas } from '../../../model/taskMeta';
import { ITaskFiles, ITaskPdfs, ITaskWords } from '../../../model/taskFile';
import { ETaskType, ETaskResourceType } from '../../../model/task';
import { PlanTaskType } from '../../../model/planFlow';
import { PlanTaskStatus } from '../../../model/planTask';
import { errorNodeStat } from '../../../core/utils/jsonHelper';
import { cleanJsonNodes } from '../../../core/utils/htmlToJsonV4';
import { preCleanHtml } from '../../../core/utils/htmlToJsonV4/helper/cleanupNode';

export default class TaskV2<PERSON>ontroller extends Controller {

  private async checkProject(userId: number, bookId: number) {
    const { service } = this;
    const { statuses } = service.project.base;
    if (!bookId) return;
    const book = await service.book.getOne({ where: { id: bookId } });
    if (!book) return;
    const project = await service.project.base.getOne({ where: { id: book.projectId } });
    if (!project) return;
    if (project.status === statuses.reviewing && userId !== project.reviewUserId) {
      const res = await service.user.search([project.reviewUserId]);
      return `${res?.[0]?.nickname || project.reviewUserId}正在审核${project.projectName}，不能修改这个任务`;
    }
    if (project.status === statuses.reviewed) {
      await service.project.base.update({
        status: statuses.unreviewed,
        startReviewTime: null,
        endReviewTime: null,
        reviewUserId: 0,
      }, { where: { id: project.id } });
    }
  }

  private async isProjectUser(userId: number, bookId: number) {
    const { service, logger } = this;
    if (!bookId) {
      logger.error(`【taskV2.isProjectUser: bookeId不存在${bookId}`);
      return false;
    }
    const book = await service.book.getOne({
      where: { id: bookId },
      attributes: ['reviewUserId', 'projectId'],
    });
    if (!book) {
      logger.error(`【taskV2.isProjectUser: book不存在${bookId}`);
      return false;
    }
    if (book.reviewUserId === userId) {
      return true;
    }
    if (!book.projectId) {
      logger.error(`【taskV2.isProjectUser: book.projectId不存在${bookId}`);
      return false;
    }
    const project = await service.project.base.getOne({
      where: { id: book.projectId },
      attributes: ['reviewUserId'],
    });
    return Boolean(project) && project?.reviewUserId === userId;
  }

  private async isAdminUser(userId: number) {
    const { service } = this;
    return await service.themis.hasRoleByUserId(userId, ({ name }) => name === '管理员' || name === '子管理员');
  }

  @validate({
    subject: ['chinese', 'math', 'en-math'],
    taskName: {
      type: 'string',
      max: 64,
    },
    appKey: 'string',
    file: {
      type: 'object',
      rule: {
        words: 'object',
        workOrderWordBasePath: 'object?',
      },
    },
    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',
        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    taskType: {
      type: 'enum',
      values: [0, 1],
      required: false,
    },
    timeLimit: 'number?',
    timeWarning: 'number?',
  })
  public async wordCreate() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { subject, taskName, appKey, file, meta, taskType, timeLimit, timeWarning, priority } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskName: string;
      appKey: string;
      priority?: number;
      file: Partial<ITaskFiles>;
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }
    const taskId = await service.task.taskV2.wordCreate({
      appKey,
      subject,
      priority,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName: taskName || '',
      taskType,
      timeLimit,
      timeWarning,
      meta,
      file,
    });
    await service.task.file.setFiles([taskId], file);
    await service.task.meta.setMetas([taskId], meta);
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(appKey, taskId, meta);
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.wordCreate.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  public async pdfCreate() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { subject, taskName, appKey, file, meta, taskType, timeLimit, timeWarning, priority } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskName: string;
      appKey: string;
      priority?: number;
      file: Partial<ITaskFiles>;
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }

    /*
     * 创建pdf任务  / pdf - filetask / 拿到pdf2image任务id
     * open字段原用于 区别接口是否对外开放， 当前移出该功能
     */
    const taskId = await service.task.taskV2.pdfCreate({
      appKey,
      subject,
      priority,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName: taskName || '',
      taskType,
      timeLimit,
      timeWarning,
      meta,
      file,
    });
    await service.task.file.setFiles([taskId], file);
    await service.task.meta.setMetas([taskId], meta);

    // 加入history
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(appKey, taskId, meta);
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.pdfCreate.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  @validate({
    subject: ['chinese', 'math', 'en-math'],
    taskName: {
      type: 'string',
      max: 64,
    },
    appKey: 'string',
    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    taskType: {
      type: 'enum',
      values: [0, 1],
      required: false,
    },
    timeLimit: 'number?',
    timeWarning: 'number?',
    file: {
      type: 'object',
      rule: {
        zips: 'array',
        pdfs: 'array',
        fbds: 'object',
        workOrderFbdBasePath: 'object?',
      },
    },
  })
  public async fbdCreate() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { subject, taskName, priority, appKey, meta, taskType, timeLimit, timeWarning, file } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskName: string;
      priority?: number;
      appKey: string;
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      file: Partial<ITaskFiles>;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }
    const taskId = await service.task.taskV2.fbdCreate({
      appKey,
      subject,
      open: false,
      priority,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName,
      taskType,
      timeLimit,
      timeWarning,
      meta,
      file,
    });
    await service.task.file.setFiles([taskId], file);
    await service.task.meta.setMetas([taskId], meta);
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(appKey, taskId, meta);
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.fbdCreate.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  @validate({ taskId: 'number' })
  public async getOneById() {
    const { ctx, service } = this;
    const { taskId } = ctx.input as {
      taskId: number;
    };

    const task = await service.task.taskV2.getOneById(taskId);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.bookId) {
      const book = await service.book.getOne({ where: { id: task.bookId } });
      if (book) task.projectId = book.projectId;
    }
    task.pdf = service.task.base.getUrl(task.appKey, taskId, 'pdf');
    task.html = service.task.base.getUrl(task.appKey, taskId, 'html');
    task.baseUrl = service.task.base.getOssBaseUrl(task.appKey, task.taskId);
    const meta = await service.task.meta.getMetas({ taskId });
    const file = await service.task.file.getFiles({ taskId });
    if (file) {
      task.file = file;
    }
    if (meta) {
      task.meta = meta;
      if (Object.keys(meta).length) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, taskId, 'meta.json');
      }
    }
    ctx.body = {
      status: 0,
      data: task,
    };
  }

  @validate({ taskId: 'number' })
  public async markConfirm() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { taskId, costTime } = ctx.input;
    const { userId } = ctx.data;
    const task = await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status !== statuses.marking) {
      return ctx.body = baseError.dataNotExistError('无法提交的任务状态');
    }
    if (task.markUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('当前不是相关标注者，无权限提交');
    }

    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.unreviewed,
            endMarkTime: new Date(),
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.INIT,
        }, { transaction })
      ]);
    });
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.markConfirm.id,
        costTime: costTime || 0,
        data: '',
      });
      const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
      await service.task.stat.stashMarkHtml(task.appKey, taskId);

      // 对比 machine.html 和 html 的字符串差异数量
      const machineHtml = await service.task.base.getOssData(task.appKey, taskId, 'machine.html');
      this.logger.info(`diffCharCount taskid: ${taskId} machineHtml.length: ${machineHtml.length} html.length: ${html.length}`);
      const diffCharCount = html.length - machineHtml.length;
      await service.task.base.update(
        { diffCharCount },
        { where: { taskId } }
      );

      const meta = await service.task.meta.getMetas({ taskId });
      const pdfCount = service.task.meta.calcPdfCount(meta.pdfInfo);
      await service.task.stat.newStat({
        html,
        taskId,
        type: 'mark',
        appKey: task.appKey,
        subject: task.subject,
        resourceType: task.resourceType!,
        imageCount: 0,
        pdfCount,
        cost: costTime || 0,
        userId,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    authCode: 'string?',
    specialOperator: {
      type: 'enum',
      values: ['project_check', 'admin_check'],
      required: false,
    },
  })
  public async reviewConfirm() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { taskId, costTime, authCode, specialOperator } = ctx.input as {
      taskId: number;
      costTime?: number;
      specialOperator?: 'project_check' | 'admin_check',
      authCode?: string;
    };
    let adminUserId: number | undefined;
    if (authCode) {
      adminUserId = await service.authCode.authCode(authCode);
      if (!adminUserId) {
        return ctx.body = baseError.permissionError(`授权码${authCode}无效`);
      }
    }
    const { userId } = ctx.data;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    const isSpecial = specialOperator && (
      specialOperator === 'project_check' ?
        await this.isProjectUser(userId, task.bookId) :
        await this.isAdminUser(userId)
    );
    if (task.status === statuses.reviewing && task.reviewUserId !== userId || (task.status === statuses.reviewed || task.status === statuses.operatAdmin) && !isSpecial) {
      return ctx.body = baseError.permissionError('无权限操作');
    }
    if (task.status === statuses.operatAdmin && task.operatAdminUserId !== userId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }

    const runningTime = Number(new Date());

    const html = await service.task.base.getOssData(task.appKey, taskId, 'html');

    const json = await service.task.base.convert2Json(task.appKey, taskId, html);
    const catalog = service.task.base.getCatalog(task.appKey, taskId, json);
    const errorStat = errorNodeStat(json);
    const isV3 = await service.task.base.isV3(taskId);
    if (errorStat.count && !authCode && !isV3) {
      return ctx.body = baseError.permissionError('存在节点错误时，请使用授权码');
    }
    await service.task.base.setOssData(task.appKey, taskId, 'html', preCleanHtml(html));
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await service.task.base.formateAndUploadDiffFile(task.appKey, taskId, html);

    // 对比 machine.html 和 html 的字符串差异数量
    const machineHtml = await service.task.base.getOssData(task.appKey, taskId, 'machine.html');
    this.logger.info(`diffCharCount taskid: ${taskId} machineHtml.length: ${machineHtml.length} html.length: ${html.length}`);
    const diffCharCount = html.length - machineHtml.length;
    await service.task.base.update(
      { diffCharCount },
      { where: { taskId } }
    );

    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.dataCleaning,
            endReviewTime: new Date(),
            errorNodeCount: errorStat.count,
            catalog: catalog.length ? JSON.stringify(catalog) : '',
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction })
      ]);
    });

    this.app.logRunningTime(runningTime, ` /taskV2/reviewConfirm 数据清洗回调 task:${JSON.stringify(taskId)}`);

    ctx.runInBackground(async() => {
      const runningTime2 = Number(new Date());

      /*
       * const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
       * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
       */

      let errorInfo = '';
      errorInfo = await service.task.base.checkOssData({ appKey: task.appKey, taskId, srcExtension: 'diff.json.html', dstExtension: 'diff.json' });
      if (!errorInfo) errorInfo = await service.task.base.checkOssData({ appKey: task.appKey, taskId, srcExtension: 'diff.docx.html', dstExtension: 'docx' });
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.reviewConfirm.id,
        costTime: costTime || 0,
        data: '',
      });

      // 感觉不需要都清洗一遍，最后发布的时候再全部清洗一下
      await service.subjectScript.runSubjectScript(taskId, true);
      const meta = await service.task.meta.getMetas({ taskId });
      this.logger.info(`${taskId} metas :${JSON.stringify(meta)}`);
      const pdfCount = service.task.meta.calcPdfCount(meta.pdfInfo);
      this.logger.info(`${taskId} pdfCount :${pdfCount}`);
      // 加入待统计列表
      await service.task.stat.push(task.taskId);
      this.logger.info(`${taskId} push`);
      await service.task.stat.newStat({
        html,
        taskId,
        type: specialOperator || 'review',
        appKey: task.appKey,
        subject: task.subject,
        imageCount: 0,
        pdfCount,
        resourceType: task.resourceType!,
        cost: costTime || 0,
        userId,
      });
      this.logger.info(`${taskId} stat`);
      // }

      await this.app.logRunningTime(runningTime2, ` /taskV2/reviewConfirm runningInback task:${JSON.stringify(taskId)}`);
    });

    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    // 当前task内标题的索引
    index: 'number',
    body: 'string?',
    level: 'number?',
  })
  /**
   * @func
   * @desc 修改JSON标题，根据任务内的索引查找到所有图片对应HTML内的标题，然后再转成JSON。
   * @param { number } taskId - 任务ID
   * @param { number } index - 标题在对应任务内的索引
   * @param { ?string } body - 修改对应标题的内容
   * @param { ?number } level - 修改对应标题的内容层级
   */
  public async changeChapter() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const { taskId, index, body, level, specialOperator } = ctx.input as {
      taskId: number;
      index: number;
      body?: string;
      level?: number;
      specialOperator?: 'project_check' | 'admin_check';
    };

    const task = await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status === statuses.reviewing && task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId);
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }

    const { appKey } = task;
    const html = await service.task.taskV2.changeChapter({
      appKey,
      taskId,
      index,
      body,
      level,
    });
    if (!html) {
      return ctx.body = baseError.dataNotExistError('html不存在');
    }

    await service.task.base.setOssData(task.appKey, taskId, 'html', html);

    const json = await service.task.base.convert2Json(task.appKey, task.taskId, html);
    const errorStat = errorNodeStat(json);
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });

    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.reviewChangeChapter.id,
        costTime: 0,
        data: '',
      });
    });

    ctx.body = {
      status: 0,
      data: {
        json,
        errorNodeCount: errorStat.count,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async refreshChapter() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const { taskId, specialOperator } = ctx.input as {
      taskId: number;
      specialOperator?: 'project_check' | 'admin_check';
    };

    const task = await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status === statuses.reviewing && task.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId);
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }

    const { appKey } = task;
    const html = await service.task.taskV2.refreshChapter({
      appKey,
      taskId,
    });
    if (!html) {
      return ctx.body = baseError.dataNotExistError('处理失败');
    }

    await service.task.base.setOssData(task.appKey, taskId, 'html', html);

    const json = await service.task.base.convert2Json(task.appKey, task.taskId, html);
    const errorStat = errorNodeStat(json);
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });

    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.refreshChapter.id,
        costTime: 0,
        data: '',
      });
    });

    ctx.body = {
      status: 0,
      data: {
        json,
        errorNodeCount: errorStat.count,
      },
    };
  }

  @validate({
    taskId: 'number',
    specialOperator: {
      type: 'enum',
      values: ['project_check', 'admin_check'],
      required: false,
    },
  })
  public async regenerate() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const { taskId, specialOperator } = ctx.input as {
      taskId: number;
      specialOperator?: 'project_check' | 'admin_check';
    };
    const { userId } = ctx.data;

    const task = await service.task.base.getOne({ where: { taskId } });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    const isSpecial = specialOperator && (
      specialOperator === 'project_check' ?
        await this.isProjectUser(userId, task.bookId) :
        await this.isAdminUser(userId)
    );

    if (task.status === statuses.reviewing && task.reviewUserId !== userId || (task.status === statuses.reviewed || task.status === statuses.operatAdmin) && !isSpecial) {
      return ctx.body = baseError.permissionError('无权限操作');
    }
    if (task.status === statuses.operatAdmin && task.operatAdminUserId !== userId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }

    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types.reviewRegenerate.id,
        costTime: 0,
        data: '',
      });
    });

    const html = await service.task.base.getOssData(task.appKey, taskId, 'html', true);
    if (specialOperator === 'admin_check') {
      // 重新生成的时候, 进来的是标注任务的 taskId, 找到它的所有审核任务, 然后更新它们的 html, 并重新生成 json, 上传然后返回
      const originTasks = await service.task.base.getOriginTasks(taskId);
      if (originTasks.length) {
        // 拆分 html
        const originHtmls = service.task.base.splitHtml(html);
        // 全部转 json 并上传
        await Promise.all(originTasks.map(async(originTask, index) => {
          const _json = await service.task.base.convert2Json(originTask.appKey, originTask.taskId, originHtmls[index]);
          // 更新 errorStat
          const _errorStat = errorNodeStat(_json);
          await service.task.base.update({ errorNodeCount: _errorStat.count }, { where: { taskId: originTask.taskId } });
          await service.task.base.setOssData(originTask.appKey, originTask.taskId, 'internal.json', _json);
          return service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'json');
        }));
        return ctx.body = {
          status: 0,
          data: { },
        };
      }
    }
    const json = await service.task.base.convert2Json(task.appKey, task.taskId, html);
    const errorStat = errorNodeStat(json);
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'json');
    await service.task.base.update({ errorNodeCount: errorStat.count }, { where: { taskId } });
    ctx.body = {
      status: 0,
      data: {
        json,
        errorNodeCount: errorStat.count,
      },
    };
  }

  public async reviewGetJsonUrls() {
    const { ctx } = this;
    const { taskId, bookId } = ctx.input;
    const { statuses } = this.service.task.base;
    if (!taskId && !bookId) {
      ctx.body = baseError.paramsError('参数 taskId、bookId 二选一');
      return;
    }
    const whereOpt: any = {};
    if (taskId) {
      const taskIds = typeof taskId === 'number' ? [taskId] : taskId.split(',');
      whereOpt.taskId = taskIds.length > 1 ? { $in: taskIds } : taskIds[0];
    }
    if (bookId) {
      whereOpt.bookId = bookId;
    }
    const tasks = await this.service.task.base.getAll({
      where: whereOpt,
      attributes: ['taskId', 'appKey', 'status', 'taskName', 'errorNodeCount', 'reviewUserId'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    tasks.forEach((task: any) => {
      const hasJson = [statuses.reviewing, statuses.reviewed].includes(task.status);
      if (hasJson) {
        task.jsonUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'json', true);
        task.pdfUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'pdf', true);
        task.internalJsonUrl = this.service.task.base.getUrl(task.appKey, task.taskId, 'internal.json', true);
      }
    });
    ctx.body = {
      status: 0,
      data: tasks,
    };
  }

  @validate({
    appKey: 'string',
    baseDir: 'string',
  })
  public async getOssInfo() {
    const { config, ctx } = this;
    const { appKey, baseDir } = ctx.input as {
      appKey: string;
      baseDir: string;
    };
    const { accessKeyId, accessKeySecret, bucket, region } = config.aliOss;
    return ctx.body = {
      status: 0,
      data: {
        accessKeyId,
        accessKeySecret,
        bucket,
        region,
        key: `open/${appKey}/${baseDir}/`,
      },
    };
  }

  @validate({ taskId: 'number', fbdName: 'string?' })
  public async restart() {
    const { service, ctx, app } = this;
    const { taskId, file, newVersion, isNewWord, runAiFixed } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task: any = await service.task.base.getOne({ where: { taskId } });
    const meta = await service.task.meta.getMetas({ taskId });
    if (newVersion) {
      meta.bookType = '试卷';
    } else {
      meta.bookType = '正文';
    }
    if (!task) {
      return ctx.body = baseError.dataNotExistError('该任务不存在');
    }

    if (task.status === statuses.reviewed) {
      return ctx.body = baseError.dataAlreadyExistError('无法操作的任务状态');
    }

    if (task.resourceType !== ETaskResourceType.WORDV2 && task.resourceType !== ETaskResourceType.FBDV2) {
      return ctx.body = baseError.paramsError('非大文件流程任务');
    }

    if (task.resourceType === ETaskResourceType.WORDV2) {
      const resFile = await service.task.file.getFiles({ taskId });
      file.pdfs = resFile.pdfs || [];
      const words: ITaskWords = file.words;
      words.body.forEach((item) => {
        if (item.isPreHandle) {
          const curPdf = (file.pdfs as ITaskPdfs).find((pdf) => pdf.name === item.name);
          if (curPdf) {
            file.pdfs.splice(file.pdfs.indexOf(curPdf), 1);
          }
        }
      });
    }

    try {
      await app.model.transaction(async(transaction) => {
        await Promise.all([
          service.task.taskV2.restart(task, file, meta, isNewWord, runAiFixed),
          service.plan.task.onChangeTaskStatus({
            taskId,
            taskType: PlanTaskType.MARK,
            targetStatus: PlanTaskStatus.PENDING,
          }, { transaction }),
          service.task.file.setFiles([taskId], file, { transaction }),
          service.plan.task.onChangeTaskStatus({
            taskId,
            taskType: PlanTaskType.REVIEW,
            targetStatus: PlanTaskStatus.PENDING,
          }, { transaction })
        ]);
      });
      ctx.runInBackground(async() => {
        await service.task.history.create({
          userId,
          taskId,
          type: service.task.history.taskV2Types.restart.id,
          costTime: 0,
          data: '',
        });
      });
      ctx.body = { status: 0 };
    } catch (e) {
      ctx.body = baseError.serverError(e as any);
    }
  }

  @validate({
    taskId: 'number',
    htmlLength: 'number',
    type: 'string',
  })
  public async save() {
    const { ctx, service, logger } = this;
    const { taskId, htmlLength, type, autoSave, specialOperator } = ctx.input as {
      taskId: number;
      htmlLength: number;
      type: 'mark' | 'review' | 'split';
      autoSave?: boolean;
      specialOperator?: 'project_check' | 'admin_check';
    };
    const { statuses } = service.task.base;
    const { userId } = ctx.data;
    if (!['mark', 'review', 'split'].includes(type)) {
      return ctx.body = baseError.paramsError('保存类型不对');
    }
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }

    if (task.status === statuses.reviewing && task.reviewUserId !== userId) {
      logger.error(`【taskV2.save】: ${taskId}任务不是任务审核者`);
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.marking && task.markUserId !== userId) {
      logger.error(`【taskV2.save】: ${taskId}任务不是任务标注者`);
      return ctx.body = baseError.dataNotExistError('没有操作权限');
    }
    if (task.status === statuses.reviewed) {
      const isAdmin = await specialOperator && (
        specialOperator === 'project_check' ?
          await this.isProjectUser(userId, task.bookId) :
          await this.isAdminUser(userId)
      );
      if (!isAdmin) {
        logger.error(`【taskV2.save】: ${taskId}不是管理员`);
        return ctx.body = baseError.permissionError('没有操作权限');
      }
      const res = await this.checkProject(userId, task.bookId); // 这里会回滚项目的状态
      if (res) {
        return ctx.body = baseError.permissionError(res);
      }
      await service.task.base.update(
        { status: statuses.operatAdmin, operatAdminUserId: userId },
        { where: { taskId } }
      );
    }
    if (task.status === statuses.operatAdmin && userId !== task.operatAdminUserId) {
      const res = await service.user.search([task.operatAdminUserId]);
      return ctx.body = baseError.permissionError(`${res?.[0]?.nickname || task.operatAdminUserId}正在处理当前任务`);
    }
    // 审核任务保存
    if (task.mergedTaskId) {
      const mergedTask = await service.task.base.getOne({ where: { taskId: task.mergedTaskId } });
      // 保存的时候， 需要判断 specialOperator 是不是 project_check, 如果是，就说明是项目审核的保存，就需要查找 mergedTask 修改其 html
      // fix: 这里不需要判断是在哪里保存了，只要保存就上传重新生成 json, 任务审核时 需要看到不同的 目录结构
      if (mergedTask) {
        // 先查找所有的 任务
        const tasks = await service.task.base.getOriginTasks(mergedTask.taskId);
        // 获取 mergedTask 的 html
        const mergedTaskHtml = await service.task.base.getOssData(mergedTask.appKey, mergedTask.taskId, 'html');
        // 拆分 html
        const mergedTaskHtmls = service.task.base.splitHtml(mergedTaskHtml);
        // 获取当前任务的 html
        const html = await service.task.base.getOssData(task.appKey, task.taskId, 'html');
        // 替换对应的 html
        const index = tasks.findIndex((item) => item.taskId === task.taskId);
        if (index !== -1) {
          mergedTaskHtmls[index] = html;
        }
        // 上传 html
        await service.task.base.setOssData(mergedTask.appKey, mergedTask.taskId, 'html', mergedTaskHtmls.join(''));

        // 重新生成 json 上传
        // const mergedTaskJson = await service.task.base.convert2Json(mergedTask.appKey, mergedTask.taskId, mergedTaskHtmls.join(''));
        // await service.task.base.setOssData(mergedTask.appKey, mergedTask.taskId, 'json', mergedTaskJson);
      }
    } else {
      const isV3 = await service.task.base.isV3(taskId);
      if (isV3) {
        const originTasks = await service.task.base.getOriginTasks(taskId);
        // 拆分 html
        const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
        const originHtmls = service.task.base.splitHtml(html);
        if (originHtmls.length !== originTasks.length) {
          return ctx.body = baseError.serverError('保存失败！请检查分割线完整性！');
        }
        // 上传 html
        await Promise.all(originTasks.map((originTask, index) => {
          return service.task.base.setOssData(originTask.appKey, originTask.taskId, 'html', originHtmls[index]);
        }));
        this.logger.info(`【taskV2.save】: ${taskId}保存成功`);
      }
    }
    ctx.body = { status: 0 };
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId,
        type: service.task.history.taskV2Types[`${type}Save`].id,
        costTime: 0,
        data: JSON.stringify({
          autoSave,
          specialOperator,
          htmlSize: htmlLength,
        }),
      });
    });
  }

  @validate({
    text_list: {
      type: 'array',
      required: true,
      itemType: 'string',
    },
  })
  public async analysisChapterLevel() {
    const { service, ctx } = this;
    const { text_list } = ctx.input as {
      text_list: string[];
    };
    const chapterLevels = await service.task.taskV2.analysisChapterLevel(text_list);
    if (!chapterLevels || chapterLevels.length !== text_list.length) return ctx.body = baseError.dataNotExistError('分析标题层级异常');
    ctx.body = {
      status: 0,
      data: chapterLevels,
    };
  }

  @validate({ taskId: 'number' })
  public async mergePreHandleHtml() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as {
      taskId: number;
    };
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.resourceType !== ETaskResourceType.FBDV2 && task.resourceType !== ETaskResourceType.WORDV2) {
      return ctx.body = baseError.paramsError('任务类型错误');
    }
    const file = await service.task.file.getFiles({ taskId });
    const files = task.resourceType === ETaskResourceType.FBDV2 ? file.fbds! : file.words!;
    try {
      await service.task.taskV2.mergePreHandleHtml(task.appKey, task.taskId, files);
    } catch (_) {
      return ctx.body = baseError.serverError('预处理文件合并异常');
    }
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async pdf2images() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as {
      taskId: number;
    };
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已删除');
    }
    const file = await service.task.file.getFiles({ taskId });
    if (file.pdfs) {
      const errorInfo = await service.task.taskV2.pdf2image(file.pdfs, true);
      if (errorInfo) {
        return ctx.body = baseError.serverError(errorInfo);
      }
      await service.task.file.setFiles([taskId], { pdfs: file.pdfs });
      ctx.body = {
        status: 0,
        data: {
          taskId,
          pdfs: file.pdfs,
        },
      };
    } else {
      ctx.body = baseError.dataNotExistError('数据异常，pdf不存在');
    }
  }

  @validate({ taskId: 'number' })
  public async generateMachineDocx() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as {
      taskId: number;
    };
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已删除');
    }
    ctx.runInBackground(async() => {
      const html = await service.task.base.getOssData(task.appKey, taskId, 'machine.html');
      const docKey = service.task.base.getOssKey(task.appKey, taskId, 'machine.docx');
      await service.oss.convertHtmlToDocAndUpload(docKey, html);
    });
    ctx.body = { status: 0 };
  }

  @validate({ html: 'string' })
  public async getInternalJson() {
    const { service, ctx } = this;
    const { html, subject } = ctx.input as {
      html: string;
      subject: string;
    };
    const json = await service.task.base.convert2JsonV2(html, subject);
    ctx.body = {
      status: 0,
      data: json,
    };
  }

  public async updateTaskPage() {
    const { service, ctx } = this;
    const { taskId, page } = ctx.input;
    await service.task.meta.setMetas([taskId], page);
    ctx.body = { status: 0 };
  }
}
