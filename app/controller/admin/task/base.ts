/**
 * @file 任务控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import * as _ from 'lodash';
import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';
import { PlanTaskType } from '../../../model/planFlow';
import { PlanTaskStatus } from '../../../model/planTask';
import { ITaskMetas } from '../../../model/taskMeta';
import { separateJsonByChapter } from '../../../core/utils/jsonHelper';
import { cleanFilename } from '../../../core/utils/helper';
import { ETaskType } from '../../../model/task';
import { EImageMarkStatus } from '../../../model/image';
import { supportExtension } from '../../../service/task/base';
import { Op } from 'sequelize';
import * as dateformat from 'dateformat';

export default class TaskBaseController extends Controller {

  @validate({ taskId: 'number' })
  public async delete() {
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { userId } = ctx.data;
    const { hexinApps } = service.appl;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.bookId) {
      return ctx.body = baseError.dataNotExistError('暂时不支持删除书本任务');
    }
    if (!hexinApps.includes(task.appKey) && task.status === statuses.reviewed) {
      return ctx.body = baseError.dataNotExistError('无法删除已发布的任务');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.delete({ transaction, where: { taskId } }),
        service.plan.task.onDeleteTask(taskId, { transaction })
      ]);
    });
    ctx.runInBackground(async() => {
      // 记录操作日志
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.otherTypes.del.id,
        data: '',
        costTime: 0,
      });
      const images = await service.image.getAll({ where: { taskId }, attributes: ['imageId'] });
      if (!images.length) {
        return;
      }
      await Promise.all([
        service.image.delete({ where: { taskId } }),
        service.formula.delete({ where: { imageId: images.map((item) => item.imageId) } })
      ]);
    });
    ctx.body = { status: 0 };
  }

  public async getListByAdmin() {
    const { ctx, service } = this;
    const { key, status, appKey, notest, unpublished, taskType, resourceType } = ctx.input;
    const { statuses } = service.task.base;
    const { hexinApps } = service.appl;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {} as any;
    if (!isNaN(status)) {
      whereOpt.status = Number(status);
    } else if (unpublished) {
      whereOpt.status = { $lt: statuses.reviewed };
    }
    if (appKey) {
      whereOpt.appKey = appKey;
    } else if (notest) {
      whereOpt.appKey = { $not: hexinApps };
    }
    whereOpt.key = key;
    if (taskType) {
      whereOpt.taskType = typeof taskType === 'number' ? taskType : taskType.split(',');
    }
    if (resourceType || resourceType === 0) {
      whereOpt.resourceType = resourceType;
    }
    const [count, tasks] = await Promise.all([
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'desc']],
      })
    ]);
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      const file = fileDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      if (meta) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json');
      }
      task.baseUrl = service.task.base.getOssBaseUrl(task.appKey, task.taskId);
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  /**
   * @description 批量检索任务 - 不分页
   */
  public async getList() {
    const { ctx, service } = this;
    const { status, appKey, subject, projectId, startTime, endTime } = ctx.input;
    const { statuses } = service.task.base;
    const whereOpt = {} as any;
    if (Number(projectId)) {
      const project = await service.project.base.getRelateOne({ where: { id: Number(projectId) } });
      const bookId = project?.books?.question?.bookId;
      if (project && bookId) {
        whereOpt.bookId = bookId;
        whereOpt.mergedTaskId = null;
      } else {
        return ctx.body = baseError.dataNotExistError('项目图书不存在');
      }
    }
    if (!isNaN(status)) {
      whereOpt.status = Number(status);
    }
    if (appKey) {
      whereOpt.appKey = appKey;
    }
    if (whereOpt.status === statuses.reviewed) {
      if (startTime) {
        whereOpt.endReviewTime = { $gte: startTime };
      }
      if (endTime) {
        whereOpt.endReviewTime = { ...(whereOpt.endReviewTime || {}), $lt: endTime };
      }
    }
    let tasks = await service.task.base.getRelatedList({
      where: whereOpt,
      order: whereOpt.bookId ? [['bookOrder', 'asc'], ['id', 'desc']] : [['id', 'desc']],
    }, false);
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const meta = metaDict[task.taskId];
      const file = fileDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      if (meta) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'meta.json');
      }
    });
    if (subject) {
      tasks = tasks.filter((task: any) => task.meta.subject === subject);
    }
    ctx.body = {
      status: 0,
      data: { tasks },
    };
  }

  @validate({
    ids: {
      type: 'array',
      itemType: 'number',
      min: 1,
    },
  })
  public async getUrls() {
    const { service, ctx } = this;
    const { ids } = ctx.input as { ids: number[] };
    const tasks = await service.task.base.getList({
      where: { taskId: ids, status: service.task.base.statuses.reviewed },
      attributes: ['taskId', 'appKey', 'taskName'],
    });
    const data = tasks.map((task) => {
      return {
        id: task.taskId,
        name: task.taskName,
        json: service.task.base.getUrl(task.appKey, task.taskId, 'json', true),
        docx: service.task.base.getUrl(task.appKey, task.taskId, 'docx', true),
      };
    });
    ctx.body = { status: 0, data };
  }

  public async getOverview() {
    const { app, service, ctx } = this;
    const { statuses } = service.task.base;
    const { col, fn } = app.model;
    const tasks = await service.task.base.getAll({
      where: { status: { $lt: statuses.reviewed, $ne: statuses.error } },
      attributes: ['appKey', [fn('COUNT', col('id')), 'count']],
      group: ['appKey'],
    }) as any;
    if (!tasks.length) {
      return ctx.body = {
        status: 0,
        data: [],
      };
    }
    const apps = await service.appl.getAllByUc({
      where: { appKey: tasks.map((item) => item.appKey) },
      attributes: ['appKey', 'appName'],
    });
    const appMap = {};
    for (const { appName, appKey } of apps) {
      appMap[appKey] = appName;
    }
    let allCount = 0;
    for (const { count } of tasks) {
      allCount += count;
    }
    const data = [{ appKey: '', count: allCount, appName: '' }].concat(tasks).map((item) => {
      item.appName = item.appKey && appMap[item.appKey] ? appMap[item.appKey] : '全部';
      return item;
    });
    ctx.body = {
      status: 0,
      data: _.orderBy(data, (d) => d.count),
    };
  }

  @validate({ taskId: 'number' })
  public async getHistory() {
    const { service, ctx } = this;
    const { taskId } = ctx.input;
    const histories = await service.task.history.getDetailByTaskId(taskId);
    ctx.body = {
      status: 0,
      data: histories,
    };
  }

  @validate({ taskId: 'number' })
  public async restart() {
    // 重跑内容识别
    const { ctx, service, app } = this;
    const { taskId } = ctx.input;
    const { statuses } = service.task.base;
    const { hexinApps } = service.appl;
    const { userId } = ctx.data;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['appKey', 'status', 'rerun'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status === statuses.reviewed && !hexinApps.includes(task.appKey)) {
      return ctx.body = baseError.dataAlreadyExistError('无法操作的任务状态');
    }
    const images = await service.image.getAll({
      where: task.rerun ?
        { taskId, disabled: false, rerun: true } :
        { taskId, disabled: false },
      attributes: ['imageId', 'appKey'],
    });
    const imageMap = {};
    images.forEach((image) => {
      imageMap[image.imageId] = 0;
    });
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        // 删除公式
        service.formula.delete(
          { transaction, where: { imageId: images.map((item) => item.imageId) } }
        ),
        // 重置图片状态
        service.image.update(
          { marked: EImageMarkStatus.init, reviewed: false },
          { transaction, where: { taskId } }
        ),
        // 重置任务状态
        service.task.base.update(
          {
            status: statuses.columnProcessed,
            markUserId: task.rerun ? undefined : 0,
            reviewUserId: 0,
            isCallback: false,
            callbackError: false,
            startMarkTime: task.rerun ? undefined : null,
            endMarkTime: task.rerun ? undefined : null,
            startReviewTime: null,
            endReviewTime: null,
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.PENDING,
        }, { transaction }),
        service.task.base.resetTaskConfig(taskId, 'imageStructProcessor', imageMap)
      ]);
    });
    ctx.runInBackground(async() => {
      // 记录操作日志
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.otherTypes.restart.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number?',
    taskCode: 'string?',
    withHtml: 'boolean?', // true
    withUsername: 'boolean?', // true
    withAppName: 'boolean?', // true
    withImages: 'boolean?', // true
    withMeta: 'boolean?', // false
    withBaseUrl: 'boolean?', // false
  })
  public async getOneById() {
    const { ctx, service } = this;
    let {
      taskId,
      taskCode,
      withHtml = true,
      withUsername = true,
      withAppName = true,
      withImages = true,
      withMeta = false,
      withBaseUrl = false,
    } = ctx.input;
    if (!taskId && !taskCode) {
      return ctx.body = baseError.paramsError('缺少参数 taskId');
    }
    if (!taskId) {
      const task = await service.task.base.getTaskByCode(taskCode, { attributes: ['taskId'] });
      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }
      taskId = task.taskId;
    }
    const task = await service.task.base.getRelateOne(
      { where: { taskId } },
      { withHtml, withUsername, withAppName }
    );
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (withBaseUrl) {
      task.baseUrl = service.task.base.getOssBaseUrl(task.appKey, task.taskId);
    }
    const [images, meta, disabledImages] = await Promise.all([
      withImages ? service.image.getAll({
        where: { taskId, disabled: false },
        attributes: [
          'imageId', 'originalId', 'sourceId',
          'marked', 'reviewed',
          'filename', 'appKey', 'taskOrder',
          'rerun', 'rerunTimes'
        ],
        order: [['id', 'ASC']],
      }) as any : null,
      withMeta ? service.task.meta.getMetas({ taskId: task.taskId }) : null,
      withImages ? service.image.getAll({
        where: { taskId, disabled: true },
        attributes: [
          'imageId', 'originalId', 'sourceId',
          'marked', 'reviewed',
          'filename', 'appKey', 'taskOrder',
          'rerun', 'rerunTimes'
        ],
        order: [['id', 'ASC']],
      }) as any : null
    ]);
    if (images) {
      images.forEach((image) => {
        image.url = service.image.getUrl(image.appKey, image.imageId, 'jpg', false);
      });
      task.images = service.image.sortImageByFile(images);
    }
    if (meta) {
      task.meta = meta;
      if (Object.keys(meta).length) {
        task.metaJsonUrl = service.task.base.getUrl(task.appKey, taskId, 'meta.json');
      }
    }
    if (disabledImages) {
      task.disabledImages = disabledImages;
    }
    // 看看是不是标注任务
    // const isV3 = await service.task.base.isV3(taskId);
    // if (isV3) {
    //   const originTasks = await service.task.base.getOriginTasks(taskId);
    //   task.originTasks = originTasks;
    // }
    ctx.body = {
      status: 0,
      data: task,
    };
  }

  public async saveTaskMeta() {
    const { ctx, service } = this;
    const { taskId, meta } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    await service.task.meta.setMetas([taskId], meta);
    ctx.body = { status: 0 };
  }

  public async saveTaskFile() {
    const { ctx, service } = this;
    const { taskId, file } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    await service.task.file.setFiles([taskId], file);
    ctx.body = { status: 0 };
  }

  @validate({
    taskIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  public async getListByIds() {
    const { ctx, service } = this;
    const { taskIds } = ctx.input as { taskIds: number[] };
    const taskList = await service.task.base.getAll({ where: { [Op.or]: taskIds.map((taskId) => ({ taskId })) } });

    ctx.body = {
      status: 0,
      taskList,
    };
  }

  public async checkHtml() {
    const { ctx, service } = this;
    const { htmls } = ctx.input;
    const errorList: { index: number, errorInfo: string }[] = [];
    await Promise.all(htmls.map(async(html, index) => {
      const errorInfo = await service.task.base.checkHtml(html);
      if (errorInfo) {
        errorList.push({
          index,
          errorInfo,
        });
      }
    }));
    ctx.body = {
      status: 0,
      errorList,
    };
  }

  @validate({ taskId: 'number' })
  public async getRecognitionProgress() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status !== service.task.base.statuses.autoProcessing) {
      return ctx.body = baseError.dataNotExistError('任务不在自动识别中');
    }
    // 从 redis 获取识别进度：1 是已经识别的
    const res = await service.task.base.getRecognitionProgress(taskId);
    const keys = Object.keys(res);
    // 看看 value 有多少个 0
    let count = 0;
    keys.forEach((key) => {
      if (Number(res[key]) === 1) {
        count++;
      }
    });
    ctx.body = {
      status: 0,
      data: {
        total: keys.length,
        successCount: count,
      },
    };
  }

  @validate({ bookId: 'number' })
  public async getListByBookId() {
    const { ctx, service } = this;
    const { key, status, bookId } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = { key, bookId } as any;
    if (status >= -1) {
      whereOpt.status = status;
    }
    whereOpt.mergedTaskId = null;

    const [count, tasks] = await Promise.all([
      service.task.base.relatedCount({ where: whereOpt }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
        order: [['bookOrder', 'asc'], ['id', 'desc']],
      })
    ]);
    const fileDict = await service.task.file.getFilesDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: tasks.map((t) => t.taskId) } });
    tasks.forEach((task: any) => {
      const file = fileDict[task.taskId];
      const meta = metaDict[task.taskId];
      task.meta = meta || {};
      task.file = file || {};
      task.pdfCount = service.task.meta.calcPdfCount(metaDict[task.taskId]?.pdfInfo);
    });
    // *********************************
    // 判断是否是 v2, v1 任务，这里不能展示原始任务，只显示合并任务
    if (tasks?.some((v) => v.mergedTaskId)) {
      const mergedTask = tasks.filter((v) => !v.mergedTaskId);
      return ctx.body = {
        status: 0,
        data: {
          page,
          pageSize,
          count,
          tasks: mergedTask,
        },
      };
    }
    // *********************************
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks,
      },
    };
  }

  @validate({ bookId: 'number', taskId: 'number' })
  public async bindBook() {
    const { ctx, service, app } = this;
    const { taskId, bookId } = ctx.input;
    const bookStatuses = service.book.statuses;
    const [task, book, bookOrder] = await Promise.all([
      service.task.base.getOne({ where: { taskId }, attributes: ['appKey', 'bookId', 'status'] }),
      service.book.getOne({ where: { id: bookId } }),
      service.task.base.model.max('bookOrder', { where: { bookId } })
    ]);
    if (!task || !book) {
      return ctx.body = baseError.dataNotExistError('任务或者书本信息不存在');
    }
    if (task.bookId) {
      return ctx.body = baseError.dataAlreadyExistError('已关联了书本信息');
    }
    // 查看当前需要关联书本的其他任务信息
    const { count, reviewed, marked } = await service.book.getSubTaskStatuses(bookId);
    const allTaskReviewed = reviewed === count && count > 0;
    const allTaskMarked = marked === count && count > 0;
    let bookStatus = bookStatuses.processing;
    if (allTaskReviewed) {
      bookStatus = [bookStatuses.reviewed, bookStatuses.reviewing].includes(book.status) ?
        bookStatuses.reviewing :
        bookStatuses.unreviewed;
    } else if (allTaskMarked) {
      bookStatus = bookStatuses.unreviewed;
    }
    const projectMeta = await service.project.meta.getMetas({ projectId: book.projectId });
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          { bookId, bookOrder: (bookOrder || 0) + 1 },
          { where: { taskId }, transaction }
        ),
        service.book.update(
          { status: bookStatus, taskCount: count + 1 },
          { where: { id: bookId }, transaction }
        ),
        service.image.update(
          { bookId },
          { where: { taskId }, transaction }
        ),
        service.task.meta.setMetas([taskId], projectMeta, { transaction })
      ]);
    });
    // 同步项目 oss meta.json 到任务
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      const sourceKey = service.project.base.getOssKey(book.appKey, book.projectId, 'meta.json');
      const key = service.task.base.getOssKey(task.appKey, taskId, 'meta.json');
      await ossClient.copy(key, sourceKey);
    });
    ctx.body = { status: 0 };
  }

  @validate({ bookId: 'number', taskId: 'number' })
  public async unbindBook() {
    const { ctx, service } = this;
    const { taskId, bookId } = ctx.input;
    const { statuses } = service.task.base;
    const bookStatuses = service.book.statuses;
    const [task, book] = await Promise.all([
      service.task.base.getOne({ where: { taskId }, attributes: ['bookId', 'status'] }),
      service.book.getOne({ where: { id: bookId }, attributes: ['status'] })
    ]);
    if (!task || !book) {
      return ctx.body = baseError.dataNotExistError('任务或者书本信息不存在');
    }
    if (!task.bookId) {
      return ctx.body = baseError.dataAlreadyExistError('未关联书本信息');
    }
    // 查看当前需要关联书本的其他任务信息
    const { count, reviewed, marked } = await service.book.getSubTaskStatuses(bookId);
    const allTaskReviewed = count > 0 && (reviewed === count || (reviewed === count - 1 && task.status !== statuses.reviewed));
    const allTaskMarked = count > 0 && (marked === count || (reviewed === count - 1 && task.status < statuses.unreviewed));
    let bookStatus = bookStatuses.processing;
    if (allTaskReviewed) {
      bookStatus = [bookStatuses.reviewed, bookStatuses.reviewing].includes(book.status) ?
        bookStatuses.reviewing :
        bookStatuses.unreviewed;
    } else if (allTaskMarked) {
      bookStatus = bookStatuses.unreviewed;
    }
    // 如果是 v3, 需要将所有关联的任务全部取消
    const isV3 = await service.task.base.isV3(taskId);
    let originTasks;
    if (isV3) {
      originTasks = await service.task.base.getOriginTasks(taskId);
    }

    await Promise.all([
      service.book.update({ status: bookStatus, taskCount: count - 1 }, { where: { id: bookId } }),
      service.task.base.update({ bookId: 0, bookOrder: 0 }, { where: { taskId } }),
      service.image.update({ bookId: 0 }, { where: { taskId } }),
      ...(originTasks || []).map((item) => service.task.base.update({ bookId: 0, bookOrder: 0 }, { where: { taskId: item.taskId } }))
    ]);
    ctx.body = { status: 0 };
  }

  @validate({
    bookId: 'number',
    tasks: {
      type: 'array',
      itemType: 'object',
      rule: {
        taskId: 'number',
        order: 'number',
      },
      min: 1,
    },
  })
  public async reorderInBook() {
    const { ctx, service } = this;
    const { bookId, tasks } = ctx.input as {
      bookId: number;
      tasks: { taskId: number; order: number }[];
    };
    const [bookExists, taskCount] = await Promise.all([
      service.book.exists({ where: { id: bookId } }),
      service.task.base.count({ where: { taskId: tasks.map(({ taskId }) => taskId), bookId } })
    ]);
    if (!bookExists) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    if (taskCount !== tasks.length) {
      return ctx.body = baseError.dataNotExistError('部分任务不存在该图书下');
    }
    await service.task.base.bulkCreate(
      tasks.map(({ taskId, order }) => {
        return { taskId, bookOrder: order };
      }) as any,
      { updateOnDuplicate: ['bookOrder'] }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    bookId: 'number',
    taskId: 'number',
    direction: ['up', 'down'],
  })
  public async swapOrderInBook() {
    const { ctx, service } = this;
    const { bookId, taskId, direction } = ctx.input;
    const [bookExists, task] = await Promise.all([
      service.book.exists({ where: { id: bookId } }),
      service.task.base.getOne({ where: { taskId, bookId }, attributes: ['bookOrder'] })
    ]);
    if (!bookExists) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    if (!task) {
      return ctx.body = baseError.dataNotExistError('该图书下不存在该任务');
    }
    const delta = direction === 'up' ? -1 : 1;
    await service.task.base.update(
      { bookOrder: task.bookOrder },
      { where: { bookId, bookOrder: task.bookOrder + delta } }
    );
    await service.task.base.update(
      { bookOrder: task.bookOrder + delta },
      { where: { taskId } }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    subject: ['chinese', 'math', 'en-math'],
    imageIds: {
      type: 'array',
      itemType: 'string',
      min: 1,
      max: 250,
    },
    taskName: {
      type: 'string',
      max: 64,
    },
    appKey: 'string',
    priority: 'number?',

    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    taskType: {
      type: 'enum',
      values: [0, 1],
      required: false,
    },
    timeLimit: 'number?',
    timeWarning: 'number?',
  })
  public async create() {
    const { ctx, service } = this;
    const { subject, priority, taskName, appKey, imageIds, meta, taskType, timeLimit, timeWarning } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskName: string;
      appKey: string;
      priority?: number;
      imageIds: string[];
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }
    const taskId = await service.task.base.relatedCreate({
      appKey,
      subject,
      priority,
      images: imageIds,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName: taskName || '',
      taskType,
      timeLimit,
      timeWarning,
    }, meta);
    await service.task.meta.setMetas([taskId], meta);
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(appKey, taskId, meta);
    });
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  @validate({ taskId: 'number' })
  public async rerun() { // 重新预切图
    const { service, ctx } = this;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return;
    }
    // statuses.preCropCheck
    await service.task.base.update({ status: -105, preprocessUserId: 0 }, { where: { taskId } });
    await service.task.base.pushPreCropCheck([task], { type: '' });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async pdfRerun() { // pdf任务重新进入转图片流程
    const { service, ctx, logger } = this;
    const { statuses } = service.task.base;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return;
    }
    const file = await service.task.file.getFiles({ taskId });
    await service.task.taskV2.pdf2image(file.pdfs! , true, true);
    await service.task.file.setFiles([taskId], file);
    let errorInfo: string | undefined;
    try {
      // 拿到pdf2img task_id 改状态为pdf预处理中
      errorInfo = await service.task.taskV2.pdf2image(file.pdfs! , false, true);
      if (!errorInfo) {
        await service.task.taskV2.update({ status: statuses.pending, splitUserId: 0 }, { where: { taskId } });
      }
    } catch (e) {
      logger.error(`controller.task.base.pdfRerun:${e}, task: ${taskId}`);
      errorInfo = (e as any).message || '系统异常';
    }
    await service.task.file.deleteFiles([taskId], ['pdfs', 'imgs']);
    await service.task.file.setFiles([taskId], file);
    ctx.body = {
      status: 0,
      taskId,
    };
  }

  @validate({ taskId: 'number' })
  public async columnRerun() {
    const { service, ctx } = this;
    const { taskId } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status !== service.task.base.statuses.columnAutoProcessing) {
      return ctx.body = baseError.dataAlreadyExistError('任务状态不正确');
    }
    await service.task.base.update({ status: service.task.base.statuses.columnQueue }, { where: { taskId } });
    ctx.body = { status: 0 };
    const updateTime = dateformat(new Date(), 'yyyy-mm-dd HH:MM:ss');
    await service.task.base.update(
      { updateTime, rerun: true },
      { where: { id: task.id } }
    );
    // 获取图片信息 一起把 rerun 设为true
    await service.image.update(
      { rerun: true },
      { where: { taskId: task.taskId } }
    );
    // 清空 redis
    if (taskId) {
      await this.service.task.base.resetTaskConfig(taskId, 'imageColumnProcessor');
      this.logger.info(`task ${taskId} resetTaskConfig`);
      // 记录日志 - 自动重跑
      this.ctx.runInBackground(async() => {
        // 记录操作日志
        await this.service.task.history.create({
          taskId: taskId!,
          userId: this.service.user.aiFilterUserId,
          type: this.service.task.history.otherTypes.restart.id,
          costTime: 0,
          data: '',
        });
      });
    }

  }

  @validate({
    taskName: { type: 'string', required: false, max: 64 },
    taskId: 'number',

    meta: {
      type: 'object',
      rule: {
        type: 'number?',
        textType: 'number?',
        columnType: 'number?',
        collectType: 'number?',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
  })
  public async update() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { userId } = ctx.data;

    let { taskId, taskName, meta } = ctx.input as {
      taskId: number;
      taskName?: string;
      meta: Partial<ITaskMetas>,
    };
    taskName = taskName && taskName.trim();
    const [task, existMeta] = await Promise.all([
      service.task.base.getOne({
        where: { taskId },
        attributes: ['appKey', 'taskName', 'bookId', 'status', 'markUserId', 'taskType'],
      }),
      service.task.meta.getMetas({ taskId })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status === service.task.base.statuses.reviewed) {
      return ctx.body = baseError.dataAlreadyExistError('任务已发布');
    }
    if (task.bookId) {
      return ctx.body = baseError.dataAlreadyExistError('图书任务请修改对应图书项目信息');
    }
    if (taskName && taskName !== task.taskName) {
      await service.task.base.update(
        { taskName },
        { where: { taskId } }
      );
    }
    meta = { ...existMeta, ...meta };

    const shouldQuitMark = task.taskType === ETaskType.limit &&
      meta.subject !== existMeta.subject &&
      task.status <= statuses.marking &&
      task.markUserId > 0;
    await app.model.transaction(async(transaction) => {
      await service.task.meta.setMetas([taskId], meta, { transaction });
      if (shouldQuitMark) {
        const update: any = {
          markUserId: 0,
          startMarkTime: null,
        };
        if (task.status === statuses.marking) {
          update.status = statuses.unmarked;
        }
        await service.task.base.update(
          update,
          { transaction, where: { taskId } }
        );
      }
    });
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(task.appKey, taskId, meta);
    });
    if (shouldQuitMark) {
      ctx.runInBackground(async() => {
        await service.task.history.create({
          taskId,
          userId,
          type: service.task.history.markTypes.quitOnChangeSubject.id,
          data: `${existMeta.subject}>${meta.subject}`,
          costTime: 0,
        });
      });
    }

    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', imageIds: { type: 'array', itemType: 'string' } })
  public async reorderImages() {
    const { ctx, service } = this;
    const { taskId, imageIds } = ctx.input;
    const count = await service.image.count({
      where: {
        taskId,
        disabled: false,
      },
    });
    if (count !== imageIds.length) {
      return ctx.body = baseError.dataNotExistError('部分图片不存在');
    }
    await service.image.bulkCreate(
      imageIds.map((imageId: string, index: number) => {
        return { imageId, taskOrder: index + 1 };
      }),
      { updateOnDuplicate: ['taskOrder'] }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    level: 'number',
    json: 'array?',
    url: 'string?',
    filename: 'string',
  })
  public async separateJson() {
    const { service, ctx, config } = this;
    let { level, json, url, filename } = ctx.input as { level: number; json?: any[]; url?: string; filename: string };
    if (level < 0) {
      return ctx.body = baseError.paramsError(`level:${level}; `);
    }
    if (!json && !url) {
      return ctx.body = baseError.paramsError('确实json内容或地址');
    }

    if (!json) {
      url = url!.replace(config.aliOss.host, config.aliOss.privateHost);
      json = await service.oss.fetch(url, 'json');
    }

    const separateResult = separateJsonByChapter(json!, level);

    if (_.isEmpty(separateResult)) {
      return ctx.body = baseError.dataNotExistError(`不存在${level}级目录`);
    }

    // 移除文件名中的无效字符
    const data = separateResult.map((item, index) => {
      const path = item.path.map(cleanFilename);
      return {
        fileName: `${path.join('/')}_${index + 1}.json`,
        data: JSON.stringify([item.node]),
      };
    });
    ctx.attachment(`${cleanFilename(filename)}.zip`);
    ctx.set('Content-Type', 'application/octet-stream');
    const archive = await service.archiver.archiveByOption({ data, compressionType: 'zip' });
    ctx.body = archive;
  }

  public async getUnAddApplList() {
    const { service, ctx, app } = this;
    const { fn, col } = app.model;
    const { statuses } = service.task.base;
    const applKeys = await service.task.base.getAll({
      where: {
        status: { $ne: statuses.reviewed },
        taskType: ETaskType.unset,
      },
      attributes: [
        'appKey',
        [fn('count', col('id')), 'taskCount']
      ],
      group: ['appKey'],
    });
    const appls = await service.appl.getAllByUc({
      where: { appKey: applKeys.map((t) => t.appKey) },
      attributes: ['appKey', 'appName', 'userId', 'isActive', 'isTest'],
    });
    const taskCountMap = _.keyBy(applKeys, (t) => t.appKey);
    appls.forEach((a) => {
      Object.assign(a, taskCountMap[a.appKey]);
    });
    ctx.body = {
      status: 0,
      data: appls,
    };
  }

  @validate({
    page: 'number',
    pageSize: 'number',
    /*
     * search?: string | number,
     * status?: number[],
     */
    appKeys: 'string?', // string[]
  })
  public async getUnAddTaskListPage() {
    const { ctx, service } = this;
    const { statuses } = service.task.base;
    const toNumberArray = (d) => (typeof d === 'number' ? [d] : (d ? d.split(',').map((id) => Number(id)) : null));
    const toStringArray = (d) => (d ? d.split(',') : null);

    const inputAppKeys = ctx.input.appKeys != null ? toStringArray(ctx.input.appKeys) : null;
    const inputStatus = ctx.input.status != null ? toNumberArray(ctx.input.status) : null;

    const { page, pageSize, search, filterPriority7 } = ctx.input;

    const baseWhereOpt: any = {
      key: search, // service.task.base.relatedList/Count handles key internally
      taskType: ETaskType.unset,
      mergedTaskId: null,
    };

    if (search && isNaN(Number(search))) {
      baseWhereOpt.bookId = { [Op.ne]: 0 };
    }

    if (inputStatus) {
      baseWhereOpt.status = { [Op.in]: inputStatus };
    } else {
      baseWhereOpt.status = { [Op.ne]: statuses.reviewed };
    }

    if (inputAppKeys) {
      baseWhereOpt.appKey = { [Op.in]: inputAppKeys }; // Assuming service handles $in for appKey
    }

    const whereOptForTasksAndTotalCount = { ...baseWhereOpt };
    if (filterPriority7) {
      whereOptForTasksAndTotalCount.priority = 7;
    }

    const whereOptForPriorityCount = {
      ...baseWhereOpt,
      priority: 7,
    };

    const [count, tasksFromDb, priorityCount] = await Promise.all([
      service.task.base.relatedCount({ where: whereOptForTasksAndTotalCount }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where: whereOptForTasksAndTotalCount,
        order: [['id', 'desc']],
      }),
      service.task.base.relatedCount({ where: whereOptForPriorityCount })
    ]);

    const filteredTasks = tasksFromDb.filter((task) => {
      return !task.taskName.endsWith('复制');
    });

    ctx.body = {
      status: 0,
      data: {
        count,
        priorityCount,
        list: filteredTasks,
        page, // It's good practice to return page and pageSize
        pageSize,
      },
    };
  }

  @validate({ taskId: 'number', errorInfo: 'string' })
  public async returnError() {
    const { ctx, service, app } = this;
    const { taskId, errorInfo } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if ([
      statuses.columnProcessing,
      statuses.marking,
      statuses.unreviewed,
      statuses.reviewing,
      statuses.reviewed,
      statuses.error
    ].includes(task.status)) {
      return ctx.body = baseError.dataNotExistError('无法操作的任务状态');
    }
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            errorInfo,
            status: statuses.error,
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.MARK,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction }),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction })
      ]);
    });
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.otherTypes.returnBack.id,
        data: JSON.stringify({ errorInfo }),
        costTime: 0,
      });
      await service.task.base.deleteTaskConfig(taskId, 'imageColumnProcessor');
      await service.task.base.deleteTaskConfig(taskId, 'imageStructProcessor');
      // 延迟两个小时回调
      await service.task.callback.callback(Object.assign(task, {
        errorInfo,
        status: statuses.error,
      }), 3, 2 * 60 * 60 * 1000);
      if (task.ticketId) {
        // 关闭工单
        await service.workSheet.submitError({
          ticketId: task.ticketId,
          errorInfo,
        });
        this.logger.info(`close ticket ${task.ticketId} success`);
      }
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskIds: {
      type: 'array',
      itemType: 'number',
    },
    extension: 'string',
  })
  public async getOssFilesPath() {
    const { ctx, service } = this;
    const { taskIds, extension } = ctx.input as { taskIds: number[], extension: supportExtension };
    const tasks = await Promise.all(taskIds.map(async(taskId) => await service.task.base.getOne({ where: { taskId }, attributes: ['taskId', 'appKey', 'status'] })));
    const nullTask: number[] = [];
    tasks.forEach((task, index) => {
      if (!task) {
        nullTask.push(taskIds[index]);
      }
    });

    if (nullTask.length) {
      return ctx.body = baseError.dataNotExistError(`存在无效的任务ID${nullTask.join(', ')}`);
    }

    const filterTasks = tasks.filter((task) => task);

    const unReviewedTask: number[] = [];
    filterTasks.forEach((task) => {
      if (task!.status !== 7) {
        unReviewedTask.push(task!.taskId);
      }
    });

    if (unReviewedTask.length) {
      return ctx.body = baseError.dataNotExistError(`存在未发布的任务ID${unReviewedTask.join(', ')}`);
    }

    const urls = await Promise.all(filterTasks.map(async(task) => {
      const { appKey, taskId } = task!;
      return await service.task.base.getUrl(appKey, taskId, extension);
    }));
    ctx.body = {
      status: 0,
      urls,
    };
  }

  @validate({
    taskIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  public async revoke() {
    const { ctx, service, app, logger } = this;
    const { taskIds } = ctx.input;
    const { statuses } = service.task.base;
    let taskList = await service.task.base.getAll({ where: { [Op.or]: taskIds.map((taskId) => ({ taskId })) } });
    const { marking, unreviewed, reviewing, dataCleanfailed, reviewed } = statuses;
    const allowStatuses = [marking, unreviewed, reviewing, dataCleanfailed, reviewed];
    taskList = taskList.filter((task) => allowStatuses.includes(task.status));
    if (!taskList.length) {
      return ctx.body = baseError.dataNotExistError('不存在重新运行的任务');
    }

    ctx.runInBackground(async() => {
      try {
        await Promise.all(taskList.map(({ taskId, status }) => app.model.transaction(async(transaction) => {
          await Promise.all([
            service.task.base.update(
              {
                status: statuses.unmarked,
                reviewUserId: 0,
                markUserId: 0,
                startReviewTime: null,
                endReviewTime: null,
                startMarkTime: null,
                endMarkTime: null,
              },
              { transaction, where: { taskId } }
            ),
            service.image.update({ reviewed: false, marked: EImageMarkStatus.init }, { transaction, where: { taskId } }),
            service.plan.task.onChangeTaskStatus({
              taskId,
              taskType: PlanTaskType.MARK,
              targetStatus: PlanTaskStatus.REJECTED,
            }, { transaction }),
            service.plan.task.onChangeTaskStatus({
              taskId,
              taskType: PlanTaskType.REVIEW,
              targetStatus: PlanTaskStatus.PENDING,
            }, { transaction })
          ]);
          if ([statuses.error, statuses.reviewed].includes(status)) {
            logger.info(`after task revoked, task-callbacking : ${taskId} will be canceled!`);
            // 从待统计列表中删除
            await service.task.stat.del(taskId);
          }
        })));
      } catch (e) {
        logger.error('task.base.revoke.runInBackground.catch', e);
      }
    });

    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    appKey: 'string',
    extension: 'string',
  })
  public async hasOssData() {
    const { ctx, service } = this;
    const { taskId, appKey, extension } = ctx.input as {
      taskId: number;
      appKey: string;
      extension: supportExtension
    };
    const isExist = await service.task.base.hasOssData(appKey, taskId, extension);
    ctx.body = {
      status: 0,
      data: { isExist },
    };
  }

  @validate({
    taskId: 'number',
    name: 'string',
  })
  public async changeName() {
    const { ctx, service } = this;
    const { taskId, name } = ctx.input;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    try {
      await service.task.base.update({ taskName: name }, { where: { taskId } });
    } catch (e) {
      return ctx.body = baseError.dataNotExistError('修改失败');
    }
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async imageHtmlRestart() {
    // 如果任务状态是 error 或者 unmarked, 那么可以重新启动
    const { ctx, service, logger } = this;
    const { taskId } = ctx.input;
    logger.info(`imageHtmlRestart taskId: ${taskId}`);
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    // 获取 meta
    const meta = await service.task.meta.getMetas({ taskId });
    if (!meta) {
      return ctx.body = baseError.dataNotExistError('任务 meta 不存在');
    }
    if (meta.imageHtmlVersion !== 'v2' && !task.ticketId) {
      // return ctx.body = baseError.dataNotExistError('不是 v2 版本的任务');
    }
    const images = await service.image.getAll({ where: { taskId, disabled: 0 } });
    const imageUrls = images.map((item) => service.image.getUrl(task.appKey, item.imageId, 'jpg', false, false));
    this.logger.info(`imageHtmlRestart imageUrls: ${imageUrls}`);
    await this.service.rbs.initRBSQueue({
      task_id: task.taskId.toString(),
      task_type: 'image2html',
      task_info: {
        task_id: task.taskId.toString(),
        subject: task.subject,
        image_urls: imageUrls,
        get_task_parameters: [
          'task_id',
          'subject',
          'image_urls'
        ],
        callback_extras: [
          'task_id',
          'subject',
          'image_urls'
        ],
        run_type: 'common',
        // callback_url: 'https://c190-188-253-117-92.ngrok-free.app/api/open/task/updateV2',
        callback_url: 'http://xdoc.open.hexinedu.com/api/open/task/updateV2',
        push_time: new Date().getTime(),
        timestamp: new Date().getTime(),
      },
    });
    logger.info(`imageHtmlRestart taskId: ${taskId} success`);
    // 把状态重置到预切图检查
    await this.service.task.base.update({ status: service.task.base.statuses.ocrProcessing, rerun: true }, { where: { taskId } });
    return ctx.body = { status: 0 };
  }

  @validate({
    pdf: 'string',
    text: 'string?',
  })
  public async getPDFPageNum() {
    const { ctx, app, config } = this;
    const { pdf, text } = ctx.input;

    try {
      const { data } = await app.curl(`${config.solr.host}${config.solr.pdfSearch}`, {
        method: 'GET',
        dataType: 'json',
        data: { q: `book:"${pdf}" AND page_text:(${text || '\'\''})` },
      });

      ctx.body = {
        status: 0,
        data,
      };
    } catch (e) {
      ctx.body = baseError.serverError('获取PDF页码失败');
    }
  }

  public async autoFixHtml() {
    const { ctx, service } = this;
    const { taskId } = ctx.request.body;

    try {
      // 获取任务信息
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['taskId', 'appKey'],
      });

      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }

      // 获取任务的 meta 信息
      const taskMeta = await service.task.meta.getMetas({ taskId });

      if (!taskMeta || !taskMeta.subject) {
        return ctx.body = baseError.dataNotExistError('任务的学科信息不存在');
      }

      // 获取 html 和 machine.html 的 URL
      const htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'html', true, true);
      const machineHtmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'machine.html', true, true);

      // 调用自动修复接口
      const result = await ctx.curl('http://47.100.171.12:8088/api/open/xdoc_v2_html/auto_fix', {
        method: 'POST',
        contentType: 'application/json',
        data: {
          task_id: task.taskId.toString(),
          subject: taskMeta.subject,
          app_key: task.appKey,
          html_url: htmlUrl,
          machine_html_url: machineHtmlUrl,
        },
        dataType: 'json',
        timeout: 60000,
      });

      if (result.status !== 200) {
        ctx.body = baseError.dataNotExistError('自动修复接口调用失败');
      }

      ctx.body = {
        status: 0,
        msg: 'success',
        data: result.data.data,
      };
    } catch (error) {
      ctx.logger.error('自动修复 HTML 失败:', error);
      ctx.body = baseError.dataNotExistError(error);
    }
  }

  public async restoreHtml() {
    const { ctx, service } = this;
    const { taskId } = ctx.request.body;

    try {
      // 获取任务信息
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['taskId', 'appKey'],
      });

      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }

      // 获取 html 和 machine.html 的 URL
      const htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'html', true, true);
      const machineHtmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'machine.html', true, true);

      // 调用还原接口
      const result = await ctx.curl('http://47.100.171.12:8088/api/open/xdoc_v2_html/restore', {
        method: 'POST',
        contentType: 'application/json',
        data: {
          task_id: task.taskId.toString(),
          html_url: htmlUrl,
          machine_html_url: machineHtmlUrl,
        },
        dataType: 'json',
        timeout: 60000,
      });

      if (result.status !== 200) {
        return ctx.body = baseError.dataNotExistError('HTML还原接口调用失败');
      }

      return ctx.body = {
        status: 0,
        msg: 'success',
        data: result.data.data,
      };
    } catch (error) {
      ctx.logger.error('还原 HTML 失败:', error);
      return ctx.body = baseError.serverError('还原 HTML 失败');
    }
  }

  public async restoreMachineHtml() {
    const { ctx, service } = this;
    const { taskId } = ctx.request.body;

    try {
      // 获取任务信息
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['taskId', 'appKey'],
      });

      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }

      // 检查是否为指定的 appKey
      const allowedAppKeys = ['d45dcb5325227a23b421cdeb', 'fc7539b21810cd4f0f0fb620'];
      if (!allowedAppKeys.includes(task.appKey)) {
        return ctx.body = baseError.dataNotExistError('该任务不支持此操作');
      }

      const machineHTML = await service.task.base.getOssData(task.appKey, task.taskId, 'machine.html', true, true);
      if (!machineHTML) {
        return ctx.body = baseError.dataNotExistError('machine.html内容不存在');
      }
      await service.task.base.setOssData(task.appKey, task.taskId, 'html', machineHTML);
      // 恢复 xdoc 任务状态
      await service.task.base.update(
        { status: service.task.base.statuses.split },
        { where: { taskId } }
      );
      return ctx.body = {
        status: 0,
        msg: 'success',
      };
    } catch (error) {
      ctx.logger.error('还原 machine.html 失败:', error);
      return ctx.body = baseError.serverError('还原 machine.html 失败');
    }
  }

  public async rehtmlFix() {
    const { ctx, service, logger, config } = this;
    const { taskId } = ctx.request.body;
    const { statuses } = service.task.base;

    try {
      // 获取任务信息
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['taskId', 'appKey', 'subject'],
      });

      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }

      // 检查是否为指定的 appKey
      const allowedAppKeys = ['d45dcb5325227a23b421cdeb', 'fc7539b21810cd4f0f0fb620'];
      if (!allowedAppKeys.includes(task.appKey)) {
        return ctx.body = baseError.dataNotExistError('该任务不支持此操作');
      }

      // 获取任务的 meta 信息
      const meta = await service.task.meta.getMetas({ taskId });

      // 获取 html 的 URL
      const htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'machine.html', true, false);

      // 设置上传路径
      const bucket = config.aliOss.bucket;
      const uploadPath = service.task.base.getOssKey(task.appKey, task.taskId, 'fixed.html');

      // 进入HTML修复流程
      await service.rbs.initRBSQueue({
        task_id: task.taskId.toString(),
        task_type: 'xdoc_html_fix_priority',
        task_info: {
          task_id: task.taskId.toString(),
          html_url: htmlUrl,
          subject: meta?.subject || task.subject,
          app_key: task.appKey,
          bucket_name: bucket,
          upload_path: uploadPath,
          is_ai_edit: meta.isAIEdit,
          get_task_parameters: [
            'task_id',
            'html_url',
            'subject',
            'is_ai_edit',
            'app_key',
            'bucket_name',
            'upload_path'
          ],
          callback_extras: [
            'task_id',
            'html_url',
            'subject',
            'app_key',
            'bucket_name',
            'upload_path'
          ],
          run_type: 'common',
          callback_url: config.ngrok.callbackUrl + '/api/open/task/updateHtmlFix',
          push_time: new Date().getTime(),
          timestamp: new Date().getTime(),
        },
      });

      logger.info(`admin.task.base.rehtmlFix: ${taskId}`);

      // 更新任务状态为 HTML 修复中
      await service.task.base.update(
        { status: statuses.htmlFixProcessing },
        { where: { taskId } }
      );

      return ctx.body = {
        status: 0,
        msg: 'success',
      };
    } catch (error) {
      ctx.logger.error('重跑 HTML 修复失败:', error);
      return ctx.body = baseError.serverError('重跑 HTML 修复失败');
    }
  }
}
