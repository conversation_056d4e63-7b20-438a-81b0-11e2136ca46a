/**
 * @file word 任务
 */

import { Controller } from 'egg';
import * as sequelize from 'sequelize';

import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';
import { ITaskMetas } from '../../../model/taskMeta';
import { ETaskType } from '../../../model/task';

export default class TaskWordController extends Controller {
  @validate({
    subject: ['chinese', 'math', 'en-math'],
    taskName: {
      type: 'string',
      max: 64,
    },
    appKey: 'string',
    wordIds: {
      type: 'array',
      itemType: 'string',
      min: 1,
      max: 250,
    },
    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',

        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    taskType: {
      type: 'enum',
      values: [0, 1],
      required: false,
    },
    timeLimit: 'number?',
    timeWarning: 'number?',
  })
  public async create() {
    const { ctx, service } = this;
    const { subject, taskName, appKey, wordIds, meta, taskType, timeLimit, timeWarning, priority } = ctx.input as {
      subject: 'math' | 'chinese' | 'en-math';
      taskName: string;
      appKey: string;
      priority?: number;
      wordIds: string[];
      meta: Partial<ITaskMetas>;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    };
    const exists = await service.appl.exists({ where: { appKey } });
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('客户信息不存在');
    }
    const taskIds = await service.task.base.wordCreate({
      appKey,
      subject,
      priority,
      inputType: 'id',
      words: wordIds,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName: taskName || '',
      taskType,
      timeLimit,
      timeWarning,
    });
    await service.task.meta.setMetas(taskIds, meta);
    ctx.runInBackground(async() => {
      await Promise.all(
        taskIds.map(async(taskId) => {
          await service.task.base.setOssMeta(appKey, taskId, meta);
        })
      );
    });
    ctx.body = {
      status: 0,
      data: { taskIds },
    };
  }

  @validate({
    bookId: 'number',
    taskName: {
      type: 'string',
      max: 64,
    },
    wordIds: {
      type: 'array',
      itemType: 'string',
      min: 1,
      max: 250,
    },
  })
  public async subTaskCreate() {
    const { service, ctx } = this;
    const { bookId, taskName, wordIds } = ctx.input as {
      bookId: number;
      taskName: string;
      wordIds: string[];
    };
    const bookStatuses = service.book.statuses;
    const [book, bookOrder] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.task.base.model.max('bookOrder', { where: { bookId } })
    ]);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('图书不存在');
    }
    const projectMeta = await service.project.meta.getMetas({ projectId: book.projectId });
    const taskIds = await service.task.base.wordCreate({
      appKey: book.appKey,
      subject: book.subject,
      open: false,
      callbackUrl: '',
      extra: '',
      isTest: false,
      taskName,
      bookId,
      bookOrder: (bookOrder || 0) + 1,
      inputType: 'id',
      words: wordIds,
    });
    await service.task.meta.setMetas(taskIds, projectMeta);
    await service.book.update({
      status: bookStatuses.processing,
      taskCount: sequelize.literal(`taskCount + ${taskIds.length}`),
    }, { where: { id: bookId } });

    // 将项目的 oss meta.json 同步到每个任务
    ctx.runInBackground(async() => {
      const ossClient = this.service.oss.createOss();
      const sourceKey = service.project.base.getOssKey(book.appKey, book.projectId, 'meta.json');
      await Promise.all(taskIds.map(async(taskId) => {
        const key = service.task.base.getOssKey(book.appKey, taskId, 'meta.json');
        await ossClient.copy(key, sourceKey);
      }));
    });

    ctx.body = {
      status: 0,
      data: { taskIds },
    };
  }

}
