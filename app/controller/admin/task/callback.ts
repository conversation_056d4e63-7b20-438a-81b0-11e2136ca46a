/**
 * @file 任务回调控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../../core/base/baseError';
import validate from '../../../core/decorators/validate';

export default class TaskCallbackController extends Controller {

  @validate({ taskId: 'number' })
  public async callback() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const { statuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    if (task.status !== statuses.reviewed) {
      return ctx.body = baseError.dataAlreadyExistError('任务未发布');
    }
    if (task.isCallback) {
      return ctx.body = baseError.dataAlreadyExistError('任务已经回调，无须重复回调');
    }
    await service.task.callback.callback(task, 1);
    ctx.body = { status: 0 };
  }

  @validate({
    taskIds: {
      type: 'array',
      itemType: 'number',
    },
  })
  public async batchCallback() {
    const { ctx, service } = this;
    const { taskIds } = ctx.input as { taskIds: number[] };
    const { statuses } = service.task.base;
    const tasks = await Promise.all(taskIds.map(async(taskId) => await service.task.base.getOne({ where: { taskId } })));
    const filterTasks = tasks.filter((task) => task && task.status === statuses.reviewed);
    if (!filterTasks.length) {
      return ctx.body = baseError.dataNotExistError('不存在需要回调的任务');
    }
    ctx.runInBackground(async() => {
      await Promise.all(filterTasks.map(async(task) => await service.task.callback.callback(task!, 1)));
    });
    ctx.body = {
      status: 0,
      tasks: filterTasks,
    };
  }
}
