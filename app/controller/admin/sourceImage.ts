/**
 * @file 原始图片操作
 */

'use strict';

import { Controller } from 'egg';
import { ESourceImageStatus } from '../../model/sourceImage';
import { ETaskResourceType } from '../../model/task';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class SourceImageController extends Controller {

   @validate({
     taskId: { type: 'number', required: false },
     bookId: { type: 'number', required: false },
     rerun: { type: 'boolean', required: false, default: false },
     taskType: {
       type: 'array',
       itemType: 'number',
       required: false,
     },
   })
  public async apply() {
    const { service, ctx, logger } = this;
    const { taskId, taskType } = ctx.input;
    if (!taskId) {
      return ctx.body = baseError.paramsError('taskId 不能为空');
    }
    const { userId } = ctx.data;
    const { statuses } = service.task.base;
    let task;
    const whereOpt: any = { taskId, status: statuses.preCropChecked };
    if (taskType) {
      whereOpt.taskType = taskType;
    }
    task = await service.task.base.getOne({
      where: whereOpt,
      attributes: ['taskId', 'rerun', 'preprocessUserId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('暂无可申领的任务');
    }
    if (task.preprocessUserId === userId) {
      return ctx.body = { status: 0, data: { taskId } };
    }
    const appliedTaskId = task.taskId;
    const processUserIdKey = 'preprocessUserId';
    const [affectedRows] = await service.task.base.update(
      {
        [processUserIdKey]: userId,
      },
      {
        where: {
          taskId: appliedTaskId,
          status: statuses.preCropChecked,
          [processUserIdKey]: 0,
        },
      }
    );
    if (!affectedRows) {
      return ctx.body = baseError.dataAlreadyExistError('任务已被申领');
    }
    logger.info(`task ${taskId} status changed : ${statuses.columnProcessing}`);
    const { status } = await service.task.base.getOne({ where: { taskId: appliedTaskId } }) as any;
    logger.info(`after affectedRows : ${affectedRows}, appliedTaskId : ${appliedTaskId}, status : ${status}`);
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        userId,
        taskId: appliedTaskId,
        type: service.task.history.preCropTypes.apply.id,
        costTime: 0,
        data: '',
      });
    });
    ctx.body = { status: 0, data: { taskId: appliedTaskId } };
  }

   public async getPreCropTaskList() {
     const { service, ctx } = this;
     const { statuses } = service.task.base;
     const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
     const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
     const key: string = ctx.input.key;
     const isOutAdmin: boolean = ctx.input.isOutAdmin;
     const where = { key, status: statuses.preCropChecked };
     const [count, tasks] = await Promise.all(!isOutAdmin ? [
       service.task.base.relatedCount({ where }),
       service.task.base.getRelatedList({
         page,
         pageSize,
         where,
         order: [['id', 'desc']],
       })
     ] : [
       service.task.base.exactCount({ where }),
       service.task.base.getExactList({
         page,
         pageSize,
         where,
         order: [['id', 'desc']],
       })
     ]);

     ctx.body = {
       status: 0,
       data: {
         page,
         pageSize,
         count,
         list: tasks,
       },
     };
   }

  @validate({ taskId: 'number' })
   public async getPreCropImages() {
     const { ctx, service } = this;
     const { taskId } = ctx.input as { taskId: number };
     const images = await service.sourceImage.getAll({ where: { taskId } });
     images.forEach((item) => {
       const url = service.image.getUrl(item.appKey, item.imageId, 'jpg');
       const info = item.info ? JSON.parse(item.info) : undefined;
       const result = item.result ? JSON.parse(item.result) : [];
       Object.assign(item, { url, info, result });
     });
     ctx.body = { status: 0, data: images };
   }

  @validate({ taskId: 'number' })
  public async restartPreCrop() {
    // 重新手工切图
    const { ctx, service, app } = this;
    const { userId } = ctx.data;
    const { taskId } = ctx.input as { taskId: number };
    const { statuses } = service.task.base;
    const taskExists = await service.task.base.exists({
      where: {
        taskId,
        status: [
          statuses.columnAutoProcessed, statuses.columnProcessing, statuses.columnProcessed,
          statuses.unmarked
        ],
        rerun: 0,
      },
    });
    if (!taskExists) {
      return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
    }
    const existsImage = await service.sourceImage.exists({ where: { taskId } });
    if (!existsImage) {
      return ctx.body = baseError.dataNotExistError('任务不存在原图');
    }
    await app.model.transaction(async(transaction) => {
      await service.image.delete({ transaction, where: { taskId } });
      await service.task.base.update({
        status: statuses.preCropChecked,
        preprocessUserId: 0,
        markUserId: 0,
      }, { transaction, where: { taskId } });
      await service.sourceImage.update({ status: ESourceImageStatus.init }, { transaction, where: { taskId } });
    });

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.restart.id,
        data: '',
        costTime: 0,
      });
      await service.task.base.deleteTaskConfig(taskId, 'imageColumnProcessor');
      await service.task.base.deleteTaskConfig(taskId, 'imageStructProcessor');
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    data: {
      type: 'array',
      required: false,
      itemType: 'object',
      rule: {
        imageId: 'string',
        result: {
          type: 'array',
          required: false,
          itemType: 'object',
          rule: {
            x: 'number',
            y: 'number',
            w: 'number',
            h: 'number',
            i: 'number?',
          },
          min: 0,
          max: 8,
        },
      },
      min: 1,
    },
  })
  public async submitPreCrop() {
    // 提交手工切图结果
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    const { taskId, data } = ctx.input as {
      taskId: number;
      // 每个图片的切图结果
      data: {
        imageId: string;
        result?: { x: number, y: number, w: number, h: number, i?: number }[];
      }[];
    };
    const res = await service.sourceImage.submitPreCrop(taskId, data);
    if (res.type === 0 || res.type === 1) {
      return ctx.body = res.msg;
    }
    //
    // const task = await service.task.base.getOne({
    //   where: { taskId, status: statuses.preCropChecked },
    //   attributes: ['id'],
    // });
    // if (!task) {
    //   return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
    // }
    // const existImageCount = await service.sourceImage.count({
    //   where: { imageId: data.map(item => item.imageId), taskId },
    // });
    // if (existImageCount !== data.length) {
    //   return ctx.body = baseError.dataNotExistError('部分图片不存在');
    // }
    // // 保存图片结果，更新图片状态
    // // @todo：这里建议用 bulkCreate 方法重写，优化性能！
    // for (let d = 0; d < data.length; d += 1) {
    //   await service.sourceImage.update(
    //       { status: ESourceImageStatus.finished, result: data[d].result?.length ? JSON.stringify(data[d].result) : '' },
    //       { where: { imageId: data[d].imageId, taskId } });
    // }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.submit.id,
        data: JSON.stringify(data),
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number', isNewVersion: 'boolean?' })
  public async finishPreCrop() {
    // 完成手工切图。系统将按提交结果对图片切割。
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    const { taskId, isNewVersion } = ctx.input as { taskId: number; isNewVersion?: boolean };

    // 如果传入了 isNewVersion 参数，将其存储在 task_meta 中的 isNewImageVersion 字段
    if (typeof isNewVersion === 'boolean') {
      const currentMeta = await service.task.meta.getMetas({ taskId });
      await service.task.meta.setMetas([taskId], { ...currentMeta, isNewImageVersion: isNewVersion });
    }

    const res = await service.sourceImage.finishTask(taskId);
    if (res) {
      ctx.body = res;
      return;
    }
    /*
     * const [affectedCount] = await service.task.base.update(
     *   { status: statuses.preCropProcessed },
     *   { where: { taskId, status: statuses.preCropChecked } },
     * );
     * if (!affectedCount) {
     *   return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
     * }
     * await service.task.base.pushPreCrop(taskId);
     */

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.finish.id,
        data: '',
        costTime: 0,
      });

      // 更新 stat
      const task = await service.task.base.getOne({ where: { taskId } });
      const images = await service.sourceImage.getAll({ where: { taskId } });
      await service.task.stat.newStat({
        html: '',
        taskId,
        type: 'pre_crop',
        appKey: task!.appKey,
        subject: task!.subject,
        imageCount: 0,
        resourceType: task!.resourceType!,
        userId,
        preCropCount: images.length,
      });
    });

    ctx.body = { status: 0 };
  }

  @validate({ appKey: 'string', imageId: 'string', angle: 'number' })
  public async rotateImage() {
    const { ctx, service } = this;
    const { appKey, imageId, angle } = ctx.input as { appKey: string, imageId: string, angle: 90 | -90 | 180 };
    await service.task.base.rotatePreImage(appKey, imageId, angle);

    ctx.body = { status: 0 };
  }

  @validate(['number'])
  public async batchFinishPreCrop() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // 重新写一个方法从数据库一次获取到request body中任务的所有图片信息
    const taskIds = Object.values(ctx.input as number[]);
    const res = await service.sourceImage.batchFinishPreCrop(taskIds, ctx.userId);
    res.forEach((taskInfo) => {
      const { taskId, imageLength, task } = taskInfo;
      // 记录操作日志
      ctx.runInBackground(async() => {
        await service.task.history.create({
          taskId: taskInfo.taskId,
          userId,
          type: service.task.history.preCropTypes.finish.id,
          data: '',
          costTime: 0,
        });

        // 更新 stat
        await service.task.stat.newStat({
          html: '',
          taskId,
          type: 'pre_crop',
          appKey: task!.appKey,
          subject: task!.subject,
          imageCount: 0,
          resourceType: task!.resourceType!,
          userId,
          preCropCount: imageLength,
        });
      });
    });
    ctx.body = { status: 0 , data: res };
  }

  public async getFinishPreCropTaskList() {
    const { service, ctx } = this;
    const taskId = ctx.input.taskId;
    const where = { taskId: Number(taskId) };
    const tasks = await service.sourceImage.getList({
      where,
      order: [['id', 'desc']],
    });

    ctx.body = {
      status: 0,
      data: {
        list: tasks.map((v) => ({
          createTime: v.createTime,
          originImageUrl: service.image.getUrl(v.appKey, v.imageId, 'jpg'),
          taskId: v.taskId,
          result: v.result,
          info: v.info,
        })),
      },
    };
  }

  @validate({ taskId: 'number' })
  public async rerunTask() {
    // 重跑任务：如果走了新流程，修改状态为预切图，任务的 resourceType 修改为 IMAGE
    const { ctx, service, app } = this;
    const { userId } = ctx.data;
    const { taskId } = ctx.input as { taskId: number };

    // 检查任务是否存在
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    // 从 task_meta 中读取 isNewImageVersion 参数
    const taskMeta = await service.task.meta.getMetas({ taskId });
    const isNewImageVersion = taskMeta.isNewImageVersion;

    if (isNewImageVersion) {
      // 如果走了新流程，修改任务状态和资源类型
      await app.model.transaction(async(transaction) => {
        const { statuses } = service.task.base;

        // 修改任务状态为预切图检查完成状态，resourceType 修改为 IMAGE
        await service.task.base.update({
          status: statuses.preCropChecked,
          resourceType: ETaskResourceType.IMAGE,
          preprocessUserId: 0,
          markUserId: 0,
          reviewUserId: 0,
          errorInfo: '',
        }, { transaction, where: { taskId } });

        // 重置 source_image 状态
        await service.sourceImage.update(
          { status: ESourceImageStatus.init },
          { transaction, where: { taskId } }
        );
      });

      // 记录操作日志
      ctx.runInBackground(async() => {
        await service.task.history.create({
          taskId,
          userId,
          type: service.task.history.preCropTypes.restart.id,
          data: JSON.stringify({ isNewImageVersion: true }),
          costTime: 0,
        });
      });

      ctx.body = {
        status: 0,
        message: '任务重跑成功，已修改为新流程（预切图状态，资源类型为图片）',
      };
    } else {
      // 如果没有走新流程，返回提示
      ctx.body = {
        status: 1,
        message: '该任务未使用新流程，无需重跑',
      };
    }
  }
}
