/**
 * @file 公式处理
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class FormulaController extends Controller {

  @validate({ url: 'string', taskId: 'number?' })
  public async detect() {
    const { ctx, app, logger } = this;
    const { url, taskId } = ctx.input;
    const data = await app.getLatexV2(url);
    if (!data || !data.latex) {
      logger.info(`get mp result error, url : ${url}, data : ${data ? JSON.stringify(data) : null}`);
      return ctx.body = baseError.serverError(data && data.error ? data.error : '服务异常');
    }
    if (taskId) {
      const task = await this.service.task.base.getOne({ where: { taskId } });
      if (task) {
        await this.service.task.base.update({ formulaRequestCount: (task.formulaRequestCount || 0) + 1 }, { where: { taskId } });
      }
    }
    ctx.body = {
      status: 0,
      data: {
        latex: data.latex,
        confidence: Math.floor(data.confidence * 100) / 100,
      },
    };
  }

  public async detectInside() {
    const { ctx, app } = this;
    let latex = '';
    const res = await app.getLatexInside(ctx.input.url);
    latex = res || '';
    ctx.body = {
      status: 0,
      data: {latex},
    };
  }
}
