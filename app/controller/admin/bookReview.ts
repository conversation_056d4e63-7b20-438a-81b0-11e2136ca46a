/**
 * @file 书本控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import * as _ from 'lodash';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class BookReviewController extends Controller {

  @validate({ bookId: 'number' })
  public async apply() {
    const { service, ctx } = this;
    const { bookId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.book;
    const taskStatuses = service.task.base.statuses;
    const [book, exists] = await Promise.all([
      service.book.getOne({ where: { id: bookId } }),
      service.task.base.exists({ where: { bookId, status: { $not: taskStatuses.reviewed }, mergedTaskId: null } })
    ]);
    if (!book || exists || [statuses.reviewed, statuses.reviewing].includes(book.status)) {
      return ctx.body = baseError.dataNotExistError('书本不存在, 或者是无法审核的任务状态');
    }
    await service.book.update(
      { reviewUserId: userId, startReviewTime: new Date(), status: statuses.reviewing },
      { where: { id: bookId } }
    );
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.bookHistory.create({
        bookId,
        userId,
        type: service.bookHistory.reviewTypes.apply,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ bookId: 'number' })
  public async quit() {
    const { service, ctx } = this;
    const { bookId } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.book;
    const book = await service.book.getOne({ where: { id: bookId } });
    if (!book || book.status !== statuses.reviewing) {
      return ctx.body = baseError.dataNotExistError('书本不存在, 或者是无法退回的任务状态');
    }
    if (book.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人审核，无权限');
    }
    await service.book.update(
      { reviewUserId: 0, startReviewTime: null, status: statuses.unreviewed },
      { where: { id: bookId } }
    );
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.bookHistory.create({
        bookId,
        userId,
        type: service.bookHistory.reviewTypes.quit,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ bookId: 'number', costTime: { type: 'number', required: false } })
  public async confirm() {
    const { service, ctx } = this;
    const { bookId, projectId, costTime } = ctx.input;
    const { statuses } = service.book;
    const { userId } = ctx.data;
    const [books, tasks] = await Promise.all([
      service.book.getAll({
        where: { projectId },
        attributes: ['id', 'status', 'appKey', 'reviewUserId'],
      }),
      service.task.base.getAll({
        where: { bookId, mergedTaskId: null },
        attributes: ['taskId', 'status', 'appKey'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      })
    ]);
    let hasBook = false;
    let bookAppKey;
    let hasAllReviewed = true;
    for (const { status, id, appKey, reviewUserId } of books) {
      if (id === bookId) {
        hasBook = true;
        bookAppKey = appKey;
        if (status !== statuses.reviewing) {
          return ctx.body = baseError.dataAlreadyExistError('无法提交的书本状态');
        }
        if (reviewUserId !== userId) {
          return ctx.body = baseError.dataNotExistError('非本人审核，无权限');
        }
      }
      if (status !== statuses.reviewed && id !== bookId) {
        hasAllReviewed = false;
      }
    }
    if (!hasBook) {
      return ctx.body = baseError.dataNotExistError('书本不存在');
    }
    for (const { taskId, status } of tasks) {
      if (status !== service.task.base.statuses.reviewed) {
        return ctx.body = baseError.dataNotExistError(`部分任务未审核完毕,任务id:${taskId}`);
      }
    }
    await service.book.combineAndUploadByTaskIds(bookAppKey, bookId, tasks);
    await service.book.update(
      { status: statuses.reviewed, endReviewTime: new Date() },
      { where: { id: bookId } }
    );
    if (hasAllReviewed) {
      await service.project.base.update(
        { status: service.project.base.statuses.unreviewed },
        { where: { id: projectId } }
      );
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.bookHistory.create({
        bookId,
        userId,
        type: service.bookHistory.reviewTypes.confirm,
        data: '',
        costTime: costTime || 0,
      });
    });
    ctx.body = { status: 0 };
  }

}
