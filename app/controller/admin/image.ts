/**
 * @file 图片控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import * as _ from 'lodash';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class ImageController extends Controller {

  @validate({ imageId: 'string' })
  public async getOneById() {
    const { ctx, service } = this;
    const { imageId } = ctx.input;
    const image = await service.image.getOne({
      where: { imageId, disabled: false },
      attributes: ['imageId', 'marked', 'reviewed', 'taskId', 'appKey', 'originalId', 'sourceId'],
    }) as any;
    if (!image) {
      return ctx.body = baseError.dataNotExistError('图片不存在');
    }
    const task = await service.task.base.getRelateOne({ where: { taskId: image.taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在或已经删除');
    }
    const formulas = await service.formula.getAll({
      where: { imageId, disabled: false },
      attributes: ['formulaId', 'latex', 'coordinate', 'marked', 'reviewed', 'confidence'],
    }) as any[];
    formulas.forEach((formula) => {
      formula.url = service.formula.getUrl(image.appKey, imageId, formula.coordinate);
    });
    image.taskName = task.taskName;
    image.url = service.image.getUrl(image.appKey, image.imageId, 'jpg', false);
    image.missUrl = service.image.getMissImageUrl(image.appKey, image.imageId);
    image.originalUrl = image.originalId ? service.image.getUrl(image.appKey, image.originalId, 'jpg', false) : null;
    image.sourceUrl = image.sourceId ? service.image.getUrl(image.appKey, image.sourceId, 'jpg', false) : null;
    image.html = await service.image.getOssData(image.appKey, imageId, 'html');
    image.formulas = _.orderBy(formulas, (f) => -f.confidence);
    ctx.body = {
      status: 0,
      data: image,
    };
  }

  @validate({ imageId: 'string' })
  public async getUrls() {
    const { ctx, service } = this;
    const imageIds = (ctx.input.imageId || '').split(',');
    if (!imageIds.length) {
      return ctx.body = baseError.paramsError();
    }
    const images = await service.image.getAll({ where: { imageId: imageIds }, attributes: ['taskId'] });
    if (!images.length) {
      return ctx.body = baseError.dataNotExistError('图片不存在');
    }
    const taskIds = Array.from(new Set(images.map((item) => item.taskId)));
    const [task, allImages] = await Promise.all([
      service.task.base.getOne({ where: { taskId: taskIds }, attributes: ['appKey'] }),
      service.image.getAll({
        where: { taskId: taskIds, disabled: false },
        attributes: ['imageId', 'filename', 'appKey', 'taskOrder', 'originalId'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task || !allImages || !allImages.length) {
      return ctx.body = baseError.serverError('数据异常');
    }
    ctx.body = {
      status: 0,
      data: service.image.sortImageByFile(allImages).map((item) => {
        return {
          imageId: item.imageId,
          url: service.image.getUrl(item.appKey, item.imageId, 'jpg', false),
          originalUrl: item.originalId ? service.image.getUrl(item.appKey, item.originalId, 'jpg', false) : null,
        };
      }),
    };
  }

  @validate({ imageId: 'string' })
  public async getOriginalUrls() {
    const { ctx, service } = this;
    const imageIds: string[] = _.uniq((ctx.input.imageId || '').split(','));
    if (!imageIds.length) {
      return ctx.body = baseError.paramsError();
    }
    const images = await service.image.getAll({
      where: { imageId: imageIds },
      attributes: ['imageId', 'filename', 'appKey', 'taskOrder', 'originalId'],
      order: [['id', 'ASC']],
    });
    if (!images.length) {
      return ctx.body = baseError.dataNotExistError('图片不存在');
    }
    type TImage = { imageId: string; url: string; images: { imageId: string; url: string }[] };
    const data: TImage[] = [];
    const map = {} as { [url: string]: TImage };
    service.image.sortImageByFile(images).forEach((item) => {
      const columnImageId = item.imageId as string;
      const originalId = item.originalId as string;
      const columnUrl = service.image.getUrl(item.appKey, columnImageId, 'jpg', false);
      const originalUrl = originalId ? service.image.getUrl(item.appKey, originalId, 'jpg', false) : null;
      const url = originalUrl || columnUrl;
      const imageId = originalId || columnImageId;
      const columnImage = { imageId: columnImageId, url: columnUrl };
      if (!map[url]) {
        const image = { imageId, url, images: [] };
        map[url] = image;
        data.push(image);
      }
      map[url].images.push(columnImage);
    });

    ctx.body = {
      status: 0,
      data,
    };
  }
}
