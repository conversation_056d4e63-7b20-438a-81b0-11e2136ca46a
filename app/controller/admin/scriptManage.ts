/**
 * @file 应用控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import { spawn } from 'child_process';
import * as path from 'path';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';

/* tslint:disable:prefer-template */
const SCRIPTS = [
  {
    id: 0,
    name: '【希沃】脚本1 移除末级 chapter',
    desc: '针对大于3级的末级chapter，将chapter的body，' +
      '插入到下级的所有question节点中，并移除该末级chapter节点，子节点合并到上一级。\n' +
      '如末级chapter节点的字节中仅包含para节点，直接移除para节点，并移除该末级chapter节点。',
    file: 'script1.py',
  },
  {
    id: 0,
    name: '【希沃】脚本2 移除小题标签',
    desc: '移除所有小题的 question tag 和 difficulty',
    file: 'script2.py',
  },
  {
    id: 0,
    name: '【希沃】脚本3 减少 chapter 层级',
    desc: '将大于2级或大于3级（可以给个参数）的chapter节点全部移除，' +
      '移除的每一个Chapter的子节点中question节点向上合并。\n' +
      '（将整个JSON的目录结构调整为2级或3级），需要参数指定层级！',
    params: ['指定层级'],
    file: 'script3.py',
  },
  {
    id: 0,
    name: '脚本 使chapter的level连续',
    desc: '通过补全的方式，让所有chapter的level是连续的。\n' +
      '现阶段会存在chapter level父子节点不连续的情况。\n' +
      '在不连续的时候通过增加chapter节点的方式让level保持连续。\n' +
      '增加的chapter的Body按照上一级的chapter的内容来进行添加。',
    file: 'script4.py',
  },
  {
    id: 0,
    name: '【希沃】脚本5 内容处理',
    desc: '1.数组类型的答案替换为用英文分号分隔的字符串。\n' +
      '2.选择题：如果题干里没有括号或者横线的话，题干部分最后，选项字母之前统一增加一个括号。\n' +
      '3.填空题：里面的作答区域如果是括号统一替换成横线。\n' +
      '4.判断题：统一增加选项字段，选项后的内容根据答案来确定。' +
      '比如：答案是错误，正确，那么我们在题干部分增加 A.正确；B.错误 这两个选项',
    file: 'script5.py',
  },
  {
    id: 0,
    name: '【希沃】脚本6 减少 question 层级',
    desc: '对于嵌套太深的试题，将父节点的题干合并到子节点中，并删除父节点，来减少层级，保证深度不超过2。\n' +
      '简单题目最多保留 level=1/2，材料题保留level=0/1',
    file: 'script6.py',
  },
  {
    id: 0,
    name: '【希沃】脚本4 移除 disabled',
    desc: '删除disabled="disabled"',
    file: 'script_remove_disabled.py',
  },
  {
    id: 0,
    name: '【锐学堂】锐学堂定制JSON转换',
    desc: '锐学堂定制JSON转换',
    file: 'transformJson.js',
  },
  {
    id: 0,
    name: '【出版】段落中将p拆分成多个段落',
    desc: '段落中将p拆分成多个段落',
    file: 'script7.py',
  }
];

SCRIPTS.forEach((item, index) => {
  item.id = index + 1;
  item.params = item.params || [];
});

export default class ScriptManageController extends Controller {
  async getScripts() {
    const { ctx } = this;
    ctx.body = {
      status: 0,
      data: {
        scripts: SCRIPTS,
        params: ['学科，如 math'],
      },
    };
  }

  @validate({
    jsonStr: 'string?',
    projectId: 'number?',
    taskId: 'number?',
    scripts: {
      type: 'array',
      itemType: 'object',
      rule: {
        id: 'number',
        params: {
          type: 'array',
          itemType: 'string',
          required: false,
        },
      },
      min: 1,
    },
    params: {
      type: 'array',
      itemType: 'string',
      required: false,
    },
  })
  async runScripts() {
    const { ctx, service, config } = this;
    let { jsonStr, projectId, taskId, scripts, params } = ctx.input as {
      jsonStr?: string;
      projectId?: number;
      taskId?: number;
      scripts: {
        id: number;
        params?: string[];
      }[];
      params?: string[];
    };
    if (jsonStr && projectId && taskId || !jsonStr && !projectId && !taskId) {
      return ctx.body = baseError.paramsError('参数 jsonStr, projectId, taskId 三选一');
    }
    let json;
    let extraParams;
    if (jsonStr) {
      extraParams = params || [''];
    } else if (projectId) {
      const project = await service.project.base.getOne({
        where: { id: projectId },
        attributes: ['appKey'],
      });
      if (!project) {
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }
      json = await service.project.base.getOssData(project.appKey, projectId, 'official.json');
      const meta = await service.project.meta.getMetas({ projectId, key: 'subject' });
      jsonStr = JSON.stringify(json);
      extraParams = [meta.subject || ''];
    } else if (taskId) {
      const task = await service.task.base.getOne({
        where: { taskId },
        attributes: ['appKey'],
      });
      if (!task) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }
      json = await service.task.base.getOssData(task.appKey, taskId, 'json');
      const meta = await service.task.meta.getMetas({ taskId, key: 'subject' });
      jsonStr = JSON.stringify(json);
      extraParams = [meta.subject || ''];
    } else {
      return;
    }

    if (scripts.some((item) => !SCRIPTS[item.id - 1])) {
      return ctx.body = baseError.dataNotExistError('脚本不存在');
    }

    for (const item of scripts) {
      const script = SCRIPTS[item.id - 1];
      const filePath = path.resolve(__dirname, '../../../scripts', script.file);
      const sp = spawn(
        script.file.endsWith('.py') ? config.python.bin : 'node',
        [filePath, ...item.params || [], ...extraParams]
      );
      sp.stdin.end(jsonStr, 'utf-8');
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const data: Buffer[] = [];
        sp.stdout.on('data', (chunk) => data.push(chunk));
        sp.stdout.on('end', () => resolve(Buffer.concat(data)));
        sp.stderr.on('data', (e) => reject(e.toString()));
      });
      jsonStr = buffer.toString();
    }

    ctx.body = {
      status: 0,
      data: jsonStr,
    };

  }
}
