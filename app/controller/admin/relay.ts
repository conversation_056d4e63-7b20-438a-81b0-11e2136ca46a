
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class RelayController extends Controller {
  @validate({ url: 'string' })
  public async getImagesPosInImage() {
    const { ctx, app } = this;
    const { url } = ctx.input as {
      url: string;
    };
    try {
      const res = await app.curl('http://47.100.171.12:55556/pdf_image_extract', {
        method: 'POST',
        dataType: 'json',
        data: { image_url: url },
      });
      if (res.status === 200) {
        ctx.body = {
          status: 0,
          data: { imagesPos: res.data },
        };
      } else {
        throw new Error('接口异常');
      }
    } catch (e) {
      ctx.body = baseError.serverError('接口异常');
    }
  }
}
