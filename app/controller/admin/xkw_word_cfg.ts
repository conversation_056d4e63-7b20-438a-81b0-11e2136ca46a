const config = {
  'header': {
    'content': '<p><img src=\'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/temp/xkw-header1.png\' alt=\'\' width=\'100\' height=\'50\' /><img src=\'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/temp/xkw-header2.png\' alt=\'\' width=\'100\' height=\'50\' /></p>',
    'style': {
      'font_size': '10.5pt',
      'font_family': {
        'east_asia': 'SimSun',
        'latin': 'Times New Roman',
      },
      'color': '000000',
    },
    'is_over_page_width': false,
    'frame': {
      'position': {
        'x': 0,
        'y': -960,
      },
      'anchor': {
        'horizontal': 'margin',
        'vertical': 'margin',
      },
      'alignment': {
        'x': 'right',
        'y': 'top',
      },
    },
  },
  'page_cfg': {
    'page_size': {
      'width': '21cm',
      'height': '29.7cm',
    },
    'page_margin': {
      'top': '2.3cm',
      'bottom': '2.3cm',
      'left': '1.7cm',
      'right': '1.7cm',
    },
    'spacing': { 'line': 288 },
    'paper_orientation': 'portait',
    'page_column_count': false,
  },
  'global_cfg': {
    'indent_unit_length': 2,
    'image': {
      'align': 'left',
      'zoom_rate': 100,
    },
    'table': {
      'align': 'center',
      'style': {
        'font_family': {
          'east_asia': 'SimSun',
          'latin': 'Times New Roman',
        },
        'color': '000000',
      },
    },
    'question': {
      'is_children_body_follow_parent_body': false,
      'answer_in_answer_space': false,
      'clear_indent_exclude_material': true,
      'merge_material_answer': false,
    },
    'bracket': {
      'bracket_length': 3,
      'space_style': {
        'font': {
          'latin': '宋体',
          'east_asia': '宋体',
          'ascii': '宋体',
          'hAnsi': '宋体',
        },
      },
    },
    'inline_serial_choice_answer': true,
    'use_replace_text': false,
    'answer_in_docx_ending': true,
  },
  'default_style': {
    'font_family': {
      'east_asia': '宋体',
      'latin': 'Times New Roman',
    },
    'color': '000000',
    'line_height': 1.5,
  },
  'class_cfg_list': [
    {
      'identity': { 'node_type': 'chapter' },
      'style': {
        'body': {
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 1 },
      },
      'style': {
        'body': {
          'bold': true,
          'font_size': '14pt',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
          'text_align': 'center',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 2 },
      },
      'style': {
        'body': {
          'bold': true,
          'font_size': '14pt',
          'text_align': 'center',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 3 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 4 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 5 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 6 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 7 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 8 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'chapter',
        'content': { 'level': 9 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'bold': true,
          'text_align': 'left',
        },
      },
    },
    {
      'identity': { 'node_type': 'paragraph' },
      'style': {
        'body': {
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
        },
        'answer': {
          'color': 'ff0000',
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
          'before': { 'content': '[答案]' },
        },
        'source': {
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
        },
      },
    },
    {
      'identity': {
        'node_type': 'paragraph',
        'content': { 'level': 1 },
      },
      'style': {
        'body': {
          'font_size': '12pt',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
        },
        'answer': {
          'color': 'ff0000',
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
          'before': { 'content': '[答案]' },
        },
        'source': {
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
        },
      },
    },
    {
      'identity': { 'node_type': 'question' },
      'style': {
        'serial_number': { 'after': { 'content': '．' } },
        'source': {
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
          'font_size': '10.5pt',
          'before': { 'content': '[' },
          'after': { 'content': ']' },
        },
        'choice_letter': { 'after': { 'content': '．' } },
        'answer': {
          'color': '000000',
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
          'before': { 'content': '【答案】' },
        },
        'analysis': {
          'color': '000000',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
          'font_size': '10.5pt',
          'before': { 'content': '【解析】' },
        },
        'body': {
          'bold': false,
          'font_size': '10.5pt',
          'font_family': {
            'east_asia': '宋体',
            'latin': 'Times New Roman',
          },
          'text_align': 'left',
        },
      },
    },
    {
      'identity': {
        'node_type': 'question',
        'question_type': 'material',
        '_has_child': true,
      },
      'style': {
        'body': {
          'font_family': {
            'east_asia': '楷体',
            'latin': 'Times New Roman',
          },
        },
      },
    },
    {
      'identity': { 'node_type': 'question' },
      'config': { 'body': { 'bracket_length': 3 } },
    },
    {
      'identity': [
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '例' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '）' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': ')' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '】' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '①' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '②' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '③' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '④' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑤' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑥' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑦' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑧' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑨' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑩' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❶' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❷' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❸' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❹' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❺' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❻' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❼' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❽' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❾' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '❿' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒈' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒉' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒊' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒋' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒌' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒍' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒎' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒏' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒐' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⒑' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑴' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑵' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑶' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑷' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑸' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑹' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑺' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑻' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑼' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '⑽' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈠' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈡' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈢' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈣' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈤' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈥' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈦' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈧' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈨' },
        },
        {
          'node_type': 'question',
          '_has_keyword': { 'serial_number': '㈩' },
        }
      ],
      'style': { 'serial_number': { 'after': { 'content': '' } } },
    }
  ],
  'disable_choice_layout': false,
  'is_word_html': false,
};

export default config;
