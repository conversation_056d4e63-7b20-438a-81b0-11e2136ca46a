/**
 * @file 开放任务控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
// import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { IClientMetas } from '../../model/clientMeta';
import baseError from '../../core/base/baseError';
import { EMarkJsonStatus } from '../../model/project';
import { iterateNode } from '../../core/utils/treeHelper';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';

export default class OpenTaskController extends Controller {

  // 查看应用下任务列表
  @validate({ appKey: 'string' })
  public async getList() {
    const { ctx, service } = this;
    const { appKey } = ctx.data;
    const { key } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = { appKey, bookId: 0 } as any;
    if (key) {
      if (/^[0-9]+$/.test(key)) {
        whereOpt.taskId = Number(key);
      } else {
        whereOpt.taskName = { $like: `%${key}%` };
      }
    }
    const { statuses, openStatuses } = service.task.base;
    const [count, tasks] = await Promise.all([
      service.task.base.count({ where: whereOpt }),
      service.task.base.getList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'ASC']],
      })
    ]);
    let clientMetas: Partial<IClientMetas> | undefined;
    const hasSuccessCallback = tasks.some((task) => task.status !== statuses.error && task.isCallback);
    if (hasSuccessCallback) {
      const appl = await service.appl.getOneByUc({
        where: { appKey },
        attributes: ['userId'],
      });
      if (appl && appl.userId) {
        clientMetas = await service.client.meta.getMetas({
          userId: appl.userId,
          key: ['exportJson', 'exportDocx'],
        });
      }
    }
    const config = service.client.meta.setDefaultMetas(clientMetas || {});
    const metaDict = await service.task.meta.getMetasDict({ taskId: tasks.map((t) => t.taskId) });
    const metaInfoDict = await service.project.meta.getMetaInfoDict(metaDict);
    const data = tasks.map((task) => {
      const markJson = task.markJsonStatus === EMarkJsonStatus.FINISHED ?
        service.task.base.getUrl(task.appKey, task.taskId, 'mark.official.json') :
        undefined;
      const markJsonStatus = task.markJsonStatus;
      // 只有当回调完成（成功或者失败后）才可以被查询
      if (task.status === statuses.error && task.isCallback) {
        return {
          taskId: task.taskId,
          taskName: task.taskName,
          subject: task.subject,
          imageCount: task.imageCount,
          status: openStatuses.failed,
          errorInfo: task.errorInfo,
          extra: task.extra,
          markJson,
          markJsonStatus,
        };
      }
      if (!task.isCallback) {
        return {
          taskId: task.taskId,
          taskName: task.taskName,
          subject: task.subject,
          imageCount: task.imageCount,
          status: openStatuses.processing,
          extra: task.extra,
          markJson,
          markJsonStatus,
        };
      }
      const meta = metaInfoDict[task.taskId];
      return {
        taskId: task.taskId,
        taskName: task.taskName,
        subject: task.subject,
        imageCount: task.imageCount,
        extra: task.extra,
        status: openStatuses.successful,
        isCallback: task.isCallback,
        callbackError: task.callbackError,
        docx: config.exportDocx ?
          service.task.base.getUrl(task.appKey, task.taskId, 'docx', false) :
          undefined,
        json: config.exportJson ?
          service.task.base.getUrl(task.appKey, task.taskId, 'json', false) :
          undefined,
        markJson,
        markJsonStatus,
        meta,
        metaJson: meta ? service.task.base.getUrl(task.appKey, task.taskId, 'meta.json', false) : undefined,
      };
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks: data,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async getOneById() {
    const { ctx, service } = this;
    const { taskId } = ctx.data as { taskId: number };
    const { statuses, openStatuses } = service.task.base;
    const task = await service.task.base.getOne({ where: { taskId, bookId: 0 } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    let clientMetas: Partial<IClientMetas> | undefined;
    const hasSuccessCallback = task.status !== statuses.error && task.isCallback;
    if (hasSuccessCallback) {
      const appl = await service.appl.getOneByUc({
        where: { appKey: task.appKey },
        attributes: ['userId'],
      });
      if (appl && appl.userId) {
        clientMetas = await service.client.meta.getMetas({
          userId: appl.userId,
          key: ['exportJson', 'exportDocx'],
        });
      }
    }
    const config = service.client.meta.setDefaultMetas(clientMetas || {});
    const meta = await service.task.meta.getMetas({ taskId });
    const metaInfo = await service.project.meta.getMetaInfo(meta);
    // 只有当回调完成（成功或者失败后）才可以被查询
    let data: any;
    if (task.status === statuses.error && task.isCallback) {
      data = {
        taskId: task.taskId,
        taskName: task.taskName,
        subject: task.subject,
        imageCount: task.imageCount,
        status: openStatuses.failed,
        errorInfo: task.errorInfo,
        extra: task.extra,
      };
    } else if (!task.isCallback) {
      data = {
        taskId: task.taskId,
        taskName: task.taskName,
        subject: task.subject,
        imageCount: task.imageCount,
        status: openStatuses.processing,
        extra: task.extra,
      };
    } else {
      data = {
        taskId: task.taskId,
        taskName: task.taskName,
        subject: task.subject,
        imageCount: task.imageCount,
        extra: task.extra,
        status: openStatuses.successful,
        isCallback: task.isCallback,
        callbackError: task.callbackError,
        docx: config.exportDocx ?
          service.task.base.getUrl(task.appKey, task.taskId, 'docx', false) :
          undefined,
        json: config.exportJson ?
          service.task.base.getUrl(task.appKey, task.taskId, 'json', false) :
          undefined,
        meta: metaInfo,
        metaJson: meta ? service.task.base.getUrl(task.appKey, task.taskId, 'meta.json', false) : undefined,
      };
    }

    data.markJsonStatus = task.markJsonStatus;
    data.markJson = task.markJsonStatus === EMarkJsonStatus.FINISHED ?
      service.task.base.getUrl(task.appKey, task.taskId, 'mark.official.json') :
      undefined;

    ctx.body = {
      status: 0,
      data,
    };
  }

  @validate({
    type: ['imageStructProcessor'],
    appKey: 'string',
    subject: ['chinese', 'math', 'en-math'],
    imageIds: {
      type: 'array',
      itemType: 'string',
      max: 30,
    },
    callbackUrl: {
      type: 'string',
      required: false,
      max: 256,
    },
    taskName: {
      type: 'string',
      required: false,
      max: 64,
    },
    extra: {
      type: 'string',
      required: false,
      max: 128,
    },
    meta: {
      type: 'object',
      rule: {
        type: 'number',
        textType: 'number',
        columnType: 'number',
        collectType: 'number',
        stage: 'string?',
        subject: 'string?',
        gradeUid: 'string?',
        editionUid: 'string?',
      },
    },
    open: 'boolean?',
  })
  public async create() {
    const { ctx, service } = this;
    // type暂时支持imageStructProcessor,只支持异步任务
    const { callbackUrl, subject, imageIds, taskName, extra, appKey, meta, open } = ctx.input;
    const taskId = await service.task.base.relatedCreate({
      appKey,
      subject,
      callbackUrl: callbackUrl || '',
      images: imageIds,
      open: open || false, // 控制图片是否转存，检查有效性
      extra: extra || '',
      isTest: false,
      taskName: taskName || '',
    }, {});
    await service.task.meta.setMetas([taskId], meta);
    ctx.runInBackground(async() => {
      await service.task.base.setOssMeta(appKey, taskId, meta);
    });
    ctx.body = {
      status: 0,
      data: { taskId },
    };
  }

  public async getJsonUrls() {
    const { ctx, service } = this;
    const { taskId, projectId, bookId } = ctx.input;
    const { statuses } = service.task.base;
    if (!taskId && !projectId && !bookId) {
      ctx.body = baseError.paramsError('参数 taskId、bookId、projectId 三选一');
      return;
    }
    const whereOpt: any = {};
    if (taskId) {
      const taskIds = typeof taskId === 'number' ? [taskId] : taskId.split(',');
      whereOpt.taskId = taskIds.length > 1 ? { $in: taskIds } : taskIds[0];
    }
    if (bookId) {
      whereOpt.bookId = bookId;
    } else if (projectId) {
      const book = await service.book.getOne({
        where: { projectId, type: service.book.types.question },
        attributes: ['id'],
      });
      whereOpt.bookId = book!.id;
    }
    const tasks = await service.task.base.getAll({
      where: whereOpt,
      attributes: ['taskId', 'appKey', 'status'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    tasks.forEach((task: any) => {
      const hasJson = [statuses.reviewing, statuses.reviewed].includes(task.status);
      if (hasJson) {
        task.jsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'json', true);
        task.internalJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'internal.json', true);
      }
      task.markJsonUrl = service.task.base.getUrl(task.appKey, task.taskId, 'mark.json', true);
    });
    ctx.body = {
      status: 0,
      data: tasks,
    };
  }

  public async saveJson() {
    const { ctx, service } = this;
    const { taskId, data } = ctx.input;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['appKey'],
    });
    if (!task) {
      ctx.body = baseError.dataNotExistError('任务不存在');
      return;
    }
    await service.task.base.setOssData(task.appKey, taskId, 'mark.json', data);
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    force: 'boolean?',
  })
  public async startMarkJson() {
    const { service, ctx } = this;
    const { taskId, force } = ctx.input as {
      taskId: number;
      force?: boolean;
    };
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['markJsonStatus', 'appKey'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.markJsonStatus === EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('任务正在标注json中');
    }
    if (task.markJsonStatus === EMarkJsonStatus.FINISHED && force ||
      task.markJsonStatus === EMarkJsonStatus.INIT) {
      // 使用 internal.json 做为 mark.json
      await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'mark.json');
    } else {
      const oss = service.oss.createOss();
      try {
        // 检查是否有。已经标注过应该是有的
        await oss.head(service.task.base.getOssKey(task.appKey, taskId, 'mark.json'));
      } catch (e) {
        // 使用 internal.json 做为 mark.json
        await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'mark.json');
      }
    }

    await service.task.base.update(
      { markJsonStatus: EMarkJsonStatus.ING },
      { where: { taskId } }
    );

    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async finishMarkJson() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as { taskId: number };
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['markJsonStatus', 'appKey'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.markJsonStatus !== EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('任务不在标注json中');
    }

    let json: any[] = await service.task.base.getOssData(task.appKey, taskId, 'mark.json');
    // 过滤不导出
    json = json.filter((node) => node.isExport !== false);
    for (const { node } of iterateNode(json)) {
      node.children = node.children ? node.children.filter((node) => node.isExport !== false) : [];
      delete node.isExport;
    }
    // 试题添加题型标签名称、知识点名称
    await service.project.base.setJsonTags(json);
    await service.task.base.setOssData(task.appKey, taskId, 'mark.official.json', cleanJsonNodes(json));
    await service.task.base.update(
      { markJsonStatus: EMarkJsonStatus.FINISHED },
      { where: { taskId } }
    );

    ctx.body = { status: 0 };
  }

}
