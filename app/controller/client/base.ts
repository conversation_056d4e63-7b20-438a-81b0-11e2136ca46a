'use strict';

import { Controller } from 'egg';

export default class ClientController extends Controller {
  public async getConfig() {
    const { ctx, service } = this;
    const { userId } = ctx.data;

    const metas = await service.client.meta.getMetas({ userId });
    const data = service.client.meta.setDefaultMetas(metas);

    ctx.body = {
      status: 0,
      data: {
        userId,
        data,
      },
    };
  }
}
