/**
 * @file 图片控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class ApplicationController extends Controller {

  @validate({ taskId: 'number' })
  public async getAllByTaskId() {
    const { ctx, service } = this;
    const { taskId } = ctx.input;
    const [exists, images] = await Promise.all([
      service.task.base.exists({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, originalId: '' },
        attributes: ['imageId', 'filename', 'appKey', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!exists) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    ctx.body = {
      status: 0,
      data: service.image.sortImageByFile(images).map((image) => {
        return {
          imageId: image.imageId,
          url: service.image.getUrl(image.appKey, image.imageId, 'jpg'),
        };
      }),
    };
  }
}
