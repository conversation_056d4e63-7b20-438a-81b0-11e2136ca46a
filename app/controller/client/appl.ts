/**
 * @file 应用控制
 * <AUTHOR>
 */
'use strict';

import { Controller } from 'egg';

export default class ApplicationController extends Controller {
  public async getAllByUser() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    const applications = await service.appl.getAllByUc({
      where: { userId, isActive: true },
      attributes: ['appKey', 'isTest', 'appName'],
    });
    ctx.body = {
      status: 0,
      data: applications.map((item) => {
        item.appSecret = '**********';
        return item;
      }),
    };
  }
}
