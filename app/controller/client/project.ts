/**
 * @file 项目控制
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { EMarkJsonStatus } from '../../model/project';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import { iterateNode } from '../../core/utils/treeHelper';

export default class ProjectController extends Controller {

  // 查看应用下项目列表
  @validate({ appKey: 'string' })
  public async getList() {
    const { ctx, service } = this;
    const { appKey } = ctx.data;
    const { key } = ctx.input;
    const { statuses, openStatuses } = service.project.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = { appKey } as any;
    if (key) {
      whereOpt.projectName = { $like: `%${key}%` };
    }
    const [count, projects] = await Promise.all([
      service.project.base.count({ where: whereOpt }),
      service.project.base.getList({
        page,
        pageSize,
        where: whereOpt,
        attributes: ['id', 'appKey', 'status', 'markJsonStatus', 'projectName'],
        order: [['id', 'DESC']],
      })
    ]);
    const metasDict = await service.project.meta.getMetasDict({ projectId: projects.map((p) => p.id) });
    const metaInfoDict = await service.project.meta.getMetaInfoDict(metasDict);
    const data = projects.map((project) => {
      const meta = metasDict[project.id] || {};
      const metaInfo = metaInfoDict[project.id];
      if (meta.bookName && metaInfo) {
        (metaInfo as any).bookName = meta.bookName;
      }
      return {
        id: project.id,
        projectName: project.projectName,
        status: project.status === statuses.reviewed
          ? openStatuses.successful
          : openStatuses.processing,
        markJsonStatus: project.markJsonStatus,
        json: project.status === statuses.reviewed
          ? service.project.base.getUrl(project.appKey, project.id, 'official.json')
          : undefined,
        markJson: project.markJsonStatus === EMarkJsonStatus.FINISHED
          ? service.project.base.getUrl(project.appKey, project.id, 'mark.official.json')
          : undefined,
        docxUrl: project.status === statuses.reviewed
          ? service.project.base.getUrl(project.appKey, project.id, 'docx')
          : undefined,
        docxZipUrl: project.status === statuses.reviewed
          ? service.project.base.getUrl(project.appKey, project.id, 'docx.zip')
          : undefined,
        meta,
        metaInfo,
        metaJson: metaInfo ? service.project.base.getUrl(project.appKey, project.id, 'meta.json') : undefined,
      };
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        projects: data,
      },
    };
  }

  @validate({ projectId: 'number' })
  public async getOneById() {
    const { service, ctx } = this;
    const { statuses, openStatuses } = service.project.base;
    const { projectId } = ctx.input;
    const [project, meta] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['id', 'appKey', 'status', 'markJsonStatus', 'projectName'],
      }),
      service.project.meta.getMetas({ projectId })
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    const metaInfo = await service.project.meta.getMetaInfo(meta);
    if (meta.bookName && metaInfo) {
      (metaInfo as any).bookName = meta.bookName;
    }

    const data = {
      id: project.id,
      projectName: project.projectName,
      status: project.status === statuses.reviewed
        ? openStatuses.successful
        : openStatuses.processing,
      markJsonStatus: project.markJsonStatus,
      json: project.status === statuses.reviewed
        ? service.project.base.getUrl(project.appKey, project.id, 'official.json')
        : undefined,
      markJson: project.markJsonStatus === EMarkJsonStatus.FINISHED
        ? service.project.base.getUrl(project.appKey, project.id, 'mark.official.json')
        : undefined,
      docxUrl: project.status === statuses.reviewed
        ? service.project.base.getUrl(project.appKey, project.id, 'docx')
        : undefined,
      docxZipUrl: project.status === statuses.reviewed
        ? service.project.base.getUrl(project.appKey, project.id, 'docx.zip')
        : undefined,
      meta,
      metaInfo,
      metaJson: metaInfo ? service.project.base.getUrl(project.appKey, project.id, 'meta.json') : undefined,
    };
    ctx.body = {
      status: 0,
      data,
    };
  }

  public async startMarkJson() {
    const { service, ctx } = this;
    const { projectId, force } = ctx.input;
    const [project, book] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['markJsonStatus'],
      }),
      service.book.getOne({
        where: { projectId, type: service.book.types.question },
        attributes: ['id'],
      })
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (project.markJsonStatus === EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('项目正在标注json中');
    }
    const tasks = await service.task.base.getAll({
      where: { bookId: book!.id },
      attributes: ['taskId', 'appKey'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    if (project.markJsonStatus === EMarkJsonStatus.FINISHED && force ||
      project.markJsonStatus === EMarkJsonStatus.INIT) {
      // 使用 internal.json 做为 mark.json
      await Promise.all(tasks.map(async(task) => {
        await service.task.base.copyOssData(task.appKey, task.taskId, 'internal.json', 'mark.json');
      }));
    } else {
      // 已经标注过，仅复制新增的子任务
      const oss = service.oss.createOss();
      await Promise.all(tasks.map(async(task) => {
        try {
          // 检查是否有
          await oss.head(service.task.base.getOssKey(task.appKey, task.taskId, 'mark.json'));
        } catch (e) {
          await service.task.base.copyOssData(task.appKey, task.taskId, 'internal.json', 'mark.json');
        }
      }));
    }

    await service.project.base.update(
      { markJsonStatus: EMarkJsonStatus.ING },
      { where: { id: projectId } }
    );

    ctx.body = { status: 0 };
  }

  public async finishMarkJson() {
    const { service, ctx } = this;
    const { projectId } = ctx.input;
    const [project, book] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['markJsonStatus', 'appKey'],
      }),
      service.book.getOne({
        where: { projectId, type: service.book.types.question },
        attributes: ['id'],
      })
    ]);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (project.markJsonStatus !== EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('项目不在标注json中');
    }
    const tasks = await service.task.base.getAll({
      where: { bookId: book!.id },
      attributes: ['taskId', 'appKey'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });

    const jsons = await Promise.all(tasks.map(async(task) => {
      const json = await service.task.base.getOssData(task.appKey, task.taskId, 'mark.json');
      return json;
    }));
    let json = service.book.combineJson(jsons);
    // 过滤不导出
    json = json.filter((node) => node.isExport !== false);
    for (const { node } of iterateNode(json)) {
      node.children = node.children ? node.children.filter((node) => node.isExport !== false) : [];
      delete node.isExport;
    }
    // 试题添加题型标签名称、知识点名称
    await service.project.base.setJsonTags(json);

    await service.project.base.setOssData(project.appKey, projectId, 'mark.official.json', cleanJsonNodes(json));
    await service.project.base.update(
      { markJsonStatus: EMarkJsonStatus.FINISHED },
      { where: { id: projectId } }
    );

    ctx.body = { status: 0 };
  }

}
