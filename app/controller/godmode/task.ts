'use strict';

import { Controller } from 'egg';
import { IClientMetas } from '../../model/clientMeta';
import { EMarkJsonStatus } from '../../model/project';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { iterateNode } from '../../core/utils/treeHelper';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import { PlanTaskType } from '../../model/planFlow';
import { PlanTaskStatus } from '../../model/planTask';

export default class GodmodeTaskController extends Controller {
  @validate({ taskId: 'number' })
  public async confirm() {
    const { ctx, service, app } = this;
    const { statuses } = service.task.base;
    const { taskId } = ctx.input as { taskId: number; };
    const [task, images] = await Promise.all([
      service.task.base.getOne({ where: { taskId } }),
      service.image.getAll({
        where: { taskId, disabled: false },
        attributes: ['reviewed', 'imageId', 'filename', 'taskOrder'],
        order: [['id', 'ASC']],
      })
    ]);
    if (!task) {
      return ctx.body = baseError.dataNotExistError('图片任务不存在');
    }
    const allImageIds: string[] = [];
    for (const { imageId } of service.image.sortImageByFile(images || [])) {
      allImageIds.push(imageId);
    }
    const html = await service.task.base.combineByImageIds(task.appKey, allImageIds, taskId);
    const json = await service.task.base.convert2Json(task.appKey, taskId, html);
    const catalog = service.task.base.getCatalog(task.appKey, taskId, json);
    await service.task.base.setOssData(task.appKey, taskId, 'html', html);
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await app.model.transaction(async(transaction) => {
      await Promise.all([
        service.task.base.update(
          {
            status: statuses.dataCleaning,
            endReviewTime: new Date(),
            catalog: catalog.length ? JSON.stringify(catalog) : '',
          },
          { transaction, where: { taskId } }
        ),
        service.plan.task.onChangeTaskStatus({
          taskId,
          taskType: PlanTaskType.REVIEW,
          targetStatus: PlanTaskStatus.FINISHED,
        }, { transaction })
      ]);
    });
    ctx.runInBackground(async() => {
      await service.subjectScript.runSubjectScript(taskId, true);
      const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
      await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
    });
    ctx.body = { status: 0 };
  }

  public async getList() {
    const { ctx, service } = this;
    const { key, appKey } = ctx.input;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = { appKey, bookId: 0 } as any;
    if (key) {
      if (/^[0-9]+$/.test(key)) {
        whereOpt.taskId = Number(key);
      } else {
        whereOpt.taskName = { $like: `%${key}%` };
      }
    }
    const { statuses, openStatuses } = service.task.base;
    const [count, tasks] = await Promise.all([
      service.task.base.count({ where: whereOpt }),
      service.task.base.getList({
        page,
        pageSize,
        where: whereOpt,
        order: [['id', 'ASC']],
      })
    ]);
    let clientMetas: Partial<IClientMetas> | undefined;
    const hasSuccessCallback = tasks.some((task) => task.status !== statuses.error && task.isCallback);
    if (hasSuccessCallback) {
      const appl = await service.appl.getOneByUc({
        where: { appKey },
        attributes: ['userId'],
      });
      if (appl && appl.userId) {
        clientMetas = await service.client.meta.getMetas({
          userId: appl.userId,
          key: ['exportJson', 'exportDocx'],
        });
      }
    }
    const config = service.client.meta.setDefaultMetas(clientMetas || {});
    const metaDict = await service.task.meta.getMetasDict({ taskId: tasks.map((t) => t.taskId) });
    const metaInfoDict = await service.project.meta.getMetaInfoDict(metaDict);
    const data = tasks.map((task) => {
      const markJson = task.markJsonStatus === EMarkJsonStatus.FINISHED ?
        service.task.base.getUrl(task.appKey, task.taskId, 'mark.official.json') :
        undefined;
      const markJsonStatus = task.markJsonStatus;
      // 只有当回调完成（成功或者失败后）才可以被查询
      if (task.status === statuses.error && task.isCallback) {
        return {
          taskId: task.taskId,
          taskName: task.taskName,
          subject: task.subject,
          imageCount: task.imageCount,
          status: openStatuses.failed,
          errorInfo: task.errorInfo,
          extra: task.extra,
          markJson,
          markJsonStatus,
        };
      }
      if (!task.isCallback) {
        return {
          taskId: task.taskId,
          taskName: task.taskName,
          subject: task.subject,
          imageCount: task.imageCount,
          status: openStatuses.processing,
          extra: task.extra,
          markJson,
          markJsonStatus,
        };
      }
      const meta = metaInfoDict[task.taskId];
      return {
        taskId: task.taskId,
        taskName: task.taskName,
        subject: task.subject,
        imageCount: task.imageCount,
        extra: task.extra,
        status: openStatuses.successful,
        isCallback: task.isCallback,
        callbackError: task.callbackError,
        docx: config.exportDocx ?
          service.task.base.getUrl(task.appKey, task.taskId, 'docx', false) :
          undefined,
        json: config.exportJson ?
          service.task.base.getUrl(task.appKey, task.taskId, 'json', false) :
          undefined,
        markJson,
        markJsonStatus,
        meta,
        metaJson: meta ? service.task.base.getUrl(task.appKey, task.taskId, 'meta.json', false) : undefined,
      };
    });
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        tasks: data,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async finishMarkJson() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as { taskId: number };
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['markJsonStatus', 'appKey'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.markJsonStatus !== EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('任务不在标注json中');
    }

    let json: any[] = await service.task.base.getOssData(task.appKey, taskId, 'mark.json');
    // 过滤不导出
    json = json.filter((node) => node.isExport !== false);
    for (const { node } of iterateNode(json)) {
      node.children = node.children ? node.children.filter((node) => node.isExport !== false) : [];
      delete node.isExport;
    }
    // 试题添加题型标签名称、知识点名称
    await service.project.base.setJsonTags(json);
    await service.task.base.setOssData(task.appKey, taskId, 'mark.official.json', cleanJsonNodes(json));
    await service.task.base.update(
      { markJsonStatus: EMarkJsonStatus.FINISHED },
      { where: { taskId } }
    );
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    force: 'boolean?',
  })
  public async startMarkJson() {
    const { service, ctx } = this;
    const { taskId, force } = ctx.input as {
      taskId: number;
      force?: boolean;
    };
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['markJsonStatus', 'appKey'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.markJsonStatus === EMarkJsonStatus.ING) {
      return ctx.body = baseError.dataAlreadyExistError('任务正在标注json中');
    }
    if (task.markJsonStatus === EMarkJsonStatus.FINISHED && force ||
      task.markJsonStatus === EMarkJsonStatus.INIT) {
      // 使用 internal.json 做为 mark.json
      await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'mark.json');
    } else {
      const oss = service.oss.createOss();
      try {
        // 检查是否有。已经标注过应该是有的
        await oss.head(service.task.base.getOssKey(task.appKey, taskId, 'mark.json'));
      } catch (e) {
        // 使用 internal.json 做为 mark.json
        await service.task.base.copyOssData(task.appKey, taskId, 'internal.json', 'mark.json');
      }
    }

    await service.task.base.update(
      { markJsonStatus: EMarkJsonStatus.ING },
      { where: { taskId } }
    );

    ctx.body = { status: 0 };
  }

  testCb() {
    const { ctx } = this;

    this.logger.info('<<<<<', ctx.input);

    ctx.body = {
      status: 0,
      input: ctx.input,
    };
  }
}
