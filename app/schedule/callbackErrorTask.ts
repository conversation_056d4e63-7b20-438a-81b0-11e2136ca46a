/**
 * @file 回调异常任务
 * <AUTHOR>
 */
'use strict';

import BaseSubscription from '../core/base/baseSubscription';

export default class CallbackErrorTask extends BaseSubscription {
  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '30s',
      type: 'worker',
      disable: false,
      immediate: true,
      env: ['dev','pre' ,'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, ctx } = this;
    const tasks = await service.task.callback.getAllExpiredTasks();
    if (!tasks.length) {
      return;
    }
    for (const { taskId, times } of tasks) {
      const task = await service.task.base.getOne({ where: { taskId } });
      if (!task || task.isCallback) {
        continue;
      }
      ctx.runInBackground(async() => {
        await service.task.callback.callback(task, times);
      });
    }
  }
}
