'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';
import { cleanJsonNodes } from '../core/utils/htmlToJsonV4';
import {escapeRegExp} from '../core/utils/helper';
import * as JSZip from 'jszip';
import {removeCommonExtensions} from '../core/utils';

export default class PublishTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.applyProject, 5);
  }

  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev', 'pre', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, app, logger } = this;
    const { statuses } = service.project.base;
    const {projectId, userId, costTime} = await service.project.base.popFromApplyProject();
    if (!projectId) {
      return;
    }
    this.logger.info(`[applyProject] 任务 ${projectId} 开始重新生成`);
    const runningTime = Number(new Date());
    try {
      const { types } = service.book;
      const [project, books] = await Promise.all([
        service.project.base.getOne({ where: { id: projectId } }),
        service.book.getAll({
          where: { projectId },
          attributes: ['id', 'type'],
        })
      ]);
      const book = books.find((_book) => _book.type === types.question);
      if (!project) {
        return;
      }
      if (!book) {
        return;
      }
      const allTasks = await service.task.base.getAll({
        where: { bookId: books.map((_book) => _book.id) },
        attributes: ['taskId', 'appKey', 'bookId', 'taskName', 'status'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      });
      const zip:Map<string|number, JSZip> = new Map();
      const projectZipX = new JSZip();
      zip.set('project', new JSZip());
      const projectBuffer: Buffer[] = [];
      const fileName:string[] = [];
      // 1. 拿到每个任务的jsons
      await Promise.all(allTasks.map(async(task) => {
        let json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'preprocessed.internal.json');
        // 没有 json 预处理的任务 / 数据
        if (!json) {
          json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.internal.json');
        }
        // 老数据需要兼容。直接读取json
        if (!json) {
          json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json');
        }

        const jsonBuffer = Buffer.from(JSON.stringify(cleanJsonNodes(json)));
        const re = new RegExp(escapeRegExp(project.projectName + '-'), 'g');
        // 这里有两种情况， 一种是 name-练习-1, 一种是 name-多Word拆分任务-1-练习-1
        let name = removeCommonExtensions(task.taskName.replace(re, '')?.split('-')[0] || task.taskName);
        this.logger.info(`[applyProject] taskName: ${task.taskName}, name: ${name}`);
        if (project.endWithWords) {
          if (!zip.has(task.taskId)) {
            zip.set(task.taskId, new JSZip());
          }
          zip.get(task.taskId)?.file(`${name}.json`, jsonBuffer);
        }
        projectBuffer.push(jsonBuffer);
        const originName = name;
        if (fileName.includes(name)) {
          // 如果有重名的，就加上（1）
          name = `${name}（${fileName.filter((n) => n === name).length}）`;
        }
        fileName.push(originName);

        zip.get('project')?.file(`${name}.json`, jsonBuffer);
        projectZipX.file(`${name}.json`, jsonBuffer);
        return json;
      }));

      const json = await service.book.combineJsonByTaskIds(allTasks.sort((a, b) => b.taskId - a.taskId));
      await service.project.base.setOssData(project.appKey, projectId, 'json', json);
      await service.project.base.setOssData(project.appKey, projectId, 'official.json', cleanJsonNodes(json));

      // 2. 合并jsons到一个zip => JSON
      const jsonZipMap: Map<number, Buffer> = new Map();

      // 把所有的任务JSON分开打包
      for (const [id] of zip) {
        if (id !== 'project') {
          jsonZipMap.set(Number(id), await zip.get(id)?.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' }) || new Buffer([]));
        }
      }
      // 上传项目json, 这个地方是xdoc需要用的, 需要两份. 一份是合并的, 一份是分开的
      // 单独打包一个项目的
      const projectZipInXDOC = await projectZipX.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' });
      await service.project.base.setOssData(project.appKey, projectId, 'official.json.zip', projectZipInXDOC);
      // 3. 同步到工单
      const projectZip = await zip.get('project')?.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' }) || new Buffer([]);
      if (project.workOrder) {
        await app.curl(`${this.config.workOrder.apiTask}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            business_project_id: `${projectId}`,
            ticket_id: project.workOrder,
            system_id: 0,
          }),
        });
        const bucket = this.config.workOrder.bucket;
        const ossClient = service.oss.createOss(bucket);
        await ossClient.put(`worksheet/${project.workOrder}/${project.projectName}-JSON.zip`, projectZip);
        await app.curl(`${this.config.workOrder.apiTask}/api/open/content/v1/deliverable`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            is_cover: true,
            project_id: String(projectId),
            process: 'OTHER',
            ticket_id: project.workOrder,
            name: `${project.projectName}-JSON.zip`,
            path:`worksheet/${project.workOrder}/${project.projectName}-JSON.zip`,
          }),
        });
      }

      await service.project.base.update(
        { status: statuses.reviewed },
        { where: { id: projectId } }
      );
      logger.info(`[applyWordsProject] finished!!!!! project_id=${projectId}`);

      // 记录操作日志
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.reviewTypes.confirm,
        data: '',
        costTime: costTime || 0,
      });

    } catch (e) {
      await service.project.base.update(
        { status: statuses.error, errorInfo: '发布文件生成异常' },
        { where: { id: projectId } }
      );
      logger.error(`[applyWordsProject]: exception!!!!! projectId: ${projectId}`, e);
    }
    await this.app.logRunningTime(runningTime, `applyWordsProject project: ${projectId}`);
  }
}
