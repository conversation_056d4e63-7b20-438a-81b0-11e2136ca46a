'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class UpdatePlanTaskCounts extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updatePlanTaskCounts);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() { // 更新计划的任务数量
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service } = this;
    let planId = await service.plan.base.pop();
    while (planId) {
      await service.plan.base.statTaskByPlanId([planId]);
      await service.plan.base.finishStat([planId]);
      planId = await service.plan.base.pop();
    }
  }
}
