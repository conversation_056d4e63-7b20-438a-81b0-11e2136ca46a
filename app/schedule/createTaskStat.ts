/**
 * @file 创建任务统计
 * <AUTHOR>
 */
'use strict';

import BaseSubscription from '../core/base/baseSubscription';
import * as dateformat from 'dateformat';
import { ETaskResourceType } from '../model/task';

export default class CreateTaskStat extends BaseSubscription {
  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      cron: '0 0 2 * * *', // 每天凌晨2:00
      type: 'worker',
      disable: false,
      // immediate:true,
      env: ['dev','pre', 'prod'],
    };
  }

  private getCostTime(histories: { type: number; costTime: number; userId: number; }[], operatorUserId: number, statType: string) {
    if (!histories.length) {
      return 0;
    }
    const { service } = this;
    let time = 0;
    const breakTypes = {
      column: service.task.history.columnTypes.apply.id,
      mark: service.task.history.markTypes.apply.id,
      review: service.task.history.reviewTypes.apply.id,
    };
    const timeTypes = {
      column: service.task.history.columnTypes.save.id,
      mark: service.task.history.markTypes.confirm.id,
      review: service.task.history.reviewTypes.confirm.id,
    };
    for (const { type, costTime, userId } of histories) {
      if (userId === operatorUserId && type === timeTypes[statType]) {
        time += costTime;
      }
      if (type === breakTypes[statType]) {
        break;
      }
    }
    return time;
  }

  private computeQuesNode(tree: any[], leaf: boolean) {
    if (!Array.isArray(tree) || !tree.length) {
      return 0;
    }
    let count = 0;
    for (const { node_type, children } of tree) {
      if (node_type === 'question' && (!leaf || (!children.length && children))) {
        count += 1;
      } else {
        count += this.computeQuesNode(children, leaf);
      }
    }
    return count;
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, logger } = this;
    // 获取前一天的所有未统计任务id
    const now = new Date();
    /*
     * const tasks = await service.task.base.getAll({ where:{ status:7 }, attributes:['taskId', 'updateTime'] });
     * for (const { taskId, updateTime } of tasks) {
     *   await app.redis.zadd('unstat:task:sorted:set', `${+new Date(updateTime)}`, `${taskId}`);
     * }
     */
    const { statuses } = service.task.base;
    const { types } = service.task.stat;
    const { hexinApps } = service.appl;
    const lastDayTime = Number(new Date(`${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} 00:00:00`));
    const taskIds = await service.task.stat.getAllUnstatTask(lastDayTime);
    if (!taskIds.length) {
      return;
    }
    for (const item of taskIds) {
      const taskId = Number(item);
      const task = await service.task.base.getOne({ where: { taskId } });
      // if (!task || task.status !== statuses.reviewed) {
      if (!task || task.status !== statuses.reviewed || hexinApps.includes(task.appKey)) {
        await service.task.stat.del(taskId);
        continue;
      }
      const histories = await service.task.history.getAll({
        where: { taskId },
        attributes: ['type', 'costTime', 'userId'],
        order: [['id', 'DESC']],
      });
      let imageCount = 0;
      let pdfCount = 0;
      if ([ETaskResourceType.FBDV2, ETaskResourceType.WORDV2].includes(task.resourceType!)) {
        const meta = await service.task.meta.getMetas({ taskId });
        pdfCount = service.task.meta.calcPdfCount(meta.confirmPdfInfo || meta.pdfInfo);
      } else {
        imageCount = await service.image.count({ where: { taskId, originalId: '' } });
      }
      const questionTree = await service.task.base.getOssData(task.appKey, task.taskId, 'json');
      const leafQuestionCount = this.computeQuesNode(questionTree, true);
      const parentQuestionCount = this.computeQuesNode(questionTree, false);
      const stats = types.map((type, index) => {
        const userId = [task.preprocessUserId, task.markUserId, task.reviewUserId][index];
        return {
          taskId,
          type,
          imageCount,
          pdfCount,
          resourceType: task.resourceType!,
          userId,
          leafQuestionCount,
          parentQuestionCount,
          date: dateformat(type === 'review' && task.endReviewTime || type === 'mark' && task.endMarkTime || task.updateTime, 'yyyy-mm-dd'),
          costTime: this.getCostTime(histories, userId, type),
        };
      });
      // 避免二次统计，先删除之前的统计结果（主要是那种已发布撤回的问题）
      await service.task.stat.delete({ where: { taskId } });
      try {
        await service.task.stat.bulkCreate(stats);
        logger.info(`create task stat ${taskId} ok!`);
      } catch (e) {
        logger.info(`create task stat ${taskId} error`, e);
      }
      await service.task.stat.del(taskId);
    }
  }
}
