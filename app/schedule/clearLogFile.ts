/**
 * @file 删除日志文件
 * <AUTHOR>
 */
'use strict';

import * as dateformat from 'dateformat';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as rimraf from 'rimraf';
import { promisify } from 'util';
import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

// 过期时间
const EXPIRE_DURATION = 5 * 24 * 60 * 60 * 1000;

export default class ClearLogFile extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.clearLogFile);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      cron: '0 0 3 * * *', // 每天凌晨3:00
      type: 'worker',
      disable: false,
      env: ['dev','pre' ,'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { config, logger } = this;
    const { dir, appLogName, coreLogName, agentLogName, errorLogName } = config.logger;
    const files = await promisify(fs.readdir)(dir);
    // 删除egg的进程日志
    try {
      const filepath = path.join(os.homedir(), 'logs');
      await promisify(rimraf)(filepath);
      logger.info(`clear egg cluster log file ${filepath} success!`);
    } catch (e) {
      logger.error('clear egg cluster log file error:', e);
    }
    if (!files || !files.length) {
      logger.info('no expire log files!');
      return;
    }
    const scheduleLogger = config.customLogger && config.customLogger.scheduleLogger;
    const scheduleLogFile = scheduleLogger.file || '';
    const filenamePrefix = [appLogName, coreLogName, agentLogName, errorLogName];
    if (scheduleLogFile) filenamePrefix.push(scheduleLogFile);
    const isLogReg = new RegExp(`^(${filenamePrefix.join('|')})`, 'i');
    for (const filename of files) {
      isLogReg.lastIndex = 0;
      if (!isLogReg.test(filename)) {
        return;
      }
      const dateTime = filename.replace(isLogReg, '').substr(1, filename.length);
      const expireTime = dateformat(Number(new Date()) - EXPIRE_DURATION, 'yyyy-mm-dd');
      if (!dateTime || dateTime > expireTime) {
        return;
      }
      const filepath = path.join(dir, filename);
      try {
        await promisify(fs.unlink)(filepath);
        logger.info(`clear business log file ${filepath} success!`);
      } catch (e) {
        logger.error(`clear business log file ${filepath} error:`, e);
      }
    }
  }
}
