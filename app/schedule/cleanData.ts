'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

// @todo：看起来目前代码里没有 pushCleanData 的逻辑，可能已经废弃。
export default class CleanDataTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.cleanData, 16);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1.5s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'local', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, logger } = this;
    const { statuses } = service.task.base;
    const taskId = await service.task.base.popCleanData();
    if (!taskId) return;
    const task = await service.task.base.getOne({ where: { taskId, status: statuses.dataCleaning } });
    if (!task) {
      logger.info(`【数据清洗定时任务LOG】数据清洗任务状态错误，不是数据清洗中状态 ${taskId} `);
      return;
    }
    try {
      await service.subjectScript.generateFormattedData(task);
    } catch (e) {
      logger.error(`【数据清洗定时任务LOG】${taskId}复制数据错误${e}`);
      await service.task.base.update(
        { status: statuses.dataCleanfailed },
        { where: { taskId } }
      );
    }
  }
}
