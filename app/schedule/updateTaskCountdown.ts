'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class UpdateTaskCountdown extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updateTaskCountdown);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1m',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() { // 更新任务倒计时和预警
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service } = this;
    await service.task.base.updateTaskCountdown();
  }
}
