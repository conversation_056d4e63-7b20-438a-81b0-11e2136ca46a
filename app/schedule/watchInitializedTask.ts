'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class watchInitializedTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.watchInitializedTask, 12);
  }

  static get schedule() {
    return {
      interval: '60s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const {service, logger} = this.ctx;
    const {statuses} = service.task.base;
    // const taskId = await service.task.base.getTaskParamsTaskId();
    // 查询 10-30 分钟内状态为 init 或 error 的任务
    const tasks = await service.task.base.getAll({
      where: {
        status: statuses.init,
        createTime: {
          $gt: new Date(new Date().getTime() - 30 * 60 * 1000),
          $lt: new Date(new Date().getTime() - 10 * 60 * 1000),
        },
      },
    });
    logger.info(`[watchInitializedTask] tasks=${tasks.length}`);
    if (!tasks.length) {
      return;
    }
    for (const task of tasks) {
      const taskId = task.taskId;
      logger.info(`[watchInitializedTask] taskId=${taskId}`);

      const taskParams = await service.task.base.getTaskParams(taskId);

      // 获取请求参数
      try {
        if (!Object.keys(taskParams).length) {
          return;
          // 更新状态为失败， 提交异常
          // 通知机器人
        }

        // 重试一次
        const _taskId = await service.task.base.relatedCreateWidthTaskId(taskParams);
        logger.info(`[watchInitializedTask] 开始创建 taskId=${taskId}`);
        if (!_taskId) {
          // 更新状态为失败， 提交异常
          await service.task.base.update({
            errorInfo: '图片转存失败',
            status: statuses.error,
          }, {where: {taskId}});
          await service.robot.sendRobotMessageInTask(taskId);
          // 通知机器人
        }
        logger.info(`[watchInitializedTask] 创建成功 taskId=${taskId}`);
        // 更新工单的关联
        // await service.workSheet.reLinkTickedId('task', taskParams.ticketId, [{old_project_id: String(taskId), new_project_id: String(newTaskId)}]);
        // 删除 redis 中的请求参数
        await service.task.base.deleteTaskParams(taskId);
        logger.info(`[watchInitializedTask] 删除相关参数 taskId=${taskId}`);
      } catch (e) {
        logger.info(`[watchInitializedTask] taskId=${taskId} error=${e.message}`);
      }
    }
  }
}
