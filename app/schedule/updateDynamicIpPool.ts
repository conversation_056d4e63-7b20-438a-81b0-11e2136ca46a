/**
 * @file 更新动态ip池
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class UpdateDynamicIpPoolTask extends BaseSubscription {
  private readonly maxPoolSize = 3;

  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updateDynamicIpPoolTask);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '2s',
      type: 'worker',
      disable: true,
      env: ['prod','pre'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service } = this;
    let availableIps = await service.proxy.pool.getAll();
    // 如果有ip，需要验证是否可用
    if (availableIps.length) {
      availableIps = await service.proxy.dynamicIp.filterAvailableIps(availableIps);
    }
    if (availableIps.length < this.maxPoolSize) {
      const ips = await service.proxy.dynamicIp.getNewIps(1);
      if (ips.length) {
        await service.proxy.pool.push(ips);
      }
    }
  }
}
