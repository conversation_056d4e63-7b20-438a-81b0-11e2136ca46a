'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class UpdatePlanGroupTaskStat extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updatePlanGroupTaskCounts);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() { // 更新计划任务组的任务数量
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service } = this;
    let groupId = await service.plan.group.pop();
    while (groupId) {
      await service.plan.group.statTaskByGroupId([groupId]);
      await service.plan.group.finishStat([groupId]);
      groupId = await service.plan.group.pop();
    }
  }
}
