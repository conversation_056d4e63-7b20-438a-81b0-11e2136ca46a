'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class TimeoutMonitorTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.timeoutMonitor);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      // 每3秒检查一次
      interval: '3s',
      type: 'worker',
      disable: false,
      immediate: false,
      env: ['dev', 'pre', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }

    const { service, logger } = this;
    const runningTime = Number(new Date());

    try {
      // 获取所有已超时的任务
      const expiredTasks = await service.timeout.getExpiredTasks();

      if (!expiredTasks.length) {
        logger.debug('[TimeoutMonitor] 没有超时任务');
        return;
      }

      logger.info(`[TimeoutMonitor] 发现 ${expiredTasks.length} 个超时任务`);

      // 处理每个超时任务
      for (const task of expiredTasks) {
        try {
          // 发送超时通知
          await service.timeout.sendTimeoutNotification(task);

          logger.info(`[TimeoutMonitor] 处理超时任务: ${task.taskId}:${task.type}, 超时时间: ${task.timeout}ms`);
        } catch (error) {
          logger.error(`[TimeoutMonitor] 处理超时任务失败: ${task.taskId}:${task.type}`, error);
        }
      }

      // 清理已处理的超时任务
      await service.timeout.cleanupExpiredTasks(expiredTasks);

      logger.info(`[TimeoutMonitor] 处理完成，共处理 ${expiredTasks.length} 个超时任务`);

    } catch (error) {
      logger.error('[TimeoutMonitor] 监控超时任务时发生错误:', error);
    } finally {
      this.app.logRunningTime(runningTime, 'TimeoutMonitor processed');
    }
  }
}
