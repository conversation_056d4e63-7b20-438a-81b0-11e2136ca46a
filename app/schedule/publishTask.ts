'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';
// import { preCleanHtml } from '../core/utils/htmlToJsonV4/helper/cleanupNode';

export default class PublishTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.publishTask, 2);
  }

  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  /**
   * 任务发布的队列
   * 1. 生成 html 和 .docx 文件
   * 2. 数据清洗
   */
  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service } = this;
    const taskId = await service.task.base.popFromPublish();
    this.logger.info(`[PublishTask] 任务 ${taskId} 开始发布`);
    if (!taskId) {
      return;
    }
    const runningTime = Number(new Date());
    try {
      const task = await service.task.base.getOne({ where: { taskId } });

      if (!task) {
        throw new Error(`[PublishTask] 任务 ${taskId} 不存在`);
      }

      // 字符数统计
      // const charCount = service.task.base.getCharCount(html);

      // @todo：这个 errorInfo 好像没有地方用到，应该根据判断对任务的状态进行设置，内容一致性检查的异常信息
      let errorInfo = '';
      errorInfo = await service.task.base.checkOssData({
        taskId,
        appKey: task.appKey,
        srcExtension: 'diff.json.html',
        dstExtension: 'diff.json',
      });
      if (!errorInfo) {
        errorInfo = await service.task.base.checkOssData({
          taskId,
          appKey: task.appKey,
          srcExtension: 'diff.docx.html',
          dstExtension: 'docx',
        });
      }
      await service.subjectScript.runSubjectScript(taskId, true, 3, 'task');
    } catch (e) {
      // @todo：执行异常更新任务状态为失败，目前没有这个逻辑
    }
    this.app.logRunningTime(runningTime, `publishTask task:${JSON.stringify(taskId)}`);
  }
}
