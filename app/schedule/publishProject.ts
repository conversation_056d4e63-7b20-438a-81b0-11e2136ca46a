'use strict';

import { Context } from 'egg';
import * as J<PERSON><PERSON><PERSON> from 'jszip';
import BaseSubscription from '../core/base/baseSubscription';
import { cleanJsonNodes } from '../core/utils/htmlToJsonV4';
import { ETaskResourceType } from '../model/task';
import { flattenByLevelOneQuestions, json2Jpg } from '../core/utils/treeHelper';
import { iterateNode } from '../core/utils/treeHelper';
import axios from 'axios';

export default class PublishProject extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.publishProject, 1);
  }

  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev', 'pre', 'prod'],
    };
  }

  /**
   * 项目发布的队列
   * 1. 找到项目下的任务，合并项目 Json
   * 2. 合并得到项目 Html，转换成 .docx 文件
   * 3. 打包项目、任务的结果文件压缩包
   * 4. 和工单系统进行通信
   * 5. 信息统计
   */
  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    // @todo：不同任务间可以优化成并行的，进一步提升效率
    const { service, logger, config, app } = this;
    const projectId = await service.project.base.popFromPublish();
    if (!projectId) {
      return;
    }
    logger.info(`[PublishProject] start!!!!! project_id=${projectId}`);

    const runningTime = Number(new Date());

    const { statuses } = service.project.base;
    const { types } = service.book;

    try {
      const [project, books,projectMeta] = await Promise.all([
        service.project.base.getOne({ where: { id: projectId } }),
        service.book.getAll({
          where: { projectId },
          attributes: ['id', 'type'],
        }),
        service.project.meta.getMetas({ projectId })
      ]);
      if (!project) {
        throw new Error(`[PublishProject] 项目 ${projectId} 不存在！`);
      }
      const book = books.find((_book) => _book.type === types.question);
      if (!book) {
        throw new Error(`[PublishProject] 项目 ${projectId} 试题图书不存在！`);
      }
      const allTasks = await service.task.base.getAll({
        where: { bookId: books.map((_book) => _book.id) },
        attributes: ['taskId', 'appKey', 'bookId', 'taskName', 'status', 'mergedTaskId'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      });
      let questionTasks = allTasks.filter((_task) => _task.bookId === book.id);

      // 如果是新流程，需要筛选出审核任务
      if (projectMeta.isNewFlow) {
        // 根据 bookOrder， 从小到大排序
        questionTasks = allTasks.filter((_task) => _task.bookId === book.id).filter((task) => task.mergedTaskId);
        logger.info(`[PublishProject] project_id=${projectId} new flow, questionTasks: ${questionTasks.map((t) => t.taskId).join(',')}`);
      }
      questionTasks = questionTasks.sort((a, b) => b.bookOrder - a.bookOrder);
      logger.info(`[PublishProject] project_id=${projectId} old flow, questionTasks: ${questionTasks.map((t) => t.taskId).join(',')}`);
      // 找到项目下的任务，合并项目 Json, 注意这里的json为了兼容o2o做成数组形式了
      const json: {project?: { [key: number]: any[] }, task?: { [key:number]: any[] }} = {};
      if (project.endWithWords) {
        json.task = {};
        for (let i = 0; i < questionTasks.length; i++) {
          const _task = questionTasks[i];
          // 这里获取的已经是最终的json, 不需要再处理和上传了
          json.task[_task.taskId] = await service.book.getJsonByTaskId(_task); // 所有task 的json 都在json.task里, 下面的projectZIP就用这个, 一个task一个json文件, 压缩到一个zip中
        }
      }
      const _json = { project: { [projectId]: await service.book.combineJsonByTaskIds(questionTasks) } };
      await service.project.base.setOssData(project.appKey, projectId, 'json', _json.project![projectId]);
      const officialJson: any = cleanJsonNodes(_json.project![projectId]);
      await service.project.base.setOssData(project.appKey, projectId, 'official.json', officialJson);
      const jsonURL = service.project.base.getUrl(project.appKey, projectId, 'official.json', true);

      logger.info(`[PublishProject] project_id=${projectId} combine json done`);

      // 合并得到项目 Html，转换成 .docx 文件
      const htmls = await Promise.all(allTasks.map(async(_task) => {
        const html: string = await service.task.base.getOssData(_task.appKey, _task.taskId, 'formatted.html');
        return html;
      }));
      const html = htmls.join('\n');
      await service.project.base.setOssData(project.appKey, projectId, 'html', html);
      logger.info(`[PublishProject] project_id=${projectId} setHtml Success`);
      if (project.workOrder) {
        logger.info(`[PublishProject] project_id=${projectId} execute workorder begin`);
        const bucket = config.workOrder.bucket;
        const sourceBucket = config.aliOss.bucket;
        const ossClient = service.oss.createOss(bucket);
        if (project.endWithWords) {
          // 把 json 复制到 worksheet 里
          for (let i = 0; i < questionTasks.length; i++) {
            const _task = questionTasks[i];
            await ossClient.copy(
              `worksheet/${project.workOrder}/deliverable/xdoc/${_task.taskId}.json`,
              `/${sourceBucket}/${service.task.base.getOssKey(_task.appKey, _task.taskId, 'json')}`);
          }
        } else {
          await ossClient.copy(
            `worksheet/${project.workOrder}/deliverable/xdoc/${projectId}.json`,
            `/${sourceBucket}/${service.project.base.getOssKey(project.appKey, projectId, 'official.json')}`);
        }
        await ossClient.copy(
          `worksheet/${project.workOrder}/deliverable/xdoc/${projectId}.html`,
          `/${sourceBucket}/${service.project.base.getOssKey(project.appKey, projectId, 'html')}`);

        // 在写入 pdf 页数的时候，如果没有的话，需要判断一下是否是新的数据结构
        let pdfCount = project?.pdfCount;
        if (!pdfCount && projectMeta?.pageCountInfos?.length) {
          pdfCount = projectMeta?.pageCountInfos[0].internalPages;
        }
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            business_project_id: `${projectId}`,
            ticket_id: project.workOrder,
            system_id: 0,
            xdoc_pdf_page_count: pdfCount || 0,
            delivery: projectMeta.isOpen ? { json_url: jsonURL } : undefined,
          }),
        });

        try {
          const res1 = await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/ticket/v1/handle`, {
            method: 'GET',
            dataType: 'json',
            data: { ticket_id: project.workOrder },
          });
          const workOrderInfo = res1.data.data;
          logger.info(`[PublishProject] project_id=${projectId} publish worksheet info ${workOrderInfo && workOrderInfo.delivery}}`);
          if (workOrderInfo && workOrderInfo.delivery && [6, 3].includes(workOrderInfo.delivery)) {
            logger.info(`[PublishProject] project_id=${projectId} publish worksheet json begin }`);
            // const jsonBuffer: Buffer[] = [];
            const bufferMap = new Map<number | string, Buffer>();
            if (project.endWithWords) {
              for (const taskKey in json.task) {
                bufferMap.set(taskKey, Buffer.from(JSON.stringify((json.task[taskKey]))));
              }
            } else {
              bufferMap.set(projectId, Buffer.from(JSON.stringify(cleanJsonNodes(_json.project ? _json.project[projectId] : [] || []))));
            }
            const projectZip = new JSZip();
            let res: Buffer = new Buffer([]);
            // 如果不是o2o的工单, map只会有一个project, 否则是若干个task
            for (const [id, buffer] of bufferMap) {
              const zip = new JSZip();
              zip.file(`${project.projectName}.json`, buffer);
              res = await zip.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' });
              if (project.endWithWords) {
                // 如果是 o2o 的话, 要注意这个名字是文件的名字
                const taskName = questionTasks.find((task) => Number(task.taskId) === Number(id))?.taskName || project.projectName;
                projectZip.file(`${taskName}.json`, buffer);
              }
            }
            const projectZipRes = await projectZip.generateAsync({ type: 'nodebuffer', compression: 'DEFLATE' });
            const result = await ossClient.put(`worksheet/${project.workOrder}/${project.projectName}-JSON.zip`, project.endWithWords ? projectZipRes : res);
            logger.info(`[PublishProject] project_id=${projectId} put res ${JSON.stringify(result)}`);

            // @todo：同步到工单发布文件接口。
            const config2 = {
              process: 'OTHER',
              ticket_id: project.workOrder,
              name: `${project.projectName}-JSON.zip`,
              path: `worksheet/${project.workOrder}/${project.projectName}-JSON.zip`,
            };
            logger.info(`[PublishProject] project_id=${projectId} publish worksheet json config ${JSON.stringify(config2)}`);

            const name = `${project.projectName}-JSON.zip`;
            const { data: res3 } = await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/content/v1/deliverable`, {
              method: 'POST',
              dataType: 'json',
              data: JSON.stringify({
                is_cover: true,
                project_id: String(projectId),
                process: 'OTHER',
                ticket_id: project.workOrder,
                name,
                // 注意这里要替换,相应
                path: `worksheet/${project.workOrder}/${name}`,
              }),
            });
            logger.info(`[PublishProject] project_id=${projectId} worksheet res ${JSON.stringify(res3)} `);
          }
          logger.info(`[PublishProject] project_id=${projectId} publish worksheet json end`);
        } catch (e) {
          logger.info(`[PublishProject] project_id=${projectId} publish worksheet json fail. ${e}`);
        }

        logger.info(`[PublishProject] project_id=${projectId} execute workorder done`);
      }

      if (allTasks.every((item) => item.resourceType === ETaskResourceType.FBDV2 || item.resourceType === ETaskResourceType.WORDV2)) {
        const metaDict = await service.task.meta.getMetasDict({ taskId: { $in: allTasks.map((t) => t.taskId) } });
        const pdfInfos = Object.values(metaDict).map((item) => item.pdfInfo).filter((item) => item);
        const pageMap: any = {};
        pdfInfos.forEach((item) => {
          const { bodyPageStart, bodyPageEnd } = item!;
          pageMap[bodyPageStart] = pageMap[bodyPageStart] ? pageMap[bodyPageStart] + 1 : 1;
          pageMap[bodyPageEnd] = pageMap[bodyPageEnd] ? pageMap[bodyPageEnd] + 1 : 1;
        });
        pdfInfos.forEach((item) => {
          const { bodyPageStart, bodyPageEnd } = item!;
          if (pageMap[bodyPageStart] !== 1 && bodyPageStart) {
            item!.bodyPageStart = `${Number(item!.bodyPageStart) + 0.5}`;
          }
          if (pageMap[bodyPageEnd] !== 1 && bodyPageEnd) {
            item!.bodyPageEnd = `${Number(item!.bodyPageEnd) - 0.5}`;
          }
        });
        await Promise.all(Object.entries(metaDict).map(([taskId, meta]) => {
          return service.task.meta.setMetas([Number(taskId)], { confirmPdfInfo: meta.pdfInfo });
        }));
        await Promise.all(allTasks.map(async(task) => {
          const markHtml: string = await service.task.base.getOssData(task.appKey, task.taskId, 'mark.html');
          const meta = metaDict[task.taskId];
          const pdfCount = service.task.meta.calcPdfCount(meta.pdfInfo);
          const histories = await service.task.history.getAll({
            where: { taskId: task.taskId },
            attributes: ['type', 'costTime', 'userId'],
            order: [['id', 'DESC']],
          });
          await service.task.stat.newStat({
            html: markHtml,
            taskId: task.taskId,
            type: 'mark',
            appKey: task.appKey,
            subject: task.subject,
            resourceType: task.resourceType!,
            imageCount: 0,
            pdfCount,
            cost: service.task.stat.getCostTime(histories, task.markUserId, 'mark'),
            userId: task.markUserId,
          });
          const reviewHtml: string = await service.task.base.getOssData(task.appKey, task.taskId, 'html');
          await service.task.stat.push(task.taskId);
          await service.task.stat.newStat({
            html: reviewHtml,
            taskId: task.taskId,
            type: 'review',
            appKey: task.appKey,
            subject: task.subject,
            resourceType: task.resourceType!,
            imageCount: 0,
            pdfCount,
            cost: service.task.stat.getCostTime(histories, task.reviewUserId, 'review'),
            userId: task.reviewUserId,
          });
        }));
        logger.info(`[PublishProject] project_id=${projectId} stat done`);
      }

      await service.project.base.update(
        { status: statuses.reviewed },
        { where: { id: projectId } }
      );

      logger.info(`[PublishProject] finished!!!!! project_id=${projectId}`);
      // 不知道是什么任务，先只管 word
      if (projectMeta.isAIEdit || projectMeta.isAICheck) {
        // 将AI编辑的处理放到后台执行，不阻塞主流程
        this.ctx.runInBackground(async() => {
          try {
            // 生成 AI 格式化 JSON
            logger.info(`[PublishProject] json 结构化问题处理开始: ${projectId}`);
            const _aiEditJson = await service.book.combineAiEditJsonByTaskIds(questionTasks);
            const aiFormattedJson = await service.ai.formatJSON(_aiEditJson, projectMeta.isAICheck ? 'check' : 'analysis');
            logger.info(`[PublishProject] json 结构化问题处理成功: ${projectId}`);
            await json2Jpg(aiFormattedJson, projectMeta.isAICheck ? 'check' : 'analysis');
            logger.info(`[PublishProject] json 转图成功: ${projectId}`);
            await service.project.base.setOssData(project.appKey, projectId, 'ai.json', aiFormattedJson);
            logger.info(`[PublishProject] ai.json 上传成功: ${projectId}`);
            const htmlUrl = service.project.base.getUrl(project.appKey, projectId, 'html');
            const file = await service.task.file.getFiles({ taskId: allTasks[0].taskId });
            logger.info(`[PublishProject] file: ${JSON.stringify(file)}`);
            const words = file.words!;
            const wordsArr = [...words.body, ...words.answer];
            const wordUrl = wordsArr[0].url;
            logger.info(`[PublishProject] wordUrl: ${wordUrl}`);
            // 先打平 json，然后批量入队，不要一次性全加进去
            const flatJson = flattenByLevelOneQuestions(aiFormattedJson)
              .filter((node) => node.node_type === 'question');
            logger.info(`[PublishProject] flatJson.length: ${flatJson.length}`);
            // 整体存一份
            const key = service.task.base.getAiOssKey(project.appKey, `${projectId}`, 'ai.flatten.json');
            await service.oss.upload(key, JSON.stringify(flatJson), 'string');
            // 每个题存一份
            // 先写入所有task_id到redis
            const redisKeys = flatJson.map((item, index) => `ai_edit_single:${projectId}-${item.node_id}-${index}`);
            logger.info(`[PublishProject] redisKeys: ${JSON.stringify(redisKeys)}`);
            // 删除所有以projectId开头的key
            const allKeys = await app.redis.keys(`ai_edit_single:${projectId}-*`);
            if (allKeys.length > 0) {
              await app.redis.del(...allKeys);
              logger.info(`[PublishProject] 删除旧的redis keys: ${JSON.stringify(allKeys)}`);
            }
            await Promise.all(redisKeys.map((key) => app.redis.set(key, '0')));
            logger.info('[PublishProject] redisKeys set done');

            function checkBodyEmpty(item: any): boolean {
              // 递归检查所有节点的 body 是否为空
              for (const { node } of iterateNode([item])) {
                if (node.content?.body) {
                  // 去掉所有非 img、table 标签，保留 img、table 标签
                  if (node.content.body.includes('img') || node.content.body.includes('table')) {
                    return false;
                  }
                  const cleanBody = node.content.body.replace(/<(?!\/?(?:img|table)\b)[^>]*>/gi, '');
                  // 去掉 img、table 标签，检查是否还有文本内容
                  const textOnly = cleanBody.replace(/<\/?(?:img|table)[^>]*>/gi, '').replace(/\s+/g, ' ').trim();

                  if (textOnly.length > 0) {
                    return false;
                  }
                }
              }
              return true;
            }

            async function checkImageUrlAvailable(url: string): Promise<boolean> {
              try {
                const res = await axios.head(url, { timeout: 300 });
                return res.status === 200;
              } catch {
                return false;
              }
            }

            for (const item of flatJson) {
              const index = flatJson.findIndex((i) => i.node_id === item.node_id);
              const redisKey = `ai_edit_single:${projectId}-${item.node_id}-${index}`;

              // 检查 body 是否为空
              if (checkBodyEmpty(item)) {
                await app.redis.set(redisKey, '-2');
                logger.info(`[PublishProject] body 内容为空, redis 置为-2: ${redisKey}`);
                continue;
              }

              // 递归检查所有节点的 image_url
              let hasInvalidImageUrl = false;
              for (const { node } of iterateNode([item])) {
                if (!node.image_url || typeof node.image_url !== 'string') {
                  // 跳过空的image_url，不检查连通性
                  continue;
                }
                const ok = await checkImageUrlAvailable(node.image_url);
                if (!ok) {
                  hasInvalidImageUrl = true;
                  break;
                }
              }
              if (hasInvalidImageUrl) {
                await app.redis.set(redisKey, '-1');
                logger.info(`[PublishProject] image_url 连通性异常, redis 置为-1: ${redisKey}`);
                continue;
              }
              // 正常流程
              const key = service.task.base.getAiOssKey(project.appKey, `${projectId}-${item.node_id}-${index}`, 'ai.flatten.json');
              await service.oss.upload(key, JSON.stringify([item]), 'string');
              logger.info(`[PublishProject] ai.flatten.json 上传成功: ${key}`);

              const subjectMapping: any = {
                biology: 'cz_shengwu',
                chemistry: 'cz_huaxue',
                chinese: 'cz_yuwen',
                computer_science: 'cz_jisuanji',
                daode_fazhi: 'cz_daofa',
                english: 'cz_yingyu',
                geography: 'cz_dili',
                history: 'cz_lishi',
                math: 'cz_shuxue',
                physics: 'cz_wuli',
                science: 'cz_kexue',
              };

              // 每个json单独入队
              await service.rbs.initRBSQueue({
                task_id: `${projectId}-${item.node_id}-${index}`,
                task_type: projectMeta.isAICheck ? 'ai_check' : 'ai_edit',
                task_info: {
                  task_id: `${projectId}-${item.node_id}-${index}`,
                  word_url: wordUrl,
                  html_url: htmlUrl,
                  json_url: service.task.base.getAiUrl(project.appKey, `${projectId}-${item.node_id}-${index}`, 'ai.flatten.json'),
                  app_key: project.appKey,
                  subject: subjectMapping[(projectMeta.subject || project.subject) as any] || 'cz_yuwen',
                  word_type: projectMeta.isAICheck ? 'check' : 'analysis',
                  get_task_parameters: [
                    'task_id',
                    'word_url',
                    'html_url',
                    'json_url',
                    'subject',
                    'app_key',
                    'word_type'
                  ],
                  callback_extras: [
                    'task_id',
                    'word_url',
                    'html_url',
                    'app_key',
                    'subject',
                    'word_type'
                  ],
                  run_type: 'common',
                  callback_url: `${config.ngrok.callbackUrl}/api/open/task/proofreadingDocxSingle`,
                  // callback_url: 'https://upward-gibbon-genuinely.ngrok-free.app/api/open/task/proofreadingDocxSingle',
                  push_time: new Date().getTime(),
                  timestamp: new Date().getTime(),
                },
              });
              await app.redis.set(redisKey, '0');
              await service.timeout.addTimeoutTask(
                `${projectId}-${item.node_id}-${index}`,
                'ai_edit_single',
                10.5 * 60 * 1000,
                {
                  title: 'ai-edit任务超时',
                  content: `离线-任务ID: ${projectId}-${item.node_id}-${index} 的AI编辑任务执行超时，请检查ai-edit服务状态`,
                  receiveId: 4,
                  extra: service.ai.formatTaskParams({
                    taskId: `${projectId}-${item.node_id}-${index}`,
                    appKey: project.appKey,
                    subject: projectMeta.subject || project.subject,
                    stage: projectMeta.stage,
                    wordType: projectMeta.isAICheck ? '批量校对-子任务' : '批量解析-子任务',
                  }),
                }
              );
              logger.info(`[PublishProject] ai_edit_single 入队成功: ${projectId}-${item.node_id}-${index}`);
            }
            logger.info(`[PublishProject] ai_edit_single 入队成功: ${projectId}`);
          } catch (error) {
            const trace = error.stack;
            logger.error(`[PublishProject] ai-edit后台处理异常: ${projectId}, ${JSON.stringify(trace)}`);
          }
        });
      }
    } catch (e) {
      await service.project.base.update(
        { status: statuses.error, errorInfo: '发布文件生成异常' },
        { where: { id: projectId } }
      );
      logger.error(`[PublishProject]: exception!!!!! projectId: ${projectId}, ${JSON.stringify(e)}`);
    }

    this.app.logRunningTime(runningTime, `publishProject project:${projectId}`);

  }
}
