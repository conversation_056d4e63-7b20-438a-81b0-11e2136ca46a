'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import BaseSubscription from '../core/base/baseSubscription';
import { TaskProcessTypes } from '../service/task/base';

interface IResultDataBase {
  taskId: number;
  imageId: string;
  result?: any;
  type: TaskProcessTypes;
  priority?: boolean;
}

interface IStructResultData extends IResultDataBase {
  result?: {
    formulas: {
      id: string;
      latex: string;
      disabled?: boolean;
      confidence?: number;
      coordinate?: number[];
    }[];
  };
  type: 'imageStructProcessor';
}

interface IColumnResultData extends IResultDataBase {
  result?: {
    aiFilter?: boolean | number;
    multiple?: boolean | number;
    points: any[] | string;
  };
  type: 'imageColumnProcessor';
}

type TResultData = IStructResultData | IColumnResultData;

export default class UpdateImageTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updateImageTaskColumnProcessor);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  private async updateImageColumnProcessorResult(
    task: { taskId: number; bookId: number; rerun?: boolean },
    image: { taskOrder: number },
    data: IColumnResultData
  ) {
    const { service, ctx } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, result, type } = data;
    // 更新图片识别完成，标识为 1
    await service.task.base.setTaskStatus(taskId, type, imageId);
    if (result) {
      const points = typeof result.points === 'string' ? JSON.parse(result.points) as any[] : result.points;
      const latexResult: any[] = [];
      const columnResult: any[] = [];
      points.forEach((item) => {
        if (!['paragraph', '$par', '$pac', 'heading'].includes(item.type)) {
          latexResult.push(item);
        } else {
          columnResult.push(item);
        }
      });
      this.logger.info(`updateImageColumnProcessorResult imageId:${imageId} result:${JSON.stringify(result)}`);
      if (task.rerun || !result.aiFilter) {
        await service.image.update(
          {
            multiple: Boolean(result.multiple),
            columnResult: columnResult.length ? JSON.stringify(columnResult) : '',
            latexResult: latexResult.length ? JSON.stringify(latexResult) : '',
          },
          { where: { imageId } }
        );
      } else {
        await service.image.update(
          {
            multiple: Boolean(result.multiple),
            columnResult: columnResult.length ? JSON.stringify(columnResult) : '',
            latexResult: latexResult.length ? JSON.stringify(latexResult) : '',
          },
          { where: { imageId } }
        );
        await service.column.relatedUpdate({
          columnResult,
          latexResult,
          imageId,
          taskId: task.taskId,
          bookId: task.bookId,
          taskOrder: image.taskOrder,
          rerun: task.rerun!,
        });
        // 记录操作日志
        ctx.runInBackground(async() => {
          await service.task.history.create({
            taskId,
            userId: service.user.aiFilterUserId,
            costTime: 0,
            type: service.task.history.columnTypes.save.id,
            data: JSON.stringify({ imageId }),
          });
        });
      }
    }
    // 判断若当前任务的全部图片都识别完成，进入下一步
    if (await service.task.base.hasAllCompleted(taskId, type)) {
      await service.task.base.deleteTaskConfig(taskId, 'imageColumnProcessor');
      this.logger.info(`task ${taskId} status changed : ${statuses.columnAutoProcessed}`);
      await service.task.base.update({ status: statuses.columnAutoProcessed }, {
        where: {
          taskId,
          status: { $lt: statuses.columnAutoProcessed },
        },
      });
    }
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, logger } = this;
    const data: TResultData = await service.image.popFromQueue();
    if (!data) {
      logger.info('[updateImageColumnProcessorResult] 队列为空～～～');
      return;
    }
    
    // 记录是否为高优先级任务
    const priorityLog = data.priority ? ' [高优先级]' : '';
    logger.info(`[updateImageColumnProcessorResult] 取出任务${priorityLog}，taskId: ${data.taskId}, imageId: ${data.imageId}, type: ${data.type}`);
    
    if (data.type === 'imageStructProcessor') { // 不是期望处理的任务类型就重新放回队列
      logger.info('[updateImageColumnProcessorResult] struct 是识别任务，重新插回队尾！');
      return await service.image.pushToQueue(JSON.stringify(data));
    }
    const runningTime = Number(new Date());
    const { statuses } = service.task.base;
    const { taskId, imageId, type } = data;
    logger.info(`[updateImageColumnProcessorResult] params is ${JSON.stringify({ taskId, imageId, type })}`);
    const taskProcessStatus = await service.task.base.getTaskStatus(taskId, type, imageId);
    if (taskProcessStatus === '1') {
      // 若当前图片已经处理完成，则当作无效状态、不予处理
      logger.warn(`无效的图片状态! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    const [task, image] = await Promise.all([
      service.task.base.getOne({
        where: { taskId, status: statuses.columnAutoProcessing },
        attributes: ['taskId', 'bookId', 'appKey', 'subject', 'markUserId', 'rerun', 'resourceType'],
      }),
      service.image.getOne({
        where: { imageId },
        attributes: ['appKey', 'taskOrder'],
      })
    ]);
    if (!task) {
      logger.warn(`任务不存在或任务状态无效! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    if (!image) {
      logger.warn(`图片不存在! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    switch (data.type) {
    case 'imageColumnProcessor':
      await this.updateImageColumnProcessorResult(task, image, data);
      break;
    default:
      logger.info(`there is no available processing type, data : ${JSON.stringify(data)}`);
      break;
    }
    this.app.logRunningTime(runningTime, `updateImageTask task:${JSON.stringify(taskId)}`);
  }
}
