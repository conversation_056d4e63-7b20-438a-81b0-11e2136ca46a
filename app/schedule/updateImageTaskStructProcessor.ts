/**
 * @file 更新图片识别结果
 * <AUTHOR>
 */

'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import BaseSubscription from '../core/base/baseSubscription';
import { PlanTaskType } from '../model/planFlow';
import { PlanTaskStatus } from '../model/planTask';
import { getHtml, parseHtml } from '../core/utils/htmlHelper';
import { convertLatexToPlainText, formatLatexHtml } from '../core/utils/helper';
import { TaskProcessTypes } from '../service/task/base';
import { ETaskResourceType } from '../model/task';

interface IResultDataBase {
  taskId: number;
  imageId: string;
  result?: any;
  type: TaskProcessTypes;
  priority?: boolean;
}

interface IStructResultData extends IResultDataBase {
  result?: {
    formulas: {
      id: string;
      latex: string;
      disabled?: boolean;
      confidence?: number;
      coordinate?: number[];
    }[];
  };
  type: 'imageStructProcessor';
}

interface IColumnResultData extends IResultDataBase {
  result?: {
    aiFilter?: boolean | number;
    multiple?: boolean | number;
    points: any[] | string;
  };
  type: 'imageColumnProcessor';
}

type TResultData = IStructResultData | IColumnResultData;

export default class UpdateImageTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'global', ctx.app.config.scheduleLockKey.updateImageTaskStructProcessor);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  private async updateImageStructProcessorResult(
    task: { appKey: string; subject: string; markUserId: number; rerun?: boolean; resourceType?: ETaskResourceType },
    image: { appKey: string },
    data: IStructResultData
  ) {
    const { service, logger } = this;
    const { statuses } = service.task.base;
    const { taskId, imageId, result, type } = data;
    const logPrefix = `${taskId}:${imageId}`;
    const formulas = result && result.formulas ? result.formulas : [];
    logger.info(`start update struct processor ${logPrefix}!`);
    // 清空图片下所有公式数据
    await service.formula.delete({ where: { imageId } });
    if (formulas.length) {
      const newFormulas = formulas.map((item) => {
        return {
          imageId,
          formulaId: item.id,
          latex: item.latex,
          marked: false,
          reviewed: false,
          disabled: item.disabled || false,
          confidence: item.confidence ? _.round(item.confidence, 4) : 0,
          coordinate: item.coordinate ? item.coordinate.join(',') : '',
        };
      });
      logger.info(`update struct processor ${logPrefix}, formulas : ${formulas.length}`);
      await service.formula.bulkCreate(newFormulas);
    }
    if (task.subject === 'chinese') {
      // 中文学科，将公式转换成纯文本
      const html = await service.image.getOssData(image.appKey, imageId, 'html');
      let newHtml = formatLatexHtml(html);
      const nodes = parseHtml(newHtml);
      convertLatexToPlainText(nodes);
      newHtml = getHtml(nodes);
      if (newHtml !== html) {
        await service.image.setOssData(image.appKey, imageId, 'html', newHtml);
      }
    }
    await service.task.base.setTaskStatus(taskId, type, imageId);
    const hasAllCompleted = await service.task.base.hasAllCompleted(taskId, type);
    logger.info(`after update struct processor ${logPrefix}, taskId : ${taskId},  hasAllCompleted : ${hasAllCompleted}`);
    if (!hasAllCompleted) {
      /*
       * @todo：存在部分图片识别失败，任务状态设置为异常
       * await service.task.base.update({
       *   status: statuses.error,
       *   errorInfo: `存在部分图片 ${type} 识别失败`,
       * }, { where: { taskId } });
       */
      return;
    }
    const images = await service.image.getAll({
      where: { taskId, disabled: false },
      attributes: ['imageId', 'filename', 'taskOrder'],
      order: [['id', 'ASC']],
    });
    await service.task.base.update({
      status: task.rerun && task.markUserId ? statuses.marking : statuses.unmarked,
      rerun: false,
    }, { where: { taskId } });
    await service.image.update({ rerun: false }, { where: { taskId } });
    await service.plan.task.onChangeTaskStatus({
      taskId,
      taskType: PlanTaskType.MARK,
      targetStatus: task.rerun && task.markUserId ? PlanTaskStatus.ING : PlanTaskStatus.INIT,
    });
    await service.plan.task.onChangeTaskStatus({
      taskId,
      taskType: PlanTaskType.REVIEW,
      targetStatus: PlanTaskStatus.PENDING,
    });
    await service.task.base.deleteTaskConfig(taskId, 'imageStructProcessor');
    const appKey = task.appKey;
    const imageIds: string[] = service.image.sortImageByFile(images).map(({ imageId }) => {
      return imageId;
    });
    const html = await service.task.stat.stashMachineHtml(appKey, taskId, imageIds);
    await service.task.stat.newStat({
      html,
      taskId,
      type: 'machine',
      resourceType: task.resourceType!,
      appKey,
      subject: task.subject,
      imageCount: images.length,
    });
  }

  public async start() {
    this.logger.info(`[updateImageStructProcessorResult] process.argv is ${JSON.stringify(process.argv)}`);
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      this.logger.info('[updateImageStructProcessorResult] 跳出');
      return;
    }
    const { service, logger } = this;
    const data: TResultData = await service.image.popFromQueue();
    if (!data) {
      logger.info('[updateImageStructProcessorResult] 队列为空～～～');
      return;
    }
    
    // 记录是否为高优先级任务
    const priorityLog = data.priority ? ' [高优先级]' : '';
    logger.info(`[updateImageStructProcessorResult] 取出任务${priorityLog}，taskId: ${data.taskId}, imageId: ${data.imageId}, type: ${data.type}`);
    
    if (data.type === 'imageColumnProcessor') {
      logger.info('[updateImageStructProcessorResult] column 是划块任务，重新插回队尾！');
      return await service.image.pushToQueue(JSON.stringify(data));
    }
    const runningTime = Number(new Date());
    const { statuses } = service.task.base;
    const { taskId, imageId, type } = data;
    logger.info(`[updateImageStructProcessorResult] params is ${JSON.stringify({ taskId, imageId, type })}`);
    const taskProcessStatus = await service.task.base.getTaskStatus(taskId, type, imageId);
    if (taskProcessStatus === '1') {
      // 若当前图片已经处理完成，则当作无效状态、不予处理
      logger.warn(`无效的图片状态! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    const [task, image] = await Promise.all([
      service.task.base.getOne({
        where: {
          taskId,
          status: statuses.autoProcessing,
        },
        attributes: ['taskId', 'bookId', 'appKey', 'subject', 'markUserId', 'rerun', 'resourceType'],
      }),
      service.image.getOne({
        where: { imageId },
        attributes: ['appKey', 'taskOrder'],
      })
    ]);
    if (!task) {
      logger.warn(`任务不存在或任务状态无效! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    if (!image) {
      logger.warn(`图片不存在! ${JSON.stringify({ taskId, imageId, type })}`);
      return;
    }
    switch (data.type) {
    case 'imageStructProcessor':
      await this.updateImageStructProcessorResult(task, image, data);
      break;
    default:
      logger.info(`there is no available processing type, data : ${JSON.stringify(data)}`);
      break;
    }

    this.app.logRunningTime(runningTime, `updateImageTask task:${JSON.stringify(taskId)}`);

  }
}
