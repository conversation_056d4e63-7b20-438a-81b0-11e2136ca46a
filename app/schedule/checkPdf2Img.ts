'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';
import { ITaskFiles } from '../model/taskFile';
import * as uuid from 'uuid/v4';
import { ETaskResourceType } from '../model/task';

export default class CheckPdf2Img extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.checkPdf2Img, 1);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre' ,'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, config } = this;
    const { statuses } = service.task.base;

    const tasks = await service.task.base.getAll({
      where: { resourceType: ETaskResourceType.PDF, status: statuses.pending },
      attributes: ['taskId', 'status', 'appKey', 'taskName'],
    });
    if (!tasks.length) {
      return;
    }
    this.logger.info('[CheckPdf2Img] start, tasks:', JSON.stringify(tasks));
    const runningTime = Number(new Date());
    await Promise.all(
      tasks.map(async(task) => {
        try {
          const taskFile = await service.task.file.getFiles({ taskId: Number(task.taskId) });
          const { pdfs } = taskFile;
          this.logger.info(`[CheckPdf2Img] task ${task.taskId} begin`);

          // 判断是否全部生成完成
          let finishCount = 0;
          for (const pdf of pdfs!) {
            if (pdf.task_id) {
              const { data } = await this.app.curl(
                `${config.workOrder?.apiTask || config.workOrder.api}/api/open/queue/v1/pdf_to_image`,
                {
                  method: 'GET',
                  dataType: 'json',
                  data: { task_id: pdf.task_id },
                }
              );
              if (data?.data?.status == 3) finishCount++;
              else if (data?.data?.status < 0) {
                this.logger.info(`[checkPdf2img.error] taskId: ${task.taskId}`);
                await this.service.task.taskV2.update(
                  {
                    status: statuses.error,
                    errorInfo: data?.data?.error_info,
                  },
                  { where: { taskId: Number(task.taskId) } }
                );
                // 通知机器人
                const errorData = {
                  type: 'error',
                  receive_id: 13,
                  server: { name: 'xdoc线上服务' },
                  content: {
                    title: 'pdf预处理异常',
                    text: `schedule.checkPdf2img---statusError---taskId---> ${task.taskId}
                    errorInfo: ${JSON.stringify(data?.data?.error_info) || ''}`,
                  },
                };
                service.robot.sendRobotMessage(errorData);
                return;
              }
            } else {
              return await this.service.task.taskV2.update(
                {
                  status: statuses.error,
                  errorInfo: '此pdf初始化失败，未生成pdf2image的taskId',
                },
                { where: { taskId: Number(task.taskId) } }
              );
            }
          }
          if (finishCount !== pdfs?.length) return;
          this.logger.info(`[CheckPdf2Img] task ${task.taskId} finish ConvertImg`);

          // 多 PDF 转 image
          const imgResult: any[] = [];
          for (const pdf of pdfs!) {
            // pdf 转图片完成 获取图片
            const { data } = await this.app.curl(
              `${config.workOrder?.apiTask || config.workOrder.api}/api/open/content/v1/file_mata/pdf_to_image_solr`,
              {
                method: 'GET',
                dataType: 'json',
                data: { md5: pdf.id },
              }
            );
            const imgList = data.data.pdf_image;
            let index = 0;
            const imgRes: any[] = [];
            const { env } = this.app;
            for (const item of imgList) {
              const newImageId = uuid().replace(/-/g, '');
              await service.task.base.uploadImage(
                task.appKey,
                `https://${item.bucket_name || 'hexin-worksheet'}.oss-cn-shanghai${env === 'prod' ? '-internal' : ''}.aliyuncs.com/` + item.path,
                newImageId
              );
              // 存图片的info
              const info = await service.image.getImageInfo(
                task.appKey,
                newImageId
              );
              imgRes.push({
                ...item,
                id: newImageId,
                name: task.taskName + '_' + index,
                info: JSON.stringify(info),
                pdfId: pdf.id,
                type: pdf.type || 'body',
              });
              index++;
            }

            imgResult.push(...imgRes);
          }
          const file: Partial<ITaskFiles> = { imgs: imgResult };
          await service.task.file.setFiles([task.taskId], file);
          await this.service.task.taskV2.update(
            { status: statuses.split },
            { where: { taskId: Number(task.taskId) } }
          );
          this.logger.info(`${task.taskId} 已完成pdf转图片`);
          return { imgResult, taskId: Number(task.taskId) };
        } catch (e) {
          await this.service.task.taskV2.update(
            {
              status: statuses.error,
              errorInfo: `${(e as any).message}`,
            },
            { where: { taskId: Number(task.taskId) } }
          );
        }
      })
    );

    this.app.logRunningTime(
      runningTime,
      `CheckPdf2Img tasks:${JSON.stringify(tasks)}`
    );
  }
}
