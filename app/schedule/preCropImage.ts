/**
 * @file 执行手工切图结果，将原图拆分成图
 */

'use strict';
import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

// 人工切图完成走到这里
export default class PreCropImageTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.preCropImage, 16);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, logger, ctx } = this;
    const { statuses } = service.task.base;
    const taskId = await service.task.base.popPreCrop();
    if (!taskId) return;

    const runningTime = Number(new Date());

    const task = await service.task.base.getOne({ where: { taskId, status: statuses.preCropProcessed } });
    if (!task) {
      logger.info(`预切图任务状态错误，不是手工切图完成状态 ${taskId} `);
      return;
    }
    const images = await service.sourceImage.getAll({
      where: { taskId },
      attributes: ['imageId', 'appKey', 'bookId', 'filename', 'taskOrder', 'result', 'info'],
    });
    let success = false;
    try {
      // 使用 result 信息拆分 source_image，并将 source_image 同步到 image 中。并修改任务状态。
      await service.task.base.finishPreCropImage(taskId, images);
      success = true;
    } catch (e) {
      logger.error(`预切图失败 ${taskId}`, e);
      await service.task.base.update({ status: statuses.preCropChecked }, { where: { taskId } });
    }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId: service.user.aiFilterUserId,
        costTime: 0,
        type: service.task.history.preCropTypes.crop.id,
        data: JSON.stringify({ success }),
      });
    });

    this.app.logRunningTime(runningTime, `preCropImage tasks:${JSON.stringify(taskId)}`);
  }
}
