'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';
import { errorNodeStat } from '../core/utils/jsonHelper';
import { cleanJsonNodes } from '../core/utils/htmlToJsonV4';
// import { preCleanHtml } from '../core/utils/htmlToJsonV4/helper/cleanupNode';

export default class PublishTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.generateImages, 4);
  }

  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  /**
   * 任务发布的队列
   * 1. 生成 html 和 .docx 文件
   * 2. 数据清洗
   */
  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, app } = this;
    const { statuses } = service.task.base;
    const { userId, body, level, index, taskId } = await service.task.base.popFromGenerateImages();
    this.logger.info(`[popFromGenerateImages] 任务 ${taskId} 开始重新生成`);
    if (!taskId) {
      return;
    }
    const runningTime = Number(new Date());
    try {
      const [task, images] = await Promise.all([
        service.task.base.getOne({ where: { taskId } }),
        service.image.getAll({
          where: { taskId, disabled: false },
          attributes: ['reviewed', 'imageId', 'filename', 'taskOrder'],
          order: [['id', 'ASC']],
        })
      ]);
      if (!task) {
        this.logger.info(`[popFromGenerateImages] 任务 ${taskId} 不存在`);
        return;
      }
      const allImageIds: string[] = [];
      for (const { imageId } of service.image.sortImageByFile(images as any[])) {
        allImageIds.push(imageId);
      }

      const { appKey } = task;

      const { html, imageId } = await service.task.base.changeChapter({
        allImageIds,
        appKey,
        index,
        body,
        level,
      });
      if (!html || !imageId) {
        this.logger.info(`[popFromGenerateImages] 任务 ${taskId} html 不存在`);
        await service.task.base.update(
          { generateStatus: 2 },
          { where: { taskId } }
        );
        return;
      }
      await service.image.setOssData(appKey, imageId, 'html', html);
      if (task.status === statuses.reviewed) {
        const docKey = service.task.base.getOssKey(task.appKey, taskId, 'docx');
        const res1 = await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
        const res2 = await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
        if (res1.html.length !== res2.html.length || res1.xml1.length !== res2.xml1.length || res1.xml2.length !== res2.xml2.length) {
          await service.task.base.update(
            {
              errorInfo: res1.html.length !== res2.html.length && 'html长度不相等' || res1.xml1.length !== res2.xml1.length && 'xml1长度不相等' || res1.xml2.length !== res2.xml2.length && 'xml2长度不相等' || '',
              status: statuses.error,
            },
            { where: { taskId } }
          );
        }
        await service.task.base.setOssData(task.appKey, Number(`${taskId}111`), 'html', res1.html);
        await service.task.base.setOssData(task.appKey, Number(`${taskId}112`), 'html', res1.xml1);
        await service.task.base.setOssData(task.appKey, Number(`${taskId}113`), 'html', res1.xml2);
        await service.task.base.setOssData(task.appKey, Number(`${taskId}211`), 'html', res2.html);
        await service.task.base.setOssData(task.appKey, Number(`${taskId}212`), 'html', res2.xml1);
        await service.task.base.setOssData(task.appKey, Number(`${taskId}213`), 'html', res2.xml2);

        let formattedHtml = await service.task.base.getOssData(task.appKey, taskId, 'formatted.html');
        formattedHtml = await service.task.base.changeHtmlChapter(formattedHtml, index, body, level);

        const json2 = await service.task.base.convert2Json(task.appKey, taskId, formattedHtml);

        await service.task.base.setOssData(task.appKey, taskId, 'formatted.html', formattedHtml);
        await service.task.base.setOssData(task.appKey, taskId, 'formatted.internal.json', json2);
        await service.task.base.setOssData(task.appKey, taskId, 'formatted.json', cleanJsonNodes(json2));

      }
      const htmls = await service.task.base.combineAndUploadByImageIds(task.appKey, task.taskId, allImageIds);
      const json = await service.task.base.convert2Json(task.appKey, task.taskId, htmls);
      const errorStat = errorNodeStat(json);
      await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
      await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));

      const { mongo } = app;
      await mongo.db.collection('error_node_stat').insertOne({
        taskId,
        userId,
        action: 'regenerate',
        timestamp: new Date().getTime(),
        ...errorStat,
      });

      await service.task.base.update({ errorNodeCount: errorStat.count, generateStatus: 1 }, { where: { taskId } });

    } catch (e) {
      this.logger.error(`[popFromGenerateImages] 任务 ${taskId} 生成失败`, e);
      await service.task.base.update(
        { generateStatus: 2 },
        { where: { taskId } }
      );
    }
    await this.app.logRunningTime(runningTime, `popFromGenerateImages task: ${taskId}`);
  }
}
