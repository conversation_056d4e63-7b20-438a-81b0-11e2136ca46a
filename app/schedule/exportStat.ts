'use strict';

import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';
import * as _ from 'lodash';
import * as xlsx from 'xlsx';
import * as dateformat from 'dateformat';
import { retry } from '../core/utils';

export default class PublishTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.exportData, 3);
  }

  static get schedule() {
    return {
      interval: '1.2s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  /**
   * 任务发布的队列
   * 1. 生成 html 和 .docx 文件
   * 2. 数据清洗
   */
  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, app } = this;
    const input = await service.task.stat.popFromExport();
    if (!input || !Object.keys(input)?.length || !input.key) {
      this.logger.info('[ExportData] 没有任务');
      return;
    }
    const match: any = {};
    try {
      this.logger.info(`[ExportData] 开始导出 ${input.key}`);
      if (input.taskIds) match.task_id = { $in: input.taskIds };
      if (input.userIds) match.user_id = { $in: input.userIds };
      if (input.types) match.type = { $in: input.types };
      if (input.subjects) match.subject = { $in: input.subjects };
      if (input.startTime) match.stat_at = { $gte: input.startTime };
      if (input.endTime) match.stat_at = { ...match.stat_at, $lt: input.endTime };
      const project = { _id: 0 };
      const taskAttrs: string[] = [];
      const userAttrs: string[] = [];
      const appAttrs: string[] = [];
      const clientAttrs: string[] = [];
      const metaAttrs: string[] = [];
      const projectAttrs: string[] = [];
      input.columns.forEach((item) => {
        if (item.prop === 'formula_request_count') {
          taskAttrs.push('formulaRequestCount');
        }
        if (item.prop.startsWith('basic.')) {
          // 任务相关属性
          const prop = item.prop.substring('basic.'.length);
          if (prop === 'startTime') {
            taskAttrs.push('startMarkTime');
            taskAttrs.push('startReviewTime');
          } else if (prop === 'endTime') {
            taskAttrs.push('endMarkTime');
            taskAttrs.push('endReviewTime');
          } else {
            taskAttrs.push(prop);
          }
        } else if (item.prop.startsWith('app.')) {
          // 应用相关属性
          const prop = item.prop.substring('app.'.length);
          appAttrs.push(prop);
        } else if (item.prop.startsWith('client.')) {
          // 客户相关属性
          const prop = item.prop.substring('client.'.length);
          clientAttrs.push(prop);
        } else if (item.prop.startsWith('user.')) {
          // 用户相关属性
          const prop = item.prop.substring('user.'.length);
          userAttrs.push(prop);
        } else if (item.prop.startsWith('meta.')) {
          // 额外属性
          const prop = item.prop.substring('meta.'.length);
          metaAttrs.push(prop);
        } else if (item.prop.startsWith('project.')) {
          // 项目属性
          const prop = item.prop.substring('project.'.length);
          projectAttrs.push(prop);
        } else {
          // 统计属性
          if (item.prop !== 'formula_request_count') {
            project[item.prop] = 1;
          }
        }
      });
      if (taskAttrs.length || metaAttrs.length || projectAttrs.length || input.projectIds) {
        project['task_id'] = 1;
        projectAttrs.push('id');
        taskAttrs.push('taskId');
      }
      if (projectAttrs.length || input.projectIds) {
        taskAttrs.push('bookId');
        projectAttrs.push('id');
      }
      if (input.ticketIds?.length) {
        projectAttrs.push('workOrder');
      }
      if (userAttrs.length) {
        project['user_id'] = 1;
        userAttrs.push('userId');
      }
      if (appAttrs.length) {
        taskAttrs.push('appKey');
        appAttrs.push('appKey');
      }
      if (clientAttrs.length) {
        appAttrs.push('userId');
        clientAttrs.push('userId');
      }
      type PromiseValue<T> = T extends PromiseLike<infer U> ? U : never;
      let books: PromiseValue<ReturnType<typeof service.book.getAll>> | undefined;
      let tasks: PromiseValue<ReturnType<typeof service.task.base.getAll>> | undefined;
      let markSubjects: PromiseValue<ReturnType<typeof service.task.meta.getMetasDict>> | undefined;
      if (input.projectIds || input.ticketIds) {
        let projectIds: number[] = [];
        if (input.projectIds) {
          projectIds = input.projectIds;
        } else if (input.ticketIds) {
          const projects = await service.project.base.getAll({ where: { workOrder: input.ticketIds }, attributes: ['id'] });
          projectIds = projects.map((_project) => _project.id);
        }
        books = await service.book.getAll({ where: { projectId: projectIds }, attributes: ['id', 'projectId'] });
        const query: any = { bookId: books.map((b) => b.id) };
        if (input.taskIds) query.taskId = input.taskIds;
        if (input.appKeys) query.appKey = input.appKeys;
        tasks = await service.task.base.getAll({ where: { ...query, mergedTaskId: null }, attributes: taskAttrs });
        match.task_id = { $in: tasks.map((task) => task.taskId) };
      } else if (input.appKeys) {
        const query: any = { appKey: input.appKeys };
        if (input.taskIds) query.taskId = input.taskIds;
        tasks = await service.task.base.getAll({ where: { ...query, mergedTaskId: null }, attributes: taskAttrs });
        match.task_id = { $in: tasks.map((task) => task.taskId) };
      }
      const stats = await app.mongo.db.collection('stat').find(match, project).toArray();
      this.logger.info(`[ExportData] 开始导出 ${input.key}，共 ${stats.length} 条`);
      const userIds = [...new Set<number>(stats.map((item) => item.user_id).filter((i) => i))];
      const taskIds = [...new Set<number>(stats.map((item) => item.task_id).filter((i) => i))];
      const [users, metaDict] = await Promise.all([
        userAttrs.length && userIds.length ? app.model.query(
          `select ${userAttrs.join(',').replace(/;/g, '')} from user_center.ob_user_info where userId in (:userIds)`,
          { raw: true, type: app.model.QueryTypes.SELECT, replacements: { userIds } }
        ) : [],
        metaAttrs.length && taskIds.length ?
          service.task.meta.getMetasDict({ taskId: taskIds, key: metaAttrs }) :
          {}
      ]);
      const metaInfoDict = await service.project.meta.getMetaInfoDict(metaDict, true);
      const _bookMap: any = {};
      const _projectMap: any = {};
      const _metaMap: any = {};
      const _taskMap: any = {};
      const _statsMap: any = {};
      if (!tasks) {
        tasks = taskAttrs.length && taskIds.length ? await service.task.base.getAll({
          where: { taskId: taskIds, mergedTaskId: null },
          attributes: taskAttrs,
        }) : [];
        // 没输入 taskId 的情况下
      }
      for (const task of tasks) {
        // 获取这个任务的总字符 html.p.char
        let book;
        let _project;
        let meta;
        let _tasks;
        let _stats;
        if (!_bookMap[task.bookId]) {
          book = await service.book.getOne({ where: { id: task.bookId } });
          // 处理过就别再查询一次了, 没有 book就不需要再往下走
          _bookMap[task.bookId] = book;
        } else {
          book = _bookMap[task.bookId];
        }
        if (!book) continue;
        if (!_projectMap[book.projectId]) {
          _project = await service.project.base.getOne({ where: { id: book.projectId } });
          _projectMap[book.projectId] = _project;
        } else {
          _project = _projectMap[book.projectId];
        }
        if (!_metaMap[_project.id]) {
          meta = await service.project.meta.getMetas({ projectId: _project.id });
          _metaMap[_project.id] = meta;
        } else {
          meta = _metaMap[_project.id];
        }
        if (_taskMap[_project.id]) {
          _tasks = _taskMap[_project.id];
        } else {
          _tasks = await service.task.base.getAll({ where: { bookId: task.bookId, mergedTaskId: null } });
          const taskMetas = await service.task.meta.getMetasDict({ taskId: _tasks.map((t) => t.taskId) });
          // 获取 path
          _tasks.forEach((v) => {
            v.path = taskMetas[v.taskId.toString()]?.path;
          });
          _taskMap[_project.id] = _tasks;
        }
        if (_statsMap[_project.id]) {
          _stats = _statsMap[_project.id];
        } else {
          _stats = await app.mongo.db.collection('stat').find({ ...match, task_id:  { $in: _tasks.map((t) => t.taskId) } }).toArray();
          _statsMap[_project.id] = _stats;
        }
        const projectTotalPage = meta.pageCountInfos?.[0]?.internalPages || 0;
        const taskFile = await service.task.file.getFiles({taskId: task.taskId});
        const wordPageNum = taskFile?.words?.body?.[0]?.after_page_num;
        const isOpenWord = meta.isOpen;
        _project.projectTotalPage = projectTotalPage;
        _project.isOpenWord = isOpenWord ? wordPageNum : 0;
        // 计算总字符数
        const allChar = _stats.filter((stat, index, arr) => arr.findIndex((s) => s.task_id === stat.task_id) === index).reduce((prev, cur) => prev + (cur?.html?.p?.char || 0), 0);
        _project.projectTotalChar = allChar;
        const _stat = stats.find((stat) => stat.task_id === task.taskId);
        const taskChar = _stat?.html?.p?.char;
        // 算出这个任务的占比
        const taskPage = taskChar / allChar * projectTotalPage;
        (task as any).taskPage = taskPage;
      }
      if (!books) {
        const bookIds = projectAttrs.length ? [...new Set(tasks.map((item) => item.bookId))] : [];
        books = bookIds.length ?
          await service.book.getAll({ where: { id: bookIds }, attributes: ['id', 'projectId'] }) :
          [];
      }
      const projectIds = [...new Set(books.map((b) => b.projectId))];
      const projects = projectIds.length && projectAttrs.length ?
        await service.project.base.getAll({ where: { id: projectIds }, attributes: projectAttrs }) :
        [];
      const appKeys = [...new Set(tasks.map((item) => item.appKey).filter((i) => i))];
      const apps = appAttrs.length && appKeys.length ? await service.appl.getAllByUc({
        where: { appKey: appKeys },
        attributes: appAttrs,
      }) : [];
      const clientUserIds = [...new Set(apps.map((item) => item.userId).filter((i) => i))];
      const clients = clientAttrs.length && clientUserIds.length ? await service.client.base.getAll({
        where: { userId: clientUserIds },
        attributes: clientAttrs,
      }) : [];
      tasks.forEach((basic) => {
        ['startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime', 'createTime'].forEach((p) => {
          basic[p] = dateformat(basic[p], 'yyyy-mm-dd HH:MM:ss');
        });
      });
      const userMap = _.keyBy(users, (user) => user.userId);
      const taskMap = _.keyBy(tasks, (basic) => basic.taskId);
      const appMap = _.keyBy(apps, (app) => app.appKey);
      const clientMap = _.keyBy(clients, (client) => client.userId);
      const bookMap = _.keyBy(books, (book) => book.id);
      const projectMap = _.keyBy(projects, (project) => project.id);
      input.columns.push({ title: '总页数', prop: 'totalPage' });
      input.columns.push({ title: '页数', prop: 'pages' });
      // input.columns.push({ title: '个数', prop: 'taskCount' });
      input.columns.push({ title: '流程', prop: 'flow' });
      const header = input.columns.map((item) => item.title);
      const data = [header];
      const typeTitles = { machine: '机器', mark: '标注', review: '审核', project_check: '项目审核', admin_check: '抽检' };
      const subjectTitles = { chinese: '中英', math: '数理', 'en-math': '英文数理' };
      let _stats: typeof stats = [];
      for (const stat of stats) {
        // 项目 ID
        const task = { ...taskMap[stat.task_id], startTime: '', endTime: '' };
        const book = { ...bookMap[task.bookId] };
        const _project = { ...projectMap[book.projectId] };
        stat.projectId = _project.id;
        stat.workOrder = _project.workOrder;
        // 页数, 在 _taskMap 中找到对应的 tasks, 然后找到 reviewUserId 是自己的那个 task, 然后取出在 taskMap 中找到所有的 task, 然后计算总页数
        stat.totalPage = _projectMap[_project.id]?.projectTotalPage || 0;
        stat.word_page_num = _projectMap[_project.id]?.isOpenWord ? _projectMap[_project.id]?.isOpenWord : 0;
        const targetTask = _taskMap[_project.id]?.find((_task) => task.taskId === _task.taskId); // 区分 v1, v2
        stat.pages = (task as any).taskPage || 0;
        stat.flow = targetTask?.path === '1' ? 'v1' : targetTask?.path === '2' ? 'v2' : '-';
        stat.formula_request_count = task.formulaRequestCount || 0;
        _stats.push({ ...stat });
      }
      if (input.sort.item !== 'ticketId') {
        _stats = _stats.sort((a, b) => {
          // 根据 input.sort.item， input.sort.order 排序
          const sort = input.sort;
          if (sort) {
            const aSortValue = _.get(a, sort.item);
            const bSortValue = _.get(b, sort.item);
            if (sort.order === 'asc') {
              return aSortValue > bSortValue ? 1 : -1;
            }
            return aSortValue < bSortValue ? 1 : -1;
          }
          return 0;
        });
      } else {
        // 根据 ticketId 输入顺序排序
        const ticketIds = input.ticketIds || [];
        if (ticketIds.length) {
          this.logger.info(`[ExportData] ticketIds: ${ticketIds.length}`);
          _stats = _stats.sort((a, b) => {
            return ticketIds.indexOf(a.workOrder) - ticketIds.indexOf(b.workOrder);
          });
        } else {
          // 没有 ticketId 的情况下，按照 task_id 排序
          _stats = _stats.sort((a, b) => a.task_id - b.task_id);
        }
      }
      _stats.forEach((stat) => {
        const user = userMap[stat.user_id];
        const task = { ...taskMap[stat.task_id], startTime: '', endTime: '' };
        const book = { ...bookMap[task.bookId] };
        const project = { ...projectMap[book.projectId] };
        const app = appMap[task.appKey];
        const client = clientMap[(app || {}).userId];
        const meta = { ...metaDict[stat.task_id], ...metaInfoDict[stat.task_id] };

        task.startTime = task[({ mark: 'startMarkTime', review: 'startReviewTime' })[stat.type]];
        task.endTime = task[({ mark: 'endMarkTime', review: 'endReviewTime' })[stat.type]];

        stat.user = user;
        stat.basic = task;
        stat.project = project;
        stat.app = app;
        stat.client = client;
        stat.meta = meta;

        if (stat.cost) {
          stat.cost = Math.round(stat.cost / 1000);
        }
        if (stat.stat_at) {
          stat.stat_at = dateformat(stat.stat_at, 'yyyy-mm-dd HH:MM:ss');
        }
        // map title
        stat.type = typeTitles[stat.type] || stat.type;
        stat.subject = ['biology'].includes(markSubjects?.[stat.task_id.toString()]?.subject || '') ? '中英' : subjectTitles[stat.subject] || stat.subject;
        const row = input.columns.map((item) => {
          const value = _.get(stat, item.prop);
          if (item.title === '合计') {
            // 获取合计的项
            const items = item.prop.split('+').map((v) => v.trim());
            // 如果 items 有 input.columns 中不存在的项, 则输出 0（参数错误）
            if (items.some((v) => !input.columns.some((i) => i.prop === v))) {
              return '0（合计项参数错误）';
            }
            return items.reduce((prev, cur) => prev + _.get(stat, cur), 0);
          }
          return value;
        });
        data.push(row);
      });
      const ws = xlsx.utils.aoa_to_sheet(data);
      const wb = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(wb, ws, '统计');
      const buffer = xlsx.write(wb, { type: 'buffer', bookType: 'xlsx' });
      this.logger.info(`[ExportData] 开始导出 ${input.key}，完成 buffer 构造，共 ${stats.length} 条数据`);
      // 上传到 oss
      await retry(async() => await service.oss.upload(input.key!, buffer), [], 3);
    } catch (e) {
      this.logger.info(`[ExportData] 导出 ${input.key} 失败，原因：${e}`);
    }
  }
}
