/**
 * @file mysql通用字段
 * <AUTHOR>
 */

import * as Sequelize from 'sequelize';
import superSequelize from '../../../typings/app/core/modelService';

const bizAttributes: superSequelize.DefineAttributes = {
  id: {
    type: Sequelize.INTEGER(11),
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键',
    allowUpdate: false,
  },
  updateTime: {
    type: Sequelize.DATE(3),
    allowNull: false,
    defaultValue: Sequelize.NOW,
    comment: '更新时间',
  },
  createTime: {
    type: Sequelize.DATE(3),
    allowNull: false,
    defaultValue: Sequelize.NOW,
    comment: '创建时间',
  },
  isDel: {
    type: Sequelize.BIGINT(16),
    defaultValue: 0,
    comment: '用于逻辑删除',
  },
};
export default bizAttributes;
