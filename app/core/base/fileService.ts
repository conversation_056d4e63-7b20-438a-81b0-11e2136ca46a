import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import * as sequelize from 'sequelize';
import superSequelize from '../../../typings/app/core/modelService';

export type IFileAttrs<TIdKey extends string, TId, TFileAttributes> = {
  key: keyof TFileAttributes;
  value: string;
} & { [key in TIdKey]: TId };

export const fileAttributes: superSequelize.DefineAttributes = {
  key: {
    type: sequelize.STRING(32),
    allowNull: false,
    comment: '属性名',
    allowUpdate: false,
  },
  value: {
    type: sequelize.TEXT('long'),
    allowNull: false,
    comment: '属性json值',
    allowUpdate: true,
  },
};

export default class FileService<IFileIdKey extends string,
  TFileId,
  TFileAttributes,
  I extends superSequelize.Instance<IFileAttrs<IFileIdKey, TFileId, TFileAttributes>>,
  A extends superSequelize.Attributes<IFileAttrs<IFileIdKey, TFileId, TFileAttributes>>>
  extends ModelService<I, A> {
  constructor(
    ctx: Context,
    model: sequelize.Model<I, A>,
    schema: superSequelize.DefineAttributes,
    public readonly defaultFiles: { [key in keyof TFileAttributes]?: string },
    public readonly idKey: IFileIdKey
  ) {
    super(ctx, model, schema);
  }

  public async getFiles(where: sequelize.WhereOptions<A>) {
    const files = await this.getAll({ where, attributes: ['key', 'value'] });
    const data: Partial<TFileAttributes> = {};
    files.forEach((item) => {
      data[item.key] = JSON.parse(item.value);
    });
    return data;
  }

  public async getFilesDict(where: sequelize.WhereOptions<A>) {
    const files = await this.getAll({ where, attributes: [this.idKey, 'key', 'value'] });
    const data: { [key: string]: Partial<TFileAttributes> } = {};
    files.forEach((item) => {
      const id = `${item[this.idKey]}`;
      data[id] = data[id] || {};
      data[id][item.key] = JSON.parse(item.value);
    });
    return data;
  }

  public setDefaultFiles(files: Partial<TFileAttributes>) {
    const result = {};
    const defaultFiles = this.defaultFiles;
    Object.keys(defaultFiles).forEach((key) => {
      result[key] = key in files ? files[key] : JSON.parse(defaultFiles[key]);
    });
    return result as TFileAttributes;
  }

  public async setFiles(
    ids: TFileId[],
    data: Partial<TFileAttributes>,
    options?: { transaction?: sequelize.Transaction }
  ) {
    const records = Object.keys(data).map((key) => {
      return { key, value: JSON.stringify(data[key]) };
    });
    const fileRecords: any[] = [];
    ids.forEach((id) => {
      records.forEach(({ key, value }) => {
        fileRecords.push({ [this.idKey]: id, key, value });
      });
    });
    await this.bulkCreate(fileRecords, { ...options, updateOnDuplicate: ['value'] });
  }

  public async deleteFiles(
    ids: TFileId[],
    keys: (keyof TFileAttributes)[],
    options?: { transaction?: sequelize.Transaction }
  ) {
    await this.delete({
      ...options,
      where: { [this.idKey]: ids, key: keys } as any,
    });
  }

}
