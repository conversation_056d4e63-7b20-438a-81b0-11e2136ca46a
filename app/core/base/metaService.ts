import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import * as sequelize from 'sequelize';
import superSequelize from '../../../typings/app/core/modelService';
import { IPdfInfo } from '../../model/taskMeta';

export type IMetaAttrs<TIdKey extends string, TId, TMetaAttributes> = {
  key: keyof TMetaAttributes;
  value: string;
} & { [key in TIdKey]: TId };

export const metaAttributes: superSequelize.DefineAttributes = {
  key: {
    type: sequelize.STRING(32),
    allowNull: false,
    comment: '属性名',
    allowUpdate: false,
  },
  value: {
    type: sequelize.STRING(512),
    allowNull: false,
    comment: '属性json值',
    allowUpdate: true,
  },
};

export default class MetaService<IMetaIdKey extends string,
  TMetaId,
  TMetaAttributes,
  I extends superSequelize.Instance<IMetaAttrs<IMetaIdKey, TMetaId, TMetaAttributes>>,
  A extends superSequelize.Attributes<IMetaAttrs<IMetaIdKey, TMetaId, TMetaAttributes>>>
  extends ModelService<I, A> {
  constructor(
    ctx: Context,
    model: sequelize.Model<I, A>,
    schema: superSequelize.DefineAttributes,
    public readonly defaultMetas: { [key in keyof TMetaAttributes]?: string },
    public readonly idKey: IMetaIdKey
  ) {
    super(ctx, model, schema);
  }

  public async getMetas(where: sequelize.WhereOptions<A>) {
    const metas = await this.getAll({ where, attributes: ['key', 'value'] });
    const data: Partial<TMetaAttributes> = {};
    metas.forEach((item) => {
      data[item.key] = JSON.parse(item.value);
    });
    return data;
  }

  public async getMetasDict(where: sequelize.WhereOptions<A>) {
    const metas = await this.getAll({ where, attributes: [this.idKey, 'key', 'value'] });
    const data: { [key: string]: Partial<TMetaAttributes> } = {};
    metas.forEach((item) => {
      const id = `${item[this.idKey]}`;
      data[id] = data[id] || {};
      data[id][item.key] = JSON.parse(item.value || '""');
    });
    return data;
  }

  public setDefaultMetas(metas: Partial<TMetaAttributes>) {
    const result = {};
    const defaultMetas = this.defaultMetas;
    Object.keys(defaultMetas).forEach((key) => {
      result[key] = key in metas ? metas[key] : JSON.parse(defaultMetas[key]);
    });
    return result as TMetaAttributes;
  }

  public async setMetas(
    ids: TMetaId[],
    data: Partial<TMetaAttributes>,
    options?: { transaction?: sequelize.Transaction }
  ) {
    const records = Object.keys(data).map((key) => {
      return { key, value: JSON.stringify(data[key]) };
    });
    const metaRecords: any[] = [];
    ids.forEach((id) => {
      records.forEach(({ key, value }) => {
        metaRecords.push({ [this.idKey]: id, key, value });
      });
    });
    await this.bulkCreate(metaRecords, { ...options, updateOnDuplicate: ['value'] });
  }

  public async deleteMetas(
    ids: TMetaId[],
    keys: (keyof TMetaAttributes)[],
    options?: { transaction?: sequelize.Transaction }
  ) {
    await this.delete({
      ...options,
      where: { [this.idKey]: ids, key: keys } as any,
    });
  }

  public calcPdfCount(pdfInfo: IPdfInfo | undefined) {
    if (!pdfInfo) return 0;
    const { bodyPageStart, bodyPageEnd } = pdfInfo;
    let bodyPdfCount = 0;
    // let answerPdfCount = 0;
    if (Number(bodyPageStart) && Number(bodyPageEnd) && Number(bodyPageStart) <= Number(bodyPageEnd)) {
      bodyPdfCount = Number(bodyPageEnd) - Number(bodyPageStart) + 1;
    }
    /*
     * if (+answerPageStart && +answerPageEnd) {
     *   answerPdfCount = +answerPageEnd - +answerPageStart + 1;
     * }
     */
    return bodyPdfCount;
  }

  public calcPdfAnsCount(pdfInfo: IPdfInfo | undefined) {
    if (!pdfInfo) return 0;
    const { answerPageStart, answerPageEnd } = pdfInfo;
    let answerPdfCount = 0;
    if (Number(answerPageStart) && Number(answerPageEnd)) {
      answerPdfCount = Number(answerPageEnd) - Number(answerPageStart) + 1;
    }
    return answerPdfCount;
  }
}
