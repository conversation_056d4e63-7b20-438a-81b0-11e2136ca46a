/**
 * @file 基础异常类型
 * <AUTHOR>
 */

export default {
  dataAlreadyExistError(msg?: string) {
    return {
      status: 1001,
      statusInfo: msg || '数据已经存在',
    };
  },
  paramsError(msg?: string) {
    return {
      status: 1002,
      statusInfo: msg || '参数错误',
    };
  },
  dataNotExistError(msg?: string) {
    return {
      status: 1004,
      statusInfo: msg || '数据不存在',
    };
  },
  serverError(msg?: string) {
    return {
      status: 1005,
      statusInfo: msg || '服务器错误',
    };
  },
  loginError(msg?: string) {
    return {
      status: 1006,
      statusInfo: msg || '登录凭证错误',
    };
  },
  permissionError(msg?: string) {
    return {
      status: 1008,
      statusInfo: msg || '权限错误',
    };
  },
  logicError(msg?: string) {
    return {
      status: 1009,
      statusInfo: msg || '逻辑错误',
    };
  },
};
