/**
 * @file 计时器扩展类
 * <AUTHOR>
 */
'use strict';

import { Context, Subscription } from 'egg';

export default abstract class BaseSubscription extends Subscription {
  private readonly lockType: string;
  private readonly lockKey: string;
  private readonly lockNumber: number;
  private isLock = false;

  constructor(
    ctx: Context,
    lockType: 'global' | 'process' | 'pool',
    lockKey?: string,
    lockNumber?: number
  ) {
    super(ctx);
    this.lockType = lockType;
    if (this.lockType === 'global') {
      this.lockKey = lockKey!;
    } else if (this.lockType === 'process') {
      this.isLock = false;
    } else if (this.lockType === 'pool') {
      this.lockKey = lockKey!;
      this.lockNumber = lockNumber!;
    }
  }

  public async start() {
    // 单进程异步函数
  }

  public async subscribe() {
    // 真正执行的定时函数
    const { redis, logger } = this.app;
    // logger.info('[subscribe] lockKey is', this.lockKey);
    switch (this.lockType) {
      case 'process':
      // 锁类型为进程锁时,保证worker内串行,不保证多节点下串行
        if (this.isLock) {
          return;
        }
        this.isLock = true;
        try {
          await this.start();
        } catch (e) {
          logger.error(e);
        } finally {
          this.isLock = false;
        }
        break;
      case 'global':
      // 锁类型为内存锁时,保证多节点下所有worker串行,相当于egg内置的type选项失效
        if (await redis.isGlobalLock(this.lockKey)) {
          return;
        }
        try {
          await this.start();
        } catch (e) {
          logger.error(e);
        } finally {
          await redis.set(this.lockKey, 0);
        }
        break;
      case 'pool':
      // 锁类型为进程池类型，保证一定时间内只有指定worker数目能同时执行和global锁类似
        const hasLock = await redis.hasPoolLock(this.lockKey, this.lockNumber);
        if (!hasLock) {
          return;
        }
        try {
          await this.start();
        } catch (e) {
          logger.error(e);
        } finally {
          await redis.incrby(this.lockKey, 1);
        }
        break;
      default:
      // 锁类型为空时,没有进程锁和内存,不保证任何串行
        try {
          await this.start();
        } catch (e) {
          logger.error(e);
        }
        break;
    }
  }
}
