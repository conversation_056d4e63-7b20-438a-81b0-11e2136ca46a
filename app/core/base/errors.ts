import baseError from './baseError';

export class BaseError extends Error {
  status = 200;

  public asBaseError() {
    return baseError.serverError(this.message);
  }
}

export class LogicError extends BaseError {
  public asBaseError() {
    return baseError.logicError(this.message);
  }
}

export class ParamsError extends BaseError {
  public asBaseError() {
    return baseError.paramsError(this.message);
  }
}
