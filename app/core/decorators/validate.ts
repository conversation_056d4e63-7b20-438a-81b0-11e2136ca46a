/**
 * @file 对controller的参数进行验证
 * <AUTHOR>
 */

import * as Parameter from 'parameter';
import { Context } from 'egg';
import baseError from '../base/baseError';

const parameter = new Parameter();

export default function validate(rule: object) {
  return (...args) => {
    const descriptor = args[2];
    const oldFunc = descriptor.value as any;
    descriptor.value = async function(this: any, ...args) {
      const ctx: Context = this.ctx;
      const errors = parameter.validate(rule, ctx.input);
      if (errors) {
        const [{ field, message }] = errors;
        return ctx.body = baseError.paramsError(`${field}参数错误：${message}`);
      }
      return await oldFunc.apply(this, args);
    };
  };
}
