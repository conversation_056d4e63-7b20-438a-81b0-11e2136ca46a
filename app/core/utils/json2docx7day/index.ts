// 七天定制docx
import * as _ from 'lodash';
import * as J<PERSON><PERSON><PERSON> from 'jszip';

import { IQuestionChoice, IQuestionNode, TJsonNode } from '../htmlToJsonV4';
import { findAllNodes, findNodesIterate, iterateNode } from '../treeHelper';
import {
  addTextForNode,
  escape,
  getHtml,
  getNodeSize,
  getText,
  IElementNode,
  parseHtml,
  THtmlNode
} from '../htmlHelper';
import { html2docx } from '../htmlToDocx/html2docx';
import { convertLatexToPlainText, formatLatexHtml, isHtmlStr, slice } from '../helper';
import getDefaultStyles from '../htmlToDocx/docxStyles';

export async function json2docx7day(json: TJsonNode[], mode: 'flow' | 'flow_body_answer') {
  const questions = getAllQuestions(json);

  let html;
  if (mode === 'flow') {
    html = questions.map((question) => {
      return [getBodyHtml(question), getAnswerHtml(question), getAnalysisHtml(question)].join('');
    }).join('');
  } else if (mode === 'flow_body_answer') {
    html = [
      questions.map((question) => getBodyHtml(question)).join(''),
      questions.map((question) => getAnswerHtml(question) + getAnalysisHtml(question)).join('')
    ].join('');
  }

  const { buffer } = await html2docx({
    html,
    styles: getDefaultStyles(),
  });
  return buffer;
}

export async function json2docxZip7day(json: TJsonNode[]) {
  const questions = getAllQuestions(json);
  const zip = new JSZip();
  await Promise.all(questions.map(async(question, index) => {
    const html = [getBodyHtml(question), getAnswerHtml(question), getAnalysisHtml(question)].join('');
    const { buffer } = await html2docx({
      html,
      styles: getDefaultStyles(),
    });
    zip.file(`${index + 1}.docx`, buffer);
  }));
  const buffer = await zip.generateAsync({ type: 'nodebuffer' });
  return buffer;
}

function getAllQuestions(json: TJsonNode[]) {
  // 读取所有大题试题节点。
  const questions = findAllNodes(json, ({ node }) => node.node_type === 'question') as IQuestionNode[];
  // 重新编号大题题号
  questions.forEach((question, index) => {
    question.content.serial_number = getSerialNumber(index + 1, question.content.level);
  });
  // 对所有小题，重新编号题号。（需要排除段落节点）
  for (const { node } of iterateNode(questions)) {
    const subQuestions = node.children.filter((node) => node.node_type === 'question') as IQuestionNode[];
    subQuestions.forEach((question, index) => {
      question.content.serial_number = getSerialNumber(index + 1, question.content.level);
    });
  }
  return questions;
}

function getSerialNumber(order: number, level: number) {
  if (level === 0 || level === 1) { // 大题、材料题 1 2 3
    return `${order}`;
  }
  if (level === 2) { // 小题 (1) (2) (3)
    return `(${order})`;
  }
  // 二级小题 ① ② ③
  return '①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯'[order - 1];
}

function getBodyHtml(question: IQuestionNode) {
  // 拼接试题题干html
  const htmls: string[] = [];
  // 最大填空横线长度，需要>=6
  const blankWidth = Math.max(Math.ceil(getBlankAnswerMaxWidth(question) * 1.2), 6);
  for (const { node } of iterateNode([question])) { // 遍历本题下所有题目
    const bodyNodes = parseHtml(node.content.body);
    if (node === question) {
      // 第一个试题前添加【题干序号】
      htmls.push(`<p>【题干序号】${node.content.serial_number}</p>`);
      if (node.question_type === 'material' && !node.content.body) {
        // 材料题没有题干时，不需要处理
        continue;
      }
    } else if (node.node_type === 'question') {
      // 小题题干前追加题号。如果 node.content.level === 1，说明为材料题下的大题。题号需要添加点`.`
      const serialNumber = node.content.serial_number;
      const serialNumberText = node.content.level === 1 ? `${serialNumber}.&nbsp;` : `${serialNumber}&nbsp;`;
      addTextForNode(bodyNodes, serialNumberText, 'prepend');
    }
    if (node.question_type === 'true_or_false' || node.question_type === 'choice') {
      handleBodyNodesBracket(bodyNodes);
    } else if (node.question_type === 'blank') {
      handleBodyNodesBlank(bodyNodes, blankWidth);
    }
    // 材料题题干，或者段落节点，其内容都为材料，使用楷体字体。其他情况使用默认字体
    const font = node.node_type !== 'question' || node.question_type === 'material' ? 'KaiTi' : undefined;
    htmls.push(getBlockHtml(bodyNodes, font));
    if (node.content.choices) {
      htmls.push(getChoicesHtml(node.content.choices));
    }
  }
  return htmls.join('');
}

function getAnswerHtml(question: IQuestionNode) {
  // 拼接试题答案html
  const htmls: string[] = [];
  htmls.push(`<p>【答案序号】${question.content.serial_number}</p>`);
  let serialNumber = '';
  const answersNodes: THtmlNode[][] = [];
  for (const { node } of iterateNode([question])) {
    if (node.node_type !== 'question') {
      continue;
    }
    if (node !== question) {
      serialNumber += node.content.level === 1
        ? `${node.content.serial_number}.&nbsp;`
        : `${node.content.serial_number}&nbsp;`;
    }
    if (node.children.length) {
      continue;
    }
    let questionAnswerNodes: THtmlNode[] = [];
    node.content.answer.forEach((answer, index) => {
      let nodes: THtmlNode[];
      if (isHtmlStr(answer)) {
        nodes = parseHtml(answer);
      } else {
        nodes = [{ type: 'text', content: escape(answer) }];
      }
      if (node.content.answer.length > 1 && index < node.content.answer.length - 1) {
        // 多个答案用分号分隔
        addTextForNode(nodes, '；', 'append');
      }
      questionAnswerNodes = questionAnswerNodes.concat(nodes);
    });
    if (serialNumber) {
      addTextForNode(questionAnswerNodes, serialNumber, 'prepend');
      serialNumber = '';
    }
    answersNodes.push(questionAnswerNodes);
  }
  if (answersNodes.every((nodes) => !nodes.some(isBlockNode))) {
    // 如果答案都是inline，多个答案之间用4个空格分隔
    answersNodes.forEach((nodes, index) => {
      if (index !== answersNodes.length - 1) {
        addTextForNode(nodes, '&nbsp;&nbsp;&nbsp;&nbsp;', 'append');
      }
    });
    const answerNodes = answersNodes.reduce((a, b) => a.concat(b), []);
    addTextForNode(answerNodes, '【答案】', 'prepend');
    htmls.push(getBlockHtml(answerNodes));
  } else {
    // 如果含 block 答案，所有答案用 block
    answersNodes.forEach((nodes, index) => {
      if (index === 0) {
        addTextForNode(nodes, '【答案】', 'prepend');
      }
      htmls.push(getBlockHtml(nodes));
    });
  }
  return htmls.join('');
}

function getAnalysisHtml(question: IQuestionNode) {
  // 拼接试题解析html
  const htmls: string[] = [];
  let serialNumber = '';
  let analysisNodes: THtmlNode[] = [];
  let materialAnalysisNodes: THtmlNode[] = [];
  let hasAnalysis = false;
  let hasMaterialAnalysis = false;
  if (question.content.level === 0) {
    hasMaterialAnalysis = Boolean(question.content.analysis);
    if (hasMaterialAnalysis) {
      materialAnalysisNodes = parseHtml(question.content.analysis);
    }
  }
  for (const { node } of iterateNode([question])) {
    if (node.node_type !== 'question') {
      continue;
    }
    if (node !== question) {
      serialNumber += node.content.level === 1
        ? `${node.content.serial_number}.&nbsp;`
        : `${node.content.serial_number}&nbsp;`;
    }
    if (node.children.length) {
      continue;
    }
    hasAnalysis = hasAnalysis || Boolean(node.content.analysis);
    const questionAnalysisNodes = parseHtml(node.content.analysis || '略');
    if (serialNumber) {
      addTextForNode(questionAnalysisNodes, serialNumber, 'prepend');
      serialNumber = '';
    }
    analysisNodes = analysisNodes.concat(questionAnalysisNodes);
  }
  let nodes = materialAnalysisNodes;
  if (hasAnalysis) {
    nodes = nodes.concat(analysisNodes);
  }
  if (hasAnalysis || hasMaterialAnalysis) {
    addTextForNode(nodes, '【解析】', 'prepend');
    htmls.push(getBlockHtml(nodes));
  } else {
    htmls.push('<p>【解析】略</p>');
  }
  return htmls.join('');
}

function handleBodyNodesBracket(bodyNodes: THtmlNode[]) {
  // 判断题、选择题。题干要有括号，括号里要有四个空格
  const bracketHtml = '（&nbsp;&nbsp;&nbsp;&nbsp;）';
  const bracketNodes = findAllNodes(bodyNodes, ({ node }) => { // 获取所有的bracket节点
    return node.type === 'element' && node.dataset.label === 'bracket';
  });
  if (bracketNodes.length) {
    // 所有bracket替换为括号文本
    bracketNodes.forEach((bracketNode) => {
      Object.assign(bracketNode, { type: 'text', content: bracketHtml });
    });
  } else {
    // 如果没有bracket，添加括号文本
    addTextForNode(bodyNodes, bracketHtml, 'append');
  }
}

function handleBodyNodesBlank(bodyNodes: THtmlNode[], blankWidth: number) {
  // 填空题，空要下划线替换，下划线个数取决于答案字符串长度
  const blankNodes = findAllNodes(bodyNodes, ({ node }) => { // 获取所有 bracket 和 blank 节点
    return node.type === 'element' && (node.dataset.label === 'bracket' || node.dataset.label === 'blank');
  }) as IElementNode[];
  blankNodes.forEach((node) => { // 替换成文本 `___`
    Object.assign(node, { type: 'text', content: '_'.repeat(blankWidth) });
  });
}

function getBlankAnswerMaxWidth(question: IQuestionNode) {
  // 获得一个题目中所有空的最大长度
  let maxWidth = 0;
  for (const { node } of iterateNode([question])) {
    if (node.question_type !== 'blank') {
      continue;
    }
    for (const answer of node.content.answer) {
      let width: number;
      if (isHtmlStr(answer) || /\$\$/.test(answer)) {
        // 答案为html，或者包含公式。则使用html的方式计算长度
        const nodes = parseHtml(formatLatexHtml(answer));
        convertLatexToPlainText(nodes);
        width = getNodesWidth(nodes);
      } else {
        // 答案为纯文本，计算纯文本长度。
        width = getTextWidth(answer);
      }
      if (width > maxWidth) {
        maxWidth = width;
      }
    }
  }
  return maxWidth;
}

function getChoicesHtml(choices: IQuestionChoice[]) {
  // 布局选择题选项
  const colCount = getColCount(choices); // 表格列数
  const rowCount = Math.ceil(choices.length / colCount); // 表格行数
  const cellWidth = 100 / colCount; // 列平均宽度，确定每单元格的宽度百分比
  const trs = _.range(rowCount).map((__, rowIndex) => {
    const tds = _.range(colCount).map((__, colIndex) => {
      const index = rowIndex * colCount + colIndex;
      const choice = choices[index];
      if (!choice) return;
      const { option, letter } = choice;
      const nodes = parseHtml(option);
      addTextForNode(nodes, `${letter}.&nbsp;`, 'prepend'); // 前面添加选项号
      const html = getBlockHtml(nodes);
      return `<td width="${cellWidth}%">${html}</td>`;
    }).filter((s) => s);
    return `<tr>${tds.join('')}</tr>`;
  });
  return `<table data-label="layout" data-col="${colCount}" border="0" width="100%"><tbody>${trs.join('')}</tbody></table>`;
}

function getColCount(choices: IQuestionChoice[]) {
  // 根据最长的选项，以及选项数量，确定需要将选项布局为多少列
  let maxWidth = 0;
  for (const { option } of choices) {
    const nodes = parseHtml(formatLatexHtml(option));
    const pNodes = [...slice(
      findNodesIterate(nodes, ({ node }) => node.type === 'element' && node.tagName === 'p'),
      { stop: 2 })
    ];
    if (pNodes.length > 1) {
      // 节点中有多余一个段落，直接按每行1列使用
      return 1;
    }
    convertLatexToPlainText(nodes);
    const width = getNodesWidth(nodes);
    if (width > maxWidth) {
      maxWidth = width;
    }
  }
  const threshold = 7.5;
  if (choices.length >= 4) {
    if (maxWidth <= threshold / 2) {
      return Math.min(8, choices.length);
    }
    if (maxWidth <= threshold * 2 / 3) {
      return Math.min(6, choices.length);
    }
    if (maxWidth <= threshold) {
      return 4;
    }
    if (maxWidth <= 2 * threshold) {
      return 2;
    }
    return 1;
  }
  if (choices.length === 3) {
    if (maxWidth <= threshold * 4 / 3) {
      return 3;
    }
    return 1;
  }
  if (choices.length === 2) {
    if (maxWidth <= threshold * 2) {
      return 2;
    }
    return 1;
  }
  return 1;
}

function getNodesWidth(nodes: THtmlNode[]) {
  // 计算html宽度（与多少个中午字符等宽）
  const text = getText(nodes);
  const textWidth = getTextWidth(text);
  const imgNodes = findAllNodes(nodes, ({ node }) => {
    return node.type === 'element' && node.tagName === 'img';
  }) as IElementNode[];
  /*
   * 图片没有width属性时，按12个中文字符宽度处理，不去读取其图片的真实宽度。
   * 并假设一个中文字符为16.25px宽
   */
  const imgWidth = _.sumBy(imgNodes, (img) => {
    const width = getNodeSize(img, 'width');
    return width && width.type === 'px' ? width.size / 16.25 : 12;
  });
  return textWidth + imgWidth;
}

function getTextWidth(text: string) {
  // 获取文本的宽度（约与多少中午字符等宽）
  let width = 0;
  for (let i = 0; i < text.length; i += 1) {
    const charCode = text.charCodeAt(i);
    if (charCode >= 65 && charCode <= 90) {
      width += 0.6;
    } else if (charCode >= 0 && charCode <= 255) {
      width += 0.45;
    } else if (charCode < 19968) {
      width += 1.2;
    } else {
      width += 1;
    }
  }
  return width;
}

function getBlockHtml(nodes: THtmlNode[], font?: string) {
  // 获取节点的html字符串。并且保证其为block
  if (nodes.some(isBlockNode)) {
    // 如果部分节点为block，需要将散的inline用p包裹，保证结果为多个block
    const blockNodes: IElementNode[] = [];
    let p: IElementNode | undefined;
    nodes.forEach((node) => {
      if (isBlockNode(node)) {
        // block节点
        blockNodes.push(node);
        p = undefined;
      } else {
        if (!p) {
          p = { type: 'element', tagName: 'p', children: [], dataset: {}, attrs: {}, cls: {} };
          blockNodes.push(p);
        }
        p.children.push(node);
      }
    });
    if (font) {
      // 设置每个block的字体
      blockNodes.forEach((node) => {
        node.attrs['font-east-asia'] = font;
      });
    }
    const html = getHtml(blockNodes);
    return html;
  }
  // 如果节点不包含任何block，直接使用p包裹
  const html = getHtml(nodes);
  if (font) {
    return `<p style="font-family: ${escape(font)}">${html}</p>`;
  }
  return `<p>${html}</p>`;
}

function isBlockNode(node: THtmlNode): node is IElementNode {
  // 是否为block节点
  return node.type === 'element' && (node.tagName === 'p' || node.tagName === 'table' || node.tagName === 'hr');
}
