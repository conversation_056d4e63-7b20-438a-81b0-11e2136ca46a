import * as katex from 'katex';

import { isHtmlStr } from './index';

import { iterateNode } from './treeHelper';
import { getText, parseHtml, THtmlNode } from './htmlHelper';

export function formatLatexHtml(html: string) {
  // 将html中 $$frac{1}{2}$$ 替换为 <span data-label="latex" data-value="frac{1}{2}"></span>
  const htmlStr = !isHtmlStr(html) && /\$\$/.test(html) ? escape(html) : html;
  return htmlStr.replace(/\$\$([\s\S]*?)\$\$/g, (_match, latex: string) => {
    return `<span data-label="latex" data-value="${latex.replace(/"/g, '&quot;')}"></span>`;
  });
}

export function convertLatexToPlainText(nodes: THtmlNode[]) {
  /*
   * 将html中的公式替换成纯文本。清除公式
   * 如 $$frac{1}{2}$$ 或 <span data-label="latex" data-value="frac{1}{2}"></span>，都将被替换为 1/2
   */
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type !== 'element') {
      continue;
    }
    if (node.dataset.label === 'latex') {
      const html = katex.renderToString(node.dataset.value, { output: 'html', strict: false, throwOnError: false });
      const content = getText(parseHtml(html));
      Object.assign(node, { type: 'text', content });
      stopIterateChildren!();
    }
  }
}
