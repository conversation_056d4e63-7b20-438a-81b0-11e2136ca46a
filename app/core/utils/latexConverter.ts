import * as J<PERSON><PERSON><PERSON> from 'jszip';
import * as katex from 'katex';
import { pandocAsync } from './index';
import { getHtml, parseHtml } from './htmlHelper';
import { iterateNode } from './treeHelper';
import * as matchAll from 'string.prototype.matchall';

/**
 * @todo：image2doc-processor-server 和 content-server-node 都有处理 latex 公式的代码
 * 方案1：content-server-node 提供 Http API 调用
 * 方案2：代码封装到工具库，供2个项目使用
 */

function cleanMathmlSpace(mathml: string) {
  // https://www.compart.com/en/unicode/category/Zs
  mathml = mathml.replace(/[\u00A0\u2000-\u200A\u202F\u205F\u3000]/g, ' ');
  // https://www.compart.com/en/unicode/block/U+2000
  mathml = mathml.replace(/[\u200B-\u200D]/g, ' ');
  return mathml;
}

/*
 * https://www.compart.com/en/unicode/U+0338
 * e.g. ['\u2288', '\u2286']
 * `\u0338= ̸` `\u2286=⊆` `\u2288=⊈`
 * combine ⊆ with  ̸
 * then `\u2286\u0338=⊈`
 * and ⊈ !== ⊈
 */
const NOT_OVERLAY_MAP = [
  ['\u219A', '\u2190'],
  ['\u219B', '\u2192'],
  ['\u21AE', '\u2194'],
  ['\u21CD', '\u21D0'],
  ['\u21CE', '\u21D4'],
  ['\u21CF', '\u21D2'],
  ['\u2204', '\u2203'],
  ['\u2209', '\u2208'],
  ['\u220C', '\u220B'],
  ['\u2224', '\u2223'],
  ['\u2226', '\u2225'],
  ['\u2241', '\u223C'],
  ['\u2244', '\u2243'],
  ['\u2247', '\u2245'],
  ['\u2249', '\u2248'],
  ['\u2260', '\u003D'],
  ['\u2262', '\u2261'],
  ['\u226D', '\u224D'],
  ['\u226E', '\u003C'],
  ['\u226F', '\u003E'],
  ['\u2270', '\u2264'],
  ['\u2271', '\u2265'],
  ['\u2274', '\u2272'],
  ['\u2275', '\u2273'],
  ['\u2278', '\u2276'],
  ['\u2279', '\u2277'],
  ['\u2280', '\u227A'],
  ['\u2281', '\u227B'],
  ['\u2284', '\u2282'],
  ['\u2285', '\u2283'],
  ['\u2288', '\u2286'],
  ['\u2289', '\u2287'],
  ['\u22AC', '\u22A2'],
  ['\u22AD', '\u22A8'],
  ['\u22AE', '\u22A9'],
  ['\u22AF', '\u22AB'],
  ['\u22E0', '\u227C'],
  ['\u22E1', '\u227D'],
  ['\u22E2', '\u2291'],
  ['\u22E3', '\u2292'],
  ['\u22EA', '\u22B2'],
  ['\u22EB', '\u22B3'],
  ['\u22EC', '\u22B4'],
  ['\u22ED', '\u22B5'],
  ['\u2ADC', '\u2ADD']
];

function replaceNotOverlay(mathml: string) {
  let res = mathml;
  NOT_OVERLAY_MAP.forEach(([target, source]) => {
    res = res.replace(new RegExp(`${source}\u0338`, 'g'), target);
  });
  return res;
}

function cleanMathmlTag(mathml: string) {
  const nodes = parseHtml(mathml);
  for (const item of iterateNode(nodes)) {
    if (item.node.type !== 'element') continue;
    if (['mi', 'mn', 'mo'].includes(item.node.tagName)) {
      if (!item.node.children.length || item.node.children.length > 1) {
        Object.assign(item.node, { type: 'fragment' });
      } else {
        const child = item.node.children[0];
        if (
          child.type === 'element' &&
          ['mi', 'mo', 'mover', 'munder'].includes(child.tagName)
        ) {
          Object.assign(item.node, { type: 'fragment' });
        }
      }
    }
  }
  const result = getHtml(nodes);
  return result;
}

function cleanMathml(mathml: string) {
  mathml = replaceNotOverlay(mathml);
  mathml = cleanMathmlSpace(mathml);
  mathml = cleanMathmlTag(mathml);
  return mathml;
}

/**
 * latex 转换成 mathml
 * 问题1：\mathrm 包裹的公式内容没有渲染成正体，由于版本问题 mathvariant 没有在 mi 标签上生效
 * 参考链接 https://developer.mozilla.org/en-US/docs/Web/MathML/Element/mstyle#gecko-specific_notes
 * @todo：可以考虑使用 MathJax 重构
 * https://www.mathjax.org/
 * @param latex
 */
export async function latex2mathml(latex: string[]) {
  return (await latex2mathmlPandoc(latex))
    .map((mathml, index) => {
      if (mathml) {
        return mathml;
      }
      const formatedLatex = latex[index].trim();
      if (!formatedLatex) {
        return '';
      }
      return latex2mathmlKatex([formatedLatex])[0];
    })
    .map((mathml) => {
      /**
       * 若 mathml 中有 \mathrm 标记，则该标记包裹的内容需要在 PPT 中渲染为正体
       * 为解决 mathml 的版本问题，处理含有 mathvariant="normal" 的 mstyle 标签中的 mi 标签样式
       * @todo：可以考虑封装处理 xml 的工具库
       * 参考链接：https://github.com/Leonidas-from-XIV/node-xml2js
       */
      if (/\\mathrm(\s*){(.*?)}/.test(mathml)) {
        return mathml.replace(
          /<mstyle(.*?)mathvariant="normal"(.*?)>(.*?)<\/mstyle>/g,
          (...args) => args[0].replace(/<mi>/g, '<mi mathvariant="normal">')
        );
      }
      return mathml;
    })
    .filter((mathml) => Boolean(mathml.trim()));
}

function escapeLatex(text: string) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '\\lt ')
    .replace(/>/g, '\\gt ');
}

export async function latex2mathmlPandoc(latex: string[]) {
  const latexs = latex.map((s) => s.replace(/\s+/g, ' ').trim());
  if (latex.every((l) => !l)) return latex.map(() => '');
  const latexStr = latexs
    .map((s) => `$$${escapeLatex(s)}$$`)
    .join('<p>[separator]</p>');
  const buffer = await pandocAsync(
    latexStr,
    '-f html+tex_math_dollars -t html --mathml'.split(' ')
  );
  const xml = buffer.toString('utf-8').replace(/ display="block"/g, '');
  const mathmls = xml.split('[separator]').map((xml) => {
    let mathml = xml.match(/<math[^>]*?>[\s\S]*?<\/math>/)?.[0] ?? '';
    if (mathml) {
      mathml = cleanMathml(mathml);
    }
    return mathml;
  });
  return mathmls;
}

export function latex2mathmlKatex(latex: string[]) {
  const mathmls = latex.map((latex) => {
    latex = latex.replace(/\s+/g, ' ').trim();
    if (!latex) return '';
    latex = latex.replace(/</g, '\\lt ').replace(/>/g, '\\gt ');
    let mathml =
      katex.renderToString(latex, {
        output: 'mathml',
        strict: false,
        throwOnError: false,
      }) || '';
    mathml = mathml
      .replace(/^<span class="katex">/, '')
      .replace(/<\/span>$/, '');
    mathml = cleanMathml(mathml);
    return mathml;
  });
  return mathmls;
}

function replaceOmmlOnOffVal(math: string) {
  math = math.replace(
    /<m:(degHide) m:val="([01])" *\/>/g,
    (_m: string, name: string, value: string) => {
      value = value === '1' ? 'on' : 'off';
      return `<m:${name} m:val="${value}"/>`;
    }
  );
  return math;
}

/**
 * mathml 渲染至 docx
 * @param mathml
 * @param param1
 */
export async function mathml2docx(
  mathml: string[],
  { separator = '\n', block = false } = {}
) {
  let mathmls = mathml.filter((s) => Boolean(s.trim()));
  if (!mathmls.length) {
    return;
  }
  if (block) {
    mathmls = mathmls.map((mathml) =>
      mathml.replace(/^<math/, '<math display="block"')
    );
  }
  const buffer = await pandocAsync(
    mathmls.join(separator),
    '-f html -t docx -o -'.split(' ')
  );
  return buffer;
}

/**
 * mathml 转换成 ooml，通过生成 docx 并解压的方式实现
 * 问题：正体问题，带有 mathvariant="normal" 的 mi 标签经过 pandoc 生成后样式未生效
 * 方案：单独切割出待正体文本，转换成 ooml 后添加正体样式，最后插回总 ooml 文档中
 * @param mathmls
 */
export async function mathml2omml(mathmls: string[]) {
  const buffer = await mathml2docx(mathmls, { separator: '<p>[separator]</p>' });
  if (!buffer) {
    return mathmls.map(() => '');
  }
  const zip = await JSZip.loadAsync(buffer);
  const xml = await zip.file('word/document.xml')!.async('text');
  const ooxmls = xml
    .split('[separator]')
    .map((xml) => xml.match(/<m:oMath[^>]*?>[\s\S]*?<\/m:oMath>/)?.[0] ?? '')
    .map(replaceOmmlOnOffVal);
  return ooxmls;
}

const mathml2OoxmlWithMathvariant = async(mathmls: string[]) => {
  // 抽取所有待处理的公式文本，使用`#${index}`代替
  const mathvariants = [] as string[];
  const transferedMathmls = mathmls.map((mathml) => {
    if (/\\mathrm(\s*){(.*?)}/.test(mathml)) {
      return mathml.replace(
        /<mstyle([^<>]*?)mathvariant="normal"([^<>]*?)>(.*?)<\/mstyle>/g,
        (...args) => {
          mathvariants.push(args[3]);
          return `<mstyle mathvariant="normal"><mo>#</mo><mn>${
            mathvariants.length - 1
          }</mn></mstyle>`;
        }
      );
    }
    return mathml;
  });
  const [ooxmls, trasferedOoxmls] = await Promise.all([
    // 将 mathml 其他部分转换成 ooml
    mathml2omml(transferedMathmls),
    // 将抽取部分转换成 ooml
    mathml2omml(
      mathvariants.map(
        (fragment) =>
          `<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow>${fragment}</mrow></math>`
      )
    )
  ]);
  // 待替换的位置，#1、#2...
  const RegReplacePositon =
    /<m:r>((?!<m:t>).)*<m:t>#<\/m:t><\/m:r><m:r><m:t>(\d+)?<\/m:t><\/m:r>/g;
  // <m:oMath> 标签中的内容
  const RegMathContent = /<m:oMath>(.*)<\/m:oMath>/;
  // 替换正体样式标签
  const RegStyleMr = /<m:r>/g;
  // 将后者替换回前者
  return ooxmls.map((ooxml) => {
    // logger(`[mathml2OoxmlWithMathvariant] ${ooxml}`);
    return ooxml.replace(RegReplacePositon, (...args) => {
      // # 中的数字
      const count = Number(args[2]);
      if (count < 0 || count >= trasferedOoxmls.length) {
        return args[0];
      }
      // 正体公式 <m:oMath> 标签中的内容
      const content = (trasferedOoxmls[count].match(RegMathContent) || [])[1];
      if (!content) {
        return args[0];
      }
      return content.replace(
        RegStyleMr,
        '<m:r><m:rPr><m:sty m:val="p" /></m:rPr>'
      );
    });
  });
};

/**
 * 公式处理为 ooml 替换至 PPT 中，设计思路：
 * 1、latex 转换为 mathml
 * 2、mathml 转换为 ooml
 * @param latex
 */
export async function latex2omml(latex: string[]) {
  const replacedLatex = latex.map(replaceLatex);
  const mathmls = await latex2mathml(replacedLatex);
  if (mathmls.length <= 0) {
    return [];
  }
  let ommls = await mathml2OoxmlWithMathvariant(mathmls);
  ommls = ommls.map(replaceOmml);
  return ommls;
}

const repls = [
  ['"', ' /quot/ '],
  ['&quot;', ' /quot/ '],
  ['“', ' /leftquot/ '],
  ['”', ' /rightquot/ ']
];

export function replaceLatex(latex: string) {
  let s = latex;
  repls.forEach(([symbol, rel]) => {
    s = s.replace(new RegExp(symbol, 'g'), rel);
  });
  s = s.replace(/(?<!\\)%/g, '\\%').replace(/^\^/, '{}^');
  return s;
}

export function replaceOmml(omml: string) {
  let s = omml;
  repls.forEach(([symbol, rel]) => {
    s = s.replace(new RegExp(rel.trim(), 'g'), symbol);
    s = s.replace(
      new RegExp(
        rel
          .trim()
          .split('')
          .map((s) => `<m:r><m:t>${s}</m:t></m:r>`)
          .join(''),
        'g'
      ),
      `<m:r><m:t>${symbol}</m:t></m:r>`
    );
  });
  return s;
}

export async function replaceDocxOmml(buffer: Buffer) {
  const zip = await JSZip.loadAsync(buffer);
  const docXml = await zip.file('word/document.xml')!.async('text');
  zip.file(
    'word/document.xml',
    docXml.replace(/<m:oMath>[\s\S]*?<\/m:oMath>/g, (omml) => replaceOmml(omml))
  );
  const result = await zip.generateAsync({
    type: 'nodebuffer',
    mimeType:
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    compression: 'DEFLATE',
  });
  return result;
}

export function addRunPropsForMathOmml(math: string, runProps?: string) {
  if (!runProps) return math;
  math = math
    .replace(
      /<\/m:((box)|(groupChar)|(f)|(nary)|(rad)|(d)|(sSub)|(acc)|(eqArr))Pr>/g,
      `<m:ctrlPr>${runProps}</m:ctrlPr>$&`
    )
    .replace(/<m:r>/g, `$&${runProps}`);
  return math;
}

// 校验 latex 正斜体
const LATEX_CHARACTER = [
  ['\\alpha', 'α'],
  ['\\beta', 'β'],
  ['\\gamma', 'γ'],
  ['\\delta', 'δ'],
  ['\\epsilon', 'ϵ'],
  ['\\zeta', 'ζ'],
  ['\\eta', 'η'],
  ['\\theta', 'θ'],
  ['\\iota', 'ι'],
  ['\\kappa', 'κ'],
  ['\\lambda', 'λ'],
  ['\\mu', 'μ'],
  ['\\nu', 'ν'],
  ['\\xi', 'ξ'],
  ['\\pi', 'π'],
  ['\\rho', 'ρ'],
  ['\\sigma', 'σ'],
  ['\\tau', 'τ'],
  ['\\upsilon', 'υ'],
  ['\\phi', 'φ'],
  ['\\varphi', 'φ'],
  ['\\chi', 'χ'],
  ['\\psi', 'ψ'],
  ['\\omega', 'ω'],
  ['\\Omega', 'Ω']
];

let errorMsg = '';
export function checkHtmlStraight(content) {
  const formulas = getLatex(content);
  errorMsg = '';
  if (!formulas.length) return [false, ''];
  content = formulas.join(',');
  return checkStraightContent(content);
}

export function checkLatexError(content: string) {
  const regex = /(\\mathrm{([^{}]*})[^{}]*)/g;
  let match;
  while ((match = regex.exec(content)) !== null) {
    if (match[1].includes('\\mathrm{')) {
      return true;
    }
    if (checkLatexError(match[1])) {
      return true;
    }
  }
  return false;
}

export function checkStraightContent(content: string) {
  // 正体
  if (getLatexCharacterList(['Ω']).some((i) => checkUnStraight(i, content))) {
    return [true, errorMsg];
  }
  if (
    getLatexCharacterList(['SAS', 'SSS', 'ASA', 'AAS', 'AAA', 'HL']).some((i) =>
      checkUnStraight(i, content, true)
    )
  ) {
    return [true, errorMsg];
  }
  if (getLatexCharacterList(['lim']).some((i) => checkUnStraight(i, content))) {
    return [true, errorMsg];
  }
  if (
    getLatexCharacterList(['cos', 'sin', 'tan', 'cot', 'sec', 'csc']).some(
      (i) => checkUnStraight(i, content, true)
    )
  ) {
    return [true, errorMsg];
  }
  if (getLatexCharacterList(['π']).some((i) => checkUnStraight(i, content))) {
    return [true, errorMsg];
  }
  // 斜体
  if (
    getLatexCharacterList([
      'α',
      'β',
      'γ',
      'δ',
      'ε',
      'ζ',
      'η',
      'θ',
      'ι',
      'κ',
      'λ',
      'ν',
      'ξ',
      'ο',
      'ρ',
      'ς',
      'σ',
      'τ',
      'υ',
      'φ',
      'χ',
      'ψ',
      'ω'
    ]).some((i) => checkItalic(i, content))
  ) {
    return [true, errorMsg];
  }
  return [false, ''];
}

function getLatexCharacterList(strs: string[]) {
  const res: string[] = [];
  strs.map((str) => {
    for (const list of LATEX_CHARACTER) {
      if (list.includes(str)) return res.push(...list);
    }
    res.push(str);
  });
  return res;
}

/*
 * 校验需要校验的字符key 在content 是不是被正体 \mathrm{} 包裹
 * false 时候跳过， true 时候卡住 （有该字符，且没有被正体包裹 并且不是以 \ 开头）
 */
function checkUnStraight(key: string, content: string, conpleteWord = false) {
  const container = '\\mathrm';

  // 不包含 key 直接跳过
  let regKey;
  if (key.startsWith('\\')) regKey = '\\' + key;
  else regKey = key;

  let re = new RegExp(`${regKey}`);
  if (!re.test(content)) {
    return false;
  }
  if (conpleteWord) {
    re = new RegExp(
      `(${container} ?{[^{}]*?${regKey}[^{}]*?}|\\\\${regKey}|[a-zA-Z]${regKey}|${regKey}[a-zA-Z])`,
      'g'
    );
  } else {
    re = new RegExp(
      `(${container} ?{[^{}]*?${regKey}[^{}]*?}|\\\\${regKey})`,
      'g'
    );
  }
  // 所有 key 都必须 \mathrm{ any key any } 或者 \key
  if (re.test(content)) {
    return checkUnStraight(key, content.replace(re, ''), conpleteWord);
  }
  errorMsg = `${key} 需要为正体！`;
  return true;
}

/*
 * 校验需要校验的字符key 在 content 是不是斜体
 * false 时候跳过， true 时候卡住 （有该字符，且被正体包裹）
 */
function checkItalic(key: string, content: string) {
  const container = '\\mathrm';

  // 不包含 key 直接跳过
  let regKey;
  if (key.startsWith('\\')) regKey = '\\' + key;
  else regKey = key;
  if (!new RegExp(`${regKey}`).test(content)) return false;
  // 所有 key 都必须 \mathrm{ any key any }
  const re = new RegExp(`${container} ?({[^{}]*?})`, 'g');
  const matchs = matchAll(content, re);
  for (const match of matchs) {
    if (
      match &&
      match[1].includes(key) &&
      match[1].indexOf(key) &&
      match[1][match[1].indexOf(key) - 1] !== '\\'
    ) {
      errorMsg = `${key} 需要为斜体！`;
      return true;
    }
  }
  return false;
}

function getLatex(str: string) {
  const res: string[] = [];
  const matchs = matchAll(str, /\$\$(.*?)\$\$/g);
  for (const formula of matchs) {
    if (formula.length && formula[1]) {
      res.push(formula[1]);
    }
  }
  return res;
}
