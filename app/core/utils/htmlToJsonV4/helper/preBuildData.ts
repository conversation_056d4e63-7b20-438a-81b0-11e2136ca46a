// 第二步：组装各元素必备信息
import { IHtmlNodeGroup } from './groupHtmlNode';
import {getText, THtmlNode} from '../../htmlHelper';
import { backIterate } from '../../helper';
import { EJsonErrorRule, IJsonErrorInfo } from '../index';

export type TSeparator = 'material_start_separator' | 'material_end_separator';

export interface INodeInfo {
  node_type: 'chapter' | 'none' | 'question' | 'text' | TSeparator;
  imgId?: string;
}

export interface IDataInfo {
  level?: number;
  nodes: THtmlNode[];
  errorInfo?: IJsonErrorInfo[];
}

export interface IQuestionDataInfo extends IDataInfo {
  _serialNumber: THtmlNode;
  serialNumber: string;
  answerNodes: THtmlNode[];
  explanationNodes: THtmlNode[];
  p?: string;
  _sequence: string[];
  extraNodes: THtmlNode[][];
  [propName: string]: any;
}
export interface IMaterialQuestionDataInfo extends IDataInfo {
  extraNodes: THtmlNode[][];
  _sequence: string[];
}

export function preBuildElementsData(groups: IHtmlNodeGroup[], source: 'question' | 'answer') {
  const elementsData: [INodeInfo, IDataInfo][] = [];
  let currentQuestionData: IQuestionDataInfo | undefined;
  let currentMaterialData: IMaterialQuestionDataInfo | undefined;
  groups.forEach((group, index) => {
    const preGroup = groups[index - 1];
    const nextGroup = groups[index + 1];
    const { label, _serialNumber, serialNumber, mark, imgId, nodes, p } = group;
    const level = Number(group.level!);
    if (label === 'header') {
      // 目录
      elementsData.push([{ node_type: 'chapter', imgId }, { level, nodes }]);
      currentQuestionData = undefined;
    } else if (label === 'text' || label === 'material_start_separator' || label === 'material_end_separator') {
      // 材料分割线、材料文本、二级文本
      if (label === 'material_start_separator') {
        currentMaterialData = { nodes, extraNodes: [], _sequence: [] };
        elementsData.push([{ node_type: label, imgId }, currentMaterialData]);
      } else {
        elementsData.push([{ node_type: label, imgId }, { nodes }]);
      }
      if (label === 'material_start_separator' || label === 'material_end_separator') {
        currentQuestionData = undefined;
        if (label === 'material_end_separator') {
          currentMaterialData = undefined;
        }
      }
    } else if (!label) {
      if (preGroup && preGroup.label !== 'answer' && preGroup.label !== 'explanation' && currentQuestionData) {
        // 如果前一个分组不是答案解析，则该无标记段落属于当前题目的题干
        currentQuestionData.nodes = currentQuestionData.nodes.concat(nodes);
      } else {
        // 否则是一个没有任何标记的段落
        let errorInfo: IJsonErrorInfo[] | undefined;
        // 如果上一个和下一个都是 answer，报错：可能是漏标的段落
        if (preGroup && preGroup.label === 'answer' && nextGroup && nextGroup.label === 'answer') {
          errorInfo = [{
            rule: EJsonErrorRule.answer_mismatch,
            message: '可能是漏标的答案',
          }];
        }
        elementsData.push([{ node_type: 'none', imgId }, { nodes, errorInfo }]);
      }
    } else if (label === 'extra') {
      // 附加内容
      if (currentMaterialData) {
        currentMaterialData._sequence.push(`extra${currentMaterialData.extraNodes.push(mark ? nodes.slice(1) : nodes)}`);
      } else {
        if (currentQuestionData) {
          if (mark && Number(mark.level!) === Number(currentQuestionData.level!) && mark.serialNumber === currentQuestionData.serialNumber) {
            currentQuestionData._sequence.push(`extra${currentQuestionData.extraNodes.push(nodes.slice(1))}`);
          } else if (Number(currentQuestionData.level!) === 1) {
            currentQuestionData._sequence.push(`extra${currentQuestionData.extraNodes.push(mark ? nodes.slice(1) : nodes)}`);
          } else {
            for (const [element, data ] of backIterate(elementsData)) {
              // 如果是无标记段落，继续往前查找
              if (element.node_type === 'none' || element.node_type === 'text') continue;
              // 不是试题则结束查找
              if (element.node_type !== 'question') break;
              const _level = Number(data.level!);
              // 找到所有 data-level 小于 _level 的节点
              const children = elementsData.filter((_element) => _element[0].node_type === 'question' && Number(_element[1].level!) > _level);
              const [,lastChild] = children.length ? children[children.length - 1] : [undefined, undefined];
              const lastChildrenIndex = lastChild ? groups.findIndex((_group) => {
                const groupLevel = Number(_group.mark?.level || _group.level);
                const _serial = _group.mark?.serialNumber || _group.serialNumber;
                return groupLevel === lastChild.level && _serial === (lastChild as any)?.serialNumber;
              }) : -1;
              // index 是否在最后一个 children 之后
              const reverseIndex = groups.findIndex((_group) => _group === groups[index]);
              // 找到同级试题
              if (
                mark &&
                Number(data.level!) === Number(mark.level!) &&
                mark.serialNumber === (data as IQuestionDataInfo).serialNumber
              ) {
                if (reverseIndex > lastChildrenIndex && lastChildrenIndex !== -1) {
                  (data as IQuestionDataInfo)._sequence.push('children');
                }
                (data as IQuestionDataInfo)._sequence.push(`extra${(data as IQuestionDataInfo).extraNodes.push(nodes.slice(1))}`);
                break;
              }
              // 找到一级节点还没有找到，如果是作答线之前则赋给一级根节点，如果是作答线之后，则新建这个节点。
              if (Number(data.level!) === 1 || Number(data.level!) === -1) {
                if (source === 'question') {
                  if (reverseIndex > lastChildrenIndex && lastChildrenIndex !== -1) {
                    (data as IQuestionDataInfo)._sequence.push('children');
                  }
                  (data as IQuestionDataInfo)._sequence.push(`extra${(data as IQuestionDataInfo).extraNodes.push(mark ? nodes.slice(1) : nodes)}`);
                  break;
                } else {
                  if (mark?.level) {
                    const level = Number(mark!.level!);
                    currentQuestionData = createQuestionData({ _serialNumber: mark!._serialNumber, serialNumber: mark!.serialNumber, level });
                    elementsData.push([{ node_type: 'question', imgId }, currentQuestionData]);
                    currentQuestionData['extraNodes'].push(mark ? nodes.slice(1) : nodes);
                    currentQuestionData._sequence.push(`extra${currentQuestionData.extraNodes.length}`);
                  } else {
                    // 如果 extraNode 在所有小题的最后，则需要在 _sequence 的 extra 前插入一个 'children'
                    // @struct:
                    //  - groups: 保存的是dom里的顺序
                    //  - elementsData: 解析 groups 获取到的 node 节点信息
                    const _level = Number(data.level!);
                    // 找到所有 data-level 小于 _level 的节点
                    const children = elementsData.filter((element) => element[0].node_type === 'question' && Number(element[1].level!) > _level);
                    const [,lastChild] = children.length ? children[children.length - 1] : [undefined, undefined];
                    const lastChildrenIndex = lastChild ? groups.findIndex((group) => {
                      const groupLevel = Number(group.mark?.level);
                      const _serial = group.mark?.serialNumber;
                      return groupLevel === lastChild.level && _serial === (lastChild as any)?.serialNumber;
                    }) : -1;
                    // index 是否在最后一个 children 之后
                    const reverseIndex = groups.findIndex((_group) => _group === groups[index]);
                    if (reverseIndex > lastChildrenIndex && lastChildrenIndex !== -1) {
                      (data as IQuestionDataInfo)._sequence.push('children');
                    }
                    (data as IQuestionDataInfo)._sequence.push(`extra${(data as IQuestionDataInfo).extraNodes.push(mark ? nodes.slice(1) : nodes)}`);
                  }
                  break;
                }
              }
            }
          }
        } else {
          const key = 'extraNodes';
          let questionData: IQuestionDataInfo | undefined;
          if (source === 'question') {
            questionData = findQuestionForAnswerExplanation(elementsData, currentQuestionData, group, nextGroup);
          } else {
            if (!mark) {
              questionData = findQuestionBy(elementsData, (data) => data.level === 1) || currentQuestionData;
            } else {
              const level = Number(mark.level!);
              // currentQuestionData 只有 true 才会进这个分支，参考下面 answer 的逻辑
              let shouldNewQuestion = true;
              // 兼容附加内容已经生成了试题数据的情况，向前遍历查找同级的节点，如果找到则直接使用，否则新建
              for (const [element, data] of backIterate(elementsData)) {
                if (element.node_type === 'none' || element.node_type === 'text') continue;
                if (element.node_type !== 'question') break;
                if (Number(mark.level!) !== Number(data.level!)) break;
                if (mark.serialNumber === (data as IQuestionDataInfo).serialNumber && (data as IQuestionDataInfo)._sequence.every((item) => item.startsWith('extra'))) {
                  currentQuestionData = data as IQuestionDataInfo;
                  questionData = data as IQuestionDataInfo;
                  shouldNewQuestion = false;
                  break;
                }
              }
              if (shouldNewQuestion) {
                currentQuestionData = createQuestionData({ _serialNumber: mark!._serialNumber, serialNumber: mark!.serialNumber, level });
                elementsData.push([{ node_type: 'question', imgId }, currentQuestionData]);
              }
              questionData = currentQuestionData;
            }
          }
          if (questionData) {
            questionData[key].push(mark ? nodes.slice(1) : nodes);
            questionData._sequence.push(`extra${questionData[key].length}`);
          } else {
            elementsData.push([
              { node_type: 'none', imgId },
              {
                nodes,
                errorInfo: [{
                  rule: EJsonErrorRule.answer_mismatch,
                  message: '附加内容未匹配到试题',
                }],
              }
            ]);
          }
        }
      }
    } else if (label === 'quest_num') {
      // 试题
      currentQuestionData = createQuestionData({ _serialNumber, serialNumber, level, p, nodes: nodes.slice(1) });
      elementsData.push([{ node_type: 'question', imgId }, currentQuestionData]);
    } else if (label === 'answer' || label === 'explanation') {
      // 试题答案、试题解析
      if (label === 'answer' && !mark && nextGroup && nextGroup.label === 'answer' && nextGroup.mark) {
        /*
         * 如果当前组是不带题号的答案，下一组是带题号的答案，则认为当前组不是真的答案
         * 比如 "答案：(1) A (2) B"，则"答案："无意义。直接跳过
         */
        return;
      }
      const key = label === 'answer' ? 'answerNodes' : 'explanationNodes';
      let questionData: IQuestionDataInfo | undefined;
      if (source === 'question') {
        questionData = findQuestionForAnswerExplanation(elementsData, currentQuestionData, group, nextGroup);
      } else {
        if (!mark) {
          if (label === 'answer') {
            questionData = currentQuestionData;
          } else {
            questionData = findQuestionBy(elementsData, (data) => data.level === 1) || currentQuestionData;
          }
        } else {
          const level = Number(mark.level!);
          let shouldNewQuestion = !currentQuestionData ||
            currentQuestionData[key].length ||
            currentQuestionData.serialNumber !== mark.serialNumber ||
            currentQuestionData.level !== level;
          // 兼容附加内容已经生成了试题数据的情况，向前遍历查找同级的节点，如果找到则直接使用，否则新建
          for (const [element, data] of backIterate(elementsData)) {
            if (element.node_type === 'none' || element.node_type === 'text') continue;
            if (element.node_type !== 'question') break;
            if (Number(mark.level!) !== Number(data.level!)) break;
            if (mark.serialNumber === (data as IQuestionDataInfo).serialNumber && (data as IQuestionDataInfo)._sequence.every((item) => item.startsWith('extra'))) {
              currentQuestionData = data as IQuestionDataInfo;
              questionData = data as IQuestionDataInfo;
              shouldNewQuestion = false;
              break;
            }
          }
          if (shouldNewQuestion) {
            currentQuestionData = createQuestionData({ _serialNumber: mark!._serialNumber, serialNumber: mark!.serialNumber, level });
            elementsData.push([{ node_type: 'question', imgId }, currentQuestionData]);
          }
          questionData = currentQuestionData;
        }
      }
      if (questionData) {
        /*
         * 图片填空:
         * 所以只要answer的题号匹配对了 我就能把answer丢进去。之后就是匹配个数的问题了.
         * 也就是说图片填空的功能 我需要给图片这块的文字打上quset_num便可以了。
         */
        questionData[key] = questionData[key].concat(mark ? nodes.slice(1) : nodes);
        if (getText(questionData[key]).trim()) {
          questionData._sequence.push(label === 'answer' ? 'answer' : 'analysis');
        }
      } else {
        elementsData.push([
          { node_type: 'none', imgId },
          {
            nodes,
            errorInfo: [{
              rule: EJsonErrorRule.answer_mismatch,
              message: `${label === 'answer' ? '答案' : '解析'}未匹配到试题`,
            }],
          }
        ]);
      }
    }
  });
  return elementsData;
}

function createQuestionData(data: Partial<IQuestionDataInfo>): IQuestionDataInfo {
  return {
    serialNumber: '',
    level: 1,
    nodes: [],
    answerNodes: [],
    explanationNodes: [],
    _sequence: [],
    extraNodes: [],
    ...data,
  } as any;
}

// @todo：答案去找上面对应的题目
function findQuestionForAnswerExplanation(
  elementsData: [INodeInfo, IDataInfo][],
  currentQuestionData: IQuestionDataInfo | undefined,
  group: IHtmlNodeGroup,
  nextGroup?: IHtmlNodeGroup
) {
  const byMark = (data: IQuestionDataInfo) => { // 相同题号
    return data.level === Number(group.mark!.level!) && data.serialNumber === group.mark!.serialNumber;
  };
  const findQuestionForAnswer = () => { // 查找答案所属试题
    // 若答案有题号，查找与答案有相同题号的试题；否则，返回当前试题
    return group.mark ? findQuestionBy(elementsData, byMark) : currentQuestionData;
  };
  const findQuestionForExplanation = () => { // 查找解析所属试题
    if (group.mark) {
      // 若解析有题号，查找与解析有相同题号的试题；
      return findQuestionBy(elementsData, (data: IQuestionDataInfo) => { // 相同题号
        return data.level === Number(group.mark!.level!) && data.serialNumber === group.mark!.serialNumber && data.p === group.mark!.p;
      });
    }
    if (nextGroup && !nextGroup.mark && nextGroup.label === 'answer') {
      // 若后一个节点是答案，且无题号，该解析和后一个答案一样属于当前试题
      return currentQuestionData;
    }
    // 查找有一级题号的试题
    return findQuestionBy(elementsData, (data) => data.level === 1 || data.level === -1);
  };
  return group.label === 'answer' ? findQuestionForAnswer() : findQuestionForExplanation();
}

// 反向找第一个满足条件的试题
function findQuestionBy(elementsData: [INodeInfo, IDataInfo][], by: (d: IQuestionDataInfo) => boolean) {
  for (const [element, data] of backIterate(elementsData)) {
    // 如果是无标记段落，继续往前查找
    if (element.node_type === 'none') continue;
    // 不是试题则结束查找
    if (element.node_type !== 'question') return;
    const result = data as IQuestionDataInfo;
    if (by(result)) return result;
  }
}
