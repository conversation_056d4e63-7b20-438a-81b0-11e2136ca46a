/* eslint-disable */
import { iterateNode, splitNodes } from '../treeHelper';
import { IElementNode, parseHtml, getHtml } from '../htmlHelper';
import {
  cleanQuestionAnswer,
  mergeAnswerExplanationQuestion,
  // mergeQuestionExplanation,
  setMaterialSerialNumber,
  setupMaterialBodyBlankSn
} from './helper/cleanQuestion';
import { validateJsonNodes } from './validateJsonNodes';
import { combineJson } from '../jsonHelper';
import { groupNodes, IHtmlNodeGroup } from './helper/groupHtmlNode';
import { preBuildElementsData } from './helper/preBuildData';
import { filterEmptyNode } from './helper/filterEmptyNode';
import { convertElements, distributeAnswersToSubQuestions } from './helper/convertElement';
import { buildElementTree } from './helper/buildElementTree';
import { addLabelForAnswerQuestNum, cleanupNode, mergeLabelNear } from './helper/cleanupNode';
import { IQuestionDataInfo } from './helper/preBuildData';
import { postProcessTree } from './helper/postProcessTree';

export { validateJsonNodes } from './validateJsonNodes';
export { checkQuestionType } from './checkQuestionType';

interface IBaseContent {
  level: number;
  body: string;
  _body?: string;
  meta?: { [key: string]: any }; // 用于保留状态
  notes?: INotes[];
}

export type IChapterContent = IBaseContent;

export interface IParagraphContent extends IBaseContent {
  tag: string;
  source: string;
}

export interface IQuestionChoice {
  letter: string;
  option: string;
}

export interface IExtra {
  title?: string;
  body: string;
}

export interface INotes {
  note_id: string;
  note_content: TJsonNode[];
}

export interface IQuestionContent extends IBaseContent {
  serial_number: string;
  _serial_number?: string;
  sn?: number;
  answer: string[];
  correct?: (boolean | undefined)[];
  source: string;
  analysis: string;
  _sequence: string[];
  blank_count?: number;
  bracket_count?: number;
  choices?: IQuestionChoice[];
  choice_count?: number;
  choice?: any; // 兼容老版本
  [propName: string]: any;
}

export interface IChapterData {
  node_name: string;
  content: IChapterContent;
}

export interface IParagraphData {
  content: IParagraphContent;
}

export type TBasicQuestionType = 'choice' | 'true_or_false' | 'blank' | 'other';

export interface IQuestionData {
  question_type: TBasicQuestionType | 'material';
  content: IQuestionContent;
}

export interface IMaterialQuestionData extends IQuestionData {
  question_type: 'material';
}

export interface IBasicQuestionData extends IQuestionData {
  question_type: TBasicQuestionType;
}

export enum EJsonErrorRule {
  node_content_error = 'node_content_error', // 节点内容异常
  chapter_content_empty = 'chapter_content_empty', // 目录内容为空
  chapter_children_empty = 'chapter_children_empty', // 目录子节点内容不能为空
  chapter_b_tag = 'chapter_b_tag', // 目录存在加粗样式
  paragraph_content_empty = 'paragraph_content_empty', // 段落内容为空
  paragraph_content_error = 'paragraph_content_error', // 段落内容异常
  none_paragraph = 'none_paragraph', // 无标记的段落
  question_content_empty = 'question_content_empty', // 题干内容为空
  third_level_serial_number_duplicate = 'third_level_serial_number_duplicate',  // 三级小题题号重复
  level_invalid = 'level_invalid', // 层级错误

  subs_not_empty = 'subs_not_empty', // 不能有子节点
  subs_is_empty = 'subs_is_empty', // 需要有子节点
  subs_question_min_limit = 'subs_question_min_limit', // 子题最小数量限制

  serial_number_empty = 'serial_number_empty', // 题号为空
  serial_number_format_invalid = 'serial_number_format_invalid', // 题号错误
  serial_number_format_mismatch_neighbor = 'serial_number_format_mismatch_neighbor', // 题号格式错误
  serial_number_discontinuous = 'serial_number_discontinuous', // 题号不连续
  serial_number_not_first = 'serial_number_not_first', // 第一个题目题号不是第一个序号
  serial_number_material_blank_error = 'serial_number_material_blank_error', // 材料题题干个数与其子题个数不符

  source_has_bracket = 'source_has_bracket', // 试题来源不应该有括号
  multi_source = 'multi_source', // 试题来源不应该有多个
  not_leaf_answer = 'not_leaf_answer', // 非叶子试题不能有答案
  answer_empty = 'answer_empty', // 叶子试题答案为空
  blank_width_choice = 'blank_width_choice', // 填空题有选项

  choice_letter_invalid = 'choice_letter_invalid', // 选择题选项号不符合规则
  choice_option_empty = 'choice_option_empty', // 选择题选项内容空
  choice_answer_invalid = 'choice_answer_invalid', // 选择题答案无效
  choice_answer_multiple = 'choice_answer_multiple', // 选择题答案多于一个字母
  choice_start_with_tab = 'choice_start_with_tab', // 选择题选项以制表符开头
  choice_end_with_lines = 'choice_end_with_lines', // 选择题最后一个选项内包含换行

  options_invalid = 'options_invalid', // 选择题选项数量错误
  options_mismatch_neighbor = 'options_mismatch_neighbor', // 选择题选项数与相邻题不一致

  true_or_false_invalid = 'true_or_false_invalid', // 判断题答案不符合要求
  blank_answer_mismatch = 'blank_answer_mismatch', // 填空题答案与空数量不匹配

  material_no_end = 'material_no_end', // 材料题无结束分割线
  material_no_start = 'material_no_start', // 材料题无开始分割线

  material_blank_sn = 'material_blank_sn', // 材料题题干中的题号

  answer_mismatch = 'answer_mismatch', // 答案解析未匹配到试题
  combine_answer_mismatch = 'combine_answer_mismatch', // 合并试题和答案时匹配无效
  answer_error_content = 'answer_error_content', // 答案异常内容
  analysis_error_content = 'analysis_error_content', // 解析异常内容

  answer_error_content_duplicate = 'answer_error_content_duplicate', // 答案异常内容重复
  analysis_error_content_duplicate = 'analysis_error_content_duplicate', // 解析异常内容重复

  duplicate_answer = 'duplicate_answer',
  duplicate_level_jb = 'duplicate_level_jb',

  blank_answer_start_with_jie = 'blank_answer_start_with_jie', // 填空题答案有解
  blank_answer_error = 'blank_answer_error', // 填空题答案包含表格或者换行

  error_straight_latex = 'error_straight_latex', // 正斜体错误
  blank_answer_followed_by_tab = 'blank_answer_followed_by_tab', // 填空题答案后面有制表符

  error_latex = 'error_latex', // latex 错误
  answer_table = 'answer_table', // 答案中包含表格
  chapter_content_start_with_blank = 'chapter_content_start_with_blank', // 目录内容以空格开头
  answer_blank = 'answer_blank', // 答案中包含横线标签
  error_body_option = 'error_body_option', // 题干中包含选项
  analysis_blank = 'analysis_blank', // 解析中有作答空间
  question_answer_mismatch = 'question_answer_mismatch', // 答案与试题数量不一致
  choice_option_table = 'choice_option_table', // 选项中存在表格
  question_missing_image = 'question_missing_image', // 题干提到图但缺少图片
  table_cell_inconsistent = 'table_cell_inconsistent', // 表格单元格内容不一致
  analysis_answer_duplicate = 'analysis_answer_duplicate', // 解析中疑似重复了答案
  content_serial_number_duplicate = 'content_serial_number_duplicate', // 内容开头内容与题号重复
  question_content_duplicate = 'question_content_duplicate', // 题目内容重复
  content_invalid_character = 'content_invalid_character', // 内容包含异常字符
}

export type TJsonErrorLevel = 'warn' | 'error';

export interface IJsonErrorInfo {
  rule: EJsonErrorRule; // 错误类型
  message: string; // 错误信息
  level?: TJsonErrorLevel; // 错误or警告。默认警告
}

interface INodeBase {
  imgId?: string;
  node_type: 'chapter' | 'paragraph' | 'question';
  node_level: number;
  children: TJsonNode[];
  errorInfo?: IJsonErrorInfo[];
  answerImgId?: string;
  attributes?: { [key: string]: string }; // 兼容旧版
}

export interface IChapterNode extends INodeBase, IChapterData {
  node_type: 'chapter';
}

export interface IParagraphNode extends INodeBase, IParagraphData {
  node_type: 'paragraph';
}

export interface IQuestionNode extends INodeBase, IQuestionData {
  node_type: 'question';
  children: (IBasicQuestionNode | IParagraphNode)[];
}

export interface IBasicQuestionNode extends IQuestionNode {
  question_type: TBasicQuestionType;
  children: IBasicQuestionNode[];
}

export interface IMaterialQuestionNode extends IQuestionNode {
  question_type: 'material';
}

export type TJsonNode = IChapterNode | IParagraphNode | IQuestionNode;

function preCleanHtml(html: string) {
  const result = html.trim()
    .replace(/\u200b/g, '') // 删除 zwsp
    .replace(/<br\/?>/g, '') // 删除 br
    .replace(/disabled="disabled"/g, '') // 删除不知道哪里来的disable
  /*
   * .replace(/<span ([^>]*data-label="latex"[^>]*)>([\s\S]*?)<\/span>/g, (_m, attr: string) => {
   *   const latex = attr.match(/data-value="(.*?)"/)![1];
   *   return latex.split(/■/g).map(l => {
   *     const latex = l.replace(/&/g, '&amp;').replace(/</g, '&lt;');
   *     return `<span data-label="latex" data-value="${l}">$$${latex}$$</span>`;
   *   }).join('■');
   * })
   */
    .replace(/■/g, '<span data-label="answer-sep">|</span>') // 答案分隔符
    .replace(/<table[^>]*>(\s*<tr>\s*<\/tr>)*\s*<\/table>/g, '') // 移除空白表格（没有单元格）（无 tbody 情况）
    .replace(/<table[^>]*>\s*<tbody>(\s*<tr>\s*<\/tr>)*\s*<\/tbody>\s*<\/table>/g, '') // 移除空白表格（没有单元格）
    .replace(/data-signal-desc="[\s\S]*?"/g, ''); // 移除多余的data-set
  return result;
}

function preHandleAnalysisAnswer(nodeGroups: IHtmlNodeGroup[]) { // 处理下挨着的答案和解析。
  nodeGroups.forEach((item, index) => {
    if (index === 0) return;
    const pre = nodeGroups[index - 1];
    const types = ['explanation', 'answer'];
    if (item.label && types.includes(item.label) && !item.mark && pre.label && types.includes(pre.label) && item.label !== pre.label && pre.mark && pre.mark.label === 'quest_num') {
      item.mark = pre.mark;
      item.nodes.unshift(item.mark._serialNumber!);
    }
  });
}

function preAddMarkInfo(nodeGroups) { // 给解析加标记.标识当前小题所在的大题，需要path像p一样加。
  const list: { level: number; serialNumber: string; used: boolean }[] = [];
  nodeGroups.forEach((item) => {
    if (item.label === 'quest_num') {
      list.push({
        level: Number(item.level),
        serialNumber: item.serialNumber,
        used: false,
      });
      return;
    }
    if (item.label === 'explanation' && item.mark) {
      const level = Number(item.mark.level);
      let index = 0;
      // 应该需要分情况处理
      // 1. 如果是题目-解析-题目-解析，需要从后往前寻找 index
      // 2. 如果是题目-题目-解析-解析，需要从前往后寻找 index
      for (let i = list.length - 1; i >= 0; i -= 1) {
        if (Number(list[i].level) === level && list[i].used === false) {
          list[i].used = true;
          index = i;
          break;
        }
      }

      const preSerialNumbers = list.slice(0, index);
      const preOwnnerSerialNumbers: { level: number; serialNumber: string; used: boolean }[] = [];
      for (let i = preSerialNumbers.length - 1; i >= 0; i -= 1) {
        const j = preSerialNumbers[i];
        const jLevel = Number(j.level);
        if (
          (jLevel > 0 && level > 0 || jLevel < 0 && level < 0) &&
          jLevel < level && !preOwnnerSerialNumbers.find((item) => Number(item.level!) === jLevel)
        ) {
          preOwnnerSerialNumbers.unshift(j);
        }
        if (jLevel === 1 || jLevel === -1) {
          break;
        }
      }
      item.mark.p = preOwnnerSerialNumbers.map((item) => item.serialNumber).join(',');
      item.mark._path = preOwnnerSerialNumbers;
    }
  });
}

function preAddQuestNumP(nodeGroups) { // 给quest_num加标记，标识当前小题所在的大题，需要path像p一样加。
  const questNum = nodeGroups.filter((item) => item.label === 'quest_num');
  questNum.forEach((item, index) => {
    const list = questNum.slice(0, index);
    const p: IQuestionDataInfo[] = [];
    for (let i = list.length - 1; i >= 0; i -= 1) {
      if (
        (Number(list[i].level) > 0 && Number(item.level) > 0 || Number(list[i].level) < 0 && Number(item.level) < 0) &&
        Number(list[i].level) < Number(item.level) &&
        !p.find((item) => Number(item.level!) === Number(list[i].level))) {
        p.unshift(list[i]);
      }
      if (Number(list[i].level) === 1 || Number(list[i].level) === -1) {
        break;
      }
    }
    item.p = p.map((item) => item.serialNumber).join(',');
    item._path = p;
  });
}

export function parseJson(html, source: 'question' | 'answer' = 'question') {
  if (!html) {
    return [];
  }

  // 1. html 直接转 json数组
  const cleanedHtml = preCleanHtml(html);
  let nodeTree = parseHtml(cleanedHtml);
  // 2. 清理一些没用的节点信息。
  cleanupNode(nodeTree);
  // 3. 合并相邻的题号 & 选项 1.xx 1.bb 这样连续打了两个题号合并  1. xxbb
  mergeLabelNear(nodeTree);

  if (source === 'answer') {
    addLabelForAnswerQuestNum(nodeTree);
  }

  // 4. 按题号拆分节点
  /**
   *  nodes : { quest_num:1 相关信息 } , { quest_1 -> 到下一个节点间所有同级节点 + 别的节点 } , { quest_2 }
   *  seps: [[ 标题节点,标题节点抽象信息(层级，type等) ],[]]
   */
  const [nodes, seps] = splitNodes(nodeTree, ({node, stopIterateChildren}) => {
    if (node.type === 'element' && node.tagName === 'table') {
      // 表格内不要拆分
      stopIterateChildren?.();
      return false;
    }
    return node.type === 'element' && ['quest_num'].includes(node.dataset.label!);
  });
  nodeTree = nodes;
  // 标题节点加上 节点抽象信息(mark)
  seps.forEach(([sep, markNode]) => {
    (sep as any).mark = (markNode as IElementNode).dataset;
  });
  nodeTree = filterEmptyNode(nodeTree);
  /*
   * 5. 节点分组 通过label(题目、答案、解析等) / quest_num 等进行分组 每个节点就是一个组。
   * [ {q1 带q1内容(text)},{extra1},{explanation},{q2},{answer1} ]
   */
  const nodeGroups = groupNodes(nodeTree);
  // 处理顺序如 带题号的答案连着解析 的情况，给解析带上答案 _serialNumber(题号节点的node)
  preHandleAnalysisAnswer(nodeGroups);
  // 6. 给解析加上path(大小题关系) & 题号加上path
  if (source === 'question') {
    preAddMarkInfo(nodeGroups);
    preAddQuestNumP(nodeGroups);
  }
  // 7. 对分组数据转data之前预处理 [{info}, node] 把同一道题的数据抽到一个节点去。
  const elementsData = preBuildElementsData(nodeGroups, source);
  // 8. 转data [{info}, node] => [data, ...]
  const elements = convertElements(elementsData);
  const elementTree = buildElementTree(elements);

  // 树形结构建立后，应用答案分散策略
  // 注意：此处调用是关键，必须在树形结构建立后调用，而不是在convertElements函数中调用
  // 因为答案分散策略需要处理有父子关系的节点，而这种关系只有在buildElementTree中建立
  // 如果在convertElements中调用，此时节点还是扁平的，没有建立父子关系，无法正确处理
  distributeAnswersToSubQuestions(elementTree);
  setupMaterialBodyBlankSn(elementTree);
  let notMatched: TJsonNode[] = [];
  if (source === 'answer') {
   notMatched = mergeAnswerExplanationQuestion(elementTree);
  }
  /*
   * Tips：暂时注释掉，取消小题解析合并至大题
   * mergeQuestionExplanation(elementTree);
   */
  setMaterialSerialNumber(elementTree);
  cleanQuestionAnswer(elementTree);

  postProcessTree(elementTree);

  return [elementTree, notMatched];
}

type TypeSource = 'question' | 'answer';
type TypeParams = { html: string, isOfficial?: boolean, source?: TypeSource };

export function htmlToJson({ html, isOfficial = false, source }: TypeParams) {
  let json: TJsonNode[];
  if (!source) {
    const [questionHtml, answerHtml] = html.split(/<hr[^>]+answer_separator[\s\S]*?>/i);
    const [questionJson, ] = parseJson(questionHtml, 'question');
    if (answerHtml) {
      let [answerJson, notMatched] = parseJson(answerHtml, 'answer');
      json = combineJson(questionJson, answerJson);
      !json.push(...notMatched);
    } else {
      json = questionJson;
    }
  } else {
    [json, ] = parseJson(html, source);
  }
  for (const { node } of iterateNode(json)) {
    delete node.content.meta;
  }
  if (!isOfficial) {
    validateJsonNodes(json);
  } else {
    cleanJsonNodes(json);
  }
  for (const { node } of iterateNode(json)) {
    if (node.node_type === 'question') { // 答案分割线后面的答案解析。
      if (!node.content._sequence.includes('analysis') && node.content.analysis) {
        node.content._sequence.push('analysis');
      }
      for (const key in node.content) {
        if (Object.prototype.hasOwnProperty.call(node.content, key)) {
          let tempExtraList: string[] = [];
          if (key.startsWith('extra') && !node.content._sequence.includes(key)) tempExtraList.push(key);
          tempExtraList = tempExtraList.reverse();
          tempExtraList.forEach((key) => {
            if (!node.content._sequence.includes(key)) {
              node.content._sequence.push(key);
            }
          });
        }
      }
      if (!node.content._sequence.includes('answer') && node.content.answer.length) {
        node.content._sequence.unshift('answer');
      }
      // 答案分割线后面的 _sequence 默认带answer, 如果没有答案，就去掉answer
      if (!node.content.answer.length && node.content._sequence.includes('answer')) {
        node.content._sequence.splice(node.content._sequence.indexOf('answer'), 1);
      }
    }
    if (node.node_type === 'question' && node.content.level < 0) { // 假题号
      node.content.level = Math.abs(node.content.level);
      node.content.serial_number = '';
    }
    if (node.node_type === 'question' && node.question_type === 'choice') {
      const choices = node.content.choices;
      choices?.forEach((choice) => {
        if (choice && choice.option) {
          const nodes = parseHtml(choice.option);
          for (const { node } of iterateNode(nodes)) {
            if (node.tagName === 'p' && (node.children.length === 1 && node.children[0].type === 'text' && (node.children[0].content === '&nbsp;' || node.children[0].content === '') || !node.children.length)) {
              Object.keys(node).forEach((k) => delete node[k]);
              (node as any).type = 'text';
              (node as any).conetnt = '';
            } else if (node.tagName === 'p') {
              delete node.attrs.class;
              node.cls = {};
            }
          }
          const formatChoice = getHtml(nodes);
          choice.option = formatChoice;
        }
      });
    }
    if (node.node_type === 'question') { // 清除空的节点
      if (node.content._sequence?.length) {
        const _sequence = node.content._sequence;
        if (_sequence.includes('analysis') && !node.content.analysis) {
          _sequence.splice(_sequence.indexOf('analysis'), 1);
        }
        if (_sequence.includes('answer') && !node.content.answer.length) {
          _sequence.splice(_sequence.indexOf('answer'), 1);
        }
        if (_sequence.includes('extra1') && !node.content.extra1) {
          _sequence.splice(_sequence.indexOf('extra1'), 1);
        }
      }
    }
  }
  return json;
}

export function cleanJsonNodes(json?: TJsonNode[]) {
  if (!json) {
    return;
  }
  for (const { node } of iterateNode(json)) {
    const element = node as any;
    delete element.attributes;
    delete element.parentUid;
    delete element.nodeIndex;
    delete element.errorInfo;
    delete element.uid;
    delete element.imgId;
    delete element.answerImgId;
    for (const key in element) {
      if (element.hasOwnProperty(key) && key.startsWith('_')) {
        delete element[key];
      }
    }
  }
  return json;
}
