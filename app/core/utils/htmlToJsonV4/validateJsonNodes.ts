// 试题合法性校验
import { findAllNodes, findNode, iterateNode } from '../treeHelper';
import {
  EJsonErrorRule,
  IBasicQuestionNode,
  IChapterNode,
  IJsonErrorInfo,
  IParagraphNode,
  IQuestionNode,
  TJsonNode
} from './index';
import { parseHtml } from '../htmlHelper';
import { isHtmlStr } from '../helper';
import { parseSerialNumber, sameSerialNumberType } from '../parseSerialNumber';
import { checkHtmlStraight } from '../latexConverter';

const LETTERS = 'ABCDEFGHIJKLMN';

function isEmptyText(text: string) {
  return !text || !text.trim();
}

function isEmptyHtml(html: string, blankIsEmpty = true) {
  if (!html) return true;
  const nodes = parseHtml(html);
  return !findNode(nodes, ({ node }) => {
    return node.type === 'text' && !isEmptyText(node.content) ||
      node.type === 'element' && (
        node.tagName === 'img' && Boolean(node.attrs.src) ||
        !blankIsEmpty && Boolean(node.dataset.label) && ['blank', 'bracket'].includes(node.dataset?.label || '')
      );
  });
}

// 校验答案
function validateAnswer({ content }: IQuestionNode) {
  if (!content.answer.length && !(/answer-block/).test(content.body)) {
    return [{
      rule: EJsonErrorRule.answer_empty,
      message: '试题答案不存在',
    }];
  }
  const emptyAnswerIndex: number[] = [];
  content.answer.forEach((answer, index) => {
    if (isHtmlStr(answer) ? isEmptyHtml(answer) : isEmptyText(answer)) {
      emptyAnswerIndex.push(index + 1);
    }
  });
  const emptyAnswerIndexStr = emptyAnswerIndex.join(',');
  if (emptyAnswerIndexStr) {
    const message = content.answer.length === 1 ?
      '试题答案为空' :
      `试题答案${emptyAnswerIndexStr}为空`;
    return [{
      rule: EJsonErrorRule.answer_empty,
      message,
    }];
  }
  const tableRegex = /<table\b[^>]*>([\s\S]*?)<\/table>/g;
  const blankReg = /data-label="blank"/g;
  const errorArr: IJsonErrorInfo[] = [];
  const answerString = content.answer.join();
  const body = content.body;
  const regexStudentImage = /data-label="blank"/g;
  const regexImageContainer = /img-signal-container/g;
  if (regexImageContainer) {
    const matchesStudent = body.match(regexStudentImage);
    const studentImageQuestLength = matchesStudent ? matchesStudent.length : 0;
    if (studentImageQuestLength && studentImageQuestLength !== content.answer.length) {
      errorArr.push({
        rule: EJsonErrorRule.question_answer_mismatch,
        message: '试题数量与答案数量不一致',
      });
    }
  }
  if (tableRegex.test(answerString)) {
    errorArr.push({
      rule: EJsonErrorRule.answer_table,
      message: '试题答案包含表格',
    });
  }
  const blankMatch = answerString.match(blankReg);
  if (blankMatch && blankMatch.length) {
    errorArr.push({
      rule: EJsonErrorRule.answer_blank,
      message: '答案中包含期望外的作答空间',
    });
  }
  if (errorArr.length) {
    return errorArr;
  }
  return [];
}

// 校验填空题
function validateBlank({ content, children }: IQuestionNode) {
  const error: IJsonErrorInfo[] = [];
  const body = content.body;
  if (body) {
    const regex = /<p[^>]*>(.*?)<\/p>/;
    const match = body.match(regex);
    if (match) {
      const cont = match[1];
      const tabRegex = /<span data-label="tab"> <\/span>/;
      const isFollowedByTab = tabRegex.test(cont);

      if (isFollowedByTab) {
        // 答案后面紧跟着<span data-label="tab"> </span>
        error.push({
          rule: EJsonErrorRule.blank_answer_followed_by_tab,
          message: '填空题答案后面不能有制表符',
        });
      }
    }
  }
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '填空题不能有子节点',
    });
  }
  if (content.answer.join().trim().replace(/<[^>]+>/g, '').startsWith('解')) {
    error.push({
      message: '填空题答案有解',
      rule: EJsonErrorRule.blank_answer_start_with_jie,
    });
  }
  if (content.choices && content.choices.length) {
    error.push({
      message: '填空题不能有选项',
      rule: EJsonErrorRule.blank_width_choice,
    });
  }
  if (content.answer.some((answer) => {
    const tag = Number(answer.replace(/<p><span data-label="font"[^<>]*><\/span><\/p>/, '').split('</p>').length - 1) > 1 || /<table/.test(answer);
    return tag;
  })) {
    error.push({
      message: '填空题答案包含表格或者换行',
      rule: EJsonErrorRule.blank_answer_error,
    });
  }
  const count = content.blank_count || content.bracket_count;
  if (!count || count !== content.answer.length) {
    error.push({
      rule: EJsonErrorRule.blank_answer_mismatch,
      message: `答案和问题数量不匹配,答案${content.answer.length},问题${count}`,
    });
  }
  return error;
}

// 校验选择题
function validateChoice({ content, children }: IQuestionNode, prevQuestion?: IQuestionNode) {
  let { choices, choice, answer } = content;
  const error: IJsonErrorInfo[] = [];
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '选择题不能有子节点',
    });
  }
  if (choice) {
    // 兼容旧版
    choices = Object.keys(choice).map((letter) => ({ letter, option: choice[letter] }));
    if (!choices.length) {
      choices = undefined;
    }
  }
  if (!choices || !choices.length) {
    error.push({
      rule: EJsonErrorRule.options_invalid,
      message: '选择题无选项',
    });
    return error;
  }
  if (choices.length === 1) {
    error.push({
      rule: EJsonErrorRule.options_invalid,
      message: '选择题至少2个选项',
    });
  }
  if (choices[choices.length - 1].option.split('</p>').length - 1 > 1) {
    error.push({
      rule: EJsonErrorRule.choice_end_with_lines,
      message: '选择题最后一个选项内包含换行',
    });
  }
  if (prevQuestion && prevQuestion.question_type === 'choice') {
    const neighborChoices = prevQuestion.content.choices || prevQuestion.content.choice;
    if (neighborChoices &&
      (neighborChoices.length || Object.keys(neighborChoices).length) !== choices.length) {
      error.push({
        rule: EJsonErrorRule.options_mismatch_neighbor,
        message: `选择题选项数与相邻题目${prevQuestion.content.serial_number}不一致`,
      });
    }
  }
  const letters = choices.map(({ letter }) => letter);
  const upperLetters = letters.map((letter) => letter.toUpperCase());
  const letterStrs = letters.join('');
  const isLower = letterStrs === letterStrs.toLowerCase();
  if (upperLetters.some((letter) => letter.length !== 1 || !LETTERS.includes(letter)) ||
    !LETTERS.startsWith(upperLetters.join('')) ||
    !isLower && letterStrs !== letterStrs.toUpperCase()) {
    let suggestLetters = LETTERS.substring(0, choices.length);
    if (isLower) {
      suggestLetters = suggestLetters.toLowerCase();
    }
    error.push({
      rule: EJsonErrorRule.choice_letter_invalid,
      message: `选项号应为${suggestLetters}`,
    });
  }
  const emptyOptionLetters = choices
    .filter(({ option }) => isEmptyHtml(option))
    .map(({ letter }) => letter)
    .join(',');
  if (emptyOptionLetters) {
    error.push({
      rule: EJsonErrorRule.choice_option_empty,
      message: `选项${emptyOptionLetters}内容为空`,
    });
  }
  if (answer.some((a) => a.length > 1) && answer.some((a) => /[^a-zA-Z]/.test(a))) {
    error.push({
      rule: EJsonErrorRule.choice_answer_multiple,
      message: '选择题答案多于一个字母',
    });
  }
  const letterMap = {};
  choices.forEach((sinalChoice) => {
    letterMap[sinalChoice.letter] = true;
  });
  const invalidAnswer = answer.filter((sinalAnswer) => sinalAnswer.split('').some((a) => !letterMap[a])).join(',');
  if (invalidAnswer) {
    error.push({
      rule: EJsonErrorRule.choice_answer_invalid,
      message: `答案${invalidAnswer}不在选项中`,
    });
  }
  for (const a of answer) {
    if (new Set(a).size !== a.length) {
      error.push({
        rule: EJsonErrorRule.choice_answer_invalid,
        message: `多选答案${a}重复`,
      });
      break;
    }
  }
  // 选项中不能有表格
  const tableRegex = /<table\b[^>]*>([\s\S]*?)<\/table>/g;
  choices.forEach((_choice) => {
    if (tableRegex.test(_choice.option)) {
      error.push({
        rule: EJsonErrorRule.choice_option_table,
        message: `选项${_choice.letter}包含表格`,
      });
    }
  });
  return error;
}

// 校验判断题
function validateTrueOrFalse({ content, children }: IQuestionNode) {
  const error: IJsonErrorInfo[] = [];
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '判断题不能有子节点',
    });
  }
  if (!content.correct || content.correct.some((correct) => typeof correct !== 'boolean')) {
    error.push({
      rule: EJsonErrorRule.true_or_false_invalid,
      message: '判断题答案不符合要求',
    });
  }
  return error;
}

function isChoiceQuestion(question: IQuestionNode) {
  // 'option' 兼容v1版本
  return question.question_type === 'choice' ||
    question.question_type as string === 'option';
}

// 校验试题层级
function validateQuestionContentLevel(question: IQuestionNode, parent?: TJsonNode) {
  if (parent && parent.node_type === 'question') {
    if (Math.abs(question.content.level) !== Math.abs(parent.content.level) + 1) {
      return [{
        rule: EJsonErrorRule.level_invalid,
        message: `试题层级错误，应为为${Math.abs(parent.content.level) + 1}，实际为${Math.abs(question.content.level)}`,
      }];
    }
  } else if (Math.abs(question.content.level) == null || Math.abs(question.content.level) > 1) {
    return [{
      rule: EJsonErrorRule.level_invalid,
      message: `试题层级错误，应为为1，实际为${Math.abs(question.content.level)}`,
    }];
  }
  return [];
}

// 校验题号
function validateQuestionSerialNumber(
  question: IQuestionNode, prevQuestion?: IQuestionNode, prevRootQuestion?: IQuestionNode, isInSameChapter?: boolean
) {
  if (question.question_type === 'material') {
    const blankLenth = question.content.body.match(/data-label="blank"/g)?.length;
    let childQuestionCountLevel1 = 0;
    question.children.map((q) => {
      if (q.node_type === 'question' && q.content.level === 1) {
        childQuestionCountLevel1 += 1;
      }
    });
    if (blankLenth && blankLenth !== childQuestionCountLevel1) {
      return [{
        rule: EJsonErrorRule.serial_number_material_blank_error,
        message: `材料题填空个数(${blankLenth})与其一级子题个数不符(${childQuestionCountLevel1})`,
      }];
    }
    return [];
  }
  if (isEmptyText(question.content.serial_number)) {
    return [{
      rule: EJsonErrorRule.serial_number_empty,
      message: '题号为空',
    }];
  }
  const result = parseSerialNumber(question.content.serial_number);
  if (!result) {
    return [{
      rule: EJsonErrorRule.serial_number_format_invalid,
      message: `题号不是编号,${question.content.serial_number}`,
    }];
  }
  if (result.type === 'label') {
    return [];
  }

  const pre
  = prevQuestion && prevQuestion.question_type !== 'material' ?
    prevQuestion.content.serial_number :
    prevRootQuestion ? prevRootQuestion!.content.serial_number : '';
  const cur = question.content.serial_number;

  if (prevQuestion || prevRootQuestion && (isInSameChapter || result.number !== 1)) {
    const prevResult = parseSerialNumber(
      prevQuestion && prevQuestion.question_type !== 'material' ?
        prevQuestion.content.serial_number :
        prevRootQuestion ? prevRootQuestion!.content.serial_number : ''
    );
    if (!prevResult || prevResult.type === 'label') {
      return [];
    }
    if (/例/.test(cur)) return [];
    if (!sameSerialNumberType(prevResult, result)) {
      // 上一题题号错误，或者与上一题题号格式不一致
      return [{
        rule: EJsonErrorRule.serial_number_format_mismatch_neighbor,
        message: `与上一题题号格式不一致,${pre},${cur}`,
      }];
    }
    if (prevResult.number + 1 !== result.number) {
      return [{
        rule: EJsonErrorRule.serial_number_discontinuous,
        message: `题号不连续,${pre},${cur}`,
      }];
    }
  } else if (result.number !== 1) {
    return [{
      rule: EJsonErrorRule.serial_number_not_first,
      message: `题号应该从第一个编号开始,${question.content.serial_number}`,
    }];
  }
  return [];
}

// 校验材料题，题干上的空的序号是否和题号一致
function validateMaterialBlankSn(question: IQuestionNode) {
  const re = /data-sn-text="([^"]+)"/g;
  const body = question.content.body;
  let match = re.exec(body);
  const sns: string[] = [];
  while (match) {
    sns.push(match[1]);
    match = re.exec(body);
  }
  if (!sns.length) return [];
  const snSet = new Set(sns);
  if (sns.length !== snSet.size) {
    return [{
      rule: EJsonErrorRule.material_blank_sn,
      message: '材料题题干中的题号有重复',
    }];
  }
  const questions = question.children.filter((n) => n.node_type === 'question') as IBasicQuestionNode[];
  if (snSet.size !== questions.length) {
    return [{
      rule: EJsonErrorRule.material_blank_sn,
      message: '材料题题干中的题号与题目数量不同',
    }];
  }
  for (const q of questions) {
    if (!snSet.has(q.content.serial_number)) {
      return [{
        rule: EJsonErrorRule.material_blank_sn,
        message: `材料题题干中的题号与题目题号不同,${q.content.serial_number}`,
      }];
    }
  }
  return [];
}

/**
 * 检验解析是否存在作答空间下划线的脏数据
 **/
function checkAnalysisBlank(content: string) {
  const reg = /data-label="blank"/g;
  const match = content.match(reg);
  if (match && match.length) {
    return true;
  }
  return false;
}

// 校验试题
function validateQuestion(question: IQuestionNode, prevQuestion?: IQuestionNode, parent?: TJsonNode, stopIterateChildren?: () => void) {
  let error: IJsonErrorInfo[] = [];
  const { source, body } = question.content;
  if (stopIterateChildren) {
  }

  // 添加图片验证
  error = error.concat(validateQuestionImage(question));

  if (/^\([\s\S]*?\)$/.test(source) || /^（[\s\S]*?）$/.test(source) || /^【[\s\S]*?】$/.test(source)) {
    error.push({
      rule: EJsonErrorRule.source_has_bracket,
      message: '试题来源不应该有括号',
    });
  }
  if (/data-label="source"/.test(body) && body.match(/data-label="source"/g)!.length > 1) {
    error.push({
      rule: EJsonErrorRule.multi_source,
      message: '试题来源不应该有多个',
    });
  }
  if (!question.children.length) {
    error = error.concat(validateAnswer(question));
    const isMaterialSub = parent && parent.node_type === 'question' && parent.question_type === 'material';
    const isEmptyBlank = question.question_type === 'blank';
    const tag = isMaterialSub || isEmptyBlank;
    if (!isChoiceQuestion(question) && isEmptyHtml(question.content.body, !tag)) {
      error.push({
        rule: EJsonErrorRule.question_content_empty,
        message: '题干不能为空',
      });
    }
  }
  if (isChoiceQuestion(question)) {
    error = error.concat(validateChoice(question, prevQuestion));
  } else if (question.question_type === 'true_or_false') {
    error = error.concat(validateTrueOrFalse(question));
  } else if (question.question_type === 'blank') {
    error = error.concat(validateBlank(question));
  } else {
    const { children } = question;
    const questionChildren = children.filter((node) => node.node_type === 'question');
    if (question.question_type === 'material') {
      error = error.concat(validateMaterialBlankSn(question));
      if (!questionChildren.length) {
        error.push({
          rule: EJsonErrorRule.subs_is_empty,
          message: '材料题必须有小题',
        });
      }
    }
    if (children.length && questionChildren.length < 2) {
      error.push({
        rule: EJsonErrorRule.subs_question_min_limit,
        message: '小题数量至少为2',
      });
    }
    // 如果有三级小题，并且三级小题存在题号相同的题，需要报错
    if (questionChildren.length > 1) {
      // 继续向下
      const secondLevelQuestions = questionChildren;
      // 二维数组
      const thirdLevelQuestions = secondLevelQuestions.map((node) => node.children.filter((_node) => _node.node_type === 'question'));
      if ((thirdLevelQuestions as any).flat().length && (thirdLevelQuestions as any).flat().some((v) => v.content.analysis.trim())) {
        const serialNumbers = (thirdLevelQuestions as any).flat().map((node) => node.content.serial_number);
        const serialNumberSet = new Set(serialNumbers);
        if (serialNumbers.length !== serialNumberSet.size) {
          error.push({
            rule: EJsonErrorRule.third_level_serial_number_duplicate,
            message: '三级小题存在重复题号，请检查解析位置是否正确！',
          });
        }
      }
    }
  }
  return error;
}

// 校验表格单元格
function validateTableCells(content: string): IJsonErrorInfo[] {
  const errors: IJsonErrorInfo[] = [];

  // 匹配所有table标签及其内容
  const tableRegex = /<table[^>]*>([\s\S]*?)<\/table>/g;
  let tableMatch;

  while ((tableMatch = tableRegex.exec(content)) !== null) {
    const tableContent = tableMatch[1];

    // 匹配所有td标签及其内容
    const tdRegex = /<td[^>]*>([\s\S]*?)<\/td>/g;
    const tdContents: string[] = [];
    let tdMatch;

    while ((tdMatch = tdRegex.exec(tableContent)) !== null) {
      // 获取td内容,去除HTML标签和空格
      const tdContent = tdMatch[1]
        .replace(/<[^>]+>/g, '')
        .replace(/&nbsp;/g, ' ')
        .trim();
      tdContents.push(tdContent);
    }

    // 检查是否有的单元格有内容,有的没有
    const hasContent = tdContents.some((cellContent) => cellContent.length > 0);
    const hasEmpty = tdContents.some((cellContent) => cellContent.length === 0);

    if (hasContent && hasEmpty) {
      errors.push({
        rule: EJsonErrorRule.table_cell_inconsistent,
        message: '表格中存在内容不一致的单元格',
      });
      // 一个表格报错就够了
      break;
    }
  }

  return errors;
}

// 修改 validateContent 函数
function validateContent(node: TJsonNode) {
  const error: IJsonErrorInfo[] = [];
  const { content: { body } } = node;
  const chineseEnglishBracketReg = /(\([^\)]*?）)|(（[^）]*?\))/g;

  // 检查_sequence中是否有重复的analysis
  if (node.node_type === 'question' && node.content._sequence) {
    const analysisItems = node.content._sequence.filter(item => item === 'analysis');
    if (analysisItems.length > 1) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '解析重复，检查是否有无题号解析内容或者解析题号重复',
      });
    }
  }
  // 不对称的括号
  if (body) {
    // 检查是否包含异常字符〗
    if (body.includes('〗')) {
      error.push({
        rule: EJsonErrorRule.content_invalid_character,
        message: '内容包含异常字符〗',
      });
    }
    
    // 检查body开头是否为点号
    const bodyTextContent = body.replace(/<[^>]*>/g, '').trim();
    if (bodyTextContent.startsWith('.')) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '开头的.可能没进题号',
      });
    }

    // 检查body中是否包含■符号
    if (body.includes('■')) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '■只能在答案中出现',
      });
    }

    if (/data-label="choice_option"/g.test(body)) {
      error.push({
        rule: EJsonErrorRule.error_body_option,
        message: '题干中存在选项',
      });
    }
    const leftCount = (body.match(/[(（]/g) || []).length;
    const rightCount = (body.match(/[)）]/g) || []).length;
    if (leftCount !== rightCount) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '括号不对称',
      });
    }

    if (chineseEnglishBracketReg.test(body)) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '内容中有异常的中英文括号组合',
      });
    }
    const latexStraightRes = checkHtmlStraight(body);
    if (latexStraightRes[0]) {
      error.push({
        rule: EJsonErrorRule.error_straight_latex,
        message: `公式正斜体异常，题干内容，${latexStraightRes[1]}`,
      });
    }
  }
  if (node.node_type === 'question') {
    const { content: { answer, analysis, source, choices } } = node;
    if (source) {
      // 检查来源中是否包含异常字符〗
      if (source.includes('〗')) {
        error.push({
          rule: EJsonErrorRule.content_invalid_character,
          message: '来源包含异常字符〗',
        });
      }
      
      if (chineseEnglishBracketReg.test(source)) {
        error.push({
          rule: EJsonErrorRule.node_content_error,
          message: '来源中有异常的中英文括号组合',
        });
      }
    }
    if (choices && choices.length) {
      choices.forEach((item) => {
        let hasError = false;
        
        // 检查选项中是否包含异常字符〗
        if (item.option.includes('〗')) {
          error.push({
            rule: EJsonErrorRule.content_invalid_character,
            message: `选项${item.letter}包含异常字符〗`,
          });
        }
        
        if (chineseEnglishBracketReg.test(item.option)) {
          hasError = true;
        }
        if (hasError) {
          error.push({
            rule: EJsonErrorRule.node_content_error,
            message: '选项中有异常的中英文括号组合',
          });
        }
        if (item.option.match(/^<p[^<>]*><span data-label="tab">/)) {
          error.push({
            rule: EJsonErrorRule.choice_start_with_tab,
            message: '选择题选项不能以制表符开头',
          });
        }
        const latexStraightRes = checkHtmlStraight(item.option);
        if (latexStraightRes[0]) {
          error.push({
            rule: EJsonErrorRule.error_straight_latex,
            message: `公式正斜体异常，选项内容${item.letter}，${latexStraightRes[1]}`,
          });
        }
      });
    }
    if (answer && answer.length) {
      answer.forEach((item) => {
        let hasError = false;
        
        // 检查答案中是否包含异常字符〗
        if (item.includes('〗')) {
          error.push({
            rule: EJsonErrorRule.content_invalid_character,
            message: '答案包含异常字符〗',
          });
        }
        
        if (chineseEnglishBracketReg.test(item)) {
          hasError = true;
        }
        if (hasError) {
          error.push({
            rule: EJsonErrorRule.node_content_error,
            message: '答案中有异常的中英文括号组合',
          });
        }

        const latexStraightRes = checkHtmlStraight(item);
        if (latexStraightRes[0]) {
          error.push({
            rule: EJsonErrorRule.error_straight_latex,
            message: `公式正斜体异常，答案内容，${latexStraightRes[1]}`,
          });
        }
      });
    }
    if (analysis) {
      // 检查解析中是否包含异常字符〗
      if (analysis.includes('〗')) {
        error.push({
          rule: EJsonErrorRule.content_invalid_character,
          message: '解析包含异常字符〗',
        });
      }
      
      if (chineseEnglishBracketReg.test(analysis)) {
        error.push({
          rule: EJsonErrorRule.node_content_error,
          message: '解析中有异常的中英文括号组合',
        });
      }

      const latexStraightRes = checkHtmlStraight(analysis);
      if (latexStraightRes[0]) {
        error.push({
          rule: EJsonErrorRule.error_straight_latex,
          message: `公式正斜体异常，解析内容，${latexStraightRes[1]}`,
        });
      }
      if (checkAnalysisBlank(analysis)) {
        error.push({
          rule: EJsonErrorRule.analysis_blank,
          message: '解析中有作答空间',
        });
      }
    }
  }
  // 添加表格单元格验证
  if (body) {
    error.push(...validateTableCells(body));
  }

  if (node.node_type === 'question') {
    const { content: { answer, analysis, source, choices } } = node;
    // 验证答案中的表格
    if (answer && answer.length) {
      answer.forEach((item) => {
        error.push(...validateTableCells(item));
      });
    }
    // 验证解析中的表格
    if (analysis) {
      error.push(...validateTableCells(analysis));
    }
    // 验证来源中的表格
    if (source) {
      error.push(...validateTableCells(source));
    }
    // 验证选项中的表格
    if (choices && choices.length) {
      choices.forEach((item) => {
        error.push(...validateTableCells(item.option));
      });
    }
  }

  return error;
}

function validateChapter(chapter: IChapterNode) {
  const {
    // eslint-disable-next-line camelcase
    node_name,
    content: { body },
    children,
  } = chapter;
  const tagRegex = /(?<=<[^>]+>)([\s\S]*?)(?=<\/[^>]+>)/g;
  const bodyContent = body.match(tagRegex);
  const blankReg = /^(&nbsp;|\s).*/g;

  if (isEmptyText(node_name) || isEmptyHtml(body)) {
    return [
      {
        rule: EJsonErrorRule.chapter_content_empty,
        message: '目录内容不能为空',
      }
    ];
  }
  if (bodyContent && bodyContent.length > 0 && blankReg.test(bodyContent[0])) {
    return [
      {
        rule: EJsonErrorRule.chapter_content_start_with_blank,
        message: '目录内容以空格开头',
      }
    ];
  }
  if (!children.length) {
    return [{
      rule: EJsonErrorRule.chapter_children_empty,
      message: '目录子节点内容不能为空',
    }];
  }
  return [];
}

/**
 * 验证段落节点的有效性
 * @param paragraph
 * @param parent
 * @returns
 */
function validateParagraph(paragraph: IParagraphNode) {
  const error: IJsonErrorInfo[] = [];
  if (
    /<span\s+[^>]*data-label="blank"[^>]*>[\s&nbsp;]*<\/span>/.test(
      paragraph.content.body.replace(/&nbsp;/g, ' '
      ).trim())
  ) {
    error.push({
      rule: EJsonErrorRule.paragraph_content_error,
      message: '段落内不能有空的作答横线',
    });
  }
  if (isEmptyHtml(paragraph.content.body)) {
    error.push({
      rule: EJsonErrorRule.paragraph_content_empty,
      message: '段落内容不能为空',
    });
  }
  if (paragraph.children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '段落不能有子节点',
    });
  }
  if (/data-label="source"/.test(paragraph.content.body)) {
    error.push({
      rule: EJsonErrorRule.paragraph_content_error,
      message: '段落内不能有题源',
    });
  }
  return error;
}

function getPrevQuestion(siblings: TJsonNode[], index: number) {
  for (let i = index - 1; i >= 0; i -= 1) {
    const node = siblings[i];
    if (node.node_type === 'question') {
      if (/例/.test(node.content.serial_number)) continue;
      return node;
    }
  }
}

function validateQuestionImage(question: IQuestionNode) {
  const { body } = question.content;
  if (question.content.level !== 1) {
    return [];
  }

  // Check if body contains "图" character
  if (!/图/.test(body)) {
    return [];
  }

  // Check for img tags
  const hasImage = /<img[^>]+>/.test(body);

  if (!hasImage) {
    return [{
      rule: EJsonErrorRule.question_missing_image,
      message: '题干提到图但缺少图片',
    }];
  }

  return [];
}

export function validateJsonNodes(nodes: TJsonNode[]) {
  const parentMap = new Map<IQuestionNode, TJsonNode>();
  for (const { node, parent, stopIterateChildren } of iterateNode(nodes)) {
    if (
      parent &&
      (parent.node_type !== 'question' || parent.question_type === 'material') &&
      node.node_type === 'question'
    ) {
      parentMap.set(node, parent);
    }
    if (node.node_type === 'question' && node.question_type !== 'material') {
      stopIterateChildren!();
    }
  }
  /**
   * 获取试题节点的根标题节点
   * @param question
   * @returns
   */
  function getRootQuestionChapter(question?: IQuestionNode) {
    if (!question) {
      return;
    }
    let parent = parentMap.get(question);
    if (parent && parent.node_type === 'question') {
      parent = parentMap.get(parent);
    }
    return parent;
  }

  /**
   * 是否是例题
   * @param node
   * @returns
   */
  function getExampleNode(node: IQuestionNode) {
    return /例/.test(node.content.serial_number);
  }

  function validExampleListQuestion(
    question: IQuestionNode, prevQuestion: IQuestionNode
  ) {
    const result = parseSerialNumber(question.content.serial_number);
    if (prevQuestion && result) {
      const prevResult = parseSerialNumber(prevQuestion.content.serial_number);
      if (!prevResult || prevResult.type === 'label' || result.type === 'label' || result.number === 1) {
        return [];
      }
      if (prevResult.number + 1 !== result.number) {
        return [{
          rule: EJsonErrorRule.serial_number_discontinuous,
          message: '例题题号不连续',
        }];
      }
      return [];
    } if (result && !prevQuestion && result.type !== 'label' && result.number !== 1) {
      return [{
        rule: EJsonErrorRule.serial_number_not_first,
        message: `题号应该从第一个编号开始,${question.content.serial_number}`,
      }];
    }
    return [];
  }

  const exampleNodeList: IQuestionNode[] = [];
  const rootQuestions = findAllNodes(nodes, ({ node }) => {
    return node.node_type === 'question' && node.question_type !== 'material';
  }) as IQuestionNode[];
  const rootQuestionIndexMap = new Map(rootQuestions.map((q, i) => [q, i]));
  for (const { node, siblings, index, parent, stopIterateChildren } of iterateNode(nodes)) {
    let error = node.errorInfo ?? [];
    if (node.node_type === 'chapter') {
      error = [
        ...error,
        ...validateChapter(node)
      ];
    } else if (node.node_type === 'paragraph') {
      error = [
        ...error,
        ...validateParagraph(node)
      ];
    } else if (node.node_type === 'question') {
      // 例题单独排序
      if (getExampleNode(node)) {
        exampleNodeList.push(node);
      }
      // 上一题
      const prevQuestion = getPrevQuestion(siblings, index);
      // 跨章节上一题
      const rootIndex = rootQuestionIndexMap.get(node);
      const isRootQuestion = !parent || parent.node_type !== 'question' || parent.question_type === 'material';
      const prevRootQuestion = rootIndex != null && isRootQuestion ? rootQuestions[rootIndex - 1] : undefined;
      const isInSameChapter = getRootQuestionChapter(node) === getRootQuestionChapter(prevRootQuestion);
      error = [
        ...error,
        ...validateQuestionContentLevel(node, parent),
        ...validateQuestionSerialNumber(node, prevQuestion, prevRootQuestion, isInSameChapter),
        ...validateQuestion(node, prevQuestion, parent, stopIterateChildren)
      ];
    }
    error = [
      ...error,
      ...validateContent(node)
    ];
    node.errorInfo = error.length ? error : undefined;
  }
  // 例题单独排序
  exampleNodeList.map((node, index) => {
    let error = node.errorInfo ?? [];
    const prevQuestion = exampleNodeList[index - 1];
    error = [
      ...error,
      ...validExampleListQuestion(node, prevQuestion)
    ];
    node.errorInfo = error.length ? error : undefined;
  });
  return nodes;
}
