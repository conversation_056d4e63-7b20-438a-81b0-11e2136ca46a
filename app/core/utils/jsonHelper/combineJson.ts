import { findAllNodes, iterateNode } from '../treeHelper';
import {
  checkQuestionType,
  EJsonErrorRule,
  IChapterNode,
  IJsonErrorInfo,
  IQuestionNode,
  TJsonNode
} from '../htmlToJsonV4';
// import { mergeQuestionExplanation } from '../htmlToJsonV4/helper/cleanQuestion';
import { findChoicesInBody } from '../htmlToJsonV4/helper/convertElement';

// 打平所有试题
function getLeafQuestions(nodes: TJsonNode[]) {
  const results: IQuestionNode[] = [];
  for (const { node } of iterateNode(nodes)) {
    if (node.node_type === 'question') {
      results.push(node);
    }
  }
  return results;
}

// 如果每层仅1个章节，打平
function flatChapterIfOnlyOne(nodes: TJsonNode[]) {
  let nodeTree = nodes;
  while (nodeTree.length === 1 && nodeTree[0].node_type === 'chapter') {
    nodeTree = nodeTree[0].children;
  }
  return nodeTree;
}

type TypeParentMap = Map<TJsonNode, TJsonNode | undefined>;

function getParentMap(nodes: TJsonNode[]) {
  const map: TypeParentMap = new Map<TJsonNode, TJsonNode | undefined>();
  for (const { node, parent } of iterateNode(nodes)) {
    map.set(node, parent);
  }
  return map;
}

function getRootQuestion(question: IQuestionNode, parentMap: TypeParentMap, withaterial = false) {
  let q = question;
  let p = parentMap.get(q);
  while (p && p.node_type === 'question' && (withaterial || p.question_type !== 'material')) {
    q = p;
    p = parentMap.get(q);
  }
  return q;
}

// 答案解析匹配：必须有相同级别、相同题号；必须在相同路径下
function match(
  question: TJsonNode,
  questionParentMap: TypeParentMap,
  answerObj: TJsonNode,
  answerParentMap: TypeParentMap
) {
  if (!question || !answerObj) {
    return false;
  }
  let q: TJsonNode | undefined = question;
  let a: TJsonNode | undefined = answerObj;
  while (q && a && q.node_type === a.node_type) {
    if (q.node_type === 'question') {
      if (!matchQuestion(q, a as IQuestionNode)) {
        return false;
      }
    } else if (q.node_type === 'chapter') {
      if (!matchChapter(q, a as IChapterNode)) {
        return false;
      }
    }
    q = questionParentMap.get(q);
    a = answerParentMap.get(a);
    if (q && q.node_type === 'question' && q.question_type === 'material') {
      q = questionParentMap.get(q);
    }
    if (a && a.node_type === 'question' && a.question_type === 'material') {
      a = answerParentMap.get(a);
    }
  }
  return true;
}

function matchQuestion(a: IQuestionNode, b: IQuestionNode) {
  return a.content.level === b.content.level && a.content.serial_number === b.content.serial_number;
}

function matchChapter(a: IChapterNode, b: IChapterNode) {
  if (a.content && a.content.level !== b.content.level) {
    return false;
  }
  return a.node_name === b.node_name;
}

// 合并答案解析
function combine(question: IQuestionNode, answerObj: IQuestionNode, newVersion, questionParent?: IQuestionNode) {
  if (question.content) {
    if (answerObj.content.answer && answerObj.content.answer.length) {
      if (question.content.answer.some((i) => i)) {
        addError(question, [{
          rule: EJsonErrorRule.duplicate_answer,
          message: '合并试题和答案时，答案重复（题干部分也含有答案）',
        }]);
      }
      question.content.answer = answerObj.content.answer;
      if (answerObj.content.correct) {
        question.content.correct = answerObj.content.correct;
      }
      const { blank_count, bracket_count, answer } = question.content;
      const canChoice = (blank_count || bracket_count || 0) < 2 || answer.length < 2;
      if (newVersion && canChoice && !question.content.choices && question.content.meta) {
        const res = findChoicesInBody(question.content.meta?.nodes);
        if (res) {
          const [body, choices] = res;
          if (choices.length) {
            question.content.body = body;
            question.content.choices = choices;
          }
        }
      }
      // 再次猜测题型
      question.question_type = checkQuestionType(question.content);
    }
    if (answerObj.content.analysis) {
      if (question.content.analysis) {
        addError(question, [{
          rule: EJsonErrorRule.duplicate_answer,
          message: '合并试题和答案时，解析重复（题干部分也含有解析）',
        }]);
      }
      question.content.analysis = answerObj.content.analysis;
    }
    const extraList = answerObj.content._sequence.filter((item) => item.startsWith('extra'));
    if (extraList && extraList.length) {
      extraList.map((extraKey) => {
        if (question.content[extraKey]) {
          addError(question, [{
            rule: EJsonErrorRule.duplicate_answer,
            message: '合并试题和答案时，附加内容重复（题干部分也含有附加内容）',
          }]);
        }
        if (questionParent && questionParent.question_type === 'material') {
          let extraParentLength = 0;
          for (const key in questionParent.content) {
            if (Object.prototype.hasOwnProperty.call(questionParent.content, key)) {
              if (key.startsWith('extra')) extraParentLength++;
            }
          }
          questionParent.content[`extra${extraParentLength + 1}`] = answerObj.content[extraKey];
        } else {
          question.content[extraKey] = answerObj.content[extraKey];
        }
      });
    }
    if (answerObj.content._sequence && question.content._sequence) {
      answerObj.content._sequence.map((item) => {
        if (!question.content._sequence.includes(item)) {
          if (item.startsWith('extra')) {
            const extraList = question.content._sequence.filter((item) => item.startsWith('extra'));
            const length = extraList.length;
            question.content._sequence.push(`extra${length + 1}`);
          } else {
            question.content._sequence.push(item);
          }
        }
      });
    }
  }
  if (answerObj.attributes) { // 兼容旧版
    question.attributes = question.attributes || {};
    Object.keys(answerObj.attributes).forEach((key) => {
      question.attributes![`answer-${key}`] = answerObj.attributes![key];
    });
  }
  if (answerObj.imgId) {
    question.answerImgId = answerObj.imgId;
  }
}

export function combineJson(questionJson: TJsonNode[], answerJson: TJsonNode[], newVersion = true) {
  if (!questionJson.length) {
    return answerJson;
  }
  if (!answerJson.length) {
    return questionJson;
  }
  const flatQuestionJson = flatChapterIfOnlyOne(questionJson);
  const questionParentMap = getParentMap(flatQuestionJson);
  const answerParentMap = getParentMap(answerJson);
  const questions = getLeafQuestions(flatQuestionJson);
  const answerObjs = getLeafQuestions(answerJson);
  // 如果数量相等，则简单地一对一匹配
  const simpleMatch = questions.length === answerObjs.length;
  let mismatchNodes: TJsonNode[] = [];
  let index = 0;
  questions.forEach((question) => {
    const answerObj = answerObjs[index];
    if (!simpleMatch && !match(question, questionParentMap, answerObj, answerParentMap)) {
      return;
    }
    index += 1;
    if (simpleMatch && !matchQuestion(question, answerObj)) {
      // 答案未匹配到试题
      mismatchNodes.push(addError(answerObj, [{
        rule: EJsonErrorRule.combine_answer_mismatch,
        message: '合并试题和答案时，答案找不到对应试题',
      }]));
      return;
    }
    // 合并大题的解析
    const q = getRootQuestion(question, questionParentMap);
    const a = getRootQuestion(answerObj, answerParentMap);
    if (q !== question && q.content.level === a.content.level && a.content.analysis) {
      q.content.analysis = a.content.analysis;
    }
    // 材料题把小题的附加内容都放到 材料题题干上
    const q2 = getRootQuestion(question, questionParentMap, true);

    if (q2 !== question) {
      combine(question, answerObj, newVersion, q2);
    } else {
      combine(question, answerObj, newVersion);
    }
    if (answerObj.content.body || answerObj.content.choices) {
      addError(question, [{
        rule: EJsonErrorRule.combine_answer_mismatch,
        message: '合并试题和答案时，答案中存在题干内容',
      }]);
    }
  });
  // mergeQuestionExplanation(questionJson, true);
  const mismatchAnswer = answerObjs.slice(index);
  mismatchNodes = mismatchNodes.concat(mismatchAnswer.map((n) => addError(n, [{
    rule: EJsonErrorRule.combine_answer_mismatch,
    message: '合并试题和答案时，答案找不到对应试题',
  }])));
  const answerParagraphs = findAllNodes(answerJson, ({ node }) => node.node_type === 'paragraph');
  mismatchNodes = mismatchNodes.concat(answerParagraphs.map((n) => addError(n, [{
    rule: EJsonErrorRule.combine_answer_mismatch,
    message: '合并试题和答案时，答案中存在段落',
  }])));
  return [...questionJson, ...mismatchNodes];
}

function addError(node: TJsonNode, errors: IJsonErrorInfo[]) {
  node.errorInfo = [
    ...node.errorInfo || [],
    ...errors
  ];
  return node;
}
