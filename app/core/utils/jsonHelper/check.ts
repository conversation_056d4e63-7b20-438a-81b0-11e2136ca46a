import {
  AREA_PROMPT,
  CHAPTER_SEQUENCE_PROMPT,
  EXTRACT_SCORE_PROMPT,
  SAME_OPTIONS_PROMPT
} from '../aiEdit/prompts/prompts';
import { OpenAIService } from '../aiEdit/OpenAI';
import { findAllNodes, iterateNode } from '../treeHelper';
import { ParallelController } from '../helper';

// 圆圈数字常量
export const CIRCLE_NUMBERS = [
  '①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩',
  '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰', '⑱', '⑲', '⑳',
  '㉑', '㉒', '㉓', '㉔', '㉕', '㉖', '㉗', '㉘', '㉙', '㉚',
  '㉛', '㉜', '㉝', '㉞', '㉟', '㊱', '㊲', '㊳', '㊴', '㊵',
  '㊶', '㊷', '㊸', '㊹', '㊺', '㊻', '㊼', '㊽', '㊾', '㊿'
];

// 中文数字常量
export const CHINESE_NUMBERS = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
  '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十'];

/**
 * 从给定的字符串中提取最后一个符号的 `symbolId`。
 *
 * @param {string} content 输入的字符串，可能包含多个 `symbol="..."` 格式的内容
 * @return {string} 返回最后一个 `symbolId`，如果未找到则返回空字符串
 */
function extractSymbolId(content: string): string {
  if (!content) return '';

  const symbolMatches = content.match(/symbol="([^"]+)"/g);
  if (symbolMatches && symbolMatches.length > 0) {
    const lastSymbol = symbolMatches[symbolMatches.length - 1];
    const idMatch = lastSymbol.match(/symbol="([^"]+)"/);
    if (idMatch && idMatch[1]) {
      return idMatch[1];
    }
  }
  return '';
};

/**
 * 格式化错误信息
 * @param errorInfo 原始错误信息
 * @param node 节点对象
 * @returns 格式化后的错误信息
 */
function formatErrorInfo(errorInfo: any, node: any) {
  // 提取 symbol ID
  let symbolId = '';
  if (node.content && node.content.body) {
    symbolId = extractSymbolId(node.content.body);
  }
  // 如果有 symbolId，就直接使用
  if (errorInfo.symbol_id) {
    symbolId = errorInfo.symbol_id;
  }

  return {
    id: symbolId,
    keywords: errorInfo.keywords,
    type: 'text',
    error_code: errorInfo.error_code || 100,
    level: errorInfo.level || 0,
    error_info: errorInfo.message || errorInfo.error_info || '',
    fix_info: errorInfo.fix_info || '',
  };
}

/**
 * 移除字符串中的HTML标签。
 *
 * @param {string} html 输入的包含HTML标签的字符串。
 * @param {boolean} removeSpaces=true 是否移除空格。
 * @param {boolean} keepSrc=false 保留 src。
 * @return {string} 去除HTML标签后的纯文本字符串。
 */
function removeHtmlTags(html: string, removeSpaces = true, keepSrc = false): string {
  if (!html) return '';
  return html
    .replace(/(&nbsp;|\s+)/g, removeSpaces ? '' : ' ')
    .replace(/<img[^>]*src=["']([^"']*?)["'][^>]*>/g, keepSrc ? ' $1 ' : '')
    .replace(/<[^>]*>/g, '');
}

/**
 * 检查文本中圆圈数字的完整性，包括是否存在重复或不连续的情况
 *
 * @param {string} text 输入的文本内容
 * @return {{hasError: boolean, errorType?: 'duplicate' | 'discontinuous', details: string, circleNumbers: string[]}} 结果
 */
function checkCircleNumbers(text: string): {
  hasError: boolean;
  errorType?: 'duplicate' | 'discontinuous';
  details: string;
  circleNumbers: string[];
} {
  // 先移除HTML标签
  const plainText = removeHtmlTags(text);

  // 匹配所有圆圈数字符号
  const circleNumbersRegex = new RegExp(`[${CIRCLE_NUMBERS.join('')}]`, 'g');
  const circleNumbers = plainText.match(circleNumbersRegex) || [];
  if (circleNumbers.length === 0) {
    return { hasError: false, details: '', circleNumbers: [] };
  }

  // 检查是否有重复
  const seen = new Set<string>();
  const duplicates: string[] = [];

  for (const num of circleNumbers) {
    if (seen.has(num)) {
      duplicates.push(num);
    } else {
      seen.add(num);
    }
  }

  if (duplicates.length > 0) {
    return {
      hasError: true,
      errorType: 'duplicate',
      details: `${duplicates.join(',')}重复出现`,
      circleNumbers,
    };
  }

  // 检查是否连续
  const expectedOrder = CIRCLE_NUMBERS;
  const uniqueNumbers = [...seen].sort((a, b) =>
    expectedOrder.indexOf(a) - expectedOrder.indexOf(b)
  );

  for (let i = 0; i < uniqueNumbers.length; i += 1) {
    if (uniqueNumbers[i] !== expectedOrder[i]) {
      return {
        hasError: true,
        errorType: 'discontinuous',
        details: `序号不连续，缺少${expectedOrder[i]}`,
        circleNumbers,
      };
    }
  }

  return { hasError: false, details: '', circleNumbers: [] };
}

/**
 * 根据给定的大小获取下一个圆圈号码。
 * 如果大小超出范围，则返回空字符串。
 *
 * @param {number} size 圆圈的大小
 * @return {string} 返回对应的圆圈号码，如果超出范围则返回空字符串
 */
// @ts-ignore
function getNextCircleNumber(size: number): string {
  return size < CIRCLE_NUMBERS.length ? CIRCLE_NUMBERS[size] : '';
}

/**
 * 检查文本中的图序标记是否存在格式冲突、重复或不连续的问题。
 *
 * @param {string} text 输入的文本内容。
 * @return {object} 检查结果。
 * @return {boolean} return.hasError 是否存在错误。
 * @return {('duplicate'|'discontinuous') | undefined} return.errorType 错误类型，可能为重复(duplicate)或不连续(discontinuous)。
 * @return {string} return.details 错误的详细描述信息。
 * @return {string} return.suggestedFix 修复建议。
 * @return {array} return.imageMarkers 检测到的所有图序标记列表。
 */
function checkImageSequence(text: string): {
  hasError: boolean;
  errorType?: 'duplicate' | 'discontinuous';
  details: string;
  suggestedFix: string;
  imageMarkers: string[];
} {
  // 提取img标签中的data-image-tip属性
  const imgTipRegex = /<img[^>]*data-image-tip=["']([^"']*?)["'][^>]*>/g;
  const imgTipMatches: string[] = [];
  let match;

  // 收集所有img标签中的data-image-tip属性值
  while ((match = imgTipRegex.exec(text)) !== null) {
    if (match[1]) {
      imgTipMatches.push(match[1]);
    }
  }

  // 将img标签中的data-image-tip属性内容加入到处理的文本中
  const plainText = imgTipMatches.join(' ');

  // 检查两种格式的图序标记
  const chineseImageRegex = /图(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十)/g;
  const arabicImageRegex = /图([1-9]|[1-9][0-9])/g;

  const chineseMatches = plainText.match(chineseImageRegex) || [];
  const arabicMatches = plainText.match(arabicImageRegex) || [];

  // 如果两种格式都没有图序标记，直接返回无错误
  if (chineseMatches.length === 0 && arabicMatches.length === 0) {
    return { hasError: false, details: '', suggestedFix: '', imageMarkers: [] };
  }

  // 如果同时存在两种格式，建议统一格式
  if (chineseMatches.length > 0 && arabicMatches.length > 0) {
    return {
      hasError: true,
      errorType: 'duplicate',
      details: `同时存在中文数字和阿拉伯数字的图序(${chineseMatches.join(',')} 和 ${arabicMatches.join(',')})`,
      suggestedFix: '建议统一使用一种格式，如全部使用"图一、图二..."或"图1、图2..."',
      imageMarkers: [...chineseMatches, ...arabicMatches],
    };
  }

  // 处理中文数字格式的图序
  if (chineseMatches.length > 0) {
    const imageMarkers = chineseMatches;
    const numbers = imageMarkers.map((marker) => marker.substring(1));

    // 检查是否有重复
    const seen = new Set<string>();
    const duplicates: string[] = [];

    for (const num of numbers) {
      if (seen.has(num)) {
        duplicates.push(`图${num}`);
      } else {
        seen.add(num);
      }
    }

    if (duplicates.length > 0) {
      const nextNumber = CHINESE_NUMBERS[seen.size];
      return {
        hasError: true,
        errorType: 'duplicate',
        details: `${duplicates.join(',')}重复出现`,
        suggestedFix: `建议添加"图${nextNumber}"保持连续性`,
        imageMarkers,
      };
    }

    // 检查是否连续
    const expectedOrder = CHINESE_NUMBERS;
    const uniqueNumbers = [...seen].sort((a, b) =>
      expectedOrder.indexOf(a) - expectedOrder.indexOf(b)
    );

    for (let i = 0; i < uniqueNumbers.length; i += 1) {
      if (uniqueNumbers[i] !== expectedOrder[i]) {
        return {
          hasError: true,
          errorType: 'discontinuous',
          details: `图序不连续，缺少"图${expectedOrder[i]}"`,
          suggestedFix: `建议添加"图${expectedOrder[i]}"或重新排序`,
          imageMarkers,
        };
      }
    }

    return { hasError: false, details: '', suggestedFix: '', imageMarkers };
  }

  // 处理阿拉伯数字格式的图序
  if (arabicMatches.length > 0) {
    const imageMarkers = arabicMatches;
    const numbers = imageMarkers.map((marker) => parseInt(marker.substring(1), 10));

    // 检查是否有重复
    const seen = new Set<number>();
    const duplicates: string[] = [];

    for (const num of numbers) {
      if (seen.has(num)) {
        duplicates.push(`图${num}`);
      } else {
        seen.add(num);
      }
    }

    if (duplicates.length > 0) {
      const nextNumber = seen.size + 1;
      return {
        hasError: true,
        errorType: 'duplicate',
        details: `${duplicates.join(',')}重复出现`,
        suggestedFix: `建议添加"图${nextNumber}"保持连续性`,
        imageMarkers,
      };
    }

    // 检查是否连续
    const uniqueNumbers = [...seen].sort((a, b) => a - b);

    for (let i = 0; i < uniqueNumbers.length; i += 1) {
      const expected = i + 1;
      if (uniqueNumbers[i] !== expected) {
        return {
          hasError: true,
          errorType: 'discontinuous',
          details: `图序不连续，缺少"图${expected}"`,
          suggestedFix: `建议添加"图${expected}"或重新排序`,
          imageMarkers,
        };
      }
    }

    return { hasError: false, details: '', suggestedFix: '', imageMarkers };
  }

  // 默认返回
  return { hasError: false, details: '', suggestedFix: '', imageMarkers: [] };
}

/**
 * 检查节点内容中的答案和解析是否符合要求。
 *
 * @param {any} node 待检查的节点，需包含 content 属性。
 * @return {{hasError: boolean, details: string, error_code: number}} 返回检查结果：
 * - hasError 表示是否有错误。
 * - details 为错误详情描述。
 * - error_code 为错误代码。
 */
function checkAnswerAndAnalysis(node: any): {
  hasError: boolean;
  details: string;
  error_code: number;
} {
  if (!node.content) {
    return { hasError: false, details: '', error_code: 0 };
  }

  const { answer, analysis } = node.content;

  // 检查是否有解析
  const hasAnalysis = analysis && analysis.trim().length > 0;

  // 检查答案是否为空（包括空数组或只有空字符串的数组）
  const isAnswerEmpty = !answer ||
      !Array.isArray(answer) ||
      answer.length === 0 ||
      answer.every((item) => typeof item === 'string' && item.trim() === '');

  // 如果有解析但没有答案，则报错
  if (hasAnalysis && isAnswerEmpty) {
    return {
      hasError: true,
      details: '题目只有解析，没有答案',
      error_code: 109,
    };
  }

  return { hasError: false, details: '', error_code: 0 };
}

/**
 * 检查节点内容中的地区信息是否匹配正确。
 *
 * @param {any} text 待检查的节点，需包含 content 属性。
 * @return {Promise<{hasError: boolean, details: string, error_code: number, errors?: any[]}>} 返回检查结果。
 */
async function checkAreaMatchedByLLM(text: string) {
  try {
    const openaiService = OpenAIService.getInstance();
    const { userPrompt, systemPrompt } = AREA_PROMPT(text);

    // 使用OpenAI服务检查地区匹配问题
    const res = await openaiService.createChatCompletionAndParseJSON<{ year: string, area: string }>(
      [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]
    );
    if (!res) {
      return { year: '', area: '' };
    }
    return res;
  } catch (error) {
    return { year: '', area: '' };
  }
}

/**
 * 检查选项是否存在重复项，调用大语言模型判断重复性。
 *
 * @param {string} body - 题干或上下文内容，用于大语言模型的判断依据。
 * @param {array} options - 需要检查的选项列表，每个选项包含标识符和内容。
 * @return {Promise<array>} 返回重复选项的字母数组，如果无重复则返回空数组。
 */
async function checkDuplicateOptionsByLLM(body: string, options: { letter: string, option: string }[]): Promise<any[]> {
  if (!options || options.length <= 1) {
    return [];
  }

  try {
    // 获取 OpenAI 服务实例
    const openaiService = OpenAIService.getInstance();

    // 构建提示信息
    const _cleanOptions = options.map((opt) => {
      return {
        letter: opt.letter,
        option: opt.option
          // 图片替换为 src
          .replace(/<img[^>]*src=["']([^"']+)["'][^>]*>/g, '$1')
          .replace(/<[^>]*>/g, ''),
      };
    });
    const { userPrompt, systemPrompt } = SAME_OPTIONS_PROMPT(body, _cleanOptions);

    // 使用 OpenAI 服务发送请求并解析 JSON
    const duplicateOptions = await openaiService.createChatCompletionAndParseJSON<string[]>(
      [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]
    );
    return duplicateOptions || [];
  } catch (error) {
    console.error('检查重复选项失败:', error);
    throw error;
  }
}

/**
 * 检查题目内容中的分值是否一致，并返回相关检查结果。
 *
 * @param {any} node 题目节点对象，包括题干、答案、解析及子题内容等。
 * @return {{hasError: boolean, details: string, suggestedFix: string}} 检查结果对象，包含是否有错误、错误详情和修复建议。
 */
// @ts-ignore
function checkQuestionScores(node: any) {
  // 默认返回值
  const result = {
    hasError: false,
    details: '',
    suggestedFix: '',
  };

  // 提取题干中的分值
  const bodyScore = extractScoreFromBody(node.content.body);

  // 从答案和解析中提取分值
  const { analysisScore, answerScore, totalScore } = calculateAllScores(node.content);

  // 扩展JSON数据结构，添加分值信息
  if (!node.content.scores) {
    node.content.scores = {
      body_score: bodyScore !== undefined ? bodyScore : null,
      answer_score: answerScore !== undefined ? answerScore : null,
      analysis_score: analysisScore !== undefined ? analysisScore : null,
      total_score: totalScore !== undefined ? totalScore : null,
    };
  }

  // 如果题干中没有分值，直接返回无错误（不检查分值问题）
  if (bodyScore === undefined) {
    return result;
  }

  // 检查答案是否为空
  if (!node.content.answer || node.content.answer.length === 0) {
    return result;
  }

  // 检查答案和解析中的分值是否一致
  if (analysisScore !== undefined && answerScore !== undefined && analysisScore !== answerScore) {
    result.hasError = true;
    result.details = `答案与解析分值不一致：答案中标注${answerScore}分，而解析中标注${analysisScore}分`;
    result.suggestedFix = '建议统一答案和解析中的分值标注';
    return result;
  }

  // 检查总分值与题干分值是否一致
  if (totalScore !== undefined && totalScore !== bodyScore) {
    result.hasError = true;
    result.details = `分值不一致：题干标注${bodyScore}分，而答案与解析评分点总分为${totalScore}分`;
    result.suggestedFix = '建议调整答案评分点分值或题干分值，使两者一致';
    return result;
  }

  // 如果是大题，检查小题分值之和是否等于大题总分
  if (node.children && node.children.length > 0) {
    const childrenTotalScore = calculateChildrenTotalScore(node.children);
    // 只在有子题且子题有分值时进行检查
    if (childrenTotalScore > 0 && childrenTotalScore !== bodyScore) {
      result.hasError = true;
      result.details = `大题与小题分值不一致：大题标注${bodyScore}分，但小题分值之和为${childrenTotalScore}分`;
      result.suggestedFix = '建议调整大题总分或小题分值，使两者一致';
      return result;
    }

    // 将小题总分添加到大题的分值信息中
    node.content.scores.childrenTotalScore = childrenTotalScore;
  }

  return result;
}

/**
 * 从文本中提取分数值。
 *
 * @param {string} bodyText 输入的文本内容
 * @return {number | undefined} 提取到的分值，如果未找到则返回 undefined
 */
function extractScoreFromBody(bodyText: string): number | undefined {
  if (!bodyText) return undefined;

  // 匹配括号内的分值，如"（10分）"或"(10分)"
  const scoreMatches = bodyText.match(/[（(](\d+)分[）)]/);
  if (scoreMatches && scoreMatches[1]) {
    return parseInt(scoreMatches[1], 10);
  }

  // 匹配"共XX分"格式
  const totalScoreMatch = bodyText.match(/共(\d+)分/);
  if (totalScoreMatch && totalScoreMatch[1]) {
    return parseInt(totalScoreMatch[1], 10);
  }

  return undefined;
}

/**
 * 计算内容中的所有分值，包括分析得分、答案得分和总分。
 *
 * @param {any} content 输入的内容对象，可能包含解析和答案信息。
 * @return {object} 返回一个对象，包括以下字段：analysisScore（解析得分），answerScore（答案得分），totalScore（总分）。
 */
function calculateAllScores(content: any) {
  if (!content) return {};

  // 从解析中提取分值
  let analysisScore: number | undefined;
  if (content.analysis) {
    const analysisMatches = content.analysis.match(/[（(](\d+)分[）)]/g);
    if (analysisMatches) {
      analysisScore = 0;
      for (const match of analysisMatches) {
        const scoreMatch = match.match(/(\d+)/);
        if (scoreMatch && scoreMatch[1]) {
          analysisScore += parseInt(scoreMatch[1], 10);
        }
      }
    }
  }

  // 从答案数组中提取分值
  let answerScore: number | undefined;
  if (content.answer && content.answer.length > 0) {
    answerScore = 0;
    for (const answerItem of content.answer) {
      // 跳过非字符串类型的答案项
      if (typeof answerItem !== 'string') continue;

      // 检查答案项中是否有分值标注，如"(2分)"或"（1分）"
      const scoreMatches = answerItem.match(/[（(](\d+)分[）)]/g);
      if (scoreMatches) {
        for (const match of scoreMatches) {
          const scoreMatch = match.match(/(\d+)/);
          if (scoreMatch && scoreMatch[1]) {
            answerScore += parseInt(scoreMatch[1], 10);
          }
        }
      }

      // 检查无括号的分值，如"2分"
      const directScoreMatches = answerItem.match(/(\d+)分(?![）)])/g);
      if (directScoreMatches) {
        for (const match of directScoreMatches) {
          const scoreMatch = match.match(/(\d+)/);
          if (scoreMatch && scoreMatch[1]) {
            answerScore += parseInt(scoreMatch[1], 10);
          }
        }
      }
    }

    // 如果答案数组中没有找到分值标注，则设为undefined
    if (answerScore === 0) {
      answerScore = undefined;
    }
  }

  // 计算总分值（优先使用解析分值，如果解析中没有分值则使用答案分值）
  const totalScore = analysisScore !== undefined ? analysisScore : answerScore;

  return {
    analysisScore,
    answerScore,
    totalScore,
  };
}

/**
 * 计算子节点的总分数。
 * @param {array} children 子节点数组，包含要计算分数的节点信息。
 * @return {number} 子节点总分数。
 */
function calculateChildrenTotalScore(children: any[]): number {
  let totalScore = 0;
  for (const child of children) {
    if (child.node_type === 'question' && child.content) {
      const childScore = extractScoreFromBody(child.content.body);
      if (childScore !== undefined) {
        totalScore += childScore;
      }
    }
  }

  return totalScore;
}

/**
 * 检查给定选项数组中是否存在重复选项，通过正则清理特殊字符和HTML标签后进行对比。
 * @param {array<{letter: string, option: string}>} options 包含选项字母和内容的数组
 * @return {any} 返回检查结果，包含是否有错误、重复选项详情以及附加状态和错误信息
 */
function checkDuplicateOptionsByRegex(options: { letter: string, option: string }[]): {
  hasError: boolean;
  details: string;
  duplicateOptions: { letters: string[], content: string, symbolId?: string }[];
  status?: string;
  errors?: {type: string, options: string[], symbolId?: string}[];
} {
  if (!options || options.length <= 1) {
    return { hasError: false, details: '', duplicateOptions: [], status: 'no_errors' };
  }

  // 创建一个用于存储处理后选项内容的映射
  const processedOptions: Map<string, {letters: string[], rawOptions: string[], symbolIds: string[]}> = new Map();
  const duplicateOptions: { letters: string[], content: string, symbolId?: string }[] = [];

  // 提取HTML标签中的symbol属性
  const extractSymbol = (html: string): string | undefined => {
    const symbolMatch = html.match(/symbol\s*=\s*["']([^"']*)["']/i);
    return symbolMatch ? symbolMatch[1] : undefined;
  };

  // 处理每个选项
  for (const { letter, option } of options) {
    if (!option) continue;

    // 提取symbol属性
    const symbolId = extractSymbol(option);

    // 移除HTML标签，包括img标签
    let processedOption = removeHtmlTags(option);

    // 移除空白字符和特殊字符，便于比较
    processedOption = processedOption.replace(/\s+/g, '').trim();

    if (processedOption) {
      // 检查处理后的选项是否已存在
      if (processedOptions.has(processedOption)) {
        // 添加当前选项的字母到已存在的相同选项中
        const existingData = processedOptions.get(processedOption)!;
        existingData.letters.push(letter);
        existingData.rawOptions.push(option);
        if (symbolId) {
          existingData.symbolIds.push(symbolId);
        }
      } else {
        // 添加新的选项到映射中
        processedOptions.set(processedOption, {
          letters: [letter],
          rawOptions: [option],
          symbolIds: symbolId ? [symbolId] : [],
        });
      }
    }
  }

  // 检查是否有重复选项
  for (const [content, data] of processedOptions.entries()) {
    if (data.letters.length > 1) {
      // 如果有symbol属性，添加到返回值中
      const duplicateItem: { letters: string[], content: string, symbolId?: string } = {
        letters: data.letters,
        content,
      };

      // 只有当所有重复选项都有相同的symbolId时才添加
      if (data.symbolIds.length > 0 && data.symbolIds.length === data.letters.length) {
        // 检查所有symbolId是否相同
        const allSameSymbol = data.symbolIds.every((id) => id === data.symbolIds[0]);
        if (allSameSymbol) {
          duplicateItem.symbolId = data.symbolIds[0];
        }
      }

      duplicateOptions.push(duplicateItem);
    }
  }

  // 构建返回结果
  if (duplicateOptions.length > 0) {
    const duplicateDetails = duplicateOptions.map((dup) =>
      `选项 ${dup.letters.join('、')} 内容相同: "${dup.content.substring(0, 20)}${dup.content.length > 20 ? '...' : ''}"`
    ).join('；');

    // 为了兼容原有的调用格式，添加status和errors字段
    const errors = duplicateOptions.map((dup) => ({
      type: 'text_exact_match',
      options: dup.letters,
      symbolId: dup.symbolId,
    }));

    return {
      hasError: true,
      details: `发现${duplicateOptions.length}组重复选项: ${duplicateDetails}`,
      duplicateOptions,
      status: 'found_errors',
      errors,
    };
  }

  return { hasError: false, details: '', duplicateOptions: [], status: 'no_errors' };
}

function checkQuestionScoresByLLM(node: any) {
  const _cleanBody = removeHtmlTags(node.content.body, true, true);
  const _cleanAnalysis = removeHtmlTags(node.content.analysis, true, true);
  const _cleanAnswer = removeHtmlTags(node.content.answer.join(' '), true, true);
  const scorePromptParams = {
    body: '',
    answer: '',
    analysis: '',
  };
  // 如果有 \d+ 分，就写入
  if (_cleanBody.match(/\d+分/)) {
    scorePromptParams.body = _cleanBody;
  }
  if (_cleanAnalysis.match(/\d+分/)) {
    scorePromptParams.analysis = _cleanAnalysis;
  }
  if (_cleanAnswer.match(/\d+分/)) {
    scorePromptParams.answer = _cleanAnswer;
  }
  if (!scorePromptParams.body && !scorePromptParams.answer && !scorePromptParams.analysis) {
    return {
      body_score: null,
      answer_score: null,
      analysis_score: null,
    };
  }
  const { userPrompt, systemPrompt } =
    EXTRACT_SCORE_PROMPT(scorePromptParams.body, scorePromptParams.answer, scorePromptParams.analysis);
  const openAIService = OpenAIService.getInstance();
  const res = openAIService.createChatCompletionAndParseJSON([
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt }
  ]);
  return res;
}

/**
 * 检查节点的区域信息，并根据内容匹配区域和年份。
 *
 * @param {object} node 输入的节点对象，包含相关的内容和子节点信息。
 * @return {Promise<array>} 返回异步操作后的结果数组，该数组可能为空。
 */
export async function checkNodeArea(node: any) {
  // 检查题目内容
  const bracketRegex = /[(\[（【「『](.*?)[』」)\]）】]/g;
  if (node.content && (node.content.body.match(bracketRegex)?.length || node.content.source)) {
    // 提取题源
    const { year, area } = await checkAreaMatchedByLLM(node.content.source || node.content.body);
    // 添加年份
    if (area || year) {
      if (!node.children.length) {
        node.area = area;
        node.year = year;
      }
    }
  }
  return [];
}
export function checkNodeCircleNumber(node: any) {
  const errors: any[] = [];
  const circleNumberResult = checkCircleNumbers(node.content.body);
  if (circleNumberResult.hasError) {
    errors.push({
      error_info: `题肢序号${circleNumberResult.errorType === 'duplicate' ? '重复' : '不连续'}：${circleNumberResult.details}`,
      error_code: 105,
    });
  }
  return errors;
}
export function checkNodeImageSequence(node: any) {
  const errors: any[] = [];
  // 检查图序标记的连续性
  const imageSequenceResult = checkImageSequence(node.content.body);
  if (imageSequenceResult.hasError) {
    errors.push({
      error_info: `图序${imageSequenceResult.errorType === 'duplicate' ? '重复' : '不连续'}：${imageSequenceResult.details}`,
      error_code: 106,
      fix_info: imageSequenceResult.suggestedFix,
    });
  }
  return errors;
}

/**
 * 检查节点的分值信息并更新节点的分值数据。
 *
 * @param {any} node 要检查分值的节点对象
 * @return {Promise<array>} 返回包含错误信息的数组
 */
export async function checkNodeScore(node: any) {
  // 检查分值问题
  const errors: any[] = [];
  const scoreResult: any = await checkQuestionScoresByLLM(node);
  if (scoreResult) {
    node.score_info = {
      body_score: scoreResult?.body_score,
      answer_score: scoreResult?.answer_score,
      analysis_score: scoreResult?.analysis_score,
    };
  }
  return errors;
}
export function checkNodeAnswerAndAnalysis(node: any) {
  const errors: any[] = [];
  const answerAnalysisResult = checkAnswerAndAnalysis(node);
  if (answerAnalysisResult.hasError) {
    errors.push({
      error_info: answerAnalysisResult.details,
      error_code: answerAnalysisResult.error_code,
    });
  }
  return errors;
}
export async function checkNodeDuplicateOptions(node: any) {
  const errors: any[] = [];
  if (node.content.choices && Array.isArray(node.content.choices) && node.content.choices.length > 1) {
    // 正则检查是否存在重复选项
    const duplicateOptionsResByReg = checkDuplicateOptionsByRegex(node.content.choices);
    if (duplicateOptionsResByReg.hasError) {
      const error = duplicateOptionsResByReg.duplicateOptions[duplicateOptionsResByReg.duplicateOptions?.length - 1];
      errors.push({
        error_info: duplicateOptionsResByReg.details,
        symbol_id: error?.symbolId,
        keywords: error.letters?.[(error.letters?.length || 1) - 1],
        error_code: 102,
      });
    }
    try {
      // 先过滤掉完全重复的选项（通过正则可以判断）
      let options = node.content.choices;

      // 如果存在重复选项，全部过滤掉
      if (duplicateOptionsResByReg.hasError && duplicateOptionsResByReg.duplicateOptions.length > 0) {
        // 收集所有需要排除的选项字母
        const excludeLetters = new Set<string>();

        duplicateOptionsResByReg.duplicateOptions.forEach((dupGroup) => {
          // 所有重复选项都排除
          if (dupGroup.letters && dupGroup.letters.length > 0) {
            dupGroup.letters.forEach((letter) => {
              excludeLetters.add(letter);
            });
          }
        });

        // 过滤选项，只保留未重复的选项
        options = options.filter((opt) => !excludeLetters.has(opt.letter));
      }

      const duplicateOptions = await checkDuplicateOptionsByLLM(node.content.body, options);

      // 如果LLM检测到重复选项
      if (duplicateOptions && duplicateOptions.length > 0) {
        const errorInfo = `选项 ${duplicateOptions[0].group.join('、')} 语义内容重复`;

        // 获取第一个重复选项的symbolId
        let symbolId = undefined;
        const firstLetter = duplicateOptions[0];

        if (firstLetter) {
          // 查找选项中对应的symbolId
          const optionWithSymbol = options.find((opt) => opt.letter === firstLetter[0]);
          if (optionWithSymbol && optionWithSymbol.option) {
            // 提取symbolId
            const symbolMatch = optionWithSymbol.option.match(/symbol\s*=\s*["']([^"']*)["']/i);
            if (symbolMatch && symbolMatch[1]) {
              symbolId = symbolMatch[1];
            }
          }
        }
        errors.push({
          error_info: errorInfo,
          error_code: 110,
          fix_info: '',
          symbol_id: symbolId,
          keywords: firstLetter[0] || '',
        });
      }
    } catch (error) {
      throw error;
    }
  }
  return errors;
}

/**
 * 检查节点是否为题目类型，并验证题目内容格式是否符合要求。
 *
 * @param {any} node 任意类型的节点对象，通常是包含题目内容的节点信息。
 * @return {Promise<array>} 返回包含错误信息的数组，每个错误包含具体的错误描述及相关信息，如果无错误则返回空数组。
 */
async function checkNodeQuestionContent(node: any): Promise<any[]> {
  if (node.node_type !== 'question') return [];
  try {
    // 并行执行所有检查
    const [
      ,
      circleNumberResult,
      imageSequenceResult,
      scoreResult,
      answerAnalysisResult,
      duplicateOptionsResult
    ] = await Promise.all([
      checkNodeArea(node),
      checkNodeCircleNumber(node),
      checkNodeImageSequence(node),
      checkNodeScore(node),
      checkNodeAnswerAndAnalysis(node),
      checkNodeDuplicateOptions(node)
    ]);

    // 合并所有检查结果
    return [
      ...(circleNumberResult || []),
      ...(imageSequenceResult || []),
      ...(scoreResult || []),
      ...(answerAnalysisResult || []),
      ...(duplicateOptionsResult || [])
    ].filter((error) => error);
  } catch (error) {
    return [{
      error_info: '检查节点内容时发生错误',
      error_code: 500,
      fix_info: error.message,
    }];
  }
}

async function checkNonQuestionContent(json: any[]) {
  // 存储提取的chapter节点信息
  const chapterInfos: string[] = [];

  // 递归遍历JSON树查找chapter节点
  function extractChapters(node: any) {
    // 检查当前节点是否为chapter类型
    if (node.node_type === 'chapter') {
      const level = node.node_level;
      const body = removeHtmlTags(node.content.body).trim();
      const nodeId = node.node_id;
      // 按照指定格式添加到结果数组
      chapterInfos.push(`${'#'.repeat(level)}${body}@${nodeId}`);
    }
    // 如果有子节点，继续递归处理
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        extractChapters(child);
      }
    }
  }

  // 处理JSON数组
  if (Array.isArray(json)) {
    for (const item of json) {
      extractChapters(item);
    }
  } else {
    // 处理单个对象
    extractChapters(json);
  }
  const { userPrompt, systemPrompt } = CHAPTER_SEQUENCE_PROMPT(chapterInfos);
  const openaiService = OpenAIService.getInstance();
  // 使用 OpenAI 服务发送请求并解析 JSON
  const chapterSequenceRes = await openaiService.createChatCompletionAndParseJSON<{
    node_id: string;
    error_info: string;
    fix_info: string;
  }[]>(
    [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ]
  );
  if (chapterSequenceRes && chapterSequenceRes.length > 0) {
    for (const chapterSequenceRe of chapterSequenceRes) {
      const { node_id, error_info, fix_info } = chapterSequenceRe;
      const targetChapterNode: any = findAllNodes(json, ({ node }) => node.node_id === node_id);
      if (targetChapterNode.length) {
        if (!targetChapterNode[0].errorInfo) {
          targetChapterNode[0].errorInfo = [{
            fix_info,
            error_info,
          }];
        } else {
          targetChapterNode[0].errorInfo.push({
            fix_info,
            error_info,
          });
        }
      }
    }
  }

  // 找到可能有分值的 nodes
  const nodes = findAllNodes(json, ({ node }) => node.node_type === 'chapter' &&
      (removeHtmlTags(node.content.body, true, true)?.match(/\d+分/g)?.length || 0) > 0);
  const parallelController = new ParallelController(20);
  for (const node of nodes) {
    const index = nodes.indexOf(node);
    parallelController.addTask(async() => {
      const { userPrompt: scoreUserPrompt, systemPrompt: scoreSystemPrompt } = EXTRACT_SCORE_PROMPT(
        removeHtmlTags(node.content.body, true, true), '', '');
      const res: any = await openaiService.createChatCompletionAndParseJSON([
        { role: 'system', content: scoreSystemPrompt },
        { role: 'user', content: scoreUserPrompt }
      ]);
      if (res) {
        node.score_info = {
          body_score: res?.body_score,
          answer_score: res?.answer_score,
          analysis_score: res?.analysis_score,
        };
      }
    }, index);
  }
  await parallelController.execute();
}

/**
 * 检查 json 中所有节点的分值信息是否正确
 * @param {array} json
 * @returns {Promise<array>} 返回包含错误信息的数组
 */
async function checkJsonScore(json: any[]) {

  // 添加错误信息的辅助函数
  const addErrorInfo = (node: any, errorInfo: string, fixInfo: string, symbolId?: string) => {
    if (!node.error_info) {
      node.error_info = [];
    }
    node.error_info.push({
      error_info: errorInfo,
      fix_info: fixInfo,
      symbol_id: symbolId,
    });
  };

  // 使用iterateNode遍历所有节点
  for (const { node } of iterateNode(json)) {
    // 如果是chapter节点，按规则1和2处理
    if (node.node_type === 'chapter' && node.children && node.children.length > 0) {
      // 检查是否有分值信息
      const hasBodyScore = node.score_info && node.score_info.body_score !== null;

      // 只筛选类型为question的子节点
      const questionChildren = node.children.filter((child) => child.node_type === 'question');

      // 如果没有question子节点，则跳过检查
      if (questionChildren.length === 0) continue;

      // 统计有分值信息的子节点（仅限question类型）
      const childrenWithScore = questionChildren.filter((child) =>
        child.score_info && child.score_info.body_score !== null
      );

      // 规则1: 如果有子节点有分值，找出没有分值的子节点并添加错误信息
      if (childrenWithScore.length > 0 && childrenWithScore.length < questionChildren.length) {
        for (const child of questionChildren) {
          if (!child.score_info || child.score_info.body_score === null) {
            const symbolId = extractSymbolId(child.content?.body);
            addErrorInfo(child, '该试题没有分值', '添加分值信息', symbolId);
          }
        }
      }

      // 规则2: 如果所有question子节点都有分值，检查总和是否与标题分值相等
      if (childrenWithScore.length === questionChildren.length && hasBodyScore) {
        // 计算子节点分值总和
        const childrenScoreSum = childrenWithScore.reduce(
          (sum, child) => sum + (child.score_info.body_score || 0), 0
        );

        // 比较总和与章节分值
        if (childrenScoreSum !== node.score_info.body_score) {
          const symbolId = extractSymbolId(node.content?.body);
          addErrorInfo(
            node,
            '该标题下试题分值与总分不相等',
            '检查试题的分值信息',
            symbolId
          );
        }
      }
    }

    // 如果是question节点，按规则3和4、5处理
    if (node.node_type === 'question') {
      // 规则3: 如果是有子节点的question，参考chapter的处理逻辑
      if (node.children && node.children.length > 0) {
        // 检查是否有分值信息
        const hasBodyScore = node.score_info && node.score_info.body_score !== null;

        // 统计有分值信息的子节点
        const childrenWithScore = node.children.filter((child) =>
          child.score_info && child.score_info.body_score !== null
        );

        // 如果有子节点有分值，找出没有分值的子节点并添加错误信息
        if (childrenWithScore.length > 0 && childrenWithScore.length < node.children.length) {
          for (const child of node.children) {
            if (!child.score_info || child.score_info.body_score === null) {
              const symbolId = extractSymbolId(child.content?.body);
              addErrorInfo(
                child,
                '该试题没有分值',
                '添加分值信息',
                symbolId
              );
            }
          }
        }

        // 如果所有子节点都有分值，检查总和是否与题目分值相等
        if (childrenWithScore.length === node.children.length && hasBodyScore) {
          // 计算子节点分值总和
          const childrenScoreSum = childrenWithScore.reduce(
            (sum, child) => sum + (child.score_info.body_score || 0), 0
          );

          // 比较总和与题目分值
          if (childrenScoreSum !== node.score_info.body_score) {
            const symbolId = extractSymbolId(node.content?.body);
            addErrorInfo(
              node,
              '该标题下试题分值与总分不相等',
              '检查试题的分值信息',
              symbolId
            );
          }
        }
      }
      // 规则4和5: 如果是无子节点的question，检查分值一致性
      else if (node.score_info) {
        const { body_score, answer_score, analysis_score } = node.score_info;

        // 当题干分值存在，且答案或解析分值至少有一个不为空时
        if (body_score !== null &&
            ((answer_score !== null) ||
             (analysis_score !== null ))) {

          // 规则5: 如果三者都有值且互不相等
          if (answer_score !== null &&
              analysis_score !== null &&
              body_score !== answer_score && body_score !== analysis_score &&
              answer_score !== analysis_score) {

            const symbolId = extractSymbolId(node.content?.body);
            addErrorInfo(
              node,
              '该试题题干分值与答案、解析不相等',
              '检查试题的分值信息',
              symbolId
            );
          }
          // 规则4: 如果答案分值与题干不等或解析分值与题干不等
          else if ((answer_score !== null && body_score !== answer_score) ||
                   (analysis_score !== null && body_score !== analysis_score)) {

            // 决定错误信息和从哪里提取symbolId
            let symbolId;
            let errorMsg;

            // 题干与答案不相等
            if (answer_score !== null && body_score !== answer_score) {
              errorMsg = '该试题题干分值与答案不相等';
              // 从答案中提取
              if (node.content?.answer && Array.isArray(node.content.answer) && node.content.answer.length > 0) {
                const lastAnswer = node.content.answer[node.content.answer.length - 1];
                symbolId = extractSymbolId(lastAnswer);
              }
            }
            // 题干与解析不相等
            else if (analysis_score !== null && body_score !== analysis_score) {
              errorMsg = '该试题题干分值与解析不相等';
              // 从解析中提取
              symbolId = extractSymbolId(node.content?.analysis);
            }

            // 如果未能提取到symbolId，则从题干中提取
            if (!symbolId) {
              symbolId = extractSymbolId(node.content?.body);
            }

            addErrorInfo(
              node,
              errorMsg,
              '检查试题的分值信息',
              symbolId
            );
          }
        }
      }
    }
    // 格式化 json
    node.error_info = node.error_info?.map((errorInfo) => formatErrorInfo(errorInfo, node));
  }

  return json;
}

/**
 * 检查 json 标题与内容的关联性
 */
function checkJsonChapterContent(json: any[]) {
  const result: string[] = [];

  // 定义HTML标签移除函数，保留img标签为「img」
  const cleanHtml = (text: string): string => {
    if (!text) return '';
    return text
      .replace(/<img[^>]*>/g, '「img」')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .trim();
  };

  // 遍历所有节点
  for (const { node, level } of iterateNode(json)) {
    let nodeContent = '';

    // 根据节点类型生成内容
    if (node.node_type === 'chapter') {
      // 章节节点，添加#
      const pathLevel = node.node_level || level;
      nodeContent = '#'.repeat(pathLevel) + cleanHtml(node.content?.body || '');
    } else if (node.content?.body) {
      // 非章节节点，直接取内容
      nodeContent = cleanHtml(node.content.body);
      // 如果内容超过150字符，截取前150个字符
      if (nodeContent.length > 150) {
        nodeContent = nodeContent.substring(0, 150);
      }
    }

    // 如果有内容，加入结果
    if (nodeContent) {
      // 根据层级添加缩进
      const indentation = '@'.repeat(level);
      result.push(indentation + nodeContent);
    }
  }
  // 使用换行符连接所有节点
  return result.join('\n');
}

export { formatErrorInfo, extractSymbolId, checkJsonScore, checkJsonChapterContent, checkNodeQuestionContent, checkNonQuestionContent };
