import { TJsonNode } from '../htmlToJsonV4';
import { findAllNodes } from '../treeHelper';

export { combineJson } from './combineJson';
export { separateJsonByChapter } from './separateJsonByChapter';

export function errorNodeStat(json: TJsonNode[]) {
  const errorNodes = findAllNodes(json, ({ node }) => <PERSON><PERSON>an(node.errorInfo), { stopFindChildren: false });
  const count = errorNodes.length;
  const counts = {};
  errorNodes.forEach((node) => {
    node.errorInfo!.forEach(({ rule }) => {
      counts[rule] = counts[rule] ? counts[rule] + 1 : 1;
    });
  });
  return { count, counts };
}
