import * as _ from 'lodash';
import { IChapterNode, TJsonNode } from '../htmlToJsonV4';

function isChapter(node: TJsonNode): node is IChapterNode {
  return node.node_type === 'chapter';
}

export function separateJsonByChapter(json: TJsonNode[], level: number) {
  const result: { path: string[], node: IChapterNode }[] = [];

  function handle(tree: TJsonNode[], parentPath: string[]) {
    tree.forEach((node) => {
      if (node.node_level > level) {
        return;
      }
      if (node.node_type !== 'chapter') {
        return;
      }
      const path = parentPath.concat([node.node_name]);
      if (node.node_level < level) {
        const [chapters, children] = _.partition(node.children, isChapter);
        if (children.length) {
          result.push({ path, node: { ...node, children } });
        }
        if (chapters.length) {
          handle(chapters, path);
        }
      } else {
        result.push({ path, node });
      }
    });
  }

  handle(json, []);
  return result;
}
