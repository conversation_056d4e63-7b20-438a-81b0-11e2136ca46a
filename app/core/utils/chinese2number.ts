type TCharNumMap = { [c: string]: number };

const numbers: TCharNumMap = {
  一: 1,
  兩: 2,
  两: 2,
  二: 2,
  三: 3,
  四: 4,
  五: 5,
  六: 6,
  七: 7,
  八: 8,
  九: 9,
  零: 0,
  〇: 0,
  十: 10,
};
const separateUnits: TCharNumMap = {
  萬: 10000,
  億: 10000,
  万: 10000,
  亿: 10000,
};
const units: TCharNumMap = {
  仟: 1000,
  千: 1000,
  百: 100,
  十: 10,
};

// 浮点部分转换
function float2numString(chineseStr: string) {
  const digits: number[] = [];
  for (const c of chineseStr) {
    const digit = numbers[c];
    if (digit == null) {
      return;
    }
    digits.push(digit);
  }
  return digits.join('');
}

// 整数部分转换
function int2num(chineseStr: string) {
  let stack: number[] = [];
  if (chineseStr.length === 1) {
    return numbers[chineseStr];
  }
  let total = 0;
  for (let i = 0; i < chineseStr.length; i += 1) {
    const current = chineseStr[i];
    if (current in units) {
      total += units[current] * (stack.pop() || 1);
      stack = [];
    } else if (current in separateUnits) {
      const times = (current.match(/[亿億]/) && !chineseStr.substr(i).match(/[万萬]/) ? 100000000 : 10000);
      total *= times;
      total += times * (stack.pop() || 0);
      stack = [];
    } else if (current in numbers) {
      stack.push(numbers[current]);
    } else {
      return;
    }
  }
  if (total) {
    if (stack.length > 0) total += Number(stack.join(''));
    return total;
  }
}

// 正数转换
function positiveChinese2number(chineseStr) {
  const [integerPart, floatPart] = chineseStr.split('点');
  if (floatPart) {
    const num = int2num(integerPart);
    if (num == null) {
      return;
    }
    const floatStr = float2numString(floatPart);
    if (floatStr == null) {
      return;
    }
    return Number(`${num}.${floatStr}`);
  }
  return int2num(integerPart);
}

export function chinese2Number(chineseStr) {
  if (!chineseStr) return;
  if (chineseStr.match(/^负/)) {
    const num = positiveChinese2number(chineseStr.substring(1));
    if (num == null) {
      return;
    }
    return -num;
  }
  return positiveChinese2number(chineseStr);
}
