# 角色定义
您是一个专业的文本语义分析器，擅长通过深度语义理解识别选项的本质含义。

# 题目
```
(2023沈阳)Betty is crazy about music. Her dream is to be a                       .
A.scientist        B.  painter         C.  musician         D.   singer
```

# 分析任务
**目标**: 识别并输出题目选项中所有语义重复的选项分组。
**判定标准**: 一组选项被判定为语义重复，必须同时满足以下条件：
1.  **核心语义等价**: 选项组内各选项所传达的核心信息或概念实质上等同。
2.  **共同题干依据**: 选项组内各选项的核心含义均能在题干中找到共同的指向或支持依据。

# 分析原则：
1.  **理解题意与分析**：首先准确把握题目的核心要求和考察点。
2.  **核心含义比对**：聚焦选项传达的**本质信息**，忽略表述形式差异（如主动/被动语态、肯定/否定句式）。
3.  **题干依据验证**：重复判断必须能在题干找到共同依据，允许在题干有共同大类的情况下视为重复，即使没有具体方向。
4.  **排除干扰因素**：
    *   忽略同义词替换（如"友好相处" vs "举止友善"）
    *   忽略语法结构差异（如名词化表达 vs 动词化表达）
    *   忽略程度副词（如"非常友好" vs "比较友善"）
5.  **区分度验证**：选项间必须达到显著差异，当语义重叠度超过可接受阈值或概念边界模糊时，应视为重复选项。**请注意：如果本条规则的判断与“理解题意与分析”原则的结论产生冲突，应以“理解题意与分析”的结论为准。**
    *   **语义重叠度超过可接受阈值** 指选项间的核心含义相似程度超过合理范围：
        1.  包含关系：如"音乐家"与"歌手"
        2.  交叉关系：如"社会活动"与"公共事务"
        3.  功能重叠：如"交流平台"与"信息集散地"
    *   **概念边界模糊** 指选项间的定义域存在灰色地带，例如：
        1.  职业定位模糊："心理咨询师"与"情感顾问"
        2.  行为界定不明："主动沟通"与"积极交流"
        3.  属性交叉覆盖："文化遗产"与"历史遗存"

# 通用判断框架：
当分析选项重复性时，请按以下维度进行本质比对：
1.  **行为本质比对**
    *   判断逻辑：不同选项是否描述同一行为的正/反表达。
    *   题干依据验证：题干是否使用明确的行为定义词。
2.  **关系本质比对**
    *   判断逻辑：判断逻辑：不同选项是否描述同一逻辑关系、空间关系（包括位置、方向、顺序、排列等）的不同观察角度或表述。
    *   题干依据验证：题干是否使用明确的关系、方位、**顺序**或排列限定词。

# 输出要求：
*   **如果发现意义重复的选项：**
    返回包含以下字段的JSON对象数组：
    *   `group`: 意义重复的选项字母数组
    *   `reason`: 解释为什么这些选项被判定为意义重复的具体依据，需结合分析原则和判断框架进行说明。
    例如：
    ```json
    [
      {
        "group": ["A", "B"],
        "reason": "选项A和B的核心含义相似，且题干中明确提到了相同的行为定义词。"
      }
    ]
    ```
*   **如果未发现重复选项：**
    返回空数组：
    ```json
    []
    ```

# 重要提示：
你的最终输出必须**仅**包含符合上述要求的 JSON 数组，不带任何其他文字、解释或代码块标记（如 \`\`\`json\`\`\`）。请直接输出 JSON 结果。
