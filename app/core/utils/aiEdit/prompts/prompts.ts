/**
 * SAME_OPTIONS_PROMPT 是一个生成检查选择题选项的提示字符串的方法。
 * 用于根据指定规则检查选项的数据是否存在语义重复的错误，并以JSON格式返回检查结果。
 *
 * @param {string} [_body=""] - 可选的题目内容，默认为空字符串。
 * @param {array} options - 包含选择题选项的数据数组，每个选项由字母标识和选项内容组成。
 * @returns {{ systemPrompt: string, userPrompt: string }} 返回一个包含 `systemPrompt` 和 `userPrompt` 的对象，用于提示的生成。
 */
export const SAME_OPTIONS_PROMPT = (_body = '', options: { letter: string, option: string }[]) => {
  // _body 参数在此函数中未使用，保留但注明。
  // const optionsJsonString = JSON.stringify(options, null, 2);

  // 优化 System Prompt
  const systemPrompt =
    '你是一个专业的文本语义分析器，擅长比对和识别文字描述背后隐藏的含义。你的任务是根据提供的选择题选项列表，判断其中是否存在语义完全相同（意义重复）的选项。你需要以严格的 JSON 格式输出检查结果。';

  // 优化 User Prompt
  const userPrompt = `
# 角色定义
您是一个专业的文本语义分析器，擅长通过深度语义理解识别选项的本质含义。

# 题目
\`\`\`
${_body}\n
${options.map(((v) => `${v.letter}、${v.option}`)).join('\t')}
\`\`\`

# 分析任务
**目标**: 识别并输出题目选项中所有语义重复的选项分组。
**判定标准**: 一组选项被判定为语义重复，必须同时满足以下条件：
1.  **核心语义等价**: 选项组内各选项所传达的核心信息或概念实质上等同。
2.  **共同题干依据**: 选项组内各选项的核心含义均能在题干中找到共同的指向或支持依据。

# 分析原则：
1.  **理解题意与分析**：首先准确把握题目的核心要求和考察点。
2.  **核心含义比对**：聚焦选项传达的**本质信息**，忽略表述形式差异（如主动/被动语态、肯定/否定句式）。
3.  **题干依据验证**：重复判断必须能在题干找到共同依据，允许在题干有共同大类的情况下视为重复，即使没有具体方向。
4.  **排除干扰因素**：
    *   忽略同义词替换（如"友好相处" vs "举止友善"）
    *   忽略语法结构差异（如名词化表达 vs 动词化表达）
    *   忽略程度副词（如"非常友好" vs "比较友善"）
5.  **区分度验证**：选项间必须达到显著差异，当语义重叠度超过可接受阈值或概念边界模糊时，应视为重复选项。**请注意：如果本条规则的判断与“理解题意与分析”原则的结论产生冲突，应以“理解题意与分析”的结论为准。**
    *   **语义重叠度超过可接受阈值** 指选项间的核心含义相似程度超过合理范围：
        1.  包含关系：如"音乐家"与"歌手"
        2.  交叉关系：如"社会活动"与"公共事务"
        3.  功能重叠：如"交流平台"与"信息集散地"
    *   **概念边界模糊** 指选项间的定义域存在灰色地带，例如：
        1.  职业定位模糊："心理咨询师"与"情感顾问"
        2.  行为界定不明："主动沟通"与"积极交流"
        3.  属性交叉覆盖："文化遗产"与"历史遗存"

# 通用判断框架：
当分析选项重复性时，请按以下维度进行本质比对：
1.  **行为本质比对**
    *   判断逻辑：不同选项是否描述同一行为的正/反表达。
    *   题干依据验证：题干是否使用明确的行为定义词。
2.  **关系本质比对**
    *   判断逻辑：判断逻辑：不同选项是否描述同一逻辑关系、空间关系（包括位置、方向、顺序、排列等）的不同观察角度或表述。
    *   题干依据验证：题干是否使用明确的关系、方位、**顺序**或排列限定词。

# 输出要求：
*   **如果发现意义重复的选项：**
    返回包含以下字段的JSON对象数组：
    *   \`group\`: 意义重复的选项字母数组
    *   \`reason\`: 解释为什么这些选项被判定为意义重复的具体依据，需结合分析原则和判断框架进行说明。
    例如：
    [
      {
        "group": ["A", "B"],
        "reason": "选项A和B的核心含义相似，且题干中明确提到了相同的行为定义词。"
      }
    ]
*   **如果未发现重复选项：**
    返回空数组：
    []

# 重要提示：
你的最终输出必须**仅**包含符合上述要求的 JSON 数组，**不带**任何其他文字、解释或代码块标记（如 \`\`\`json\`\`\`）。请直接输出 JSON 结果。
`;

  return {
    systemPrompt,
    userPrompt,
  };
};

/**
 * 一个用于生成特定信息提取提示文字的变量，旨在支持从文本中提取题目来源地（包括省份、城市、地区）和年份信息。
 * @constant
 * @type {(body: string) => { systemPrompt: string, userPrompt: string }}
 * @param {string} body 待分析的文本内容
 * @returns {object} 包含两个提示文字字符串的对象：`systemPrompt`（系统指示）和 `userPrompt`（用户提问模板）
 */
export const AREA_PROMPT = (body: string) => {
  const systemPrompt =
    '你是一个信息提取专家，擅长从文本中查找并提取特定的来源信息。你的任务是仔细分析提供的文本内容，从中准确识别题目的来源地（包括省份、城市、地区）和年份信息。找到后，你需要以严格的 JSON 格式输出提取的结果。';

  const userPrompt = `
请分析以下提供的文本内容：

<text>
${body}
</text>

在分析文本并提取题目的来源信息（地市/地区，可能包含省份 + 年份）时，请严格遵循以下指示：

1.  **优先查找范围:** 请重点检查文本中包含在以下任意一种括号内的内容：\`[]\`、\`()\`、\`【】\`、\`「」\`、\`『』\`、\`〈〉\`、\`<>\`。在这些括号内寻找最有可能表示来源地和年份的组合信息。
2.  **提取规则:**
    * 查找表示地市或地区的词语。
    * 查找表示年份的数字或词语（如 2023、二〇二三）。
    * 如果找到的来源信息包含省份和地市/地区，请将省份和地市/地区一并提取作为“area”。
    * 只提取在文本中*首先*找到的、符合条件（尤其是优先在括号内找到）的地市/地区和年份信息组合。
    * 提取时，不要修改原始文本中的任何内容。
3.  **输出格式:**
    * **如果成功找到**题目的来源信息（即地市/地区 + 年份），请严格按照以下 JSON 格式输出第一个匹配的结果：
        {
            "area": "提取到的地市或地区（包含省份）",
            "year": "提取到的年份"
        }
        例如，如果找到“\[北京市 2023]”，输出 \`{"area": "北京市", "year": "2023"}\`。
        如果找到“\[四川省成都市 2024]”，输出 \`{"area": "四川省成都市", "year": "2024"}\`。
    * **如果未找到**明确的题目的来源信息（即没有地市/地区 + 年份的组合，特别是在括号内），请严格按照以下 JSON 格式输出空结果：
        {
            "area": "",
            "year": ""
        }

**重要:** 你的输出*必须*且*只能*是上述两种情况中的一个 JSON 对象，不包含任何其他引导性文字、解释或多余的格式（如 Markdown 的\`\`\`json\`\`\`标记）。

请现在开始分析并输出结果：
`;

  return {
    systemPrompt,
    userPrompt,
  };
};

/**
 * 生成用于检查章节/节编号连续性的系统和用户提示信息。
 *
 * @param {array} chapters - 章节/节数组，格式为 "层级标记 标题 @ 节点ID"，层级标记以 '#' 表示（如 '#', '##'），
 * 标题为章节名称，节点ID为唯一标识符。
 * 示例：["#第一章@nodeId1", "##第一节@nodeId2", "#第2章@nodeId3"]
 * @returns {{systemPrompt: string, userPrompt: string}} 包含 systemPrompt 和 userPrompt 的对象。
 */
export const CHAPTER_SEQUENCE_PROMPT = (chapters: string[]) => {
  /**
   * 为大型语言模型生成系统和用户 prompt，用于检查章节/节节点的连续编号。
   *
   * @param chapters - 一个字符串数组，每个字符串格式为 "层级标记 标题 @ 节点ID"。
   * 层级标记为一个或多个 '#' 字符（例如，#，##）。
   * 标题为章节/节的文本内容。
   * 节点ID为唯一标识符。
   * 示例：["#第一章@nodeId1", "##第一节@nodeId2", "#第2章@nodeId3"]
   * @returns 包含 systemPrompt 和 userPrompt 的对象。
   */

  const systemPrompt = `你的角色是作为文档大纲的验证器。你将接收一个章节/节的字符串列表，每个字符串代表层级结构中的一个节点。你的任务是分析这些节点的顺序和编号，找出*同一层级内*任何编号不连续的地方。

输入格式:
输入数组中的每个字符串都遵循以下格式：\`层级标记 标题 @ 节点ID\`。
- \`层级标记\`：一个或多个 '#' 字符，表示层级（例如，'#' 是第一级，'##' 是第二级，'###' 是第三级，以此类推）。'#' 字符的数量直接对应层级数。
- \`标题\`：章节或节的文本内容（例如，"第一章", "第二节", "3. Introduction"）。
- \`节点ID\`：该节点的唯一标识符。

任务:
按顺序分析提供的列表，从头开始。对于每个节点，确定其层级并从其\`标题\`中提取其固有的编号。你必须跟踪遇到的每个层级的*预期*下一个编号。
- 根据开头的 '#' 字符数量确定当前节点的层级。
- 从\`标题\`部分提取编号（例如，"第一章" 应解析为 1，"第二节" 解析为 2，"第3章" 解析为 3，"Section 1" 解析为 1，"Section 2" 解析为 2）。需要处理中文数字（一、二、三等，第一、第二、第三等）和阿拉伯数字（1、2、3等）等常见编号格式。
- 如果当前节点与其同一层级的上一个节点层级相同：将其提取的编号与其特定层级的*预期*编号进行比较。
- 如果当前节点层级低于前一个节点（例如，从 '#' 到 '##'）：将该新较低层级的预期编号重置为 1，并检查当前节点的编号是否为 1。
- 如果提取的编号与该层级的预期编号*不匹配*，则这是编号不连续的第一个实例。

错误报告:
如果你发现不连续，立即停止分析，并返回一个包含*一个*对象描述第一个错误发现的 JSON 数组。

输出格式:
你的输出*必须*是一个严格的 JSON 数组。
- 如果你检测到*任何*编号不连续，返回一个包含*恰好一个*对象来代表在序列中找到的*第一个*不连续。此对象必须包含以下键：
    - \`node_id\`：检测到不连续的章节/节的\`节点ID\`。
    - \`error_info\`：错误的简洁描述，清楚说明在特定层级预期和实际找到的编号（例如，"第一级不连续：预期 '第二章'，实际找到 '第三章'"）。
    - \`fix_info\`：关于如何纠正标题以匹配预期顺序的明确建议（例如，"将标题改为 '第二章'"）。
- 如果你处理完整个列表并且在任何层级都没有发现编号不连续，则返回一个空的 JSON 数组：\`[]\`。

约束:
- 只检查*同一层级*节点之间的连续性。不要跨不同层级比较编号。
- 停止并在找到*第一个*错误时报告。
- 响应中*只*包含严格的 JSON 数组。JSON 之外不要包含任何其他文本、不包含任何其他引导性文字、解释或多余的格式（如 Markdown 的\`\`\`json\`\`\`标记）
- 确保 JSON 输出是有效的且格式正确。
`;

  const userPrompt = `分析以下章节/节列表，检查编号顺序错误，并按指定的 JSON 格式返回结果。

${chapters.join('\n')}

请以严格的 JSON 数组格式提供输出：`;

  return { systemPrompt, userPrompt };
};

/**
 * 生成用于提取题目各部分（正文、答案、解析）总分值的 prompt。
 *
 * @param body - 题目的正文文本。
 * @param answer - 题目的答案文本。
 * @param analysis - 题目的解析文本。
 * @returns 包含 systemPrompt 和 userPrompt 的对象。
 */
export const EXTRACT_SCORE_PROMPT = (body: string, answer: string, analysis: string) => {
  const systemPrompt =
    '你是一个专业的文本信息提取助手，擅长从题目相关的文本中识别和提取分值信息。你的任务是分析提供的题目的正文、答案和解析内容，分别提取出每个部分的**总分**，并以指定的严格 JSON 格式输出结果。';
  const userPrompt = `
请分析以下题目的正文、答案和解析内容，分别提取每个字段的总分。

<body_text>
${body}
</body_text>

<answer_text>
${answer}
</answer_text>

<analysis_text>
${analysis}
</analysis_text>

**提取规则和注意事项：**

1.  **独立处理:** 请分别处理 <body_text>、<answer_text> 和 <analysis_text> 三个字段的内容。
2.  **查找分值:** 请仔细阅读每个字段的文本，查找其中出现的分值信息。分值可能以多种形式表示，包括但不限于：
    * 明确的分数：如 (8分)、2分、5.5分 等。
    * 总分指示：如 (共30分)、满分15分 等。
    * 范围分值：如 (7～9分)、(7~8分) 等。
    * 条件或细则分值：如 (答对给4分)、(每点1分)、(答出其中两点得2分)、(每空0.5分) 等。
    * 分值与描述结合：如 (3分，符合题意即可)、(4分。论述过程...) 等。
3.  **计算总分:**
    * **如果某个字段中出现多个明确的分值（如 2分 和 1分 同时出现），请将这些明确的分值相加，得出该字段的**总分**。例如，文本“...（2分）...（1分）...”的总分应为 3 分。
    * 对于范围分值（如 7~9分），请尝试理解上下文语境，如果无法与其他明确分值相加得出总分，可以考虑提取其**最大值**作为该项的分值。
    * 对于条件或细则分值（如 每点1分，答出其中两点得2分），请尝试理解语境并提取其表示的**最终可能获得的最高分值**或最直接指定的总分。
    * 优先提取文本中明确给出的总分指示（如 共30分）。
    * 如果某个字段中未找到任何明确、可相加或可推断的分值，则该字段的总分为 null。
4.  **结果格式:** 你需要为每个字段（body、answer、analysis）提取一个总分。

**输出要求：**

你的输出必须是一个严格的 JSON 对象，包含以下三个键值对，且只包含这三个键值对：
{
  "body_score": 提取到的正文总分 (数字),
  "answer_score": 提取到的答案总分 (数字),
  "analysis_score": 提取到的解析总分 (数字)
}
分值请以数字格式输出。

**重要提示：**
你的最终输出必须**仅**是上述 JSON 对象，不包含任何其他文字、解释或格式（包括 Markdown 代码块标记）。请直接输出 JSON 结果。
`;

  return {
    systemPrompt,
    userPrompt,
  };
};

/**
 * 生成用于检查文档标题与内容一致性的 prompt。
 *
 * @param documentContent - 整个文档的文本内容，包含标题和内容行。
 * 格式说明：
 * - '#' 开头的行是标题，数量表示层级。
 * - '@' 开头的行是内容，数量表示缩进/嵌套层级。
 * - 「img」表示图片占位符。
 * @returns 包含 systemPrompt 和 userPrompt 的对象。
 */
export const CONTENT_CONSISTENCY_PROMPT = (documentContent: string) => {
  const systemPrompt = `你是一个文档结构与内容一致性检查专家。你的任务是分析提供的文档内容，理解其层级结构（由 '#' 和 '@' 符号表示），检查标题与内容之间、以及不同层级内容之间的逻辑一致性。找出所有内容与标题不匹配、或结构上存在不一致的地方，并以指定的 JSON 格式报告错误及修改建议。

文档格式说明:
- 以 '#' 开头的行表示章节标题，'#' 的数量表示层级（例如，# 是第一级，## 是第二级）。
- 以 '@' 开头的行表示内容或子节点，'@' 的数量表示相对于其上级标题或内容的缩进/嵌套层级。
- \`「img」\` 是图片占位符，在进行内容一致性检查时，请忽略其具体含义，主要关注文本内容。
- 文档内容由这些行组成，形成一个分层结构。

需要检查的一致性规则:
请在分析文档内容时，重点检查以下几点逻辑一致性问题：

1.  **标题与直接下方内容的关联性（主题匹配）:** 紧跟在某个标题（特别是标题行后面的第一段内容）后的内容，其**主题或核心信息**是否直接与该标题高度相关且一致？例如，标题是关于“单细胞生物”，但下方内容是关于“显微镜的使用”或“细胞观察”，则主题不匹配。
2.  **层级范围的符合性:** 各层级（由 '@' 符号的数量和其在结构中的位置决定）的内容以及其包含的子标题和子内容，是否都在其最直接的上级标题（或其所属的父级内容块）所限定的**主题范畴**内？层级跳跃（如一级标题下直接跟三级标题的内容）是否导致主题范围不合理？
3.  **标题的描述性与内容完整性（类型符合）:** 标题本身是否准确地概括或预示了其下方整个内容块的主题或**内容类型**？例如：
    * 一个名为"知识梳理"、"总结"或"结论"的标题下，是否有**实际的梳理要点、总结性陈述或结论内容**？如果没有，则认为内容缺失或类型不符。
    * 一个名为"练习"、"题目"或"问答"的标题下，是否有**实际的问题或题目内容**？

错误报告格式:
如果发现任何不一致的地方，请收集所有错误实例。你的输出必须是一个严格的 JSON 数组，数组的每个元素是一个描述特定错误的 JSON 对象。

每个错误对象必须包含以下键：

\`\`\`json
{
  "error_type": "错误的类型（从上面列出的规则中选择最符合的，例如：主题不匹配, 层级范围不符, 标题类型内容缺失/不符）",
  "problem_element": "与此错误直接相关的标题文本（例如 '#第六课 我的毕业季'，或 '###知识梳理'）",
  "error_description": "对发现的错误的具体描述和解释，**请清晰说明标题是什么，内容是什么，以及为什么认为不一致**（例如：'标题为“单细胞生物”，但其下方内容是关于“洋葱表皮细胞观察”（属于显微镜使用），主题不符。'）",
  "suggestion": "针对此错误的修改建议，**请给出具体的操作建议**（例如：'根据内容主题，建议将标题修改为与显微镜使用相关的描述，如“显微镜的使用”或“细胞观察实验”，或将该段内容移动到对应主题的标题下。' 或 '在“###知识梳理”标题下补充相关的知识要点内容。'）"
}
\`\`\`

如果检查完毕未发现任何不一致，返回一个空的 JSON 数组：\`[]\`。

约束:
- 请按顺序检查整个文档内容。
- 收集所有发现的不一致问题。
- 响应中**只**包含严格的 JSON 数组，不包含任何其他引导性文字、解释或 Markdown 代码块标记（如 \`\`\`json\`\`\`）。
- 确保 JSON 输出是有效的且格式正确。
`;

  const userPrompt = `
请根据你在 System Prompt 中收到的指令和规则，分析以下文档内容，检查其结构和内容的一致性，并以指定的 JSON 格式输出检查结果。

<document_content>
${documentContent}
</document_content>

请直接输出 JSON 结果：
`;

  return {
    systemPrompt,
    userPrompt,
  };
};
