// 试题合法性校验
import { findAllNodes, findNode, iterateNode } from '../../treeHelper';
import {
  EJsonErrorRule,
  IBasicQuestionNode,
  IChapterNode,
  IJsonErrorInfo,
  IParagraphNode,
  IQuestionNode,
  TJsonNode
} from './index';
import { parseHtml } from '../../htmlHelper';
import { isHtmlStr } from '../../helper';
import { parseSerialNumber, sameSerialNumberType } from '../../parseSerialNumber';
import { checkHtmlStraight } from '../../latexConverter';

// 相似度检测相关接口和类型
interface ISimilarText {
  node_id: string;
  node_str: string;
  serial_number: string;
}

interface ISimilarPair {
  text_1: string;
  node_id_1: string;
  text_2: string;
  node_id_2: string;
  similarity: number;
  serial_number_1: string;
  serial_number_2: string;
}

const LETTERS = 'ABCDEFGHIJKLMN';

/**
 * 计算两个字符串的相似度（基于最长公共子序列）
 * 参考 Python difflib.SequenceMatcher 的实现
 */
function calculateSimilarity(text1: string, text2: string): number {
  if (!text1 || !text2) return 0;
  if (text1 === text2) return 1;

  const len1 = text1.length;
  const len2 = text2.length;

  // 使用动态规划计算最长公共子序列
  const dp: number[][] = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (text1[i - 1] === text2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
      }
    }
  }

  const lcs = dp[len1][len2];
  return (2.0 * lcs) / (len1 + len2);
}

/**
 * 查找相似的文本对
 */
function findSimilarTexts(textList: ISimilarText[], threshold = 0.8): ISimilarPair[] {
  const similarList: ISimilarPair[] = [];
  const n = textList.length;

  for (let i = 0; i < n; i++) {
    for (let j = i + 1; j < n; j++) {
      const textItem1 = textList[i];
      const textItem2 = textList[j];
      const similarity = calculateSimilarity(textItem1.node_str, textItem2.node_str);

      if (similarity >= threshold) {
        similarList.push({
          text_1: textItem1.node_str,
          node_id_1: textItem1.node_id,
          text_2: textItem2.node_str,
          node_id_2: textItem2.node_id,
          similarity,
          serial_number_1: textItem1.serial_number,
          serial_number_2: textItem2.serial_number,
        });
      }
    }
  }

  return similarList;
}

/**
 * 检查公式中是否包含答案分割符■
 */
function validateFormulaAnswerSeparator(content: string): IJsonErrorInfo[] {
  const errors: IJsonErrorInfo[] = [];

  if (!content) {
    return errors;
  }

  // 匹配所有公式 $$...$$
  const formulaRegex = /\$\$([^$]*?)\$\$/g;
  let match;

  while ((match = formulaRegex.exec(content)) !== null) {
    const formulaContent = match[1];
    if (formulaContent.includes('■')) {
      errors.push({
        rule: EJsonErrorRule.content_invalid_character,
        message: '公式中包含答案分割符■',
      });
      break;
    }
  }

  return errors;
}

/**
 * 将节点内容转换为纯文本字符串用于相似度比较
 */
function nodeToString(node: IQuestionNode): string {
  const bodyList: string[] = [];
  const choiceList: string[] = [];
  const answerList: string[] = [];
  const analysisList: string[] = [];

  function processNode(currentNode: IQuestionNode) {
    // 题干
    if (currentNode.content.body) {
      bodyList.push(currentNode.content.body);
    }

    // 选项
    const choices = currentNode.content.choices;
    if (choices && choices.length) {
      choices.forEach((choice) => {
        const letter = choice.letter + '.';
        const option = choice.option;
        choiceList.push(letter + option);
      });
    }

    // 答案
    if (currentNode.content.answer && currentNode.content.answer.length) {
      const answer = currentNode.content.answer.join('、');
      answerList.push(answer);
    }

    // 解析
    if (currentNode.content.analysis) {
      analysisList.push(currentNode.content.analysis);
    }

    // 递归处理子节点
    currentNode.children.forEach((child) => {
      if (child.node_type === 'question') {
        processNode(child as IQuestionNode);
      }
    });
  }

  processNode(node);

  const htmlList = [...bodyList, ...choiceList, ...answerList, ...analysisList];
  const htmlData = htmlList.join('\n');

  // 移除HTML标签
  const textData = htmlData.replace(/<[^<>]*?>/g, '');
  return textData;
}

/**
 * 检查题目内容重复
 */
function validateQuestionDuplicate(nodes: TJsonNode[]): void {
  const questionNodes: IQuestionNode[] = [];
  const nodeMap = new Map<string, IQuestionNode>();

  // 收集所有题目节点并生成唯一ID
  for (const { node } of iterateNode(nodes)) {
    if (node.node_type === 'question') {
      const questionNode = node as IQuestionNode;
      const nodeId = `${questionNode.content.serial_number || 'unknown'}_${Math.random().toString(36).substr(2, 9)}`;
      questionNodes.push(questionNode);
      nodeMap.set(nodeId, questionNode);
      // 为后续处理添加临时ID
      (questionNode as any)._tempNodeId = nodeId;
    }
  }

  // 生成文本列表
  const textList: ISimilarText[] = questionNodes.map((node) => ({
    node_id: (node as any)._tempNodeId,
    node_str: nodeToString(node),
    serial_number: node.content.serial_number || '',
  }));

  // 查找相似文本
  const similarPairs = findSimilarTexts(textList, 0.8);

  // 为相似的节点添加错误信息
  similarPairs.forEach((pair) => {
    const node1 = nodeMap.get(pair.node_id_1);
    const node2 = nodeMap.get(pair.node_id_2);

    if (node1 && node2) {
      const similarityPercent = (pair.similarity * 100).toFixed(2);
      const errorMessage = `与第${pair.serial_number_2}题相似度${similarityPercent}%`;

      if (!node1.errorInfo) node1.errorInfo = [];
      node1.errorInfo.push({
        rule: EJsonErrorRule.question_content_duplicate,
        message: errorMessage,
      });
    }
  });

  // 清理临时ID
  questionNodes.forEach((node) => {
    delete (node as any)._tempNodeId;
  });
}

function isEmptyText(text: string) {
  return !text || !text.trim();
}

function isEmptyHtml(html: string, blankIsEmpty = true) {
  if (!html) return true;
  const nodes = parseHtml(html);
  return !findNode(nodes, ({ node }) => {
    return node.type === 'text' && !isEmptyText(node.content) ||
      node.type === 'element' && (
        node.tagName === 'img' && Boolean(node.attrs.src) ||
        !blankIsEmpty && Boolean(node.dataset.label) && ['blank', 'bracket'].includes(node.dataset?.label || '')
      );
  });
}

// 校验答案
function validateAnswer({ content, question_type }: IQuestionNode) {
  if (!content.answer.length && !(/answer-block/).test(content.body)) {
    return [{
      rule: EJsonErrorRule.answer_empty,
      message: '试题答案不存在',
    }];
  }
  const emptyAnswerIndex: number[] = [];
  content.answer.forEach((answer, index) => {
    if (isHtmlStr(answer) ? isEmptyHtml(answer) : isEmptyText(answer)) {
      emptyAnswerIndex.push(index + 1);
    }
  });
  const emptyAnswerIndexStr = emptyAnswerIndex.join(',');
  if (emptyAnswerIndexStr) {
    const message = content.answer.length === 1 ?
      '试题答案为空' :
      `试题答案${emptyAnswerIndexStr}为空`;
    return [{
      rule: EJsonErrorRule.answer_empty,
      message,
    }];
  }
  const tableRegex = /<table/g;
  // 存在data-label=blank的span标签
  const blankReg = /data-label="blank"/g;
  const errorArr: IJsonErrorInfo[] = [];
  const answerString = content.answer.join();
  const body = content.body;
  const regexStudentImage = /data-label="blank"/g;
  const regexImageContainer = /img-signal-container/g;
  if (regexImageContainer) {
    const matchesStudent = body.match(regexStudentImage);
    const studentImageQuestLength = matchesStudent ? matchesStudent.length : 0;
    if (studentImageQuestLength && studentImageQuestLength !== content.answer.length) {
      errorArr.push({
        rule: EJsonErrorRule.question_answer_mismatch,
        message: '试题数量与答案数量不一致',
      });
    }
  }

  if (tableRegex.test(answerString)) {
    errorArr.push({
      rule: EJsonErrorRule.answer_table,
      message: '试题答案包含表格',
    });
  }
  const blankMatch = answerString.match(blankReg);
  if (blankMatch && blankMatch.length) {
    errorArr.push({
      rule: EJsonErrorRule.answer_blank,
      message: '答案中包含期望外的作答空间',
    });
  }
  if (errorArr.length) {
    return errorArr;
  }
  if (question_type === 'choice' || content.choices?.length) {
    // 检查是否出现相同的选项
    const allAnswerChars = answerString.split('');
    if (new Set(allAnswerChars).size !== content.answer.length) {
      // 找出重复的选项
      const duplicates: string[] = [];
      const seen = new Set<string>();

      for (const char of allAnswerChars) {
        if (seen.has(char) && !duplicates.includes(char)) {
          duplicates.push(char);
        }
        seen.add(char);
      }

      return [{
        rule: EJsonErrorRule.answer_error_content,
        message: `选择题答案中存在重复选项：${duplicates.join('、')}`,
      }];
    }
  }
  return [];
}

// 校验填空题
function validateBlank({ content, children }: IQuestionNode) {
  const error: IJsonErrorInfo[] = [];
  const body = content.body;
  if (body) {
    const regex = /<p[^>]*>(.*?)<\/p>/;
    const match = body.match(regex);
    if (match) {
      const cont = match[1];
      const tabRegex = /<span data-label="tab"> <\/span>/;
      const isFollowedByTab = tabRegex.test(cont);

      if (isFollowedByTab) {
        // 答案后面紧跟着<span data-label="tab"> </span>
        error.push({
          rule: EJsonErrorRule.blank_answer_followed_by_tab,
          message: '填空题答案后面不能有制表符',
        });
      }
    }
  }
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '填空题不能有子节点',
    });
  }

  if (content.choices && content.choices.length) {
    // error.push({
    //   message: '填空题不能有选项',
    //   rule: EJsonErrorRule.blank_width_choice,
    // });
  }

  // 去掉标签
  if (content.answer.join().trim().replace(/<[^>]+>/g, '').startsWith('解')) {
    error.push({
      message: '填空题答案有解',
      rule: EJsonErrorRule.blank_answer_start_with_jie,
    });
  }
  if (content.answer.some((answer) => {
    const tag = Number(answer.replace(/<p><span data-label="font"[^<>]*><\/span><\/p>/, '').split('</p>').length - 1) > 1 || /<table/.test(answer);
    return tag;
  })) {
    error.push({
      message: '填空题答案包含表格或者换行',
      rule: EJsonErrorRule.blank_answer_error,
    });
  }
  const count = content.blank_count || content.bracket_count;
  if (!count || count !== content.answer.length) {
    error.push({
      rule: EJsonErrorRule.blank_answer_mismatch,
      message: `答案和问题数量不匹配,答案${content.answer.length},问题${count}`,
    });
  }
  return error;
}

// 校验选择题
function validateChoice({ content, children }: IQuestionNode, prevQuestion?: IQuestionNode) {
  let { choices, choice, answer } = content;
  const error: IJsonErrorInfo[] = [];
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '选择题不能有子节点',
    });
  }
  if (choice) {
    // 兼容旧版
    choices = Object.keys(choice).map((letter) => ({ letter, option: choice[letter] }));
    if (!choices.length) {
      choices = undefined;
    }
  }
  if (!choices || !choices.length) {
    error.push({
      rule: EJsonErrorRule.options_invalid,
      message: '选择题无选项',
    });
    return error;
  }
  if (choices.length === 1) {
    error.push({
      rule: EJsonErrorRule.options_invalid,
      message: '选择题至少2个选项',
    });
  }
  if (choices[choices.length - 1].option.split('</p>').length - 1 > 1) {
    error.push({
      rule: EJsonErrorRule.choice_end_with_lines,
      message: '选择题最后一个选项内包含换行',
    });
  }
  if (prevQuestion && prevQuestion.question_type === 'choice') {
    const neighborChoices = prevQuestion.content.choices || prevQuestion.content.choice;
    if (neighborChoices &&
      (neighborChoices.length || Object.keys(neighborChoices).length) !== choices.length) {
      error.push({
        rule: EJsonErrorRule.options_mismatch_neighbor,
        message: `选择题选项数与相邻题目${prevQuestion.content.serial_number}不一致`,
      });
    }
  }
  const letters = choices.map(({ letter }) => letter);
  const upperLetters = letters.map((letter) => letter.toUpperCase());
  const letterStrs = letters.join('');
  const isLower = letterStrs === letterStrs.toLowerCase();
  if (upperLetters.some((letter) => letter.length !== 1 || !LETTERS.includes(letter)) ||
    !LETTERS.startsWith(upperLetters.join('')) ||
    !isLower && letterStrs !== letterStrs.toUpperCase()) {
    let suggestLetters = LETTERS.substring(0, choices.length);
    if (isLower) {
      suggestLetters = suggestLetters.toLowerCase();
    }

    // 找出具体哪些选项号不对
    const invalidLetters: string[] = [];
    const expectedLetters = suggestLetters.split('');

    letters.forEach((letter, index) => {
      if (letter !== expectedLetters[index]) {
        invalidLetters.push(`${letter}(应为${expectedLetters[index]})`);
      }
    });

    const invalidLettersStr = invalidLetters.length > 0 ?
      `，错误的选项号：${invalidLetters.join('、')}` : '';

    error.push({
      rule: EJsonErrorRule.choice_letter_invalid,
      message: `选项号应为${suggestLetters}${invalidLettersStr}`,
    });
  }
  const emptyOptionLetters = choices
    .filter(({ option }) => isEmptyHtml(option))
    .map(({ letter }) => letter)
    .join(',');
  if (emptyOptionLetters) {
    error.push({
      rule: EJsonErrorRule.choice_option_empty,
      message: `选项${emptyOptionLetters}内容为空`,
    });
  }
  // 检查答案是否多于一个字母或包含非字母字符
  const invalidAnswers = answer.filter((a) => a.length > 1 || /[^a-zA-Z]/.test(a));
  if (invalidAnswers.length > 0) {
    error.push({
      rule: EJsonErrorRule.choice_answer_multiple,
      message: `选择题答案格式错误，以下答案多于一个字母或包含非字母字符：${invalidAnswers.join('、')}`,
    });
  }
  const letterMap = {};
  choices.forEach((signalChoice) => {
    letterMap[signalChoice.letter] = true;
  });
  const invalidAnswer = answer.filter((signalAnswer) => signalAnswer.split('').some((a) => !letterMap[a])).join(',');
  if (invalidAnswer) {
    error.push({
      rule: EJsonErrorRule.choice_answer_invalid,
      message: `答案${invalidAnswer}不在选项中`,
    });
  }
  for (const a of answer) {
    if (new Set(a).size !== a.length) {
      error.push({
        rule: EJsonErrorRule.choice_answer_invalid,
        message: `多选答案${a}重复`,
      });
      break;
    }
  }
  // 选项中不能有表格
  const tableRegex = /<table\b[^>]*>([\s\S]*?)<\/table>/g;
  choices.forEach((_choice) => {
    if (tableRegex.test(_choice.option)) {
      error.push({
        rule: EJsonErrorRule.choice_option_table,
        message: `选项${_choice.letter}包含表格`,
      });
    }
  });
  
  // 检查选项中是否包含多个p标签，且超出的p标签有内容
  choices.forEach((_choice) => {
    const pTagMatches = _choice.option.match(/<p\b[^>]*>([\s\S]*?)<\/p>/g);
    if (pTagMatches && pTagMatches.length > 1) {
      // 检查除了第一个p标签之外的其他p标签是否有内容
      const extraPTags = pTagMatches.slice(1);
      const hasContentInExtraPTags = extraPTags.some((pTag) => {
        // 提取p标签内的内容
        const content = pTag.replace(/<p\b[^>]*>|<\/p>/g, '');
        // 去掉HTML标签和空白字符，检查是否有实际内容
        const textContent = content.replace(/<[^>]+>/g, '').trim();
        return textContent.length > 0;
      });
      
      if (hasContentInExtraPTags) {
        error.push({
          rule: EJsonErrorRule.options_invalid,
          message: `选项${_choice.letter}疑似出现没标出来的选项`,
        });
      }
    }
  });
  
  return error;
}

// 校验判断题
function validateTrueOrFalse({ content, children }: IQuestionNode) {
  const error: IJsonErrorInfo[] = [];
  if (children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '判断题不能有子节点',
    });
  }
  if (!content.correct || content.correct.some((correct) => typeof correct !== 'boolean')) {
    error.push({
      rule: EJsonErrorRule.true_or_false_invalid,
      message: '判断题答案不符合要求',
    });
  }
  return error;
}

function isChoiceQuestion(question: IQuestionNode) {
  // 'option' 兼容v1版本
  return question.question_type === 'choice' ||
    question.question_type as string === 'option';
}

// 校验试题层级
function validateQuestionContentLevel(question: IQuestionNode, parent?: TJsonNode) {
  if (parent && parent.node_type === 'question') {
    if (Math.abs(question.content.level) !== Math.abs(parent.content.level) + 1) {
      return [{
        rule: EJsonErrorRule.level_invalid,
        message: `试题层级错误，应为为${Math.abs(parent.content.level) + 1}，实际为${Math.abs(question.content.level)}`,
      }];
    }
  } else if (Math.abs(question.content.level) == null || Math.abs(question.content.level) > 1) {
    return [{
      rule: EJsonErrorRule.level_invalid,
      message: `试题层级错误，应为为1，实际为${Math.abs(question.content.level)}`,
    }];
  }
  return [];
}

// 校验题号
function validateQuestionSerialNumber(
  question: IQuestionNode, prevQuestion?: IQuestionNode, prevRootQuestion?: IQuestionNode, isInSameChapter?: boolean
) {
  if (question.question_type === 'material') {
    const blankLenth = question.content.body.match(/data-label="blank"/g)?.length;
    let childQuestionCountLevel1 = 0;
    question.children.map((q) => {
      if (q.node_type === 'question' && q.content.level === 1) {
        childQuestionCountLevel1 += 1;
      }
    });
    if (blankLenth && blankLenth !== childQuestionCountLevel1) {
      return [{
        rule: EJsonErrorRule.serial_number_material_blank_error,
        message: `材料题填空个数(${blankLenth})与其一级子题个数不符(${childQuestionCountLevel1})`,
      }];
    }
    return [];
  }
  if (isEmptyText(question.content.serial_number)) {
    return [{
      rule: EJsonErrorRule.serial_number_empty,
      message: '题号为空',
    }];
  }
  const result = parseSerialNumber(question.content.serial_number);
  if (!result) {
    return [{
      rule: EJsonErrorRule.serial_number_format_invalid,
      message: `题号不是编号,${question.content.serial_number}`,
    }];
  }
  if (result.type === 'label') {
    return [];
  }
  const pre
    = prevQuestion && prevQuestion.question_type !== 'material' ?
      prevQuestion.content.serial_number :
      prevRootQuestion ? prevRootQuestion!.content.serial_number : '';
  const cur = question.content.serial_number;
  if (prevQuestion || prevRootQuestion && (isInSameChapter || result.number !== 1)) {
    const prevResult = parseSerialNumber(
      prevQuestion && prevQuestion.question_type !== 'material' ?
        prevQuestion.content.serial_number :
        prevRootQuestion ? prevRootQuestion!.content.serial_number : ''
    );
    if (!prevResult || prevResult.type === 'label') {
      return [];
    }
    if (/例/.test(cur)) return [];
    if (!sameSerialNumberType(prevResult, result)) {
      // 上一题题号错误，或者与上一题题号格式不一致
      return [{
        rule: EJsonErrorRule.serial_number_format_mismatch_neighbor,
        message: `与上一题题号格式不一致,${pre},${cur}`,
      }];
    }
    // 修改为差异过大时（先定个50）
    if (Math.abs(prevResult.number - result.number) > 50) {
      return [{
        rule: EJsonErrorRule.serial_number_discontinuous,
        message: `题号不连续：${pre}->${cur}`,
      }];
    }
  } else if (result.number !== 1) {
    return [{
      rule: EJsonErrorRule.serial_number_not_first,
      message: `题号应该从第一个编号开始,${question.content.serial_number}`,
    }];
  }
  return [];
}

// 校验材料题，题干上的空的序号是否和题号一致
function validateMaterialBlankSn(question: IQuestionNode) {
  const re = /data-sn-text="([^"]+)"/g;
  const body = question.content.body;
  let match = re.exec(body);
  const sns: string[] = [];
  while (match) {
    sns.push(match[1]);
    match = re.exec(body);
  }
  if (!sns.length) return [];
  const snSet = new Set(sns);
  if (sns.length !== snSet.size) {
    return [{
      rule: EJsonErrorRule.material_blank_sn,
      message: '材料题题干中的题号有重复',
    }];
  }
  const questions = question.children.filter((n) => n.node_type === 'question') as IBasicQuestionNode[];
  if (snSet.size !== questions.length) {
    return [{
      rule: EJsonErrorRule.material_blank_sn,
      message: '材料题题干中的题号与题目数量不同',
    }];
  }
  for (const q of questions) {
    if (!snSet.has(q.content.serial_number)) {
      return [{
        rule: EJsonErrorRule.material_blank_sn,
        message: `材料题题干中的题号与题目题号不同,${q.content.serial_number}`,
      }];
    }
  }
  return [];
}

/**
 * 检验解析是否存在作答空间下划线的脏数据
 **/
function checkAnalysisBlank(content: string) {
  const reg = /data-label="blank"/g;
  const match = content.match(reg);
  if (match && match.length) {
    return true;
  }
  return false;
}

// 校验表格单元格
function validateTableCells(content: string): IJsonErrorInfo[] {
  const errors: IJsonErrorInfo[] = [];

  // 匹配所有table标签及其内容
  const tableRegex = /<table[^>]*>([\s\S]*?)<\/table>/g;
  let tableMatch;

  while ((tableMatch = tableRegex.exec(content)) !== null) {
    const tableContent = tableMatch[1];

    // 匹配所有td标签及其内容
    const tdRegex = /<td[^>]*>([\s\S]*?)<\/td>/g;
    const tdContents: string[] = [];
    let tdMatch;

    while ((tdMatch = tdRegex.exec(tableContent)) !== null) {
      // 获取td内容,去除HTML标签和空格
      const tdContent = tdMatch[1]
        .replace(/<[^>]+>/g, '')
        .replace(/&nbsp;/g, ' ')
        .trim();
      tdContents.push(tdContent);
    }

    // 检查是否有的单元格有内容,有的没有
    const hasContent = tdContents.some((cellContent) => cellContent.length > 0);
    const hasEmpty = tdContents.some((cellContent) => cellContent.length === 0);

    if (hasContent && hasEmpty) {
      errors.push({
        rule: EJsonErrorRule.table_cell_inconsistent,
        message: '表格中存在内容不一致的单元格',
      });
      // 一个表格报错就够了
      break;
    }
  }

  return errors;
}

// 校验题目内容长度
function validateQuestionContentLength(question: IQuestionNode) {
  const { body } = question.content;

  if (!body) {
    return [];
  }

  // 去掉HTML标签
  const textWithoutTags = body.replace(/<[^>]*>/g, '');

  // 去掉标点符号（保留中文字符、英文字母、数字）
  const textWithoutPunctuation = textWithoutTags.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');

  // 计算字符长度
  const contentLength = textWithoutPunctuation.length;

  if (contentLength < 5) {
    return [{
      rule: EJsonErrorRule.node_content_error,
      message: '这个题内容太少了，可能有问题，请检查',
    }];
  }

  return [];
}

// 校验解析中是否重复了答案
function validateAnalysisAnswerDuplicate(question: IQuestionNode) {
  const { analysis, answer } = question.content;

  if (!analysis || !answer || !answer.length || question.question_type !== 'choice') {
    return [];
  }

  // 匹配所有p标签及其内容
  const pTagRegex = /<p[^>]*>([\s\S]*?)<\/p>/g;
  const pContents: string[] = [];
  let match;

  while ((match = pTagRegex.exec(analysis)) !== null) {
    // 获取p标签内容，去除HTML标签
    const pContent = match[1].replace(/<[^>]+>/g, '').trim();
    if (pContent) {
      pContents.push(pContent);
    }
  }

  // 如果没有找到p标签，直接返回
  if (!pContents.length) {
    return [];
  }

  // 获取最后一个p标签的内容
  const lastPContent = pContents[pContents.length - 1];

  // 检查最后一个p标签的内容是否与answer中的某一项完全一致
  for (let i = 0; i < answer.length; i++) {
    const answerText = isHtmlStr(answer[i]) ?
      answer[i].replace(/<[^>]+>/g, '').trim() :
      answer[i].trim();

    if (lastPContent === answerText) {
      return [{
        rule: EJsonErrorRule.analysis_answer_duplicate,
        message: '解析中疑似重复生成了答案，请检查解析内容',
      }];
    }
  }

  return [];
}

// 校验题干开头是否与题号重复
function validateBodySerialNumberDuplicate(question: IQuestionNode) {
  const { body, serial_number, analysis, answer } = question.content;
  const errors: IJsonErrorInfo[] = [];

  if (!serial_number) {
    return [];
  }

  const serialNumberText = serial_number.trim();

  // 检查body开头的文本是否与题号相同
  if (body) {
    const bodyText = body.replace(/<[^>]+>/g, '').trim();
    if (bodyText.startsWith(serialNumberText)) {
      errors.push({
        rule: EJsonErrorRule.content_serial_number_duplicate,
        message: '题号重复，请检查题干内容',
      });
    }
  }

  // 检查analysis开头的文本是否与题号相同
  if (analysis) {
    const analysisText = analysis.replace(/<[^>]+>/g, '').trim();
    if (analysisText.startsWith(serialNumberText)) {
      errors.push({
        rule: EJsonErrorRule.content_serial_number_duplicate,
        message: '题号重复，请检查解析内容',
      });
    }
  }

  // 检查answer第一个元素是否与题号相同
  if (answer && answer.length > 0) {
    const firstAnswer = answer[0];
    const answerText = firstAnswer.replace(/<[^>]+>/g, '').trim();
    if (answerText.startsWith(serialNumberText)) {
      errors.push({
        rule: EJsonErrorRule.content_serial_number_duplicate,
        message: '题号重复，请检查答案内容',
      });
    }
  }

  return errors;
}

// 校验试题
function validateQuestion(question: IQuestionNode, prevQuestion?: IQuestionNode, parent?: TJsonNode, stopIterateChildren?: () => void) {
  let error: IJsonErrorInfo[] = [];
  const { source, body } = question.content;

  // 添加图片验证
  error = error.concat(validateQuestionImage(question));

  // 添加内容长度验证
  error = error.concat(validateQuestionContentLength(question));

  // 添加解析答案重复验证
  error = error.concat(validateAnalysisAnswerDuplicate(question));

  // 添加题干开头与题号重复验证
  error = error.concat(validateBodySerialNumberDuplicate(question));

  if (stopIterateChildren) {
  }

  if (/^\([\s\S]*?\)$/.test(source) || /^（[\s\S]*?）$/.test(source) || /^【[\s\S]*?】$/.test(source)) {
    error.push({
      rule: EJsonErrorRule.source_has_bracket,
      message: '试题来源不应该有括号',
    });
  }
  if (/data-label="source"/.test(body) && body.match(/data-label="source"/g)!.length > 1) {
    error.push({
      rule: EJsonErrorRule.multi_source,
      message: '试题来源不应该有多个',
    });
  }
  if (!question.children.length) {
    error = error.concat(validateAnswer(question));
    const isMaterialSub = parent && parent.node_type === 'question' && parent.question_type === 'material';
    const isEmptyBlank = question.question_type === 'blank';
    const tag = isMaterialSub || isEmptyBlank;
    if (!isChoiceQuestion(question) && isEmptyHtml(question.content.body, !tag)) {
      error.push({
        rule: EJsonErrorRule.question_content_empty,
        message: '题干不能为空',
      });
    }
  }
  if (isChoiceQuestion(question)) {
    error = error.concat(validateChoice(question, prevQuestion));
  } else if (question.question_type === 'true_or_false') {
    error = error.concat(validateTrueOrFalse(question));
  } else if (question.question_type === 'blank') {
    error = error.concat(validateBlank(question));
  } else {
    const { children } = question;
    const questionChildren = children.filter((node) => node.node_type === 'question');
    if (question.question_type === 'material') {
      error = error.concat(validateMaterialBlankSn(question));
      if (!questionChildren.length) {
        error.push({
          rule: EJsonErrorRule.subs_is_empty,
          message: '材料题必须有小题',
        });
      }
    }
    if (children.length && questionChildren.length < 2) {
      error.push({
        rule: EJsonErrorRule.subs_question_min_limit,
        message: '小题数量至少为2',
      });
    }
    // 如果有三级小题，并且三级小题存在题号相同的题，需要报错
    if (questionChildren.length > 1) {
      // 继续向下
      const secondLevelQuestions = questionChildren;
      // 二维数组
      const thirdLevelQuestions = secondLevelQuestions.map((node) => node.children.filter((_node) => _node.node_type === 'question'));
      if ((thirdLevelQuestions as any).flat().length && (thirdLevelQuestions as any).flat().some((v) => v.content.analysis.trim())) {
        const serialNumbers = (thirdLevelQuestions as any).flat().map((node) => node.content.serial_number);
        const serialNumberSet = new Set(serialNumbers);
        if (serialNumbers.length !== serialNumberSet.size) {
          error.push({
            rule: EJsonErrorRule.third_level_serial_number_duplicate,
            message: '三级小题存在重复题号，请检查解析位置是否正确！',
          });
        }
      }
    }
  }
  // 如果 sequence 里有 重复的字段，报错并且把字段报出来
  const sequence = question.content._sequence;
  const sequenceSet = new Set(sequence);
  if (sequence.length !== sequenceSet.size) {
    const duplicateFields = sequence.filter((field, index, self) =>
      self.indexOf(field) !== index
    );
    error.push({
      rule: EJsonErrorRule.node_content_error,
      message: `存在重复字段：${duplicateFields.join(', ')}，请检查答案、解析位置是否正确！`,
    });
  }

  return error;
}

function validateXdocTags(content: string, fieldName: string): IJsonErrorInfo[] {
  // 2. 区块标签 (Block-level Elements)
  const XDOC_STANDARD_TAGS = [
    'div', 'p', 'section', 'span', 'strong',
    'b', 'i', 'u', 'sub', 'sup', 'table',
    'thead', 'tbody', 'tfoot', 'tr', 'th',
    'td', 'colgroup', 'col', 'hr', 'img'
  ];

  const errors: IJsonErrorInfo[] = [];

  if (!content || !content.trim()) {
    return errors;
  }

  // 匹配所有HTML标签
  const allTagsRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*\/?>/g;
  const invalidTags: string[] = [];
  let match;

  while ((match = allTagsRegex.exec(content)) !== null) {
    const tagName = match[1].toLowerCase();

    // 检查是否为标准HTML5标签
    if (!XDOC_STANDARD_TAGS.includes(tagName)) {
      // 避免重复添加相同的标签
      if (!invalidTags.includes(tagName)) {
        invalidTags.push(tagName);
      }
    }
  }

  // 如果发现非标准标签，添加错误信息
  if (invalidTags.length > 0) {
    errors.push({
      rule: EJsonErrorRule.node_content_error,
      message: `${fieldName}中存在非HTML5标准标签: ${invalidTags.join(', ')}`,
    });
  }

  return errors;
}

function validateContent(node: TJsonNode) {
  const error: IJsonErrorInfo[] = [];
  const { content: { body } } = node;
  const chineseEnglishBracketReg = /(\([^\)]*?）)|(（[^）]*?\))/g;

  // 根据节点类型确定body字段的描述
  let bodyFieldName = '';
  switch (node.node_type) {
  case 'question':
    bodyFieldName = '题干';
    break;
  case 'chapter':
    bodyFieldName = '标题';
    break;
  case 'paragraph':
    bodyFieldName = '段落';
    break;
  default:
    bodyFieldName = 'body';
  }

  // 检查body字段的HTML标签
  if (body) {
    error.push(...validateXdocTags(body, bodyFieldName));

    // 检查是否包含异常字符〗
    if (body.includes('〗')) {
      error.push({
        rule: EJsonErrorRule.content_invalid_character,
        message: '内容包含异常字符〗',
      });
    }

    // 检查公式中是否包含答案分割符■
    error.push(...validateFormulaAnswerSeparator(body));

    if (/data-label="choice_option"/g.test(body)) {
      error.push({
        rule: EJsonErrorRule.error_body_option,
        message: '题干中存在选项',
      });
    }
    // 需要先去掉所有公式
    const _body = body.replace(/\$\$[^$]*?\$\$/g, '');
    const leftCount = (_body.replace(/\$\$[^$]*?\$\$/g, '').match(/[(（]/g) || []).length;
    const rightCount = (_body.replace(/\$\$[^$]*?\$\$/g, '').match(/[)）]/g) || []).length;
    if (leftCount !== rightCount) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '左右括号数量不一致',
      });
    }
    if (chineseEnglishBracketReg.test(body)) {
      error.push({
        rule: EJsonErrorRule.node_content_error,
        message: '内容中有异常的中英文括号组合',
      });
    }
    const latexStraightRes = checkHtmlStraight(body);
    if (latexStraightRes[0]) {
      error.push({
        rule: EJsonErrorRule.error_straight_latex,
        message: `公式正斜体异常，题干内容，${latexStraightRes[1]}`,
      });
    }
    error.push(...validateTableCells(body));
  }
  if (node.node_type === 'question') {
    const { content: { answer, analysis, source, choices } } = node;
    if (source) {
      // 检查来源中是否包含异常字符〗
      if (source.includes('〗')) {
        error.push({
          rule: EJsonErrorRule.content_invalid_character,
          message: '来源包含异常字符〗',
        });
      }

      // 检查来源中公式是否包含答案分割符■
      error.push(...validateFormulaAnswerSeparator(source));

      if (chineseEnglishBracketReg.test(source)) {
        error.push({
          rule: EJsonErrorRule.node_content_error,
          message: '来源中有异常的中英文括号组合',
        });
      }
    }
    if (choices && choices.length) {
      choices.forEach((item) => {
        let hasError = false;

        // 检查选项中是否包含异常字符〗
        if (item.option.includes('〗')) {
          error.push({
            rule: EJsonErrorRule.content_invalid_character,
            message: `选项${item.letter}包含异常字符〗`,
          });
        }

        // 检查选项中公式是否包含答案分割符■
        error.push(...validateFormulaAnswerSeparator(item.option));

        if (chineseEnglishBracketReg.test(item.option)) {
          hasError = true;
        }
        if (hasError) {
          error.push({
            rule: EJsonErrorRule.node_content_error,
            message: '选项中有异常的中英文括号组合',
          });
        }
        if (item.option.match(/^<p[^<>]*><span data-label="tab">/)) {
          error.push({
            rule: EJsonErrorRule.choice_start_with_tab,
            message: '选择题选项不能以制表符开头',
          });
        }
        const latexStraightRes = checkHtmlStraight(item.option);
        if (latexStraightRes[0]) {
          error.push({
            rule: EJsonErrorRule.error_straight_latex,
            message: `公式正斜体异常，选项内容${item.letter}，${latexStraightRes[1]}`,
          });
        }
        error.push(...validateXdocTags(item.option, `选项${item.letter}`));
      });
    }
    if (answer && answer.length) {
      answer.forEach((item) => {
        let hasError = false;

        // 检查答案中是否包含异常字符〗
        if (item.includes('〗')) {
          error.push({
            rule: EJsonErrorRule.content_invalid_character,
            message: '答案包含异常字符〗',
          });
        }

        // 检查答案中公式是否包含答案分割符■
        error.push(...validateFormulaAnswerSeparator(item));

        if (chineseEnglishBracketReg.test(item)) {
          hasError = true;
        }
        if (hasError) {
          error.push({
            rule: EJsonErrorRule.node_content_error,
            message: '答案中有异常的中英文括号组合',
          });
        }

        const latexStraightRes = checkHtmlStraight(item);
        if (latexStraightRes[0]) {
          error.push({
            rule: EJsonErrorRule.error_straight_latex,
            message: `公式正斜体异常，答案内容，${latexStraightRes[1]}`,
          });
        }
        error.push(...validateXdocTags(item, '答案'));
      });
    }
    if (analysis) {
      // 检查解析中是否包含异常字符〗
      if (analysis.includes('〗')) {
        error.push({
          rule: EJsonErrorRule.content_invalid_character,
          message: '解析包含异常字符〗',
        });
      }

      // 检查解析中公式是否包含答案分割符■
      error.push(...validateFormulaAnswerSeparator(analysis));

      if (chineseEnglishBracketReg.test(analysis)) {
        error.push({
          rule: EJsonErrorRule.node_content_error,
          message: '解析中有异常的中英文括号组合',
        });
      }

      const latexStraightRes = checkHtmlStraight(analysis);
      if (latexStraightRes[0]) {
        error.push({
          rule: EJsonErrorRule.error_straight_latex,
          message: `公式正斜体异常，解析内容，${latexStraightRes[1]}`,
        });
      }
      if (checkAnalysisBlank(analysis)) {
        error.push({
          rule: EJsonErrorRule.analysis_blank,
          message: '解析中有作答空间',
        });
      }
      error.push(...validateXdocTags(analysis, '解析'));
    }
  }
  return error;
}

function validateChapter(chapter: IChapterNode) {
  const {
    // eslint-disable-next-line camelcase
    node_name,
    content: { body },
    children,
  } = chapter;
  const tagRegex = /(?<=<[^>]+>)([\s\S]*?)(?=<\/[^>]+>)/g;
  const bodyContent = body.match(tagRegex);
  const blankReg = /^(&nbsp;|\s).*/g;

  if (isEmptyText(node_name) || isEmptyHtml(body)) {
    return [{
      rule: EJsonErrorRule.chapter_content_empty,
      message: '目录内容不能为空',
    }];
  }

  if (bodyContent && bodyContent.length > 0 && blankReg.test(bodyContent[0])) {
    return [
      {
        rule: EJsonErrorRule.chapter_content_start_with_blank,
        message: '标题内容以空格开头',
      }
    ];
  }

  if (!children.length) {
    return [{
      rule: EJsonErrorRule.chapter_children_empty,
      message: '目录子节点内容不能为空',
    }];
  }
  return [];
}

function validateParagraph(paragraph: IParagraphNode) {
  const error: IJsonErrorInfo[] = [];
  if (isEmptyHtml(paragraph.content.body)) {
    error.push({
      rule: EJsonErrorRule.paragraph_content_empty,
      message: '段落内容不能为空',
    });
  }
  if (/data-label="source"/.test(paragraph.content.body)) {
    error.push({
      rule: EJsonErrorRule.paragraph_content_error,
      message: '段落内不能有题源',
    });
  }
  if (paragraph.children.length) {
    error.push({
      rule: EJsonErrorRule.subs_not_empty,
      message: '段落不能有子节点',
    });
  }
  return error;
}

function getPrevQuestion(siblings: TJsonNode[], index: number) {
  for (let i = index - 1; i >= 0; i -= 1) {
    const node = siblings[i];
    if (node.node_type === 'question') {
      if (/例/.test(node.content.serial_number)) continue;
      return node;
    }
  }
}

function validExampleListQuestion(
  question: IQuestionNode, prevQuestion: IQuestionNode
) {
  const result = parseSerialNumber(question.content.serial_number);
  if (prevQuestion && result) {
    const prevResult = parseSerialNumber(prevQuestion.content.serial_number);
    if (!prevResult || prevResult.type === 'label' || result.type === 'label' || result.number === 1) {
      return [];
    }
    if (prevResult.number + 1 !== result.number) {
      return [{
        rule: EJsonErrorRule.serial_number_discontinuous,
        message: '例题题号不连续',
      }];
    }
    return [];
  } if (result && !prevQuestion && result.type !== 'label' && result.number !== 1) {
    return [{
      rule: EJsonErrorRule.serial_number_not_first,
      message: `题号应该从第一个编号开始,${question.content.serial_number}`,
    }];
  }
  return [];
}

function validateQuestionImage(question: IQuestionNode) {
  const { body } = question.content;
  if (question.content.level !== 1) {
    return [];
  }

  // 检查是否包含明确的图片引用模式
  const imageReferencePatterns = [
    /如图/,
    /下图/,
    /上图/,
    /左图/,
    /右图/,
    /见图/,
    /图中/,
    /图示/,
    /按图/,
    /依图/,
    /参照图/,
    /根据图/,
    /从图/,
    /图\s*[0-9]+/, // 图1、图2等
    /图\s*[A-Za-z]+/, // 图A、图B等
    /[（(]图[）)]/ // (图)、（图）
  ];

  const hasImageReference = imageReferencePatterns.some((pattern) => pattern.test(body));

  if (!hasImageReference) {
    return [];
  }

  // Check for img tags
  const hasImage = /<img[^>]+>/.test(body);

  if (!hasImage) {
    return [{
      rule: EJsonErrorRule.question_missing_image,
      message: '题干提到图但缺少图片',
    }];
  }

  return [];
}

export function validateJsonNodes(nodes: TJsonNode[], processInfo?: { isError: boolean }) {
  const parentMap = new Map<IQuestionNode, TJsonNode>();
  for (const { node, parent, stopIterateChildren } of iterateNode(nodes)) {
    if (
      parent &&
      (parent.node_type !== 'question' || parent.question_type === 'material') &&
      node.node_type === 'question'
    ) {
      parentMap.set(node, parent);
    }
    if (node.node_type === 'question' && node.question_type !== 'material') {
      stopIterateChildren!();
    }
  }
  function getRootQuestionChapter(question?: IQuestionNode) {
    if (!question) {
      return;
    }
    let parent = parentMap.get(question);
    if (parent && parent.node_type === 'question') {
      parent = parentMap.get(parent);
    }
    return parent;
  }

  function getExampleNode(node: IQuestionNode) {
    return /例/.test(node.content.serial_number);
  }

  const exampleNodeList: IQuestionNode[] = [];
  const rootQuestions = findAllNodes(nodes, ({ node }) => {
    return node.node_type === 'question' && node.question_type !== 'material';
  }) as IQuestionNode[];
  const rootQuestionIndexMap = new Map(rootQuestions.map((q, i) => [q, i]));
  for (const { node, siblings, index, parent, stopIterateChildren } of iterateNode(nodes)) {
    let error = node.errorInfo ?? [];
    if (node.node_type === 'chapter') {
      error = [
        ...error,
        ...validateChapter(node)
      ];
    } else if (node.node_type === 'paragraph') {
      error = [
        ...error,
        ...validateParagraph(node)
      ];
    } else if (node.node_type === 'question') {
      // 例题单独排序
      if (getExampleNode(node)) {
        exampleNodeList.push(node);
      }

      // 上一题
      const prevQuestion = getPrevQuestion(siblings, index);
      // 跨章节上一题
      const rootIndex = rootQuestionIndexMap.get(node);
      const isRootQuestion = !parent || parent.node_type !== 'question' || parent.question_type === 'material';
      const prevRootQuestion = rootIndex != null && isRootQuestion ? rootQuestions[rootIndex - 1] : undefined;
      const isInSameChapter = getRootQuestionChapter(node) === getRootQuestionChapter(prevRootQuestion);
      error = [
        ...error,
        ...validateQuestionContentLevel(node, parent),
        ...validateQuestionSerialNumber(node, prevQuestion, prevRootQuestion, isInSameChapter),
        ...validateQuestion(node, prevQuestion, parent, stopIterateChildren)
      ];
    }
    error = [
      ...error,
      ...validateContent(node)
    ];
    node.errorInfo = error.length ? error : undefined;
    if (error.length && processInfo) {
      processInfo.isError = true;
    }
  }
  // 例题单独排序
  exampleNodeList.map((node, index) => {
    let error = node.errorInfo ?? [];
    const prevQuestion = exampleNodeList[index - 1];
    error = [
      ...error,
      ...validExampleListQuestion(node, prevQuestion)
    ];
    node.errorInfo = error.length ? error : undefined;
  });

  // 检查题目内容重复
  validateQuestionDuplicate(nodes);

  return nodes;
}
