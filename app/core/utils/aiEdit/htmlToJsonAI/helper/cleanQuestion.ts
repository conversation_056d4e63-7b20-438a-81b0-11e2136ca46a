// 最后：清理试题信息
import { iterateNode } from '../../../treeHelper';
import { addTextForHtml, getHtml, getText, parseHtml } from '../../../htmlHelper';
import { IBasicQuestionNode, IQuestionNode, TJsonNode } from '../index';
import { hasHtmlTag, strapText } from '../../../index';
import { parseSerialNumber } from '../../../parseSerialNumber';

export function setupMaterialBodyBlankSn(elementTree: TJsonNode[]) {
  for (const item of iterateNode(elementTree)) {
    if (item.node.node_type !== 'question' || item.node.question_type !== 'material') {
      continue;
    }
    item.stopIterateChildren!();
    const body = item.node.content.body;
    if (!hasHtmlTag(body)) continue;
    const nodes = parseHtml(body);
    for (const item of iterateNode(nodes)) {
      if (item.node.type !== 'element'
        || item.node.dataset.label !== 'blank'
        && item.node.dataset.label !== 'bracket') {
        continue;
      }
      item.stopIterateChildren!();
      const text = strapText(getText(item.node.children)).replace(/\./g, '');
      const snResult = parseSerialNumber(text);
      const sn = snResult?.type === 'number' ? snResult.number : undefined;
      if (sn) {
        item.node.dataset.sn = `${sn}`;
        item.node.attrs['data-sn'] = `${sn}`;
      }
      item.node.dataset['sn-text'] = text;
      item.node.attrs['data-sn-text'] = text;
    }
    const html = getHtml(nodes);
    item.node.content.body = html;
  }
}

// 合并小题目的解析到大题上
export function mergeQuestionExplanation(elementTree: TJsonNode[], existIgnore = false) {
  for (const { node, stopIterateChildren } of iterateNode(elementTree)) {
    // 先找到有小题的题目
    if (node.node_type !== 'question' || !node.children.length || node.question_type === 'material') {
      continue;
    }
    stopIterateChildren!();
    if (existIgnore && node.content.analysis) {
      continue;
    }
    const explanations: string[] = [];
    for (const item of iterateNode([node])) {
      if (node.node_type !== 'question') {
        continue;
      }
      const { analysis, level, serial_number } = item.node.content;
      if (analysis) {
        explanations.push(level === 1 ? analysis : addTextForHtml(analysis, `${serial_number} `));
      }
    }
    node.content.analysis = explanations.join('\n');
  }
}

// 设置材料题题号
export function setMaterialSerialNumber(elementTree: TJsonNode[]) {
  for (const { node, stopIterateChildren } of iterateNode(elementTree)) {
    if (node.node_type !== 'question') {
      continue;
    }
    stopIterateChildren!();
    if (node.question_type !== 'material' || node.content.serial_number) {
      continue;
    }
    const questions = node.children.filter((item) => item.node_type === 'question') as IBasicQuestionNode[];
    if (!questions.length) {
      node.content.serial_number = '';
      continue;
    }
    const first = questions[0];
    const last = questions[questions.length - 1];
    node.content.serial_number = `${first.content.serial_number}~${last.content.serial_number}`;
  }
}

// 清除大题上的空白答案
export function cleanQuestionAnswer(elementTree: TJsonNode[]) {
  for (const { node } of iterateNode(elementTree)) {
    if (node.node_type !== 'question' || !node.children.length) {
      continue;
    }
    if (!node.content.answer.join('')) {
      node.content.answer = [];
    }
  }
}

// 答案页合并同级同题号的答案和解析
export function mergeAnswerExplanationQuestion(elementTree: TJsonNode[]) {
  const notMatched: IQuestionNode[] = [];
  for (const { node } of iterateNode(elementTree)) {
    const map: { [key: string]: IQuestionNode } = {};
    const children: TJsonNode[] = [];
    node.children.forEach((node) => {
      if (node.node_type !== 'question') {
        children.push(node);
        return;
      }
      const key = `${node.content.serial_number}_${node.content.level}`;
      const pre = map[key];
      if (!pre) {
        map[key] = node;
        children.push(node);
      } else {
        pre.content.answer = pre.content.answer.concat(node.content.answer);
        pre.content.analysis += node.content.analysis;
        node.content._sequence.map((item) => {
          if (!pre.content._sequence.includes(item)) {
            pre.content._sequence.push(item);
          }
        });
        // 如果有答案，但是没题，答案会被丢掉
        // @todo: 可以试试如果匹配不上，原逻辑不变，把要丢掉的放到当前 child 的下面
        mergeSubAnswerExplanation(pre.children, node.children);
        node.children.forEach((child) => {
          if ((child as any).matched === false) {
            notMatched.push(child as IQuestionNode);
          }
        });
      }
    });
    node.children = children;
  }
  // elementTree[0].children = elementTree[0].children.concat(notMatched as any);
  return notMatched;
}

function mergeSubAnswerExplanation(target: TJsonNode[], source: TJsonNode[]) {
  const map: { [key: string]: IQuestionNode } = {};
  target.forEach((node) => {
    if (node.node_type !== 'question') return;
    const key = `${node.content.serial_number}_${node.content.level}`;
    map[key] = node;
  });
  source.forEach((node) => {
    if (node.node_type !== 'question') return;
    const key = `${node.content.serial_number}_${node.content.level}`;
    const target = map[key];
    if (!target) {
      (node as any).matched = false;
      return;
    }
    target.content.answer = target.content.answer.concat(node.content.answer);
    target.content.analysis += node.content.analysis;
    node.content._sequence.map((item) => {
      if (!target.content._sequence.includes(item)) {
        target.content._sequence.push(item);
      }
    });
    mergeSubAnswerExplanation(target.children, node.children);
  });
}
