// 第四步：将json节点组装成树
import { createTree, iterateNode } from '../../../treeHelper';
import {
  EJsonErrorRule,
  IBasicQuestionNode,
  IChapterNode,
  IJsonErrorInfo,
  IMaterialQuestionNode,
  IParagraphNode,
  IQuestionNode,
  TJsonNode
} from '../index';
import { ISeparatorElement, ITextElement } from './convertElement';

export function buildElementTree(
  elements: (IChapterNode | ITextElement | IBasicQuestionNode | ISeparatorElement)[]
) {
  if (!elements.length) {
    return [];
  }
  let dummyChapter: IChapterNode | undefined;
  if (elements[0].node_type !== 'chapter') {
    // 添加一个临时假章节
    dummyChapter = {
      node_type: 'chapter',
      node_level: 0,
      node_name: '',
      content: { level: 1, body: '' },
      children: [],
    };
    elements.unshift(dummyChapter);
  }
  let chapter: IChapterNode;
  let materialQuestion: IMaterialQuestionNode | undefined;
  const chapters: IChapterNode[] = [];
  elements.forEach((element) => {
    if (element.node_type === 'chapter') {
      // 目录
      chapter = element;
      chapters.push(chapter);
      if (materialQuestion) {
        // 新目录时，需要结束上一个材料题
        materialQuestion.errorInfo = [{
          rule: EJsonErrorRule.material_no_end,
          message: '材料题无结束分割线',
        }];
        materialQuestion = undefined;
      }
    } else if (element.node_type === 'none') {
      // 无标记段落
      if (materialQuestion) {
        if (element.errorInfo?.find((item) => item.message === '解析未匹配到试题')) {
          materialQuestion.content.analysis += element.content.body;
          if (!materialQuestion.content._sequence.includes('analysis')) materialQuestion.content._sequence.push('analysis');
        } else {
          // 可以合并到材料题题干中
          materialQuestion.content.body += element.content.body;
          materialQuestion.content.source += element.content.source;
        }
      } else {
        // 作为段落
        chapter.children.push({ ...element, node_type: 'paragraph', _node_type: 'none', node_level: 0, children: [] });
      }
    } else if (element.node_type === 'text') {
      // 标记段落
      const node = { ...element, node_type: 'paragraph', node_level: 0, children: [] } as IParagraphNode;
      if (materialQuestion) {
        // 作为材料题下的段落
        materialQuestion.children.push(node);
      } else {
        // 作为段落
        chapter.children.push(node);
      }
    } else if (element.node_type === 'question') {
      // 题目
      if (materialQuestion) {
        // 作为材料题下的题目
        materialQuestion.children.push(element);
      } else {
        // 作为题目
        chapter.children.push(element);
      }
    } else if (element.node_type === 'material_start_separator') {
      if (materialQuestion) {
        // 新材料题开始，需要结束上一个材料题
        materialQuestion.errorInfo = [{
          rule: EJsonErrorRule.material_no_end,
          message: '材料题无结束分割线',
        }];
      }
      // 材料题开始
      const contentMaterial = { _sequence: [], ...element, level: 0, serial_number: '', body: '', source: '', answer: [], analysis: '' };
      delete contentMaterial.imgId;
      delete contentMaterial.node_type;
      delete contentMaterial.errorInfo;
      materialQuestion = {
        node_type: 'question',
        node_level: 0,
        question_type: 'material',
        imgId: element.imgId,
        content: contentMaterial,
        children: [],
      };
      chapter.children.push(materialQuestion!);
    } else if (element.node_type === 'material_end_separator') {
      // 材料题结束
      if (materialQuestion) {
        materialQuestion = undefined;
      } else {
        chapter.children.push(createErrorParagraph({
          imgId: element.imgId,
          body: '材料题结束分割线',
          errorInfo: [{
            rule: EJsonErrorRule.material_no_start,
            message: '无效的材料题结束分割线',
          }],
        }));
      }
    }
  });

  if (materialQuestion) {
    // 结尾时，需要结束上一个材料题
    materialQuestion.errorInfo = [{
      rule: EJsonErrorRule.material_no_end,
      message: '材料题无结束分割线',
    }];
  }

  const getLevel = (n: TJsonNode) => Math.abs(n.content.level) || 1;
  chapters.forEach((chapter) => {
    // 目录下的材料题建立树
    chapter.children.forEach((child) => {
      if (child.node_type === 'question' && child.question_type === 'material') {
        child.children = createTree(child.children, {
          getLevel,
          canAddSub: (n) => n.node_type !== 'paragraph',
        });
      }
    });
    // 目录下的试题建立树
    chapter.children = createTree(chapter.children, {
      getLevel,
      canAddSub: (n) => !(n.node_type === 'question' && n.question_type === 'material') && n.node_type !== 'paragraph',
    });
  });
  // 目录建树
  let chapterTree = createTree(chapters as TJsonNode[], {
    getLevel,
    canAddSub: (n) => n.node_type === 'chapter',
  });
  if (dummyChapter) { // 删除假章节
    chapterTree = dummyChapter.children.concat(chapterTree.slice(1));
  }

  for (const { node } of iterateNode(chapterTree)) {
    if (node.node_type === 'chapter' || node.node_type === 'question') {
      const children = node.children;
      const minQuestionLevel = children.filter((item) => item.node_type === 'question' && item.question_type !== 'material').map((item) => Math.abs(item.content.level)).sort((a, b) => a - b)[0];
      if (minQuestionLevel && children.some((item) => item.node_type === 'question' && Math.abs(item.content.level) > minQuestionLevel)) {
        let minQuestion: IQuestionNode | undefined;
        let minQuestionChilds: TJsonNode[] = [];
        const minQuestionAllChilds: TJsonNode[] = [];
        children.forEach((item) => {
          if (item.node_type === 'question' && Math.abs(item.content.level) === minQuestionLevel) {
            if (minQuestion && minQuestionChilds.some((item) => item.node_type === 'question' && Math.abs(item.content.level) > minQuestionLevel)) {
              let lastQuestionIndex = 0;
              for (let i = minQuestionChilds.length - 1; i >= 0; i -= 1) {
                if (minQuestionChilds[i].node_type === 'question') {
                  lastQuestionIndex = i;
                  break;
                }
              }
              const list: any[] = minQuestionChilds.slice(0, lastQuestionIndex + 1);
              minQuestion.children.push(...list);
              minQuestionAllChilds.push(...list);
            }
            minQuestionChilds = [];
            minQuestion = item;
          }
          if (item.node_type === 'question' && Math.abs(item.content.level) > minQuestionLevel || item.node_type === 'paragraph') {
            minQuestionChilds.push(item);
          }
        });
        if (minQuestion && minQuestionChilds.some((item) => item.node_type === 'question' && Math.abs(item.content.level) > minQuestionLevel)) {
          let lastQuestionIndex = 0;
          for (let i = minQuestionChilds.length - 1; i >= 0; i -= 1) {
            if (minQuestionChilds[i].node_type === 'question') {
              lastQuestionIndex = i;
              break;
            }
          }
          const list: any[] = minQuestionChilds.slice(0, lastQuestionIndex + 1);
          minQuestion.children.push(...list);
          minQuestionAllChilds.push(...list);
        }
        node.children = node.children.filter((item) => !minQuestionAllChilds.find((child) => child === item));
      }
    }
  }

  for (const { node, level } of iterateNode(chapterTree)) {
    node.node_level = level + 1;
    // 删除 children 后再加回来，是为了让 children 属性在最后。
    const children = node.children;
    delete node.children;
    node.children = children;
  }

  return chapterTree;
}

function createErrorParagraph(
  { body, imgId, errorInfo }: { body: string; imgId?: string; errorInfo: IJsonErrorInfo[] }
) {
  return {
    node_type: 'paragraph',
    node_level: 0,
    imgId,
    content: { body, level: 1, tag: '', source: '' },
    children: [],
    errorInfo,
  } as IParagraphNode;
}
