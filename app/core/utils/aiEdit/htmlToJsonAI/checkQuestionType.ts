// 检查题型
import { getText, parseHtml } from '../../htmlHelper';
import { IQuestionContent, TBasicQuestionType } from './index';
import { convertLatexToPlainText, formatLatexHtml, isHtmlStr } from '../../helper';

function cleanChoiceAnswer(answer: string[]) {
  return answer.map((answer) => {
    const isHtml = isHtmlStr(answer);
    const hasLatex = /\$\$/.test(answer);
    if (!isHtml && !hasLatex) {
      return answer;
    }
    const nodes = parseHtml(formatLatexHtml(answer, { latexMode: 'dataset' }));
    if (hasLatex) {
      convertLatexToPlainText(nodes);
    }
    let cleanedAnswer = getText(nodes);
    cleanedAnswer = cleanedAnswer.replace(/[\s$.]/g, '').trim();
    if (cleanedAnswer && /^[a-z]+$/ig.test(cleanedAnswer)) {
      // 如果仅有字母，则更新为一个字母一个字符。
      return cleanedAnswer;
    }
    return answer;
  });
}

// 检查题型，同时清理一些无效数据
export function checkQuestionType(content: IQuestionContent) {
  let { blank_count, bracket_count, choices, choice, answer, correct, body } = content;
  let question_type: TBasicQuestionType = 'other';
  const blankCount = blank_count || bracket_count;
  if (blankCount) {
    question_type = 'blank';
  }
  if (choice) { // 兼容旧版
    choices = Object.keys(choice).map((letter) => ({ letter, option: choice[letter] }));
    if (!choices.length) {
      choices = undefined;
      delete content.choice;
      delete content.choice_count;
    }
  }
  if (choices) {
    content.choice_count = choices.length;
    // 少于2个空时，题干中解析选择题选项 或者 题干内有选项并且括号结尾
    if (!blankCount || blankCount < 2 || answer.length < 2 || choices.length && /<span[^<>]*?data-label="bracket"[^<>]*?>[^<>]*?<\/span[^<>]*?><\/p>/.test(body)) {
      question_type = choice ? ('option' as any) : 'choice'; // 兼容旧版
      content.answer = cleanChoiceAnswer(answer);
    }
  }
  if (!choices && (!blankCount || blankCount === 1) && correct) {
    question_type = 'true_or_false';
  }
  return question_type;
}
