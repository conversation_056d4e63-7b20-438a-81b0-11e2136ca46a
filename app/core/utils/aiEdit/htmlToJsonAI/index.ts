/* eslint-disable */
import { iterateNode, splitNodes } from '../../treeHelper';
import { IElementNode, parseHtml, getHtml } from './htmlHelper';
import {
  cleanQuestionAnswer,
  mergeAnswerExplanationQuestion,
  // mergeQuestionExplanation,
  setMaterialSerialNumber,
  setupMaterialBodyBlankSn
  // @ts-ignore
} from './helper/cleanQuestion';
import { validateJsonNodes } from './validateJsonNodes';
import { combineJson } from '../../jsonHelper';
import { groupNodes, IHtmlNodeGroup } from './helper/groupHtmlNode';
import { preBuildElementsData } from './helper/preBuildData';
import { filterEmptyNode } from './helper/filterEmptyNode';
import { convertElements } from './helper/convertElement';
import { buildElementTree } from './helper/buildElementTree';
import { addLabelForAnswerQuestNum, cleanupNode, mergeLabelNear } from './helper/cleanupNode';
import { IQuestionDataInfo } from './helper/preBuildData';
import { validateJsonRule } from './validateJsonRule';
import { postProcessTree } from './helper/postProcessTree';
import {distributeAnswersToSubQuestions} from "../../htmlToJsonV4/helper/convertElement";

export { validateJsonNodes } from './validateJsonNodes';
export { checkQuestionType } from './checkQuestionType';

interface IBaseContent {
  level: number;
  body: string;
  _body?: string;
  meta?: { [key: string]: any };  // 用于保留状态
  notes?: INotes[];
}

export interface IChapterContent extends IBaseContent {}

export interface IParagraphContent extends IBaseContent {
  tag: string;
  source: string;
}

export interface IQuestionChoice {
  letter: string;
  option: string;
}

export interface IExtra {
  title?: string;
  body: string;
}
export interface INotes {
  note_id: string;
  note_content: TJsonNode[];
}

export interface IQuestionContent extends IBaseContent {
  serial_number: string;
  _serial_number?: string;
  sn?: number;
  answer: string[];
  correct?: (boolean | undefined)[];
  source: string;
  analysis: string;
  _sequence: string[];
  blank_count?: number;
  bracket_count?: number;
  choices?: IQuestionChoice[];
  choice_count?: number;
  choice?: any; // 兼容老版本
  [propName: string]: any;
}

export interface IChapterData {
  node_name: string;
  content: IChapterContent;
}

export interface IParagraphData {
  content: IParagraphContent;
}

export type TBasicQuestionType = 'choice' | 'true_or_false' | 'blank' | 'other';

export interface IQuestionData {
  question_type: TBasicQuestionType | 'material';
  content: IQuestionContent;
}

export interface IMaterialQuestionData extends IQuestionData {
  question_type: 'material';
}

export interface IBasicQuestionData extends IQuestionData {
  question_type: TBasicQuestionType;
}

export enum EJsonErrorRule {
  node_content_error = 'node_content_error', // 节点内容异常
  chapter_content_empty = 'chapter_content_empty',  // 目录内容为空
  chapter_children_empty = 'chapter_children_empty',  // 目录子节点内容不能为空
  chapter_b_tag = 'chapter_b_tag',  // 目录存在加粗样式
  paragraph_content_empty = 'paragraph_content_empty',  // 段落内容为空
  paragraph_content_error = 'paragraph_content_error',  // 段落内容异常
  none_paragraph = 'none_paragraph',  // 无标记的段落
  question_content_empty = 'question_content_empty',  // 题干内容为空
  third_level_serial_number_duplicate = 'third_level_serial_number_duplicate',  // 三级小题题号重复

  level_invalid = 'level_invalid',  // 层级错误

  subs_not_empty = 'subs_not_empty',  // 不能有子节点
  subs_is_empty = 'subs_is_empty',  // 需要有子节点
  subs_question_min_limit = 'subs_question_min_limit',  // 子题最小数量限制

  serial_number_empty = 'serial_number_empty',  // 题号为空
  serial_number_format_invalid = 'serial_number_format_invalid',  // 题号错误
  serial_number_format_mismatch_neighbor = 'serial_number_format_mismatch_neighbor',  // 题号格式错误
  serial_number_discontinuous = 'serial_number_discontinuous',  // 题号不连续
  serial_number_not_first = 'serial_number_not_first',  // 第一个题目题号不是第一个序号
  serial_number_material_blank_error = 'serial_number_material_blank_error',  // 材料题题干个数与其子题个数不符

  multi_source = 'multi_source', // 试题来源不应该有多个
  source_has_bracket = 'source_has_bracket',  // 试题来源不应该有括号
  not_leaf_answer = 'not_leaf_answer',  // 非叶子试题不能有答案
  answer_empty = 'answer_empty',  // 叶子试题答案为空

  choice_letter_invalid = 'choice_letter_invalid',  // 选择题选项号不符合规则
  choice_option_empty = 'choice_option_empty',  // 选择题选项内容空
  choice_answer_invalid = 'choice_answer_invalid',  // 选择题答案无效
  choice_answer_multiple = 'choice_answer_multiple',  // 选择题答案多于一个字母
  choice_start_with_tab = 'choice_start_with_tab',  // 选择题选项以制表符开头
  choice_end_with_lines = 'choice_end_with_lines',  // 选择题最后一个选项内包含换行

  options_invalid = 'options_invalid',  // 选择题选项数量错误
  options_mismatch_neighbor = 'options_mismatch_neighbor',  // 选择题选项数与相邻题不一致

  true_or_false_invalid = 'true_or_false_invalid',  // 判断题答案不符合要求
  blank_answer_mismatch = 'blank_answer_mismatch',  // 填空题答案与空数量不匹配

  material_no_end = 'material_no_end',  // 材料题无结束分割线
  material_no_start = 'material_no_start',  // 材料题无开始分割线

  material_blank_sn = 'material_blank_sn', // 材料题题干中的题号

  answer_mismatch = 'answer_mismatch',  // 答案解析未匹配到试题
  combine_answer_mismatch = 'combine_answer_mismatch',  // 合并试题和答案时匹配无效
  answer_error_content = 'answer_error_content', // 答案异常内容
  analysis_error_content = 'analysis_error_content', // 解析异常内容

  answer_error_content_duplicate = 'answer_error_content_duplicate', // 答案异常内容重复
  analysis_error_content_duplicate = 'analysis_error_content_duplicate', // 解析异常内容重复

  duplicate_answer = 'duplicate_answer',
  duplicate_level_jb = 'duplicate_level_jb',

  blank_answer_start_with_jie = 'blank_answer_start_with_jie', // 填空题答案有解
  blank_width_choice = 'blank_width_choice', // 填空题有选项
  blank_answer_error = 'blank_answer_error', // 填空题答案包含表格或者换行

  error_straight_latex = 'error_straight_latex', // 正斜体错误
  blank_answer_followed_by_tab = 'blank_answer_followed_by_tab', // 填空题答案后面有制表符
  error_latex = 'error_latex', // latex 错误
  answer_table = 'answer_table', // 答案中包含表格
  chapter_content_start_with_blank = 'chapter_content_start_with_blank', // 目录内容以空格开头
  answer_blank = 'answer_blank', // 答案中包含横线
  error_body_option = 'error_body_option', // 题干中包含选项
  analysis_blank = 'analysis_blank', // 解析中有作答空间
  question_answer_mismatch = 'question_answer_mismatch', // 答案与试题数量不一致
  choice_option_table = 'choice_option_table', // 选项中存在表格
  question_missing_image = 'question_missing_image', // 题干提到图但缺少图片
  table_cell_inconsistent = 'table_cell_inconsistent', // 表格单元格内容不一致
  analysis_answer_duplicate = 'analysis_answer_duplicate', // 解析中疑似重复了答案
  content_serial_number_duplicate = 'content_serial_number_duplicate', // 内容开头内容与题号重复
  question_content_duplicate = 'question_content_duplicate', // 题目内容重复
  content_invalid_character = 'content_invalid_character', // 内容包含异常字符
}

export type TJsonErrorLevel = 'warn' | 'error';

export interface IJsonErrorInfo {
  rule: EJsonErrorRule;  // 错误类型
  message: string;  // 错误信息
  level?: TJsonErrorLevel;  // 错误or警告。默认警告
  type?: 'warning' | 'error';  // 错误等级
}

interface INodeBase {
  imgId?: string;
  node_type: 'chapter' | 'paragraph' | 'question';
  node_level: number;
  children: TJsonNode[];
  errorInfo?: IJsonErrorInfo[];
  answerImgId?: string;
  attributes?: { [key: string]: string }; // 兼容旧版
}

export interface IChapterNode extends INodeBase, IChapterData {
  node_type: 'chapter';
}

export interface IParagraphNode extends INodeBase, IParagraphData {
  node_type: 'paragraph';
  _node_type?: 'none';
}

export interface IQuestionNode extends INodeBase, IQuestionData {
  node_type: 'question';
  children: (IBasicQuestionNode | IParagraphNode)[];
}

export interface IBasicQuestionNode extends IQuestionNode {
  question_type: TBasicQuestionType;
  children: IBasicQuestionNode[];
}

export interface IMaterialQuestionNode extends IQuestionNode {
  question_type: 'material';
}

export type TJsonNode = IChapterNode | IParagraphNode | IQuestionNode;

function preCleanHtml (html: string) {
  const result = html.trim()
      .replace(/\u200b/g, '') // 删除 zwsp
      .replace(/<br\/?>/g, '') // 删除 br
      .replace(/disabled="disabled"/g, '') // 删除不知道哪里来的disable
      .replace(/■/g, '<span data-label="answer-sep">|</span>') // 答案分隔符
      .replace(/<table[^>]*>(\s*<tr>\s*<\/tr>)*\s*<\/table>/g, '') // 移除空白表格（没有单元格）（无 tbody 情况）
      .replace(/<table[^>]*>\s*<tbody>(\s*<tr>\s*<\/tr>)*\s*<\/tbody>\s*<\/table>/g, '') // 移除空白表格（没有单元格）
      .replace(/data-signal-desc="[\s\S]*?"/g, ''); // 移除多余的data-set
  return result;
}

function preHandleAnalysisAnswer (nodeGroups: IHtmlNodeGroup[]) { // 处理下挨着的答案和解析。
  nodeGroups.forEach((item, index) => {
    if (index === 0) return;
    const pre = nodeGroups[index - 1];
    const types = ['explanation', 'answer'];
    if (item.label && types.includes(item.label) && !item.mark && pre.label && types.includes(pre.label) && item.label !== pre.label && pre.mark && pre.mark.label === 'quest_num') {
      item.mark = pre.mark;
      item.nodes.unshift(item.mark._serialNumber!);
    }
  });
}

function preAddMarkInfo (nodeGroups) { // 给解析加标记.标识当前小题所在的大题，需要path像p一样加。
  let list: { level: number; serialNumber: string; used: boolean }[] = [];
  nodeGroups.forEach((item) => {
    if (item.label === 'quest_num') {
      list.push({
        level: +item.level,
        serialNumber: item.serialNumber,
        used: false
      });
      return;
    }
    if (item.label === 'explanation' && item.mark) {
      const level = +item.mark.level;
      let index = 0;
      // 应该需要分情况处理
      // 1. 如果是题目-解析-题目-解析，需要从后往前寻找 index
      // 2. 如果是题目-题目-解析-解析，需要从前往后寻找 index
      for (let i = list.length - 1; i >= 0; i -= 1) {
        if (Number(list[i].level) === level && list[i].used === false) {
          list[i].used = true;
          index = i;
          break;
        }
      }
      const preSerialNumbers = list.slice(0, index);
      const preOwnnerSerialNumbers: { level: number; serialNumber: string; used: boolean }[] = [];
      for (let i = preSerialNumbers.length - 1; i >= 0; i -= 1) {
        const j = preSerialNumbers[i];
        const jLevel = +j.level;
        if ((jLevel > 0 && level > 0 || jLevel < 0 && level < 0) && jLevel < level && !preOwnnerSerialNumbers.find(item => +item.level! === jLevel)) {
          preOwnnerSerialNumbers.unshift(j);
        }
        if (jLevel === 1 || jLevel === -1) {
          break;
        }
      }
      item.mark.p = preOwnnerSerialNumbers.map(item => item.serialNumber).join(',');
      item.mark._path = preOwnnerSerialNumbers;
    }
  });
}

function preAddQuestNumP(nodeGroups) { // 给quest_num加标记，标识当前小题所在的大题，需要path像p一样加。
  const questNum = nodeGroups.filter(item => item.label === 'quest_num');
  questNum.forEach((item, index) => {
    const list = questNum.slice(0, index);
    const p: IQuestionDataInfo[] = [];
    for (let i = list.length - 1; i >= 0; i -= 1) {
      if ((+list[i].level > 0 && +item.level > 0 || +list[i].level < 0 && +item.level < 0) && +list[i].level < +item.level && !p.find(item => +item.level! === +list[i].level)) {
        p.unshift(list[i]);
      }
      if (+list[i].level === 1 || +list[i].level === -1) {
        break;
      }
    }
    item.p = p.map(item => item.serialNumber).join(',');
    item._path = p;
  });
}

export function parseJson (html, source: 'question' | 'answer' = 'question') {
  if (!html) {
    return [];
  }
  const cleanedHtml = preCleanHtml(html);
  let nodeTree = parseHtml(cleanedHtml);
  cleanupNode(nodeTree);
  mergeLabelNear(nodeTree);

  if (source === 'answer') {
    addLabelForAnswerQuestNum(nodeTree);
  }

  // 按题号拆分节点
  const [nodes, seps] = splitNodes(nodeTree, ({ node, stopIterateChildren }) => {
    if (node.type === 'element' && node.tagName === 'table') {
      // 表格内不要拆分
      stopIterateChildren?.();
      return false;
    }
    return node.type === 'element' && ['quest_num'].includes(node.dataset.label!);
  });
  nodeTree = nodes;
  seps.forEach(([sep, markNode]) => {
    (sep as any).mark = (markNode as IElementNode).dataset;
  });
  nodeTree = filterEmptyNode(nodeTree);
  const nodeGroups = groupNodes(nodeTree);
  preHandleAnalysisAnswer(nodeGroups);
  if (source === 'question') {
    preAddMarkInfo(nodeGroups);
    preAddQuestNumP(nodeGroups);
  }
  let elementsData = preBuildElementsData(nodeGroups, source);
  const elements = convertElements(elementsData);
  const elementTree = buildElementTree(elements);
  distributeAnswersToSubQuestions(elementTree);
  setupMaterialBodyBlankSn(elementTree);
  let notMatched: TJsonNode[] = [];
  if (source === 'answer') {
    notMatched = mergeAnswerExplanationQuestion(elementTree);
  }
  // Tips：暂时注释掉，取消小题解析合并至大题
  // mergeQuestionExplanation(elementTree);
  setMaterialSerialNumber(elementTree);
  cleanQuestionAnswer(elementTree);

  postProcessTree(elementTree);

  return [elementTree, notMatched];
}

type TypeSource = 'question' | 'answer';
type TypeParams = { html: string, isOfficial?: boolean, source?: TypeSource , isSingleQuest?: boolean, from?: string };

export function htmlToJsonV5 ({ html, isSingleQuest = false, isOfficial = false, source, from }: TypeParams, subject?: string) {
  let json: TJsonNode[];
  if (!source) {
    const [questionHtml, answerHtml] = html.split(/<hr[^>]+answer_separator[\s\S]*?>/i);
    const [questionJson, ] = parseJson(questionHtml, 'question');
    if (answerHtml) {
      let [answerJson, notMatched] = parseJson(answerHtml, 'answer');
      json = combineJson(questionJson, answerJson);
      !json.push(...notMatched);
    } else {
      json = questionJson;
    }
  } else {
    [json, ] = parseJson(html, source);
  }
  for (const { node } of iterateNode(json)) {
    delete node.content.meta;
  }
  if (!isOfficial) {
    validateJsonNodes(json);
  } else {
    cleanJsonNodes(json);
  }
  for (const { node } of iterateNode(json)) {
    if (node.node_type === 'question') { // 答案分割线后面的答案解析。
      if (!node.content._sequence.includes('analysis') && node.content.analysis) {
        node.content._sequence.push('analysis');
      }
      for (const key in node.content) {
        if (Object.prototype.hasOwnProperty.call(node.content, key)) {
          let tempExtraList: string[] = [];
          if (key.startsWith('extra') && !node.content._sequence.includes(key)) tempExtraList.push(key);
          tempExtraList = tempExtraList.reverse();
          tempExtraList.forEach(key => {
            if (!node.content._sequence.includes(key)) {
              node.content._sequence.push(key);
            }
          });
        }
      }
      if (!node.content._sequence.includes('answer') && node.content.answer.length) {
        node.content._sequence.unshift('answer');
      }
      // 答案分割线后面的 _sequence 默认带answer, 如果没有答案，就去掉answer
      if (!node.content.answer.length && node.content._sequence.includes('answer')) {
        node.content._sequence.splice(node.content._sequence.indexOf('answer'), 1);
      }
    }
    if (node.node_type === 'question' && node.content.level < 0) { // 假题号
      node.content.level = Math.abs(node.content.level);
      node.content.serial_number = '';
    }
    if (node.node_type === 'question' && node.question_type === 'choice') {
      const choices = node.content.choices;
      choices?.forEach(choice => {
        if (choice && choice.option) {
          const nodes = parseHtml(choice.option);
          for (const { node } of iterateNode(nodes)) {
            if (node.tagName === 'p' && (node.children.length === 1 && node.children[0].type === 'text' && (node.children[0].content === '&nbsp;' || node.children[0].content === '') || !node.children.length)) {
              Object.keys(node).forEach(k => delete node[k]);
              (node as any).type = 'text';
              (node as any).conetnt = '';
            } else if (node.tagName === 'p') {
              delete node.attrs.class;
              node.cls = {};
            }
          }
          const formatChoice = getHtml(nodes);
          choice.option = formatChoice;
        }
      });
    }
    if (node.node_type === 'question') { // 清除空的节点
      if (node.content._sequence?.length) {
        const _sequence = node.content._sequence;
        if (_sequence.includes('analysis') && !node.content.analysis) {
          _sequence.splice(_sequence.indexOf('analysis'), 1);
        }
        if (_sequence.includes('answer') && !node.content.answer.length) {
          _sequence.splice(_sequence.indexOf('answer'), 1);
        }
        if (_sequence.includes('extra1') && !node.content.extra1) {
          _sequence.splice(_sequence.indexOf('extra1'), 1);
        }
      }
    }
  }
  validateJsonRule(json, subject);
  if (isSingleQuest) {
    if (json.length === 1 && json[0].node_type === 'paragraph') {
      const targetNode = json[0];
      // 如果这个节点的 content 只有一张图片，就修改 node_type 为 question
      if (targetNode.content.body.match(/<img[^>]+>/g)) {
        (json[0] as any).node_type = 'question';
        (json[0] as any).question_type = 'image';
        (json[0] as any).is_image_node = true;
      }
    }
  }
  // 筛选 errorInfo，只保留指定的错误类型
  const allowedErrorRules = new Set([
    'node_content_error',
    'serial_number_discontinuous',
    'serial_number_material_blank_error',
    // 'answer_empty',
    'serial_number_not_first',
    'combine_answer_mismatch',
    'paragraph_content_error',
    'chapter_b_tag',
    'table_cell_inconsistent',
    'blank_answer_start_with_jie',
    'source_has_bracket',
    'question_content_empty',
    'serial_number_empty',
    'serial_number_format_invalid',
    'serial_number_format_mismatch_neighbor',
    'error_straight_latex',
    'error_latex',
    'question_answer_mismatch',
    'blank_width_choice',
    'duplicate_answer',
    'blank_answer_mismatch',
    'options_invalid',
    'options_mismatch_neighbor',
    'multi_source',
    'question_missing_image',
    'choice_letter_invalid',
    'choice_option_empty',
    'choice_answer_invalid',
    'choice_answer_multiple',
    'true_or_false_invalid',
    'material_blank_sn',
    'question_content_duplicate',
    'content_invalid_character'
  ]);
  // 错误类型映射表
  const errorTypeMap: Record<EJsonErrorRule, 'warning' | 'error'> = {
    [EJsonErrorRule.chapter_b_tag]: 'warning',
    [EJsonErrorRule.paragraph_content_empty]: 'warning',
    [EJsonErrorRule.chapter_content_start_with_blank]: 'warning',
    [EJsonErrorRule.blank_answer_start_with_jie]: 'warning',
    [EJsonErrorRule.blank_answer_error]: 'warning',
    [EJsonErrorRule.source_has_bracket]: 'warning',
    [EJsonErrorRule.paragraph_content_error]: 'error',
    [EJsonErrorRule.none_paragraph]: 'warning',
    [EJsonErrorRule.question_content_empty]: 'warning',
    [EJsonErrorRule.level_invalid]: 'error',
    [EJsonErrorRule.serial_number_empty]: 'error',
    [EJsonErrorRule.serial_number_format_invalid]: 'error',
    [EJsonErrorRule.serial_number_format_mismatch_neighbor]: 'error',
    [EJsonErrorRule.error_straight_latex]: 'error',
    [EJsonErrorRule.error_latex]: 'error',
    [EJsonErrorRule.choice_option_table]: 'warning',
    [EJsonErrorRule.question_answer_mismatch]: 'warning',
    [EJsonErrorRule.blank_width_choice]: 'warning',
    [EJsonErrorRule.error_body_option]: 'error',
    [EJsonErrorRule.answer_table]: 'warning',
    [EJsonErrorRule.answer_blank]: 'error',
    [EJsonErrorRule.duplicate_answer]: 'warning',
    [EJsonErrorRule.blank_answer_mismatch]: 'error',
    [EJsonErrorRule.options_invalid]: 'error',
    [EJsonErrorRule.options_mismatch_neighbor]: 'error',
    [EJsonErrorRule.multi_source]: 'error',
    [EJsonErrorRule.question_missing_image]: 'warning',
    [EJsonErrorRule.material_no_end]: 'error',
    [EJsonErrorRule.material_no_start]: 'error',
    [EJsonErrorRule.node_content_error]: 'warning',
    [EJsonErrorRule.chapter_content_empty]: 'error',
    [EJsonErrorRule.chapter_children_empty]: 'error',
    [EJsonErrorRule.third_level_serial_number_duplicate]: 'error',
    [EJsonErrorRule.subs_not_empty]: 'error',
    [EJsonErrorRule.subs_is_empty]: 'error',
    [EJsonErrorRule.subs_question_min_limit]: 'warning',
    [EJsonErrorRule.serial_number_discontinuous]: 'error',
    [EJsonErrorRule.serial_number_not_first]: 'warning',
    [EJsonErrorRule.serial_number_material_blank_error]: 'error',
    [EJsonErrorRule.not_leaf_answer]: 'warning',
    [EJsonErrorRule.answer_empty]: 'warning',
    [EJsonErrorRule.choice_letter_invalid]: 'error',
    [EJsonErrorRule.choice_option_empty]: 'error',
    [EJsonErrorRule.choice_answer_invalid]: 'error',
    [EJsonErrorRule.choice_answer_multiple]: 'warning',
    [EJsonErrorRule.choice_start_with_tab]: 'warning',
    [EJsonErrorRule.choice_end_with_lines]: 'warning',
    [EJsonErrorRule.true_or_false_invalid]: 'error',
    [EJsonErrorRule.material_blank_sn]: 'warning',
    [EJsonErrorRule.answer_mismatch]: 'error',
    [EJsonErrorRule.combine_answer_mismatch]: 'error',
    [EJsonErrorRule.answer_error_content]: 'warning',
    [EJsonErrorRule.analysis_error_content]: 'warning',
    [EJsonErrorRule.answer_error_content_duplicate]: 'error',
    [EJsonErrorRule.analysis_error_content_duplicate]: 'error',
    [EJsonErrorRule.duplicate_level_jb]: 'error',
    [EJsonErrorRule.blank_answer_followed_by_tab]: 'warning',
    [EJsonErrorRule.analysis_blank]: 'error',
    [EJsonErrorRule.table_cell_inconsistent]: 'warning',
    [EJsonErrorRule.analysis_answer_duplicate]: 'error',
    [EJsonErrorRule.content_serial_number_duplicate]: 'error',
    [EJsonErrorRule.question_content_duplicate]: 'warning',
    [EJsonErrorRule.content_invalid_character]: 'error'
  };

  if (from !== 'open') {
    for (const { node } of iterateNode(json)) {
      if (node.errorInfo && node.errorInfo.length > 0) {
        node.errorInfo = node.errorInfo
          .filter(error => allowedErrorRules.has(error.rule))
          .map(error => ({
            ...error,
            type: errorTypeMap[error.rule] || 'error' // 默认为 error
          }));
      }
    }
  } else {
    // 即使是 open 来源，也为 errorInfo 添加 type 字段
    for (const { node } of iterateNode(json)) {
      if (node.errorInfo && node.errorInfo.length > 0) {
        node.errorInfo = node.errorInfo.map(error => ({
          ...error,
          type: errorTypeMap[error.rule] || 'error' // 默认为 error
        }));
      }
    }
  }

  return json;
}

export function cleanJsonNodes (json?: TJsonNode[]) {
  if (!json) {
    return;
  }
  for (const { node } of iterateNode(json)) {
    const element = node as any;
    delete element.attributes;
    delete element.parentUid;
    delete element.nodeIndex;
    delete element.errorInfo;
    delete element.uid;
    delete element.imgId;
    delete element.answerImgId;
    for (const key in element) {
      if (element.hasOwnProperty(key) && key.startsWith('_')) {
        delete element[key];
      }
    }
  }
  return json;
}
