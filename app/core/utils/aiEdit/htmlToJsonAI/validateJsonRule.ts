import {
  TJsonNode,
  IQuestionNode,
  EJsonErrorRule,
  IBasicQuestionNode
} from './index';
import { iterateNode } from '../../treeHelper';
import { getText, parseHtml } from '../../htmlHelper';
import { chinese2Number } from '../../chinese2number';

// 章节标题模式定义
interface ChapterPattern {
  // 匹配模式的正则表达式
  regex: RegExp;
  // 从匹配结果中提取序号的函数
  extractNumber: (match: RegExpMatchArray) => number | null;
  // 模式描述
  description: string;
}

// 定义各种章节标题模式
const CHAPTER_PATTERNS: ChapterPattern[] = [
  // 一、单选题 二、多选题
  {
    regex: /^([一二三四五六七八九十]+)、(.+)$/,
    extractNumber: (match) => chinese2Number(match[1]) || null,
    description: '中文数字+顿号'
  },
  // 第一部分、基础知识 第二部分、提高练习
  {
    regex: /^第([一二三四五六七八九十]+)部分、(.+)$/,
    extractNumber: (match) => chinese2Number(match[1]) || null,
    description: '第X部分'
  },
  // 第一章 数学基础 第二章 物理基础
  {
    regex: /^第([一二三四五六七八九十]+)章\s+(.+)$/,
    extractNumber: (match) => chinese2Number(match[1]) || null,
    description: '第X章'
  },
  // 第一节 概述 第二节 详述
  {
    regex: /^第([一二三四五六七八九十]+)节\s+(.+)$/,
    extractNumber: (match) => chinese2Number(match[1]) || null,
    description: '第X节'
  },
  // 1、选择题 2、填空题
  {
    regex: /^(\d+)、(.+)$/,
    extractNumber: (match) => parseInt(match[1], 10),
    description: '数字+顿号'
  },
  // 第1部分、基础知识 第2部分、提高练习
  {
    regex: /^第(\d+)部分、(.+)$/,
    extractNumber: (match) => parseInt(match[1], 10),
    description: '第N部分'
  },
  // 第1章 数学基础 第2章 物理基础
  {
    regex: /^第(\d+)章\s+(.+)$/,
    extractNumber: (match) => parseInt(match[1], 10),
    description: '第N章'
  },
  // 第1节 概述 第2节 详述
  {
    regex: /^第(\d+)节\s+(.+)$/,
    extractNumber: (match) => parseInt(match[1], 10),
    description: '第N节'
  },
  // (一) 基础题 (二) 提高题
  {
    regex: /^（([一二三四五六七八九十]+)）\s*(.+)$/,
    extractNumber: (match) => chinese2Number(match[1]) || null,
    description: '括号中文数字'
  },
  // (1) 基础题 (2) 提高题
  {
    regex: /^（(\d+)）\s*(.+)$/,
    extractNumber: (match) => parseInt(match[1], 10),
    description: '括号数字'
  }
];

// 检查同级章节序号是否连续
function checkChapterSequence(json: TJsonNode[]) {
  // 根据层级分组收集章节
  const chapterGroups = new Map<number, { node: any; index: number }[]>();

  for (const { node, siblings, level } of iterateNode(json)) {
    if (node.node_type === 'chapter') {
      if (!chapterGroups.has(level)) {
        chapterGroups.set(level, []);
      }
      const group = chapterGroups.get(level)!;
      const chapterIndex = siblings.filter((s) => s.node_type === 'chapter').indexOf(node);
      group.push({ node, index: chapterIndex });
    }
  }

  // 检查每个层级的章节序号连续性
  chapterGroups.forEach((chapters) => {
    if (chapters.length < 2) return;

    // 按模式分组处理章节
    const patternGroups = new Map<string, Array<{ node: any; number: number; text: string; originalText: string }>>();

    chapters.forEach(({ node }) => {
      const chapterName = getText(parseHtml(node.content.body)).trim();

      // 尝试匹配各种模式
      for (const pattern of CHAPTER_PATTERNS) {
        const match = chapterName.match(pattern.regex);
        if (match) {
          const number = pattern.extractNumber(match);
          if (number !== null && number > 0) {
            const patternKey = pattern.description;
            if (!patternGroups.has(patternKey)) {
              patternGroups.set(patternKey, []);
            }
            patternGroups.get(patternKey)!.push({
              node,
              number,
              text: match[1], // 提取的序号文本
              originalText: chapterName
            });
            break; // 匹配到一个模式就停止
          }
        }
      }
    });

    // 检查每个模式组的连续性
    patternGroups.forEach((chapterNumbers, patternType) => {
      if (chapterNumbers.length < 2) return;

      const sortedChapters = chapterNumbers.sort((a, b) => a.number - b.number);

      for (let i = 1; i < sortedChapters.length; i++) {
        const prev = sortedChapters[i - 1];
        const current = sortedChapters[i];

        if (current.number - prev.number !== 1) {
          // 发现不连续，在当前节点添加错误
          current.node.errorInfo = [...(current.node.errorInfo || []), {
            rule: EJsonErrorRule.serial_number_discontinuous,
            // ，缺少序号"${prev.number + 1}"
            message: `章节序号不连续（${patternType}），前一个是"${prev.text}"，当前是"${current.text}"`,
          }];
        }
      }
    });
  });
}

export function validateJsonRule(json: TJsonNode[], subject?: string) {
  // 检查章节序号连续性
  checkChapterSequence(json);

  for (const { node, siblings, index } of iterateNode(json)) {
    // checkJb(node);
    const alignRightRegex = /<p[^>]*class="[^"]*\balign-right\b[^"]*"[^>]*>.*?<\/p>/g;
    const matches = node.content.body.match(alignRightRegex);
    if (matches && matches.length > 1) {
      for (let i = 0; i < matches.length - 1; i++) {
        const currentMatchEnd = node.content.body.indexOf(matches[i]) + matches[i].length;
        const nextMatchStart = node.content.body.indexOf(matches[i + 1]);
        if (currentMatchEnd === nextMatchStart) {
          node.errorInfo = [...(node.errorInfo || []), {
            rule: EJsonErrorRule.node_content_error,
            message: '存在多行连续居右',
          }];
          break;
        }
      }
    }
    if (node.node_type === 'question' || node.node_type === 'paragraph') {
      if (/<\/table>[\s\S]*?续表[\s\S]*?<table/.test(node.content.body)) {
        // node.errorInfo = [...(node.errorInfo || []), {
        //   rule: EJsonErrorRule.node_content_error,
        //   message: '存在没有合并的续表',
        // }];
      }
    }
    if (node.node_type === 'question') {
      const bodyJson = parseHtml(node.content.body);
      const bodyText = getText(bodyJson);
      if (/第.*?题图/.test(bodyText) && !node.content.body.includes('data-label="image_desc"')) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '出现第XX题图字样, 并且没有图说, 请检查',
        }];
      }
      if (/data-description="[^"]*?答图[^"]*?"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '答图的图说出现在题干中',
        }];
      }
      if (node.content.bracket_count && node.question_type !== 'choice' && subject !== 'chinese') {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '该题是否应该是一个选择题',
        }];
      }
      if (/<\/table><table/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '请检查两个表格是否需要合并',
        }];
      }
      checkQuestionAnswerAndAnalysis(node);
    }
    if (node.node_type === 'chapter') {
      const text = getText(parseHtml(node.content.body));
      if (text.includes('学生用书')) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '\'学生用书\'字样不应该出现在标题内',
        }];
      }
      if (/\s\s/.test(text)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '标题中存在多个连续空格',
        }];
      }
      if (/data-label="bracket"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '标题里不能有括号',
        }];
      }
      if (/data-label="blank"/.test(node.content.body)) {
        // node.errorInfo = [...(node.errorInfo || []), {
        //   rule: EJsonErrorRule.question_content_empty,
        //   message: '标题里不能有作答横线',
        // }];
      }
    }
    if (node.node_type === 'paragraph') {
      if (siblings[index + 1]?.node_type === 'question') {
        // node.errorInfo = [...(node.errorInfo || []), {
        //   rule: EJsonErrorRule.paragraph_content_error,
        //   message: '检查这是否是一个段落',
        // }];
      }
      if (/data-label="blank"/.test(node.content.body)) {
        // node.errorInfo = [...(node.errorInfo || []), {
        //   rule: EJsonErrorRule.question_content_empty,
        //   message: '段落里不能有作答横线',
        // }];
      }
      if (['班级', '姓名', '分数', '题号'].some((item) => node.content.body.includes(item))) {
        // node.errorInfo = [...(node.errorInfo || []), {
        //   rule: EJsonErrorRule.question_content_empty,
        //   message: '检查该段落是否需要删除',
        // }];
      }
    }
  }
}

function checkQuestionAnswerAndAnalysis(node: IQuestionNode) {
  const errorInfo = node.errorInfo || [];
  // 答案
  node.content.answer.forEach((item) => {
    const text = getText(parseHtml(item));
    const hintContent = text.startsWith('【答案】') && '【答案】' || text.startsWith('答案') && '答案' || '';
    if (hintContent) {
      errorInfo.push({
        rule: EJsonErrorRule.answer_error_content,
        message: `确认答案开头的${hintContent}是否需要删除`,
      });
      node.errorInfo = errorInfo;
    }
  });

  if (node.content.answer.some((item) => item.includes('data-label="bracket"') || item.includes('data-label="blank"'))) {
    errorInfo.push({
      rule: EJsonErrorRule.answer_error_content,
      message: '答案中不能有空',
    });
  }

  // 解析
  const analysis = getText(parseHtml(node.content.analysis));
  const hintContent = analysis.startsWith('【解析】') && '【解析】' || analysis.startsWith('解析') && '解析' || '';
  if (hintContent) {
    errorInfo.push({
      rule: EJsonErrorRule.analysis_error_content,
      message: `确认解析开头的${hintContent}是否需要删除`,
    });
    node.errorInfo = errorInfo;
  }
  //
  // if (node.content.analysis.includes('data-label="bracket"') || node.content.analysis.includes('data-label="blank"')) {
  //   errorInfo.push({
  //     rule: EJsonErrorRule.answer_error_content,
  //     message: '解析中不能有空',
  //   });
  // }

  const childQuestion = node.children.filter((item) => item.node_type === 'question') as IBasicQuestionNode[];
  if (childQuestion.length > 1 && childQuestion.some((item) => item.content.analysis) && childQuestion.some((item) => !item.content.analysis)) {
    const noAnalysisQuestions = childQuestion.filter((item) => !item.content.analysis);
    const noAnalysisQuestionSns = noAnalysisQuestions.map((item) => item.content.serial_number);
    const hasAnalysisQuestions = childQuestion.filter((item) => item.content.analysis);
    hasAnalysisQuestions.forEach((item) => {
      if (noAnalysisQuestionSns.some((sn) => getText(parseHtml(item.content.analysis)).includes(sn))) {
        const errorInfo = item.errorInfo || [];
        errorInfo.push({
          rule: EJsonErrorRule.analysis_error_content_duplicate,
          message: `检查该试题的解析内是否包含了其它试题的解析（该试题的解析内检测出${noAnalysisQuestionSns.join(',')}题的题号）`,
        });
        item.errorInfo = errorInfo;
      }
    });
  }

  // 检查答案：如果子题目中有些有答案，有些没有答案，检查有答案的题目中是否包含了没有答案题目的题号
  if (childQuestion.length > 1 && childQuestion.some((item) => item.content.answer?.length > 0) && childQuestion.some((item) => !item.content.answer?.length)) {
    const noAnswerQuestions = childQuestion.filter((item) => !item.content.answer?.length);
    const noAnswerQuestionSns = noAnswerQuestions.map((item) => item.content.serial_number);
    const hasAnswerQuestions = childQuestion.filter((item) => item.content.answer?.length > 0);
    hasAnswerQuestions.forEach((item) => {
      const answerText = item.content.answer.map((answer) => getText(parseHtml(answer))).join('');
      if (noAnswerQuestionSns.some((sn) => answerText.includes(sn))) {
        const itemErrorInfo = item.errorInfo || [];
        itemErrorInfo.push({
          rule: EJsonErrorRule.answer_error_content_duplicate,
          message: `检查该试题的答案内是否包含了其它试题的答案（该试题的答案内检测出${noAnswerQuestionSns.join(',')}题的题号）`,
        });
        item.errorInfo = itemErrorInfo;
      }
    });
  }

  // 检查大题解析：如果子题没有解析，并且大题有解析，并且大题的解析中存在小题的题号，就报错
  if (childQuestion.length > 0 && node.content.analysis) {
    const noAnalysisChildQuestions = childQuestion.filter((item) => !item.content.analysis);
    if (noAnalysisChildQuestions.length > 0) {
      const noAnalysisChildQuestionSns = noAnalysisChildQuestions.map((item) => item.content.serial_number);
      const parentAnalysisText = getText(parseHtml(node.content.analysis));
      if (noAnalysisChildQuestionSns.some((sn) => parentAnalysisText.includes(sn))) {
        const parentErrorInfo = node.errorInfo || [];
        parentErrorInfo.push({
          rule: EJsonErrorRule.analysis_error_content_duplicate,
          message: `疑似小题题号未标出，大题解析中检测出${noAnalysisChildQuestionSns.filter((sn) => parentAnalysisText.includes(sn)).join(',')}题的题号`,
        });
        node.errorInfo = parentErrorInfo;
      }
    }
  }
}
