/**
 * OpenAIService 类，用于封装与 OpenAI 兼容的 API 交互逻辑，支持单例模式。
 */
import axios from 'axios';

export class OpenAIService {
  private static instance: OpenAIService;
  private config: {
    apiKey: string;
    baseURL: string;
    model: string;
    maxConcurrentTasks: number;
  };

  /**
   * 私有构造函数，初始化配置
   */
  private constructor() {
    // 默认配置，在未能获取到应用配置时使用
    this.config = {
      apiKey: process.env.OPENAI_API_KEY || 'c6b58699-3fd3-4497-9aee-ad18e920c24d',
      baseURL: process.env.OPENAI_BASE_URL || 'https://ark.cn-beijing.volces.com/api/v3',
      model: process.env.OPENAI_MODEL || 'doubao-1-5-pro-32k-250115',
      maxConcurrentTasks: 5, // 默认并行数量为5
    };
  }

  /**
   * 获取 OpenAIService 单例实例
   * @return {OpenAIService} OpenAIService 的单例对象
   */
  public static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  /**
   * 初始化方法，用于配置应用。
   *
   * @param {Object} appConfig 应用配置对象，包含 OpenAI 配置信息。
   * @return {void} 无返回值。
   */
  public init(appConfig: any): void {
    if (appConfig && appConfig.openai) {
      this.config = {
        apiKey: appConfig.openai.apiKey || this.config.apiKey,
        baseURL: appConfig.openai.baseURL || this.config.baseURL,
        model: appConfig.openai.model || this.config.model,
        maxConcurrentTasks: appConfig.openai.maxConcurrentTasks || this.config.maxConcurrentTasks,
      };
    }
  }

  /**
   * 获取模型名称。
   *
   * @return {string} 返回模型名称字符串
   */
  public getModel(): string {
    return this.config.model;
  }

  /**
   * 设置最大并行任务数
   *
   * @param {number} maxTasks 最大并行任务数
   */
  public setMaxConcurrentTasks(maxTasks: number): void {
    if (maxTasks > 0) {
      this.config.maxConcurrentTasks = maxTasks;
    }
  }

  /**
   * 获取当前设置的最大并行任务数
   *
   * @return {number} 当前设置的最大并行任务数
   */
  public getMaxConcurrentTasks(): number {
    return this.config.maxConcurrentTasks;
  }

  /**
   * 批量并行执行任务，控制并发数量
   *
   * @param {Function[]} tasks 任务函数数组，每个函数返回Promise
   * @param {number} concurrency 并发数量，默认使用配置的maxConcurrentTasks
   * @return {Promise<any[]>} 所有任务的执行结果
   */
  public async runParallelTasks<T>(tasks: (() => Promise<T>)[], concurrency?: number): Promise<T[]> {
    const results: T[] = [];
    const maxConcurrent = concurrency || this.config.maxConcurrentTasks;

    // 使用分组方式控制并发
    for (let i = 0; i < tasks.length; i += maxConcurrent) {
      const chunk = tasks.slice(i, i + maxConcurrent);
      const chunkResults = await Promise.all(chunk.map(task => task()));
      results.push(...chunkResults);
    }

    return results;
  }

  /**
   * 创建聊天补全请求
   * @param {Array} messages 消息列表
   * @param {Object} options 请求选项
   * @returns {Promise<any>} API响应
   */
  public async createChatCompletion(
    messages: any[],
    options: { temperature?: number; max_tokens?: number } = {}
  ) {
    const url = `${this.config.baseURL}/chat/completions`;
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.config.apiKey}`,
    };
    const data = {
      model: this.config.model,
      messages,
      temperature: options.temperature ?? 0.7,
      max_tokens: options.max_tokens,
      TopP: 0.7
    };

    try {
      const response = await axios.post(url, data, { headers });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(`API请求失败: ${error.response.status} ${error.response.statusText} - ${JSON.stringify(error.response.data)}`);
      }
      throw error;
    }
  }

  /**
   * 创建聊天补全请求并解析返回的 JSON。
   *
   * @param {Array} messages 聊天消息的数组
   * @param {Object} options 可选参数
   * @returns {Promise<T|null>} 解析后的JSON对象
   */
  public async createChatCompletionAndParseJSON<T>(
    messages: any[],
    options: { temperature?: number; max_tokens?: number } = {}
  ): Promise<T | null> {
    try {
      const response = await this.createChatCompletion(messages, options);
      if (response.choices && response.choices.length > 0) {
        const content = response.choices[0].message.content;
        if (content) {
          return JSON.parse(content.replace(/```json/g, '').replace(/```/g, '')) as T;
        }
      }
      return null;
    } catch (error) {
      throw error;
    }
  }
}
