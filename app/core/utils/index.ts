import { v4 as uuidv4 } from 'uuid';
import { spawn } from 'child_process';

export type TPromiseFunc<T extends (...args: any[]) => any> = (...args: Parameters<T>) => Promise<ReturnType<T>>;

export interface IImageInfo {
  image: Buffer | 'string'; // string for base64
  width: number;
  height: number;
}

export function getUid(len = 20) {
  const uid: string = uuidv4().toString().replace(/-/gm, '');
  if (len > uid.length) throw Error(`too large for uid length ${len}`);
  return uid.substring(0, len);
}

// 带索引遍历
export function * enumerate<T>(data: Iterable<T> | ArrayLike<T>): IterableIterator<[T, number]> {
  const arr = data as ArrayLike<T>;
  if (arr.length != null) {
    for (let i = 0; i < arr.length; i += 1) {
      yield [arr[i], i];
    }
  } else {
    const iterable = data as Iterable<T>;
    let i = 0;
    for (const item of iterable) {
      yield [item, i];
      i += 1;
    }
  }
}

// 倒序遍历
export function * backIterate<T>(list: ArrayLike<T>) {
  for (let i = list.length - 1; i >= 0; i -= 1) {
    yield list[i];
  }
}

export function * slice<T>(
  data: ArrayLike<T> | Iterable<T>,
  { start = 0, stop = -1, step = 1 }: { start?: number; stop?: number; step?: number } = {}
) {
  const arr = data as ArrayLike<T>;
  const begin = start < 0 && arr.length != null ? arr.length + start : start;
  const end = stop < 0 && arr.length != null ? arr.length + stop : stop;
  if (begin >= end) {
    return;
  }
  if (arr.length != null) {
    for (let i = begin; i < end; i += step) {
      yield arr[i];
    }
  } else {
    const iterable = data as Iterable<T>;
    for (const [item, i] of enumerate(iterable)) {
      if (i < begin) {
        continue;
      }
      if (i >= end) {
        break;
      }
      if (step === 1 || (i - begin) % step === 0) {
        yield item;
      }
    }
  }
}

// 清理文本空格
export function strapText(text: string, trim = true) {
  const res = text.replace(/\s+/g, ' ');
  return trim ? res.trim() : res;
}

export function isHtmlStr(text: string) { // 字符串内容是否为富文本
  return /(<\/\w+>)|(<\w+.*?\/>)|(&[\w\d]+;)/.test(`${text}`);
}

export function hasHtmlTag(text: string) { // 字符串内容是否为富文本
  return /(<\/\w+>)|(<\w+.*?\/>)/.test(`${text}`);
}

export function escapeHtml(text: string) { // 转义中 < &。 用于html文本
  // return `${text}`.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  return `${text}`.replace(/</g, '&lt;').replace(/>/g, '&gt;');
}

export function escapeQuot(text: string) { // 转义 "。用于html属性
  return `${text}`.replace(/"/g, '&quot;');
}

const hasOwnProperty = Object.prototype.hasOwnProperty;

export function hasOwn(val: any, key: keyof any): key is keyof typeof val {
  return hasOwnProperty.call(val, key);
}

export function merge<T = any>(...args: T[]): T {
  if (args.length < 2) return args[0];
  if (args.length === 2) {
    const [obj, other] = args;
    if (!obj || !other) return other;
    if (typeof obj !== typeof other) return other;
    if (Array.isArray(obj) || Array.isArray(other)) return other;
    if (typeof obj !== 'object') return other;
    const res: any = {};
    Object.keys(obj).forEach((key) => {
      if (!hasOwn(other, key)) {
        res[key] = obj[key];
      } else {
        res[key] = merge((obj as any)[key], (other as any)[key]);
      }
    });
    Object.keys(other).forEach((key) => {
      if (!hasOwn(obj, key)) {
        res[key] = other[key];
      }
    });
    return res;
  }
  const [obj, other, ...rest] = args;
  let res = merge(obj, other);
  for (const arg of rest) {
    res = merge(res, arg);
  }
  return res;
}

export function removePropIfEmpty<T = any>(d: T, ...keys: string[]) {
  if (!d) return d;
  const props = keys.length ? keys : Object.keys(d);
  props.forEach((key) => {
    const v = removeIfEmpty(d[key]);
    if (v === undefined) {
      delete d[key];
    }
  });
  return d;
}

export function removeIfEmpty<T = any>(d: T): T | undefined {
  if (d === undefined) return;
  if (!d || typeof d !== 'object') return d;
  if (Array.isArray(d)) {
    const res = d.map((i) => removeIfEmpty(i)).filter((i) => i !== undefined);
    if (!res.length) return;
    return res as any as T;
  }
  const entities: [string, any][] = [];
  Object.keys(d).forEach((k) => {
    const v = removeIfEmpty(d[k]);
    if (v === undefined) return;
    entities.push([k, v]);
  });
  if (!entities.length) return;
  const res: any = {};
  entities.forEach(([k, v]) => {
    res[k] = v;
  });
  return res as T;
}

const units = {
  pt: 100,
  px: 132.814723,
  mm: 35.2778,
  inc: 1.38889,
};

type TUnit = keyof typeof units | '%' | string;

export function unitConvert (value: number, fromType: TUnit, toType: TUnit): number;
export function unitConvert (value: string, fromType: TUnit | undefined, toType: TUnit): number | string | undefined;
export function unitConvert(value: string | number, fromType: TUnit | undefined, toType: TUnit) {
  let v = value;
  if (!v) return v;
  let t = fromType;
  if (t === toType || t === '%' || toType === '%') return v;
  if (typeof v === 'string' && !t) {
    const match = v.match(/^((\d+\.?\d*)|(\d*\.?\d+))(pt|px|mm|inc|%)?$/);
    if (!match) return;
    v = match[1];
    t = match[4] || 'px';
    if (t === '%') return value + t;
    if (t === toType) return Number(value);
  }
  if (!units[toType] || !units[t!]) return;
  return Number(value) * units[toType] / units[t!];
}

export function replaceInternalUrl(url: string) {
  return process.env.EGG_SERVER_ENV === 'prod'
    ? url.replace(/oss-cn-shanghai.aliyuncs.com/, 'oss-cn-shanghai-internal.aliyuncs.com')
    : url;
}

export async function pandocAsync(input: string, args: string[]) {
  const pd = spawn('pandoc', args);
  pd.stdin.end(input, 'utf-8');
  return new Promise<Buffer>((resolve, reject) => {
    const data: Buffer[] = [];
    const error: Buffer[] = []; // 大部分情况下，是警告。内容还是可以生成的，只是小部分有问题。
    pd.stdout.on('data', (chunk: Buffer) => data.push(chunk));
    pd.stderr.on('data', (chunk: Buffer) => error.push(chunk));
    pd.stdout.on('end', () => {
      if (!data.length && error.length) {
        reject(new Error(Buffer.concat(error).toString('utf-8')));
      } else {
        resolve(Buffer.concat(data));
      }
    });
  });
}

export function min<T>(arr: ArrayLike<T>, key?: keyof T | ((d: T, i: number) => number)): number {
  let m = Infinity;
  for (let i = 0, len = arr.length; i < len; i += 1) {
    const item = arr[i];
    const v = key ? (typeof key === 'function' ? key(item, i) : item[key]) : item;
    if (m > v) m = v as any;
  }
  return m;
}

export function max<T>(arr: ArrayLike<T>, key?: keyof T | ((d: T, i: number) => number)): number {
  let m = -Infinity;
  for (let i = 0, len = arr.length; i < len; i += 1) {
    const item = arr[i];
    const v = key ? (typeof key === 'function' ? key(item, i) : item[key]) : item;
    if (m < v) m = v as any;
  }
  return m;
}

export async function retry<T extends(...args: any) => Promise<any>> (func: T, args: Parameters<T>, retryCount = 1): Promise<ReturnType<T>> {
  try {
    return await func(...args as any);
  } catch (e) {
    if (retryCount > 1) {
      return retry(func, args, retryCount - 1);
    }
    throw e;
  }
}

export function * chunk<T>(a: ArrayLike<T>, size: number) { // 分页数据
  const arr = Array.from(a);
  const total = arr.length;
  for (let i = 0, m = Math.floor((total + size - 1) / size); i < m; i += 1) {
    const [start, end] = [i * size, Math.min((i + 1) * size, total)];
    yield arr.slice(start, end);
  }
}

export function htmlDecodeByRegExp(str: string) {
  let temp = '';
  if (str.length == 0) return '';
  temp = str.replace(/&amp;/g, '&');
  temp = temp.replace(/&lt;/g, '<');
  temp = temp.replace(/&gt;/g, '>');
  temp = temp.replace(/&nbsp;/g, ' ');
  temp = temp.replace(/&#39;/g, '\'');
  temp = temp.replace(/&quot;/g, '"');
  return temp;
}

export function removeCommonExtensions(fileName: string): string {
  const extensions: string[] = ['.docx', '.json', '.txt', '.pdf', '.xlsx', '.csv', '.js', '.ts', '.jpg', '.png', '.gif'];
  let newName: string = fileName;
  let done = false;

  while (!done) {
    done = true;
    for (const ext of extensions) {
      if (newName.endsWith(ext)) {
        newName = newName.substring(0, newName.length - ext.length);
        done = false;
        break;
      }
    }
  }

  return newName;
}
