/* eslint no-use-before-define: 0 */
/**
 * @file html 转 json
 * <AUTHOR>
 */

import * as _ from 'lodash';
import * as uuid from 'uuid';
import { parse } from 'himalaya';
import { combineJson } from './jsonHelper';
import { cleanJsonNodes, validateJsonNodes } from './htmlToJsonV4';

function getUUID(len = 20) {
  const result = uuid().toString().replace(/-/gm, '')
    .substr(0, len);
  return result;
}

function filterStr(str: string) {
  if (!str) {
    return str;
  }
  return `${str
    .replace(/(\n\r)/ig, '\n')
    .replace(/&nbsp;/ig, '')
    .replace(/\[MISS CHECK NOW\]/ig, '')
    .replace(/\[MISS CHAR CHECK\]/ig, '')
    .trim()}`;
}

function filterStrLabel(str: string) {
  if (!str) {
    return str;
  }
  return `${str
    .replace(/<u>/ig, '')
    .replace(/<\/u>/ig, '')
    .replace(/<i>/ig, '')
    .replace(/<\/i>/ig, '')
    .replace(/<strong>/ig, '')
    .replace(/<\/strong>/ig, '')
    .replace(/<s>/ig, '')
    .replace(/<\/s>/ig, '')
    .replace(/<em>/ig, '')
    .replace(/<\/em>/ig, '')
    .trim()}`;
}

// 获取节点信息
function formatElementNodeInfo(element, parentUid = '', nodeLevel: number, nodeIndex: number) {
  const attributes = {};
  let outerHTML = '';
  if (element.tagName === 'img') {
    outerHTML += '<img ';
  }
  if (element.attributes) {
    for (let index = 0; index < element.attributes.length; index += 1) {
      const item = element.attributes[index];
      attributes[item.key] = item.value;
      if (item.key === 'src' && element.tagName === 'img') {
        outerHTML += `${item.key}="${item.value}"`;
        outerHTML += '>';
      }
    }
  }
  element['nodeIndex'] = nodeIndex;
  element['nodeLevel'] = nodeLevel;
  element['parentUid'] = parentUid;
  element['attributes'] = attributes;
  element['outerHTML'] = outerHTML;
  element['children'] = element.children || [];
  element['uid'] = getUUID(20);
  element['nodeName'] = element.tagName || element.type;
  element['content'] = element.content || '';
  element['nodeType'] = element.type;
  element['className'] = attributes['class'] ? attributes['class'] : '';
  element['hasChildNodes'] = Boolean(element.children) && element.children.length > 0;
  if (element.nodeName === 'img') {
    element['content'] = element.outerHTML;
  }
  if (element.nodeName === 'strong' && element['className'] === '') {
    element['className'] = 'strong';
  }
  if (element.nodeName === 'u' && element['className'] === '') {
    element['className'] = 'u';
  }
  if (element.nodeName === 'i' && element['className'] === '') {
    element['className'] = 'i';
  }
  if (element.nodeName === 's' && element['className'] === '') {
    element['className'] = 's';
  }
  if (element.nodeName === 'em' && element['className'] === '') {
    element['className'] = 'em';
  }
  if (element.nodeName === 'table' && element['className'] === '') {
    element['className'] = 'table';
  }
  if (element.nodeName === 'caption' && element['className'] === '') {
    element['className'] = 'caption';
  }
  if (element.nodeName === 'tbody' && element['className'] === '') {
    element['className'] = 'tbody';
  }
  if (element.nodeName === 'tr' && element['className'] === '') {
    element['className'] = 'tr';
  }
  if (element.nodeName === 'td' && element['className'] === '') {
    element['className'] = 'td';
  }
  return element;
}

// 递归解析
function recurrenceElementHtml(htmlStr: string, nodeLevel: number, parentUid = '') {
  let result: any = [];
  if (!htmlStr) {
    return result;
  }
  const nodes: any = parse(htmlStr);

  function createBaseElements(nodesParam, nodeLevelParam, parentUidParam) {
    const elements: any = [];
    let tempNodeIndex = 0;
    for (let nodeIndex = 0; nodeIndex < nodesParam.length; nodeIndex += 1) {
      tempNodeIndex += 1;
      const node = nodesParam[nodeIndex];
      const nodeInfo = formatElementNodeInfo(node, parentUidParam, nodeLevelParam, tempNodeIndex);
      if (nodeInfo.className === 'blank-carry-answer') {
        nodeInfo.className = 'blank';
        tempNodeIndex += 1;
        const tempNodeInfo = Object.assign({}, nodeInfo, {
          nodeIndex: tempNodeIndex,
          className: 'answer',
          children: [],
        });
        tempNodeInfo.children = createBaseElements(parse(tempNodeInfo.attributes['data-answer']), tempNodeInfo.nodeLevel + 1, tempNodeInfo.uid);
        elements.push(tempNodeInfo);
      }
      if (nodeInfo.className === 'bracket-carry-answer') {
        nodeInfo.className = 'bracket';
        tempNodeIndex += 1;
        const tempNodeInfo = Object.assign({}, nodeInfo, {
          nodeIndex: tempNodeIndex,
          className: 'answer',
          children: [],
        });
        tempNodeInfo.children = createBaseElements(parse(tempNodeInfo.attributes['data-answer']), tempNodeInfo.nodeLevel + 1, tempNodeInfo.uid);
        elements.push(tempNodeInfo);
      }
      if (nodeInfo.hasChildNodes) {
        nodeInfo.children = createBaseElements(nodeInfo.children, nodeInfo.nodeLevel + 1, nodeInfo.uid);
        elements.push(nodeInfo);
      } else {
        elements.push(nodeInfo);
      }
    }
    return elements;
  }

  result = createBaseElements(nodes, nodeLevel, parentUid);
  return result;
}

// 预处理节点
function pretreatmentElements(elements: [], formatFunction: (ele: any) => any) {
  const result: any = [];
  if (_.isEmpty(elements)) {
    return result;
  }
  elements.forEach((element: any) => {
    const formatElement = formatFunction(element);
    if (formatElement.children.length > 0) {
      formatElement.children = pretreatmentElements(element.children, formatFunction);
    }
    result.push(formatElement);
  });
  return result;
}

function getLatexElementContent(element) {
  const tempBody = combineElementChildrenContent(element);
  const tempStr = tempBody.replace(/\$/ig, '').trim();
  if (tempStr === '') {
    return '';
  }
  return `$$${tempBody.replace(/\$/ig, '').trim()}$$`;
}

function getBracketElementContent(element) {
  const content = combineElementChildrenContent(element);
  return content ? `<bracket>${filterBracket(content)}</bracket>` : '<bracket></bracket>';
}

// 预处理节点
function pretreatmentElement(element: any) {
  if (!element || _.isEmpty(element)) {
    return element;
  }
  const className = element.className;
  const nodeName = element.nodeName;
  if (nodeName.match(/h[1-9]/i)) {
    element['type'] = 'chapter';
    element['body'] = combineElementChildrenContent(element);
    element.children = [];
    element['className'] = nodeName;
  } else {
    if (className && className.match('quest')) {
      element['type'] = 'question';
      element['body'] = combineElementChildrenContent(element);
      element['serialNumber'] = element['body'].replace('\.', '').trim();
      element.children = [];
    } else if (className === 'source') {
      element['type'] = 'source';
      element['body'] = combineElementChildrenContent(element);
      element.children = [];
    } else if (className === 'analysis') {
      element['type'] = 'analysis';
      element['body'] = combineElementChildrenContent(element);
      element.children = [];
    } else if (className === 'answer') {
      element['type'] = 'answer';
      element['body'] = combineElementChildrenContent(element);
      element.children = [];
    } else if (className === 'latex') {
      element['type'] = 'latex';
      element['body'] = getLatexElementContent(element);
      element.children = [];
    } else if (className === 'bracket') {
      element['type'] = 'bracket';
      element['body'] = getBracketElementContent(element);
      element.children = [];
    } else if (className === 'option') {
      element['type'] = 'option';
      element['body'] = combineElementChildrenContent(element);
      element['choiceCount'] = 1;
      element.children = [];
    } else if (className === 'blank') {
      element['type'] = 'blank';
      let blankLength = 0;
      const tempBody = combineElementChildrenContent(element);
      if (tempBody) {
        const matchArr = tempBody.match(/_/ig);
        if (matchArr) {
          blankLength = matchArr.length;
        }
      }
      element['body'] = `<blank blankLength='${blankLength}'></blank>`;
      element['blankCount'] = element['body'].match(`<blank blankLength='${blankLength}'></blank>`).length;
      element.children = [];
    } else if (nodeName === 'img') {
      element['type'] = 'image';
      element['body'] = element.content;
      element.children = [];
    } else if (className === 'knowledge') {
      element['type'] = 'paragraph';
      element['tag'] = 'knowledge';
      element['body'] = combineElementChildrenContent(element);
      element.children = [];
    } else if (className && className === 'strong') {
      element['type'] = 'text';
      element['body'] = `<strong>${combineElementChildrenContent(element)}</strong>`;
      element.children = [];
    } else if (className && className === 'i') {
      element['type'] = 'text';
      element['body'] = `<i>${combineElementChildrenContent(element)}</i>`;
      element.children = [];
    } else if (className && className === 'u') {
      element['type'] = 'text';
      element['body'] = `<u>${combineElementChildrenContent(element)}</u>`;
      element.children = [];
    } else if (className && className === 's') {
      element['type'] = 'text';
      element['body'] = `<s>${combineElementChildrenContent(element)}</s>`;
      element.children = [];
    } else if (className === 'emphasis') {
      element['type'] = 'text';
      element['body'] = `<span style="${element.attributes['style']}">${combineElementChildrenContent(element)}</span>`;
      element.children = [];
    } else if (className && className === 'table') {
      element['type'] = 'text';
      element['body'] = `<table>${combineElementChildrenContent(element)}</table>`;
      element.children = [];
    } else if (className && className === 'caption') {
      element['type'] = 'text';
      element['body'] = `<caption>${combineElementChildrenContent(element)}</caption>`;
      element.children = [];
    } else if (className && className === 'tr') {
      element['type'] = 'text';
      element['body'] = `<tr>${combineElementChildrenContent(element)}</tr>`;
      element.children = [];
    } else if (className && className === 'td') {
      element['type'] = 'text';
      element['body'] = `<td>${combineElementChildrenContent(element)}</td>`;
      element.children = [];
    } else if (className && className === 'tbody') {
      element['type'] = 'text';
      element['body'] = `<tbody>${combineElementChildrenContent(element)}</tbody>`;
      element.children = [];
    } else if (nodeName === 'text') {
      element['type'] = 'text';
      element['body'] = element.content;
    } else {
      element['type'] = 'other';
      element['body'] = element.content;
    }
  }
  element['body'] = filterStr(element['body']);
  return element;

}

// 创建节点
function getNewNode(option) {
  const {
    node_name,
    node_type,
    node_level,
    content,
    uid,
    parentUid,
    nodeIndex,
    question_type,
    attributes,
  } = option;

  const {
    children = [],
    choice_count = 0,
    choice = {},
    body = '',
    serial_number = '',
    source = '',
    analysis = '',
    answer = [],
    blank_count = 0,
    tag = '',
    level,
  } = content;

  let result = {};
  if (node_type === 'chapter') {
    result = {
      node_type,
      node_name,
      node_level,
      children,
      content: { level, body },
    };
  } else if (node_type === 'question') {
    if (question_type === 'option') {
      result = {
        uid,
        parentUid,
        node_type,
        node_level,
        question_type,
        content: {
          choice_count,
          body,
          serial_number,
          source,
          analysis,
          answer,
          choice,
          level,
        },
        children: [],
      };
    } else if (question_type === 'blank') {
      result = {
        node_type,
        node_level,
        question_type,
        content: {
          body,
          serial_number,
          source,
          analysis,
          blank_count,
          answer,
          level,
        },
        children: [],
      };
    } else if (question_type === 'other') {
      result = {
        node_type,
        node_level,
        question_type,
        content: {
          body,
          serial_number,
          source,
          analysis,
          blank_count,
          answer,
          choice_count,
          choice,
          level,
        },
        children: [],
      };
    } else {
      result = {
        node_type,
        node_level,
        question_type,
        content: {
          body,
          serial_number,
          source,
          analysis,
          blank_count,
          answer,
          choice_count,
          choice,
          level,
        },
        children: [],
      };
    }
  } else if (node_type === 'paragraph') {
    result = {
      node_type,
      node_level,
      content: {
        body,
        tag,
      },
      children: [],
    };
  }
  return Object.assign(result, {
    attributes,
    uid,
    parentUid,
    nodeIndex,
  });
}

function transferElementTreeToArr(obj, nodeLevel: number) {
  const root = obj;
  const children = root.children;
  delete root.children;
  let result: any = [];
  root.nodeLevel = nodeLevel;
  if (!root.className) {
    root.className = root.type;
  }
  result.push(root);
  children.forEach((sub) => {
    if (!sub.className) {
      sub.className = sub.type;
    }
    result = result.concat(transferElementTreeToArr(sub, root.nodeLevel + 1));
  });
  return result;
}

function parseQuestionElements(elements, nodeLevel: number) {
  if (_.isEmpty(elements)) {
    return [];
  }
  let parseElementsRes = aggregateElement(elements, nodeLevel + 1);
  parseElementsRes = distinguishElements(parseElementsRes);
  return createTree(parseElementsRes);
}

function createTree(nodes) {
  if (_.isEmpty(nodes)) {
    return [];
  }

  function newNode({ node_level = 0, parent = null, siblings = [], children = [], node }) {
    return Object.assign({ node_level, parent, siblings, children }, node);
  }

  function addChild(node, child) {
    child.parent = node;
    child.siblings = node.children;
    node.children.push(child);
  }

  function addSibling(node, sibling) {
    sibling.parent = node.parent;
    sibling.siblings = node.siblings;
    node.siblings.push(sibling);
  }

  const root = newNode({ node_level: 0, node: { node_level: 0 } });
  let pre = root;
  nodes.forEach((node) => {
    const tempNode = newNode({ node });
    let tmp = pre;
    while (tmp.node_level > tempNode.node_level && tmp.parent) tmp = tmp.parent;
    if (tmp.node_level < tempNode.node_level) {
      addChild(tmp, tempNode);
    } else {
      addSibling(tmp, tempNode);
    }
    pre = tempNode;
  });

  function removeKeys(arr: any) {
    arr.forEach((item) => {
      delete item.parent;
      delete item.siblings;
      if (item.children.length) {
        item.children = removeKeys(item.children);
      }
    });
    return arr;
  }

  return removeKeys(root.children);
}

function aggregateElement(elements, nodeLevel: number) {
  if (_.isEmpty(elements)) {
    return elements;
  }
  let lastNode = {};
  const result: any = [];
  let isQuestion = false;
  let tempNodeLevel = nodeLevel;
  elements.forEach((element) => {
    if (checkoutIsIncludeQuestion(element)) {
      isQuestion = true;
    }
    const tempNodeIndex = element.nodeIndex;
    const elementArr = transferElementTreeToArr(element, nodeLevel);
    elementArr.forEach((innerElement) => {
      innerElement.nodeIndex = tempNodeIndex;
      if (!isQuestion) {
        lastNode = Object.assign({}, innerElement);
        lastNode['nodeLevel'] = tempNodeLevel;
        lastNode['tag'] = innerElement.tag;
        result.push(lastNode);
      } else if (innerElement.type.match('question')) {
        isQuestion = true;
        lastNode = Object.assign({}, innerElement);
        lastNode['serialNumber'] = filterStr(lastNode['body']);
        lastNode['body'] = '';
        lastNode['level'] = 1;
        if (innerElement.className.match('sub')) {
          lastNode['level'] += 1;
          lastNode['nodeLevel'] = lastNode['nodeLevel'] + 1;
        } else if (innerElement.className.match('lv2')) {
          lastNode['level'] += 1;
          lastNode['nodeLevel'] = lastNode['nodeLevel'] + 1;
        } else if (innerElement.className.match('lv3')) {
          lastNode['level'] += 2;
          lastNode['nodeLevel'] = lastNode['nodeLevel'] + 2;
        } else {
          lastNode['nodeLevel'] = lastNode['nodeLevel'];
        }
        tempNodeLevel = lastNode['nodeLevel'];
        result.push(lastNode);
      } else if (innerElement.type.match('blank')) {
        lastNode = Object.assign({}, innerElement);
        lastNode['nodeLevel'] = tempNodeLevel;
        result.push(lastNode);
      } else if (innerElement.type.match('analysis')) {
        lastNode = Object.assign({}, innerElement);
        lastNode['nodeLevel'] = tempNodeLevel;
        result.push(lastNode);
      } else if (innerElement.type.match('answer')) {
        lastNode = Object.assign({}, innerElement);
        lastNode['nodeLevel'] = tempNodeLevel;
        result.push(lastNode);
      } else if (innerElement.type.match('paragraph') || innerElement.className.match('knowledge')) {
        lastNode = Object.assign({}, innerElement);
        lastNode['tag'] = innerElement.tag;
        result.push(lastNode);
      } else if (innerElement.type.match('option')) {
        lastNode = Object.assign({}, innerElement);
        lastNode['nodeLevel'] = tempNodeLevel;
        lastNode['option'] = innerElement.body.replace('.', '');
        lastNode['body'] = innerElement.body.replace(`${lastNode['option']}`, '').replace('.', '');
        result.push(lastNode);
      } else {
        if (innerElement.type.match('source')) {
          lastNode['source'] = innerElement.body;
        } else {
          lastNode['body'] += innerElement.body;
        }
      }
    });
  });
  return result;
}

function distinguishElements(elements) {
  if (_.isEmpty(elements)) {
    return [];
  }
  let newElement: any = null;
  let isQuestion = false;
  const result: any = [];
  elements.forEach((element) => {
    if (element.type === 'question') {
      isQuestion = true;
    }
    if (!isQuestion) {
      newElement = getNewNode({
        node_name: 'paragraph',
        node_type: 'paragraph',
        node_level: element.nodeLevel,
        attributes: element.attributes,
        content: {
          body: element.body,
          tag: element.tag,
        },
        uid: element.uid,
        parentUid: element.parentUid,
        nodeIndex: element.nodeIndex,
      });
      result.push(newElement);
    } else if (element.type.match('question')) {
      newElement = getNewNode({
        node_name: 'question',
        node_type: 'question',
        node_level: element.nodeLevel,
        attributes: element.attributes,
        question_type: 'other',
        content: {
          level: element.level,
          body: element.body,
          serial_number: element.serialNumber,
          source: element.source,
        },
        uid: element.uid,
        parentUid: element.parentUid,
        nodeIndex: element.nodeIndex,
      });
      result.push(newElement);
    } else if (element.type.match('analysis')) {
      newElement.content.analysis += element.body;
    } else if (element.type.match('blank')) {
      newElement.content.body += element.body;
      newElement.content.blank_count += element.blankCount;
      newElement.question_type = 'blank';
    } else if (element.type.match('option')) {
      if (newElement.question_type !== 'blank') {
        newElement.question_type = 'option';
      }
      newElement.content.choice = Object.assign(newElement.content.choice, { [element.option]: element.body });
      newElement.content.choice_count = Object.keys(newElement.content.choice).length;
    } else if (element.type.match('answer')) {
      newElement.content.answer = newElement.content.answer.concat(element.body);
    } else {
      newElement = getNewNode({
        node_name: 'paragraph',
        node_type: 'paragraph',
        node_level: element.nodeLevel,
        attributes: element.attributes,
        content: {
          body: element.body,
          tag: element.tag,
        },
        uid: element.uid,
        parentUid: element.parentUid,
        nodeIndex: element.nodeIndex,
      });
      result.push(newElement);
    }
  });
  return result;
}

function getLastChapterParentNode(chapters, nodeLevel: number) {
  if (_.isEmpty(chapters)) {
    return null;
  }
  for (let index = chapters.length - 1; index >= 0; index -= 1) {
    const chapter = chapters[index];
    if (chapter.node_level === nodeLevel) {
      if (chapter.node_type !== 'paragraph') {
        return chapter;
      }
    } else if (chapter.node_level > nodeLevel) {
      return null;
    } else if (chapter.node_level < nodeLevel) {
      return getLastChapterParentNode(chapter.children, nodeLevel);
    }
  }
}

function combineElementChildrenBody(element) {
  let body = element.body;
  if (element.children && element.children.length) {
    body = '';
    element.children.forEach((subElement) => {
      body += combineElementChildrenBody(subElement);
    });
  }
  return body;
}

function combineElementChildrenContent(element) {
  let content = element.content;
  if (element.children && element.children.length > 0) {
    element.children.forEach((subElement) => {
      if (subElement.className === 'latex') {
        content += getLatexElementContent(subElement);
        subElement.children = [];
      } else if (subElement.className === 'bracket') {
        content += getBracketElementContent(subElement);
        subElement.children = [];
      } else if (subElement.className === 'table') {
        content += `<table>${combineElementChildrenContent(subElement)}</table>`;
        subElement.children = [];
      } else if (subElement.className === 'caption') {
        content += `<caption>${combineElementChildrenContent(subElement)}</caption>`;
        subElement.children = [];
      } else if (subElement.className === 'tr') {
        content += `<tr>${combineElementChildrenContent(subElement)}</tr>`;
        subElement.children = [];
      } else if (subElement.className === 'td') {
        content += `<td>${combineElementChildrenContent(subElement)}</td>`;
        subElement.children = [];
      } else if (subElement.className === 'tbody') {
        content += `<tbody>${combineElementChildrenContent(subElement)}</tbody>`;
        subElement.children = [];
      } else if (subElement.className === 'emphasis') {
        content += `<span style="${subElement.attributes['style']}">${combineElementChildrenContent(subElement)}</span>`;
        subElement.children = [];
      } else if (subElement.className === 'strong') {
        content += `<strong>${combineElementChildrenContent(subElement)}</strong>`;
        subElement.children = [];
      } else if (subElement.className === 'i') {
        content += `<i>${combineElementChildrenContent(subElement)}</i>`;
        subElement.children = [];
      } else if (subElement.className === 'em') {
        content += `<em>${combineElementChildrenContent(subElement)}</em>`;
        subElement.children = [];
      } else if (subElement.className === 'u') {
        content += `<u>${combineElementChildrenContent(subElement)}</u>`;
        subElement.children = [];
      } else if (subElement.className === 's') {
        content += `<s>${combineElementChildrenContent(subElement)}</s>`;
        subElement.children = [];
      } else {
        content += combineElementChildrenContent(subElement);
      }
    });
  }
  return content;
}

function parseElements(elements: any, nodeLevel = 0) {
  let level = nodeLevel;
  const chapters: any = [];
  if (_.isEmpty(elements)) {
    return chapters;
  }
  let newChapter: any = null;
  let isParagraph = true;
  let isFirstParagraph = true;
  const hElementMap = {};
  let subChapters: any = [];
  const isIncludeChapter = checkoutIsIncludeChpter(elements);
  if (!isIncludeChapter) {
    const tempChapter = Object.assign({}, elements[0], { type: 'chapter', nodeName: 'h1' });
    tempChapter.children = [];
    elements.unshift(tempChapter);
  }
  for (let elementIndex = 0; elementIndex < elements.length; elementIndex += 1) {
    const element: any = elements[elementIndex];
    if (checkoutIsIncludeQuestion(element) || element.type === 'chapter') {
      isParagraph = false;
    }
    if (isParagraph || element.className === 'knowledge') {
      // 指的是上一个节点的object
      if (newChapter) {
        newChapter['children'] = newChapter['children'].concat(parseQuestionElements(subChapters, level));
        subChapters = [];
      }
      const tempBody = filterStr(combineElementChildrenBody(element));
      if (tempBody) {
        let paragraphLevel = 0;
        if (level === 0) {
          paragraphLevel = element.nodeLevel + level + 1;
        } else {
          paragraphLevel = element.nodeLevel + level;
        }
        if (isFirstParagraph) {
          paragraphLevel = paragraphLevel + 1;
          isFirstParagraph = false;
        }
        const tempNode = getNewNode({
          node_name: 'paragraph',
          node_type: 'paragraph',
          node_level: paragraphLevel,
          attributes: element.attributes,
          content: {
            body: tempBody,
            tag: element.className,
          },
          uid: element.uid,
          parentUid: element.parentUid,
          nodeIndex: element.nodeIndex,
        });
        if (newChapter) {
          newChapter.children.push(tempNode);
        } else {
          chapters.push(tempNode);
        }
      }
    } else if (/^h[1-7]$/.test(element.nodeName)) {
      level += 1;
      isParagraph = true;
      isFirstParagraph = true;
      const tag = element.nodeName;
      if (!hElementMap[tag]) {
        hElementMap[tag] = level;
      } else {
        const l = Number(tag.match(/^h([1-7])$/)[1]);
        level = hElementMap[tag];
        for (let i = l + 1; i <= 7; i += 1) {
          hElementMap[`h${i}`] = null;
        }
      }
      // 指的是上一个节点的object
      if (newChapter) {
        if (subChapters.length > 0) {
          const questions = parseQuestionElements(subChapters, level);
          newChapter.children = newChapter.children.concat(questions);
        }
        subChapters = [];
      }
      const lastParentNode = getLastChapterParentNode(chapters, level - 1);
      const body = combineElementChildrenBody(element);
      newChapter = getNewNode({
        node_name: filterStr(body),
        node_type: element.type,
        node_level: element.nodeLevel + level,
        attributes: element.attributes,
        content: { level, body },
        uid: element.uid,
        parentUid: element.parentUid,
        nodeIndex: element.nodeIndex,
      });
      if (lastParentNode) {
        lastParentNode.children.push(newChapter);
      } else {
        chapters.push(newChapter);
      }
    } else {
      subChapters.push(element);
    }
    if (elementIndex === elements.length - 1) {
      // 指的是上一个节点的object
      if (newChapter && newChapter.children && !_.isEmpty(subChapters)) {
        newChapter.children = newChapter.children.concat(parseQuestionElements(subChapters, level));
        subChapters = [];
      }
    }
  }
  let tempChapters = chapters;
  let tempNodeLevel = nodeLevel + 1;
  if (!isIncludeChapter) {
    tempChapters = chapters[0].children;
    tempNodeLevel = tempNodeLevel - 1;
  }
  return sortByNodeIndex(resetElementsLevel(tempChapters, tempNodeLevel));
}

// 按照index排序
function sortByNodeIndex(elements: []) {
  elements.forEach((element: any) => {
    element.children = sortByNodeIndex(element.children);
  });
  return _.sortBy(elements, 'nodeIndex');
}

// 重置节点的层级
function resetElementsLevel(elements: [], nodeLevel: number) {
  elements.forEach((element: any) => {
    element.node_level = nodeLevel;
    element.children = resetElementsLevel(element.children, nodeLevel + 1);
  });
  return elements;
}

/**
 * ------------------------------checkout-----------------------------
 */
// 检测是否包含试题
function checkoutIsIncludeQuestion(element) {
  let isExist = false;
  if (element.type === 'question') {
    isExist = true;
    return isExist;
  }
  if (element.children && element.children.length) {
    for (let index = 0; index < element.children.length; index += 1) {
      isExist = checkoutIsIncludeQuestion(element.children[index]);
      if (isExist) {
        return isExist;
      }
    }
  }
  return isExist;
}

function checkoutIsIncludeChpter(elements) {
  let result = false;
  if (_.isEmpty(elements)) {
    return result;
  }

  function checkoutIsIncludeChpterInline(element) {
    let isExist = false;
    if (element.nodeName.match(/h[1-7]/ig)) {
      isExist = true;
      return isExist;
    }
    if (element.children && element.children.length) {
      for (let index = 0; index < element.children.length; index += 1) {
        isExist = checkoutIsIncludeChpterInline(element.children[index]);
        if (isExist) {
          return isExist;
        }
      }
    }
    return isExist;
  }

  for (let index = 0; index < elements.length; index += 1) {
    const element = elements[index];
    result = checkoutIsIncludeChpterInline(element);
    if (result) {
      return result;
    }
  }
  return result;
}

/**
 * ------------------------------format-----------------------------
 */
// 转化为官方文档
function formatElementsToOfficel(elements: [], source: string = 'question' || 'answer') {
  if (_.isEmpty(elements)) {
    return elements;
  }
  const result: any = [];
  elements.forEach((element: any) => {
    if (element.node_type === 'question') {
      const tempElement = formatQuestionElement(element, source);
      result.push(tempElement);
    } else if (element.node_type === 'chapter') {
      result.push(formatChapterElement(element, source));
    } else if (element.node_type === 'paragraph') {
      if (formatParagraphElement(element, source)) {
        result.push(element);
      }
    }
  });
  return result;
}

// 格式试题节点
function formatQuestionElement(element, source) {
  if (source === 'question') {
    if (element.question_type === 'blank') {
      delete element.content.choice_count;
      delete element.content.options;
    } else if (element.question_type === 'option') {
      delete element.content.blank_count;
    } else if (element.question_type === 'other') {
      delete element.content.choice_count;
      delete element.content.choice;
      delete element.content.blank_count;
      delete element.content.options;
    }
  }
  if (element.content) {
    if (element.content.source) {
      element.content.source = filterBracket(removeBlackSquare(element.content.source));
    }
    if (element.content.answer) {
      let tempAnswers = [];
      element.content.answer.forEach((answerItem) => {
        tempAnswers = tempAnswers.concat(answerItem.split('■'));
      });
      const finnalAnswers: any = [];
      if (element.question_type === 'option') {
        tempAnswers.forEach((answerItem) => {
          const filterAnswer = filterStr(answerItem);
          if (filterAnswer) {
            finnalAnswers.push(filterStrLabel(filterAnswer).replace(/\$/ig, '').trim());
          }
        });
      } else {
        tempAnswers.forEach((answerItem) => {
          const filterAnswer = filterStr(answerItem);
          if (filterAnswer) {
            finnalAnswers.push(filterStrLabel(filterAnswer));
          }
        });
      }
      element.content.answer = finnalAnswers;
    }
    if (element.content.body) {
      element.content.body = removeBlackSquare(element.content.body);
    }
    if (element.content.choice) {
      Object.keys(element.content.choice).forEach((key) => {
        element.content.choice[key] = removeBlackSquare(element.content.choice[key]);
      });
    }
    if (element.content.analysis) {
      element.content.analysis = filterAnalysis(removeBlackSquare(element.content.analysis));
    }
  }
  if (element.children && element.children.length > 0) {
    element.question_type = 'other';
    element.content.answer = [];
    element.children = formatElementsToOfficel(element.children, source);
    element.children.forEach((subElement) => {
      delete subElement.content.source;
    });
    element.content.analysis = removeBlackSquare(element.content.analysis);
  }
  return element;
}

// 格式化段落节点
function formatParagraphElement(element, source: string) {
  delete element.question_type;
  if (element.children && element.children.length > 0) {
    element.children = formatElementsToOfficel(element.children, source);
  }
  if (!checkoutIsIncludeQuestion(element) && filterStr(combineBody(element).body) === '') {
    return null;
  }
  return element;
}

// combine element.body
function combineBody(element) {
  if (element.children && element.children.length > 0) {
    element.children.forEach((subElement) => {
      element.body += subElement.body;
      subElement.body = '';
    });
  }
  return element;
}

// 格式化 篇章节点
function formatChapterElement(element, source: string) {
  delete element.question_type;
  if (element.children && element.children.length > 0) {
    element.children = formatElementsToOfficel(element.children, source);
  }
  return element;
}

/**
 * ------------------------------filter-----------------------------
 */

// ■
function removeBlackSquare(str) {
  if (!str) {
    return str;
  }
  return str.replace(/■/ig, '').trim();
}

// 过滤解析
function filterAnalysis(str) {
  if (!str) {
    return str;
  }
  let tempStr = str.trim();
  if (tempStr.match('^解析:')) {
    tempStr = tempStr.replace(/^解析:/, '');
  }
  if (tempStr.match('^解析：')) {
    tempStr = tempStr.replace(/^解析：/, '');
  }
  if (tempStr.match('^解析')) {
    tempStr = tempStr.replace(/^解析/, '');
  }
  return tempStr.trim();
}

// 过滤 source
function filterBracket(str: string) {
  if (!str) {
    return str;
  }
  return str.replace(/\(/ig, '').replace(/\)/ig, '').replace(/（/ig, '').replace(/）/ig, '')
    .trim();
}

// 解析 html文件
function parseHtml(htmStr: string, nodeLevel = 0) {
  return recurrenceElementHtml(htmStr, nodeLevel, '');
}

function parseJson(html, level = 0, source) {
  if (!html) {
    return [];
  }
  const elements = parseHtml(html.toString(), level);
  const formatElements: any = pretreatmentElements(elements, pretreatmentElement);
  let json: any = parseElements(formatElements, level);
  json = formatElementsToOfficel(json, source);
  return json;
}

type Params = { html: string, level?: number, source?: 'question' | 'answer', isOfficial?: boolean };

export function htmlToJsonV2({ html, level = 0, source, isOfficial = true }: Params): any[] {
  if (!html) {
    return [];
  }
  let json;
  if (!source) {
    const re = /<p[^>]*>[^<]*<section[^>]+special-split-line[^>]+>[\s\S]*?<\/section>[^>]*<\/p>/i;
    const [questionHtml, answerHtml] = html.split(re);
    json = parseJson(questionHtml, level, 'question');
    if (answerHtml) {
      const answerJson = parseJson(answerHtml, 0, 'answer');
      json = combineJson(json, answerJson, false);
    }
  } else {
    json = parseJson(html, 0, source);
  }
  if (isOfficial) {
    cleanJsonNodes(json);
  } else {
    validateJsonNodes(json);
  }
  return json;
}
