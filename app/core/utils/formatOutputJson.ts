import { getHtml, getText, parseHtml } from './htmlHelper';
import { TJsonNode } from './htmlToJsonV4';
import { iterateNode } from './treeHelper';

export function formatOutputJson(json: TJsonNode[]) {
  for (const { node } of iterateNode(json)) {
    if (
      node.node_type === 'question' && node.content.answer.length === 1
      && node.children.length && node.children[0].node_type === 'question' && node.children[0].content.answer[0]
    ) {
      const answerText = getText(parseHtml(node.content.answer[0]));
      let isSuccess = false;
      if (['解', '证明', '答', '解:', '证明:', '答:', '解：', '证明：', '答：'].includes(answerText)) {
        node.content.answer = [];
        const answerNode = parseHtml(node.children[0].content.answer[0]);
        for (const { node } of iterateNode(answerNode)) {
          if (node.type === 'text') {
            node.content = answerText + node.content;
            isSuccess = true;
            break;
          }
        }
        let answerHtml = getHtml(answerNode);
        if (!isSuccess) answerHtml = answerText + answerHtml;
        node.children[0].content.answer[0] = answerHtml;
      }
    }
  }
  return json;
}
