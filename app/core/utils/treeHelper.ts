import axios from 'axios';
import { backIterate, enumerate , ParallelController } from './helper';
import { v4 as uuidv4 } from 'uuid';
import {
  // @ts-ignore
  checkNodeQuestionContent, checkNonQuestionContent,
  // checkJsonScore,
  formatErrorInfo
} from './jsonHelper/check';

const cheerio = require('cheerio');

export interface IterateNodeCallbackParams<T> {
  node: T;
  index: number;
  parent?: T;
  siblings: T[];
  level: number;
  stopIterateChildren?: () => void;
}

export type IterateNodePredictor<T> = (params: IterateNodeCallbackParams<T>) => boolean;

export interface IterateNodeParams<T> {
  level?: number;
  parent?: T;
  back?: boolean;
  order?: 'pre' | 'post';
  getChildren?: (n: T) => T[];
}

// 遍历树
export function* iterateNode<T>(
  nodes: T[],
  {
    level = 0,
    parent,
    back = false,
    order = 'pre',
    getChildren = (n) => (n as any).children,
  }: IterateNodeParams<T> = {}
): IterableIterator<IterateNodeCallbackParams<T>> {
  const iter = back ? backIterate(nodes) : nodes; // 正向or反向
  for (const [node, index] of enumerate(iter)) {
    let stop = false;
    if (order === 'pre') {
      // 先序 dfs
      const stopIterateChildren = () => {
        // 停止遍历（当前节点的）子节点
        stop = true;
      };
      yield {
        node,
        index,
        parent,
        siblings: nodes,
        level,
        stopIterateChildren,
      };
    }
    if (!stop) {
      // 递归遍历子节点
      const subs = getChildren(node);
      if (subs && subs.length) {
        yield* iterateNode(subs, {
          level: level + 1,
          parent: node,
          back,
          order,
          getChildren,
        });
      }
    }
    if (order === 'post') {
      // 后序 dfs
      yield { node, index, parent, siblings: nodes, level };
    }
  }
}

// 遍历叶子节点
export function* iterateLeaf<T>(
  nodes: T[],
  options: IterateNodeParams<T> = {}
) {
  for (const item of iterateNode(nodes, options)) {
    const subs = options.getChildren
      ? options.getChildren(item.node)
      : ((item.node as any).children as T[]);
    if (!subs || !subs.length) {
      yield item;
    }
  }
}

// 查找满足条件的节点
export function findNode<T>(
  nodes: T[],
  predictor: IterateNodePredictor<T>,
  options: IterateNodeParams<T> = {}
) {
  for (const item of iterateNode(nodes, options)) {
    if (predictor(item)) {
      return item.node;
    }
  }
}

// 查找所有满足条件的节点
export function* findNodesIterate<T>(
  nodes: T[],
  predictor: IterateNodePredictor<T>,
  options: IterateNodeParams<T> & { stopFindChildren?: boolean } = {}
) {
  for (const item of iterateNode(nodes, options)) {
    if (predictor(item)) {
      const { stopFindChildren = true } = options;
      if (stopFindChildren) {
        // 是否再查找满足条件的节点的子节点
        item.stopIterateChildren!();
      }
      yield item.node;
    }
  }
}

// 查找所有满足条件的节点
export function findAllNodes<T>(
  nodes: T[],
  predictor: IterateNodePredictor<T>,
  options: IterateNodeParams<T> & { stopFindChildren?: boolean } = {}
) {
  return [...findNodesIterate(nodes, predictor, options)];
}

function associateNodes(nodes: any[]) {
  for (const { node, index, parent, siblings } of iterateNode(nodes)) {
    node.$index = index;
    node.$parent = parent;
    node.$siblings = siblings;
  }
}

function disassociateNodes(nodes: any[]) {
  for (const { node } of iterateNode(nodes)) {
    delete node.$index;
    delete node.$parent;
    delete node.$siblings;
  }
}

// 按指定标签分割内容。
export function splitNodes<T>(
  nodes: T[],
  predictor: IterateNodePredictor<T>,
  childrenKey = 'children'
): [T[], [T, T][]] {
  associateNodes(nodes);
  const sepPaths = findAllNodes(nodes, predictor, { getChildren: (n) => n[childrenKey] }).map((node) => {
    const path: T[] = [];
    let tmp = node;
    while (tmp) {
      path.unshift(tmp);
      tmp = (tmp as any).$parent;
    }
    return path;
  });
  disassociateNodes(nodes);
  if (!sepPaths.length) return [nodes, []];
  for (const [{ node }, index] of enumerate(iterateNode(nodes))) {
    (node as any).$id = index;
  }
  const result: T[] = [];
  const seps: [T, T][] = [];
  let tree = nodes;
  sepPaths.forEach((path, index) => {
    const node = path[0];
    const isLeaf = path.length === 1;
    const idx = tree.findIndex((t) => (t as any).$id === (node as any).$id);
    const left = tree.slice(0, isLeaf ? idx : idx + 1);
    const sep = { ...node };
    const right = tree.slice(isLeaf ? idx + 1 : idx);
    let currentLeft = left as any;
    let currentSep = sep as any;
    let currentRight = right as any;
    let sepLeaf = isLeaf ? sep : null;
    path.slice(1).forEach((node, i) => {
      const isLeaf = i === path.length - 2;
      if (currentLeft.length) {
        const leftTail = { ...currentLeft[currentLeft.length - 1] };
        currentLeft[currentLeft.length - 1] = leftTail;
        const idx = leftTail[childrenKey].findIndex(
          (t) => t.$id === (node as any).$id
        );
        leftTail[childrenKey] = leftTail[childrenKey].slice(
          0,
          isLeaf ? idx : idx + 1
        );
        currentLeft = leftTail[childrenKey];
      }
      const sep = { ...node };
      currentSep[childrenKey] = [sep];
      if (isLeaf) sepLeaf = sep;
      currentSep = sep;
      if (currentRight.length) {
        const rightHead = { ...currentRight[0] };
        currentRight[0] = rightHead;
        const idx = rightHead[childrenKey].findIndex(
          (t) => t.$id === (node as any).$id
        );
        rightHead[childrenKey] = rightHead[childrenKey].slice(
          isLeaf ? idx + 1 : idx
        );
        currentRight = rightHead[childrenKey];
      }
    });
    result.push(...left, sep);
    if (index === sepPaths.length - 1) {
      result.push(...right);
    }
    seps.push([sep, sepLeaf!]);
    tree = right;
  });
  for (const { node } of iterateNode(nodes)) {
    delete (node as any).$id;
  }
  for (const { node } of iterateNode(result)) {
    delete (node as any).$id;
  }
  return [result, seps];
}

interface ICreateTreeParams<T> {
  getLevel: (n: T) => number;
  childrenKey?: string;
  canAddSub?: (n: T) => boolean;
}

export function createTree<T>(
  nodes: T[],
  {
    getLevel,
    childrenKey = 'children',
    canAddSub = () => true,
  }: ICreateTreeParams<T>
): T[] {
  if (!nodes.length) {
    return [];
  }

  function newNode(data?) {
    return { $parent: undefined, $siblings: [], [childrenKey]: [], ...data };
  }

  function addChild(target, child) {
    child.$parent = target;
    child.$siblings = target[childrenKey];
    target[childrenKey].push(child);
  }

  function addSibling(target, sibling) {
    sibling.$parent = target.$parent;
    sibling.$siblings = target.$siblings;
    target.$siblings.push(sibling);
  }

  const root = newNode();
  let pre = root;
  nodes.forEach((node) => {
    const current = newNode(node);
    const currentLevel = getLevel(current);
    let target = pre;
    while (
      target !== root && getLevel(target) > currentLevel && target.$parent
    ) {
      target = target.$parent;
    }
    if (
      target === root || (getLevel(target) < currentLevel && canAddSub(target))
    ) {
      addChild(target, current);
    } else {
      addSibling(target, current);
    }
    pre = current;
  });
  const nodeTree = root[childrenKey];
  disassociateNodes(nodeTree);
  return nodeTree;
}

/**
 * 生成8位的随机字符串作为UUID
 */
export function generateShortUUID(): string {
  return Math.random().toString(36).substring(2, 10);
}

// 检查图片URL是否有效
async function checkImageUrl(url: string, timeout = 3000000): Promise<boolean> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const response = await axios.head(url);
      if (response.status === 200) {
        return true;
      }
      // 如果不是 200，等待一小段时间后重试
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      // 如果距离开始时间已经超过 timeout，就返回 true
      // if (Date.now() - startTime >= timeout) {
      //   return true;
      // }
      // 否则等待一小段时间后重试
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
  }

  // 如果超时了还没有得到 200 响应，也返回 true
  return true;
}

// 清洗HTML中的img标签
// @todo: 暂时关闭
// @ts-ignore
function cleanImageTags(html: string): string {
  if (!html || 1) return html;
  try {
    const $ = cheerio.load(html, {
      decodeEntities: false,
      xmlMode: true,
    });

    // 处理所有img标签
    $('img').each((_, el) => {
      const $el = $(el);
      // 移除style属性
      $el.removeAttr('style');
      // 获取src属性
      const src = $el.attr('src');
      if (src) {
        // 检查是否包含缩放参数
        const resizePattern = /x-oss-process=image\/resize,p_(\d+)/;
        if (resizePattern.test(src)) {
          // 替换p_数字为p_150
          const newSrc = src.replace(resizePattern, 'x-oss-process=image/resize,p_150');
          $el.attr('src', newSrc);
        } else {
          // 检查是否有x-oss-process参数但没有resize
          const ossProcessPattern = /x-oss-process=image\//;
          if (ossProcessPattern.test(src)) {
            // 已有x-oss-process但没有resize,p_150，添加resize参数
            const newSrc = src.replace(ossProcessPattern, 'x-oss-process=image/resize,p_150/');
            $el.attr('src', newSrc);
          } else {
            // 没有x-oss-process参数，添加完整参数
            // 检查URL是否包含查询参数
            let newSrc = '';
            if (src.includes('?')) {
              // URL已有查询参数，添加到第一个参数前
              newSrc = src.replace('?', '?x-oss-process=image/resize,p_150');
            } else {
              // URL没有查询参数，直接添加
              newSrc = `${src}?x-oss-process=image/resize,p_150`;
            }
            $el.attr('src', newSrc);
          }
        }
      }
    });

    return $.html();
  } catch (e) {
    return html;
  }
}

// 批量检查URLs
async function batchCheckUrls(urls: string[], batchSize = 15): Promise<boolean[]> {
  const results: boolean[] = [];

  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    const batchResults = await Promise.all(
      batch.map((url) => checkImageUrl(url))
    );
    results.push(...batchResults);

    // 每批次检查后稍作延迟，避免请求过于频繁
    if (i + batchSize < urls.length) {
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
  }

  return results;
}

// 对 formatJSONInAI 出来的节点进行转图
export async function json2Jpg(json: any[], type?: string) {
  const nodesWithImages: any[] = [];
  const labelToTagMap = {
    emphasis_wave: 'w',
    emphasis_dot: 'dot',
  };

  // 第一次遍历，标记所有包含img标签的问题节点
  const nodesWithImgTags = new Map<any, boolean>();
  // 记录已经处理过图片的节点，避免重复生成
  const processedNodes = new Set<any>();

  // 创建节点副本，根据type参数决定是否保留答案和解析
  const createNodeWithoutAnswers = (node) => {
    const contentLevel = node.content?.level || 1;
    const serialNumber = type === 'check' ?
      '' : node.content?.serial_number ?
        node.question_type === 'image' ? '' :
          `&lt;qnum${contentLevel}&gt;${node.content?.serial_number || ''}&lt;/qnum${contentLevel}&gt;` : '';

    const shouldKeepAnswers = type === 'check';

    const newNode = {
      ...node,
      content: node.content ? {
        ...node.content,
        body: node.content.body,
        answer: shouldKeepAnswers ? node.content.answer : [],
        analysis: shouldKeepAnswers ? node.content.analysis : '',
        serial_number: serialNumber,
      } : { body: '', answer: [], analysis: '' },
      children: [],
    };

    // 删除可能包含的附加内容字段
    delete newNode.extra1;

    return newNode;
  };
  // 处理具有图片的题目节点
  const processImageNode = async(node: any, parent: any = null): Promise<void> => {
    // 如果节点已经处理过，则跳过
    if (processedNodes.has(node)) return;

    // 如果是小题，构建包含大题和当前小题的JSON
    if (parent && parent.node_type === 'question') {
      // 小题，将image_url设置到小题上
      const image_url = `${uuidv4()}.png`;
      const imageFullUrl = `https://sigma-temp.oss-cn-shanghai.aliyuncs.com/json_jpg/${image_url}`;
      node.image_url = imageFullUrl;
      nodesWithImages.push(imageFullUrl);

      // 构建用于生成图片的JSON（仅包含大题和当前小题）
      const parentNodeCopy = createNodeWithoutAnswers(parent);
      const childNodeCopy = createNodeWithoutAnswers(node);

      parentNodeCopy.children = [childNodeCopy];

      const nodeForImage = [parentNodeCopy];

      // 发送请求生成图片
      await axios.post('http://content-server.hexinedu.com/api/content/open/jsonToJpg', {
        json: nodeForImage,
        file_path: image_url,
      }, { headers: { 'x-request-from': 'hexin' } });

      // 标记节点已处理
      processedNodes.add(node);
    } else {
      // 如果是大题，先为大题生成包含所有小题的完整图片，然后为每个小题生成单独的图片
      // 获取所有小题
      const children = node.children || [];
      const subQuestions = children.filter((child) => child.node_type === 'question');

      // 先为大题生成包含所有小题的完整图片
      const image_url = `${uuidv4()}.jpg`;
      const imageFullUrl = `https://sigma-temp.oss-cn-shanghai.aliyuncs.com/json_jpg/${image_url}`;

      // 将image_url设置到大题上
      node.image_url = imageFullUrl;
      nodesWithImages.push(imageFullUrl);

      // 构建包含大题和所有小题的完整JSON
      const parentNodeCopy = createNodeWithoutAnswers(node);

      // 添加所有小题
      if (subQuestions.length > 0) {
        parentNodeCopy.children = subQuestions.map((childNode) => createNodeWithoutAnswers(childNode));
      }

      const nodeForImage = [parentNodeCopy];

      // 发送请求生成大题图片
      await axios.post('http://content-server.hexinedu.com/api/content/open/jsonToJpg', {
        json: nodeForImage,
        file_path: image_url,
      }, { headers: { 'x-request-from': 'hexin' } });

      // 标记大题已处理
      processedNodes.add(node);

      // 然后为每个小题生成单独的图片
      for (const childNode of subQuestions) {
        if (childNode.node_type === 'question') {
          // 如果小题已经处理过，则跳过
          if (processedNodes.has(childNode)) continue;

          // 只处理有图片的小题
          const hasImage = nodesWithImgTags.get(childNode);
          if (!hasImage) continue;

          const childImage_url = `${uuidv4()}.jpg`;
          const childImageFullUrl = `https://sigma-temp.oss-cn-shanghai.aliyuncs.com/json_jpg/${childImage_url}`;

          // 将image_url设置到小题上
          childNode.image_url = childImageFullUrl;
          nodesWithImages.push(childImageFullUrl);

          // 构建用于生成小题图片的JSON（只包含大题和当前小题）
          const bigQuestionCopy = createNodeWithoutAnswers(node);
          const smallQuestionCopy = createNodeWithoutAnswers(childNode);

          bigQuestionCopy.children = [smallQuestionCopy];

          const childNodeForImage = [bigQuestionCopy];

          // 发送请求生成小题图片
          await axios.post('http://content-server.hexinedu.com/api/content/open/jsonToJpg', {
            json: childNodeForImage,
            file_path: childImage_url,
          }, { headers: { 'x-request-from': 'hexin' } });

          // 标记小题已处理
          processedNodes.add(childNode);
        }
      }
    }
  };
  for (const { node } of iterateNode(json)) {
    node.image_url = '';
    if (node.node_type !== 'question') continue;
    // 如果是 image question，检查图片数量
    if (node.question_type === 'image') {
      const imgMatches = node.content.body.match(/<img[^>]+src="([^"]+)"/g);
      if (imgMatches) {
        if (imgMatches.length === 1) {
          // 只有一个图片，直接使用该图片的 URL
          const src = imgMatches[0].match(/src="([^"]+)"/)?.[1];
          if (src) {
            node.image_url = src;
          }
        } else {
          // 多个图片，调用 processImageNode 处理
          await processImageNode(node);
        }
      }
      continue;
    }

    const hasImgInBody = node.content && node.content.body && /<img/.test(node.content.body);
    const hasImgInChoices = node.content && node.content.choices &&
      node.content.choices.some((v) => /<img/.test(v.option));

    if (hasImgInBody || hasImgInChoices) {
      nodesWithImgTags.set(node, true);
    }
  }
  // 第二次遍历，处理图片生成
  for (const { node, parent } of iterateNode(json)) {
    if (node.node_type !== 'question') continue;
    // 跳过已处理的节点
    if (processedNodes.has(node)) continue;

    // 当前节点自身是否包含img标签
    const isSelfWithImg = nodesWithImgTags.get(node);

    // 1. 如果当前节点本身有图片
    if (isSelfWithImg) {
      await processImageNode(node, parent);
    }
    // 2. 如果当前节点是大题，检查其小题是否有图片
    else if (node.node_type === 'question' && node.children && node.children.length > 0) {
      const subQuestions = node.children.filter((child) => child.node_type === 'question');
      // 检查是否有任何小题包含图片
      const anySubHasImage = subQuestions.some((subQuestion) => nodesWithImgTags.get(subQuestion));
      // 如果有小题包含图片，则为大题也生成图片
      if (anySubHasImage) {
        await processImageNode(node, parent);
      }
    }
  }

  for (const { node } of iterateNode(json)) {
    if (node.content) {
      // 处理body
      if (node.content.body) {
        node.content.body = processDataLabels(node.content.body, labelToTagMap);
      }

      // 处理answer数组
      if (Array.isArray(node.content.answer)) {
        node.content.answer = node.content.answer.map((ans) =>
          (typeof ans === 'string' ? processDataLabels(ans, labelToTagMap) : ans)
        );
      }

      // 处理analysis
      if (node.content.analysis) {
        node.content.analysis = processDataLabels(node.content.analysis, labelToTagMap);
      }

      // 处理choices数组中的option
      if (Array.isArray(node.content.choices)) {
        node.content.choices.forEach((choice) => {
          if (choice && choice.option) {
            choice.option = processDataLabels(choice.option, labelToTagMap);
          }
        });
      }
    }
  }

  // 批量检查URLs
  const urlChecks = await batchCheckUrls(nodesWithImages);
  // 如果所有URL都有效，返回URL列表
  if (urlChecks.every((valid) => valid)) {
    return nodesWithImages;
  }

  // 如果有无效的URL，抛出错误
  throw new Error('存在无效的图片 URL');
}

/**
 * 处理HTML内容中的data-label属性
 * @param html HTML字符串
 * @param labelToTagMap 标签映射表
 * @returns 处理后的HTML字符串
 */
function processDataLabels(html: string, labelToTagMap: Record<string, string>): string {
  if (!html) return html;
  try {
    const $ = cheerio.load(html, {
      decodeEntities: false,
      // 使用XML模式，避免添加html/head/body标签
      xmlMode: true,
    });

    // 查找所有带有data-label属性的元素
    $('[data-label]').each((_, el) => {
      const $el = $(el);
      const label = $el.attr('data-label');

      // 如果在映射表中存在对应的标签
      if (label && labelToTagMap[label]) {
        // 获取元素内容
        const content = $el.html();
        // 创建新标签
        const newTag = $(`<${labelToTagMap[label]}>${content}</${labelToTagMap[label]}>`);
        // 复制除了data-label以外的所有属性
        const attributes = el.attribs;
        for (const attr in attributes) {
          if (attr !== 'data-label') {
            newTag.attr(attr, attributes[attr]);
          }
        }
        // 替换原标签
        $el.replaceWith(newTag);
      }
      // 去掉所有的 style 属性
      $el.removeAttr('style');
    });

    return $.html();
  } catch (e) {
    console.error('处理HTML标签时出错:', e);
    return html;
  }
}

/**
 * 格式化 JSON 并进行 AI 检查
 * @param json 要格式化的 JSON 数组
 * @param maxParallel 最大并行检查数量，默认为 20
 * @returns 格式化后的 JSON
 */
export async function formatJSONInAI(json: any[], type: 'analysis' | 'check' = 'analysis', maxParallel = 20) {
  const formattedJson = json;
  const nodeIdMap = new Map<any, string>();
  // 第一次遍历，生成并分配所有节点的ID
  for (const { node } of iterateNode(formattedJson)) {
    const nodeId = generateShortUUID();
    nodeIdMap.set(node, nodeId);
    node.node_id = nodeId;
    // 清空答案和解析
    if (type === 'analysis') {
      node.content.analysis = '';
      node.content.answer = [];
    }
    node.score_info = {
      body_score: null,
      answer_score: null,
      analysis_score: null,
    };
  }

  // 检查标题节点
  // await checkNonQuestionContent(formattedJson);

  // 创建并行控制器
  const parallelController = new ParallelController(maxParallel);
  const questionNodes: { node: any; index: number }[] = [];

  // 第二次遍历，设置父节点ID和收集需要检查的问题节点
  let index = 0;
  for (const { node, parent } of iterateNode(formattedJson)) {
    node.parent_node_id = parent ? nodeIdMap.get(parent) : '';

    // 检查节点是否有 errorInfo 字段
    if (node.errorInfo && Array.isArray(node.errorInfo)) {
      // 格式化每个 errorInfo 项
      node.error_info = node.errorInfo.map((error) => formatErrorInfo(error, node));
      delete node.errorInfo;
    } else {
      // 如果节点没有 errorInfo 字段，添加一个空数组
      node.error_info = [];
    }

    // 如果是 question 类型节点，添加到待检查列表
    if (node.node_type === 'question') {
      questionNodes.push({ node, index: index += 1 });
    }

    // 增加 agent_info 字段
    node.agent_info = {};
  }

  // 添加所有问题节点的检查任务到并行控制器
  for (const { node, index: questionIndex } of questionNodes) {
    parallelController.addTask(async() => {
      try {
        // 先关掉
        // const contentErrors = await checkNodeQuestionContent(node);
        const contentErrors = [];
        if (contentErrors.length > 0) {
          // 将内容错误信息添加到 node.error_info
          node.error_info = [
            ...node.error_info,
            ...contentErrors.map((v) => formatErrorInfo(v, node))
          ].filter((v) => v.error_info || v.fix_info);
        }
      } catch (e) {
        throw new Error(`检查问题节点时出错: ${e}`);
      }
    }, questionIndex);
  }

  // 执行所有检查任务
  await parallelController.execute();

  // 单个节点的分值都刷完了，最后检查一下分值信息
  // await checkJsonScore(formattedJson);

  return formattedJson;
}

// 按照一级题号打平 JSON 结构
export function flattenByLevelOneQuestions<T extends { node_type?: string, content?: { level?: number }, children?: T[], node_id?: string }>(nodes: T[]): T[] {
  const result: T[] = [];
  const processedNodes = new Set<string | number>(); // 用于跟踪已处理的节点ID

  function processNode(node: T, addToResult = true) {
    // 获取节点ID，如果没有则使用对象引用
    const nodeId = node.node_id || (node as any).__id || Math.random();

    // 如果已经处理过这个节点，跳过
    if (processedNodes.has(nodeId)) {
      return;
    }

    processedNodes.add(nodeId);

    // 检查节点类型和子节点
    const nodeType = node.node_type;
    const children = node.children || [];

    // 创建节点的副本以避免修改原始数据
    const nodeCopy = { ...node };

    // 检查是否有question类型的子节点
    const questionChildren = children.filter((child) => child.node_type === 'question');
    const hasQuestionChild = questionChildren.length > 0;

    // 只有当当前节点是question且有question子节点时，才不打平
    if (nodeType === 'question' && hasQuestionChild) {
      // 保留完整结构，不打平
      if (addToResult) {
        result.push(nodeCopy);
      }
    } else {
      // 其他情况都打平处理，但保留children字段（设为空数组）
      if ('children' in nodeCopy) {
        const childrenCopy = [...(nodeCopy.children || [])];
        nodeCopy.children = [];

        // 添加自身到结果
        if (addToResult) {
          result.push(nodeCopy);
        }

        // 递归处理子节点
        for (const child of childrenCopy) {
          processNode(child, true);
        }
      } else {
        // 没有children字段的节点直接添加
        if (addToResult) {
          result.push(nodeCopy);
        }
      }
    }
  }

  // 处理每个顶级节点
  for (const node of nodes) {
    processNode(node);
  }

  return result;
}
