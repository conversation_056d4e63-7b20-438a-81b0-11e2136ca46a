import * as katex from 'katex';
import { iterateNode } from './treeHelper';
import { escape, getText, parseHtml, THtmlNode } from './htmlHelper';

// 带索引遍历
export function * enumerate<T>(data: Iterable<T> | ArrayLike<T>): IterableIterator<[T, number]> {
  const arr = data as ArrayLike<T>;
  if (arr.length != null) {
    for (let i = 0; i < arr.length; i += 1) {
      yield [arr[i], i];
    }
  } else {
    const iterable = data as Iterable<T>;
    let i = 0;
    for (const item of iterable) {
      yield [item, i];
      i += 1;
    }
  }
}

// 倒序遍历
export function * backIterate<T>(list: ArrayLike<T>) {
  for (let i = list.length - 1; i >= 0; i -= 1) {
    yield list[i];
  }
}

export function * slice<T>(
  data: ArrayLike<T> | Iterable<T>,
  { start = 0, stop = -1, step = 1 }: { start?: number; stop?: number; step?: number } = {}
) {
  const arr = data as ArrayLike<T>;
  const begin = start < 0 && arr.length != null ? arr.length + start : start;
  const end = stop < 0 && arr.length != null ? arr.length + stop : stop;
  if (begin >= end) {
    return;
  }
  if (arr.length != null) {
    for (let i = begin; i < end; i += step) {
      yield arr[i];
    }
  } else {
    const iterable = data as Iterable<T>;
    for (const [item, i] of enumerate(iterable)) {
      if (i < begin) {
        continue;
      }
      if (i >= end) {
        break;
      }
      if (step === 1 || (i - begin) % step === 0) {
        yield item;
      }
    }
  }
}

// 清理文本空格
export function strapText(text: string, trim = true) {
  const res = text.replace(/\s+/g, ' ');
  return trim ? res.trim() : res;
}

export function cleanFilename(filename: string) {
  return filename.replace(/[\000-\031\\\/<>:"|?* ]/g, '_').replace(/\.$/, '_');
}

export function isHtmlStr(text: string) { // 字符串内容是否为富文本
  return /<\/\w+>|<\w+.*?\/>|&[\w\d]+;/.test(`${text}`);
}

export function formatLatexHtml(html: string, { latexMode = 'dataset' }: { latexMode?: 'dataset' | 'html' | 'mathml' } = {}) {
  // 将html中 $$frac{1}{2}$$ 替换为 <span data-label="latex" data-value="frac{1}{2}"></span>
  const htmlStr = !isHtmlStr(html) && /\$\$/.test(html) ? escape(html) : html;
  return htmlStr.replace(/\$\$([\s\S]*?)\$\$/g, (_match, latex: string) => {
    if (latexMode === 'dataset') {
      return `<span data-label="latex" data-value="${latex.replace(/"/g, '&quot;')}"></span>`;
    }
    return katex.renderToString(latex, { output: latexMode, throwOnError: false });
  });
}

export function convertLatexToPlainText(nodes: THtmlNode[]) {
  /*
   * 将html中的公式替换成纯文本。清除公式
   * 如 $$\frac{1}{2}$$ 或 <span data-label="latex" data-value="\frac{1}{2}"></span>，都将被替换为 1/2
   */
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type !== 'element') {
      continue;
    }
    if (node.dataset.label === 'latex') {
      const html: string = katex.renderToString(node.dataset.value, { output: 'mathml', throwOnError: false });
      const content = getText(parseHtml(html.replace(/<annotation[^>]*>[\s\S]*?<\/annotation>/g, '')));
      Object.assign(node, { type: 'text', content, dataset: {} });
      stopIterateChildren!();
    }
  }
}

export function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示整个匹配的字符串
}

// 中国地市map
export const CHINA_AREAS = new Map([
  // 直辖市 (Direct-controlled municipalities)
  ['北京', '北京'],
  ['天津', '天津'],
  ['上海', '上海'],
  ['重庆', '重庆'],

  // 省份 (Provinces)
  ['河北', '河北'],
  ['山西', '山西'],
  ['辽宁', '辽宁'],
  ['吉林', '吉林'],
  ['黑龙江', '黑龙江'],
  ['江苏', '江苏'],
  ['浙江', '浙江'],
  ['安徽', '安徽'],
  ['福建', '福建'],
  ['江西', '江西'],
  ['山东', '山东'],
  ['河南', '河南'],
  ['湖北', '湖北'],
  ['湖南', '湖南'],
  ['广东', '广东'],
  ['海南', '海南'],
  ['四川', '四川'],
  ['贵州', '贵州'],
  ['云南', '云南'],
  ['陕西', '陕西'],
  ['甘肃', '甘肃'],
  ['青海', '青海'],
  ['台湾', '台湾'], // Note: Represents the claimed Taiwan province.

  // 自治区 (Autonomous Regions)
  ['内蒙古', '内蒙古'],
  ['广西', '广西'],
  ['西藏', '西藏'],
  ['宁夏', '宁夏'],
  ['新疆', '新疆'],

  // 特别行政区 (Special Administrative Regions)
  ['香港', '香港'],
  ['澳门', '澳门'],

  // 各省份/自治区/直辖市的地级行政区 (Prefecture-level divisions within provinces/ARs/municipalities)

  // 北京 (Beijing) - 直辖市
  // Usually treated as a single unit at this level.

  // 天津 (Tianjin) - 直辖市
  // Usually treated as a single unit at this level.

  // 上海 (Shanghai) - 直辖市
  // Usually treated as a single unit at this level.

  // 重庆 (Chongqing) - 直辖市
  // Usually treated as a single unit at this level, though it has internal districts/counties directly under it.

  // 河北 (Hebei)
  ['石家庄', '河北'],
  ['唐山', '河北'],
  ['秦皇岛', '河北'],
  ['邯郸', '河北'],
  ['邢台', '河北'],
  ['保定', '河北'],
  ['张家口', '河北'],
  ['承德', '河北'],
  ['沧州', '河北'],
  ['廊坊', '河北'],
  ['衡水', '河北'],

  // 山西 (Shanxi)
  ['太原', '山西'],
  ['大同', '山西'],
  ['阳泉', '山西'],
  ['长治', '山西'],
  ['晋城', '山西'],
  ['朔州', '山西'],
  ['晋中', '山西'],
  ['运城', '山西'],
  ['忻州', '山西'],
  ['临汾', '山西'],
  ['吕梁', '山西'],

  // 内蒙古 (Inner Mongolia Autonomous Region) - 自治区
  ['呼和浩特', '内蒙古'],
  ['包头', '内蒙古'],
  ['乌海', '内蒙古'],
  ['赤峰', '内蒙古'],
  ['通辽', '内蒙古'],
  ['鄂尔多斯', '内蒙古'],
  ['呼伦贝尔', '内蒙古'], // 地级市 (Prefecture-level city)
  ['巴彦淖尔', '内蒙古'], // 地级市 (Prefecture-level city)
  ['乌兰察布', '内蒙古'], // 地级市 (Prefecture-level city)
  ['兴安盟', '内蒙古'], // 盟 (League)
  ['锡林郭勒盟', '内蒙古'], // 盟 (League)
  ['阿拉善盟', '内蒙古'], // 盟 (League)

  // 辽宁 (Liaoning)
  ['沈阳', '辽宁'],
  ['大连', '辽宁'],
  ['鞍山', '辽宁'],
  ['抚顺', '辽宁'],
  ['本溪', '辽宁'],
  ['丹东', '辽宁'],
  ['锦州', '辽宁'],
  ['营口', '辽宁'],
  ['阜新', '辽宁'],
  ['辽阳', '辽宁'],
  ['盘锦', '辽宁'],
  ['铁岭', '辽宁'],
  ['朝阳', '辽宁'],
  ['葫芦岛', '辽宁'],

  // 吉林 (Jilin)
  ['长春', '吉林'],
  ['吉林市', '吉林'],
  ['四平', '吉林'],
  ['辽源', '吉林'],
  ['通化', '吉林'],
  ['白山', '吉林'],
  ['松原', '吉林'],
  ['白城', '吉林'],
  ['延边朝鲜族自治州', '吉林'], // 自治州 (Autonomous prefecture)

  // 黑龙江 (Heilongjiang)
  ['哈尔滨', '黑龙江'],
  ['齐齐哈尔', '黑龙江'],
  ['鸡西', '黑龙江'],
  ['鹤岗', '黑龙江'],
  ['双鸭山', '黑龙江'],
  ['大庆', '黑龙江'],
  ['伊春', '黑龙江'],
  ['佳木斯', '黑龙江'],
  ['七台河', '黑龙江'],
  ['牡丹江', '黑龙江'],
  ['黑河', '黑龙江'],
  ['绥化', '黑龙江'],
  ['大兴安岭地区', '黑龙江'], // 地区 (Prefecture)

  // 江苏 (Jiangsu)
  ['南京', '江苏'],
  ['无锡', '江苏'],
  ['徐州', '江苏'],
  ['常州', '江苏'],
  ['苏州', '江苏'],
  ['南通', '江苏'],
  ['连云港', '江苏'],
  ['淮安', '江苏'],
  ['盐城', '江苏'],
  ['扬州', '江苏'],
  ['镇江', '江苏'],
  ['泰州', '江苏'],
  ['宿迁', '江苏'],

  // 浙江 (Zhejiang)
  ['杭州', '浙江'],
  ['宁波', '浙江'],
  ['温州', '浙江'],
  ['嘉兴', '浙江'],
  ['湖州', '浙江'],
  ['绍兴', '浙江'],
  ['金华', '浙江'],
  ['衢州', '浙江'],
  ['舟山', '浙江'],
  ['台州', '浙江'],
  ['丽水', '浙江'],

  // 安徽 (Anhui)
  ['合肥', '安徽'],
  ['芜湖', '安徽'],
  ['蚌埠', '安徽'],
  ['淮南', '安徽'],
  ['马鞍山', '安徽'],
  ['淮北', '安徽'],
  ['铜陵', '安徽'],
  ['安庆', '安徽'],
  ['黄山', '安徽'],
  ['滁州', '安徽'],
  ['阜阳', '安徽'],
  ['宿州', '安徽'],
  ['六安', '安徽'],
  ['亳州', '安徽'],
  ['池州', '安徽'],
  ['宣城', '安徽'],

  // 福建 (Fujian)
  ['福州', '福建'],
  ['厦门', '福建'],
  ['莆田', '福建'],
  ['三明', '福建'],
  ['泉州', '福建'],
  ['漳州', '福建'],
  ['南平', '福建'],
  ['龙岩', '福建'],
  ['宁德', '福建'],

  // 江西 (Jiangxi)
  ['南昌', '江西'],
  ['景德镇', '江西'],
  ['萍乡', '江西'],
  ['九江', '江西'],
  ['新余', '江西'],
  ['鹰潭', '江西'],
  ['赣州', '江西'],
  ['吉安', '江西'],
  ['宜春', '江西'],
  ['抚州', '江西'],
  ['上饶', '江西'],

  // 山东 (Shandong)
  ['济南', '山东'],
  ['青岛', '山东'],
  ['淄博', '山东'],
  ['枣庄', '山东'],
  ['东营', '山东'],
  ['烟台', '山东'],
  ['潍坊', '山东'],
  ['济宁', '山东'],
  ['泰安', '山东'],
  ['威海', '山东'],
  ['日照', '山东'],
  ['莱芜', '山东'], // Note: Merged into Jinan in 2019, but historically separate. Including for completeness if needed, otherwise can be removed. Data sources might vary. (Let's keep it for potential historical mapping or remove if strictly current). Based on current data, it's part of Jinan. Let's remove to be current.
  ['临沂', '山东'],
  ['德州', '山东'],
  ['聊城', '山东'],
  ['滨州', '山东'],
  ['菏泽', '山东'],

  // 河南 (Henan)
  ['郑州', '河南'],
  ['开封', '河南'],
  ['洛阳', '河南'],
  ['平顶山', '河南'],
  ['安阳', '河南'],
  ['鹤壁', '河南'],
  ['新乡', '河南'],
  ['焦作', '河南'],
  ['濮阳', '河南'],
  ['许昌', '河南'],
  ['漯河', '河南'],
  ['三门峡', '河南'],
  ['南阳', '河南'],
  ['商丘', '河南'],
  ['信阳', '河南'],
  ['周口', '河南'],
  ['驻马店', '河南'],
  ['济源', '河南'], // 河南省直管县级市，视同地级市管理 (Sub-provincial city, managed as prefecture-level)

  // 湖北 (Hubei)
  ['武汉', '湖北'],
  ['黄石', '湖北'],
  ['襄阳', '湖北'],
  ['十堰', '湖北'],
  ['荆州', '湖北'],
  ['宜昌', '湖北'],
  ['鄂州', '湖北'],
  ['荆门', '湖北'],
  ['孝感', '湖北'],
  ['咸宁', '湖北'],
  ['随州', '湖北'],
  ['恩施土家族苗族自治州', '湖北'], // 自治州 (Autonomous prefecture)
  // 省直辖县级行政单位 (Sub-provincial county-level administrative units managed directly by the province) - often grouped under a placeholder or handled separately. Let's list them under Hubei for simplicity here as they are at the same level conceptually for many uses.
  ['仙桃', '湖北'],
  ['潜江', '湖北'],
  ['天门', '湖北'],
  ['神农架林区', '湖北'], // 林区 (Forestry district)

  // 湖南 (Hunan)
  ['长沙', '湖南'],
  ['株洲', '湖南'],
  ['湘潭', '湖南'],
  ['衡阳', '湖南'],
  ['邵阳', '湖南'],
  ['岳阳', '湖南'],
  ['常德', '湖南'],
  ['张家界', '湖南'],
  ['益阳', '湖南'],
  ['郴州', '湖南'],
  ['永州', '湖南'],
  ['怀化', '湖南'],
  ['娄底', '湖南'],
  ['湘西土家族苗族自治州', '湖南'], // 自治州 (Autonomous prefecture)

  // 广东 (Guangdong)
  ['广州', '广东'],
  ['韶关', '广东'],
  ['深圳', '广东'],
  ['珠海', '广东'],
  ['汕头', '广东'],
  ['佛山', '广东'],
  ['江门', '广东'],
  ['湛江', '广东'],
  ['茂名', '广东'],
  ['肇庆', '广东'],
  ['惠州', '广东'],
  ['梅州', '广东'],
  ['汕尾', '广东'],
  ['河源', '广东'],
  ['阳江', '广东'],
  ['清远', '广东'],
  ['东莞', '广东'],
  ['中山', '广东'],
  ['潮州', '广东'],
  ['揭阳', '广东'],
  ['云浮', '广东'],

  // 广西 (Guangxi Zhuang Autonomous Region) - 自治区
  ['南宁', '广西'],
  ['柳州', '广西'],
  ['桂林', '广西'],
  ['梧州', '广西'],
  ['北海', '广西'],
  ['防城港', '广西'],
  ['钦州', '广西'],
  ['贵港', '广西'],
  ['玉林', '广西'],
  ['百色', '广西'],
  ['贺州', '广西'],
  ['河池', '广西'],
  ['来宾', '广西'],
  ['崇左', '广西'],

  // 海南 (Hainan)
  ['海口', '海南'],
  ['三亚', '海南'],
  ['三沙', '海南'], // 地级市 (Prefecture-level city)
  // 省直辖县级行政区划 (Sub-provincial county-level administrative units managed directly by the province) - similar to Hubei
  ['五指山', '海南'],
  ['琼海', '海南'],
  ['儋州', '海南'],
  ['文昌', '海南'],
  ['万宁', '海南'],
  ['东方', '海南'],
  ['定安县', '海南'], // Note: 县 (County)
  ['屯昌县', '海南'],
  ['澄迈县', '海南'],
  ['临高县', '海南'],
  ['白沙黎族自治县', '海南'], // 自治县 (Autonomous county)
  ['昌江黎族自治县', '海南'],
  ['乐东黎族自治县', '海南'],
  ['陵水黎族自治县', '海南'],
  ['保亭黎族苗族自治县', '海南'],
  ['琼中黎族苗族自治县', '海南'],

  // 四川 (Sichuan)
  ['成都', '四川'],
  ['自贡', '四川'],
  ['攀枝花', '四川'],
  ['泸州', '四川'],
  ['德阳', '四川'],
  ['绵阳', '四川'],
  ['广元', '四川'],
  ['遂宁', '四川'],
  ['内江', '四川'],
  ['乐山', '四川'],
  ['南充', '四川'],
  ['眉山', '四川'],
  ['宜宾', '四川'],
  ['广安', '四川'],
  ['达州', '四川'],
  ['雅安', '四川'],
  ['巴中', '四川'],
  ['资阳', '四川'],
  ['阿坝藏族羌族自治州', '四川'], // 自治州 (Autonomous prefecture)
  ['甘孜藏族自治州', '四川'], // 自治州 (Autonomous prefecture)
  ['凉山彝族自治州', '四川'], // 自治州 (Autonomous prefecture)

  // 贵州 (Guizhou)
  ['贵阳', '贵州'],
  ['六盘水', '贵州'],
  ['遵义', '贵州'],
  ['安顺', '贵州'],
  ['毕节', '贵州'], // 地级市 (Prefecture-level city)
  ['铜仁', '贵州'], // 地级市 (Prefecture-level city)
  ['黔西南布依族苗族自治州', '贵州'], // 自治州 (Autonomous prefecture)
  ['黔东南苗族侗族自治州', '贵州'], // 自治州 (Autonomous prefecture)
  ['黔南布依族苗族自治州', '贵州'], // 自治州 (Autonomous prefecture)

  // 云南 (Yunnan)
  ['昆明', '云南'],
  ['曲靖', '云南'],
  ['昭通', '云南'],
  ['楚雄彝族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['红河哈尼族彝族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['文山壮族苗族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['玉溪', '云南'],
  ['普洱', '云南'], // 地级市 (Prefecture-level city)
  ['临沧', '云南'], // 地级市 (Prefecture-level city)
  ['大理白族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['保山', '云南'],
  ['德宏傣族景颇族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['怒江傈僳族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['迪庆藏族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['丽江', '云南'],
  ['怒江傈僳族自治州', '云南'], // 自治州 (Autonomous prefecture) - duplicate? Check source... yes, listing again is error. Removing.
  ['临沧', '云南'], // 地级市 (Prefecture-level city) - already listed. Removing duplicate.
  ['西双版纳傣族自治州', '云南'], // 自治州 (Autonomous prefecture)
  ['德宏傣族景颇族自治州', '云南'], // 自治州 (Autonomous prefecture) - already listed. Removing duplicate.

  // Let's regenerate Yunnan carefully
  ['昆明', '云南'],
  ['曲靖', '云南'],
  ['昭通', '云南'],
  ['玉溪', '云南'],
  ['保山', '云南'],
  ['昭通', '云南'], // Duplicate, remove
  ['丽江', '云南'],
  ['普洱', '云南'],
  ['临沧', '云南'],
  ['楚雄彝族自治州', '云南'],
  ['红河哈尼族彝族自治州', '云南'],
  ['文山壮族苗族自治州', '云南'],
  ['西双版纳傣族自治州', '云南'],
  ['大理白族自治州', '云南'],
  ['德宏傣族景颇族自治州', '云南'],
  ['怒江傈僳族自治州', '云南'],
  ['迪庆藏族自治州', '云南'],


  // 西藏 (Tibet Autonomous Region) - 自治区
  ['拉萨', '西藏'],
  ['日喀则', '西藏'], // 地级市 (Prefecture-level city)
  ['昌都', '西藏'], // 地级市 (Prefecture-level city)
  ['林芝', '西藏'], // 地级市 (Prefecture-level city)
  ['山南', '西藏'], // 地级市 (Prefecture-level city)
  ['那曲', '西藏'], // 地级市 (Prefecture-level city)
  ['阿里地区', '西藏'], // 地区 (Prefecture)

  // 陕西 (Shaanxi)
  ['西安', '陕西'],
  ['铜川', '陕西'],
  ['宝鸡', '陕西'],
  ['咸阳', '陕西'],
  ['渭南', '陕西'],
  ['延安', '陕西'],
  ['汉中', '陕西'],
  ['榆林', '陕西'],
  ['安康', '陕西'],
  ['商洛', '陕西'],

  // 甘肃 (Gansu)
  ['兰州', '甘肃'],
  ['嘉峪关', '甘肃'],
  ['金昌', '甘肃'],
  ['白银', '甘肃'],
  ['天水', '甘肃'],
  ['武威', '甘肃'],
  ['张掖', '甘肃'],
  ['平凉', '甘肃'],
  ['酒泉', '甘肃'],
  ['庆阳', '甘肃'],
  ['定西', '甘肃'],
  ['陇南', '甘肃'],
  ['临夏回族自治州', '甘肃'], // 自治州 (Autonomous prefecture)
  ['甘南藏族自治州', '甘肃'], // 自治州 (Autonomous prefecture)

  // 青海 (Qinghai)
  ['西宁', '青海'],
  ['海东', '青海'], // 地级市 (Prefecture-level city)
  ['海北藏族自治州', '青海'], // 自治州 (Autonomous prefecture)
  ['黄南藏族自治州', '青海'], // 自治州 (Autonomous prefecture)
  ['海南藏族自治州', '青海'], // 自治州 (Autonomous prefecture)
  ['果洛藏族自治州', '青海'], // 自治州 (Autonomous prefecture)
  ['玉树藏族自治州', '青海'], // 自治州 (Autonomous prefecture)
  ['海西蒙古族藏族自治州', '青海'], // 自治州 (Autonomous prefecture)

  // 宁夏 (Ningxia Hui Autonomous Region) - 自治区
  ['银川', '宁夏'],
  ['石嘴山', '宁夏'],
  ['吴忠', '宁夏'],
  ['固原', '宁夏'],
  ['中卫', '宁夏'],

  // 新疆 (Xinjiang Uygur Autonomous Region) - 自治区
  ['乌鲁木齐', '新疆'],
  ['克拉玛依', '新疆'],
  ['吐鲁番', '新疆'], // 地级市 (Prefecture-level city)
  ['哈密', '新疆'], // 地级市 (Prefecture-level city)
  ['阿克苏地区', '新疆'], // 地区 (Prefecture)
  ['喀什地区', '新疆'], // 地区 (Prefecture)
  ['和田地区', '新疆'], // 地区 (Prefecture)
  ['塔城地区', '新疆'], // 地区 (Prefecture)
  ['阿勒泰地区', '新疆'], // 地区 (Prefecture)
  ['克孜勒苏柯尔克孜自治州', '新疆'], // 自治州 (Autonomous prefecture)
  ['博尔塔拉蒙古自治州', '新疆'], // 自治州 (Autonomous prefecture)
  ['昌吉回族自治州', '新疆'], // 自治州 (Autonomous prefecture)
  ['巴音郭楞蒙古自治州', '新疆'], // 自治州 (Autonomous prefecture)
  ['伊犁哈萨克自治州', '新疆'], // 自治州 (Autonomous prefecture) - Note:伊犁州管辖塔城地区和阿勒泰地区
  // 省直辖县级行政单位 (Sub-provincial county-level administrative units, Xinjiang Production and Construction Corps cities are often listed here)
  ['石河子', '新疆'],
  ['阿拉尔', '新疆'],
  ['图木舒克', '新疆'],
  ['五家渠', '新疆'],
  ['北屯', '新疆'],
  ['铁门关', '新疆'],
  ['双河', '新疆'],
  ['可克达拉', '新疆'],
  ['昆玉', '新疆'],
  ['胡杨河', '新疆'],
  ['新星', '新疆'],

  // 台湾 (Taiwan) - 中国主张的省份划分，不包含实际管辖的行政区划
  // Based on PRC's administrative claims for Taiwan Province. These are not the divisions currently administered by the ROC government.
  ['台北', '台湾'],
  ['高雄', '台湾'],
  ['台南', '台湾'],
  ['台中', '台湾'],
  ['新北', '台湾'],
  ['桃园', '台湾'],
  // 其他县市... (Many more counties and cities according to PRC claims)
  // Listing all counties/cities of Taiwan based on PRC claims would make the list even longer.
  // For practical purposes, often just listing the province or major cities is sufficient unless specific detail on the claimed divisions is needed.
  // Let's include a few more major ones based on PRC claims.
  ['基隆', '台湾'],
  ['新竹', '台湾'],
  ['嘉义', '台湾'],
  ['彰化县', '台湾'],
  ['南投县', '台湾'],
  ['云林县', '台湾'],
  ['嘉义县', '台湾'],
  ['屏东县', '台湾'],
  ['宜兰县', '台湾'],
  ['桃园县', '台湾'], // Note: Now Taoyuan City
  ['新竹县', '台湾'],
  ['苗栗县', '台湾'],
  ['彰化县', '台湾'], // Duplicate, remove
  ['南投县', '台湾'], // Duplicate, remove
  ['云林县', '台湾'], // Duplicate, remove
  ['嘉义县', '台湾'], // Duplicate, remove
  ['屏东县', '台湾'], // Duplicate, remove
  ['台东县', '台湾'],
  ['花莲县', '台湾'],
  ['澎湖县', '台湾'],
  ['金门县', '台湾'],
  ['连江县', '台湾'], // 马祖地区 (Matsu Islands)

  // Let's refine Taiwan based on common lists of PRC claimed divisions (prefecture-level equivalent)
  // Note: PRC treats the former 16 counties and 5 provincial cities as prefecture-level equivalents, plus the 6 special municipalities.
  ['台北市', '台湾'], // Often listed this way
  ['高雄市', '台湾'],
  ['台南市', '台湾'],
  ['台中市', '台湾'],
  ['新北市', '台湾'],
  ['桃园市', '台湾'],
  ['基隆市', '台湾'],
  ['新竹市', '台湾'],
  ['嘉义市', '台湾'],
  ['新竹县', '台湾'],
  ['苗栗县', '台湾'],
  ['彰化县', '台湾'],
  ['南投县', '台湾'],
  ['云林县', '台湾'],
  ['嘉义县', '台湾'],
  ['屏东县', '台湾'],
  ['宜兰县', '台湾'],
  ['桃园县', '台湾'], // Outdated, use Taoyuan City
  ['花莲县', '台湾'],
  ['台东县', '台湾'],
  ['澎湖县', '台湾'],
  ['金门县', '台湾'],
  ['连江县', '台湾'],

  // 香港 (Hong Kong Special Administrative Region) - 特别行政区
  // Usually treated as a single unit at this level.

  // 澳门 (Macao Special Administrative Region) - 特别行政区
  // Usually treated as a single unit at this level.

]);

/**
 * 并行任务控制器
 * 用于控制异步任务的并发执行数量
 */
export class ParallelController {
  private queue: Array<() => Promise<any>> = [];
  private running = 0;
  private results: any[] = [];
  private errors: any[] = [];

  constructor(private maxParallel: number) {}

  /**
   * 添加任务到队列
   * @param task 异步任务函数
   * @param index 任务索引，用于保持结果顺序
   */
  addTask(task: () => Promise<any>, index: number) {
    this.queue.push(async () => {
      try {
        const result = await task();
        this.results[index] = result;
      } catch (error) {
        this.errors[index] = error;
        console.error(`Task ${index} failed:`, error);
      }
    });
  }

  /**
   * 执行所有任务
   * @returns 所有任务的结果数组
   */
  async execute(): Promise<any[]> {
    const execute = async () => {
      while (this.queue.length > 0) {
        if (this.running >= this.maxParallel) {
          await new Promise(resolve => setTimeout(resolve, 100));
          continue;
        }

        const task = this.queue.shift();
        if (!task) continue;

        this.running++;
        try {
          await task();
        } finally {
          this.running--;
        }
      }
    };

    // 启动执行器
    await execute();

    // 如果有错误，抛出第一个错误
    if (this.errors.length > 0) {
      const firstError = this.errors.find(e => e !== undefined);
      if (firstError) throw firstError;
    }

    return this.results;
  }
}
