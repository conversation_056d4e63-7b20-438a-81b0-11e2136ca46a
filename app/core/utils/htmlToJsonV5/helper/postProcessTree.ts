import { htmlDecodeByRegExp } from '../../index';
import { iterateNode } from '../../treeHelper';
import { htmlToJsonV5, IQuestionNode, TJsonNode } from '../index';

export function postProcessTree(elementTree: TJsonNode[]) {
  for (const { node } of iterateNode(elementTree)) {
    if (
      node.node_type === 'question' &&
      node.children.length &&
      node.question_type !== 'material'
    ) {
      node.question_type = 'other';
    }

    // 拆分注解功能到notes
    setNoteProperty(node);
  }
}

// 设置 notes 信息 (data-content -> notes属性)
export function setNoteProperty(node: TJsonNode) {
  // body
  setNoteBody(node);
  if (node.node_type === 'question' && node.content._sequence) {
    node.content._sequence.map((type) => {
      if (type === 'answer') setNoteAnswer(node);
      if (type === 'analysis') setNoteAnalysis(node);
      if (/extra/.test(type)) setNoteExtra(node, type);
    });
  }
}

function transformToNoteHtml(str) {
  // 解决html被编码问题 &lt; -> p
  const preNoteHtml = htmlDecodeByRegExp(str);
  const noteHtml = htmlToJsonV5({ html: preNoteHtml });
  return noteHtml;
}

function setNoteBody(node: TJsonNode) {
  if (node?.content?.body && /data-content/.test(node.content.body)) {
    const matcher = node.content.body.match(/data-content="([\s\S]*?)"/);
    const idMatcher = node.content.body.match(/data-note-id="([\s\S]*?)"/);
    if (matcher?.length && idMatcher?.length) {
      // 清除body里面的note-content
      node.content.body = node.content.body.replace(
        /data-content="([\s\S]*?)"/g,
        ''
      );
      if (matcher[1]) {
        const noteHtml = transformToNoteHtml(matcher[1]);
        // @todo：数组 为了后续兼容同时存在analysis等
        if (!Array.isArray(node.content.notes)) node.content.notes = [];
        node.content.notes.push({
          note_id: idMatcher[1],
          note_content: noteHtml,
        });
      }
    }
  }
}

function setNoteAnswer(node: IQuestionNode) {
  if (node?.content?.answer) {
    node.content.answer.forEach((answer, index) => {
      if (/data-content/.test(answer)) {
        const matcher = answer.match(/data-content="([\s\S]*?)"/);
        const idMatcher = answer.match(/data-note-id="([\s\S]*?)"/);
        if (matcher?.length && idMatcher?.length) {
          // 清除note-content
          node.content.answer[index] = answer.replace(
            /data-content="([\s\S]*?)"/g,
            ''
          );
          if (matcher[1]) {
            const noteHtml = transformToNoteHtml(matcher[1]);
            if (!Array.isArray(node.content.notes)) node.content.notes = [];
            node.content.notes.push({
              note_id: idMatcher[1],
              note_content: noteHtml,
            });
          }
        }
      }
    });
  }
}

function setNoteExtra(node: IQuestionNode, extraName: string) {
  if (
    node?.content?.[extraName]?.body &&
    /data-content/.test(node.content[extraName].body)
  ) {
    const matcher = node.content[extraName].body.match(
      /data-content="([\s\S]*?)"/
    );
    const idMatcher = node.content[extraName].body.match(
      /data-note-id="([\s\S]*?)"/
    );
    if (matcher?.length && idMatcher?.length) {
      // 清除body里面的note-content
      node.content[extraName].body = node.content[extraName].body.replace(
        /data-content="([\s\S]*?)"/g,
        ''
      );
      if (matcher[1]) {
        const noteHtml = transformToNoteHtml(matcher[1]);
        // @todo：数组 为了后续兼容同时存在analysis等
        if (!Array.isArray(node.content.notes)) node.content.notes = [];
        node.content.notes.push({
          note_id: idMatcher[1],
          note_content: noteHtml,
        });
      }
    }
  }
}

function setNoteAnalysis(node: IQuestionNode) {
  if (node?.content?.analysis && /data-content/.test(node.content.analysis)) {
    const matcher = node.content.analysis.match(/data-content="([\s\S]*?)"/);
    const idMatcher = node.content.analysis.match(/data-note-id="([\s\S]*?)"/);
    if (matcher?.length && idMatcher?.length) {
      // 清除body里面的note-content
      node.content.analysis = node.content.analysis.replace(
        /data-content="([\s\S]*?)"/g,
        ''
      );
      if (matcher[1]) {
        const noteHtml = transformToNoteHtml(matcher[1]);
        // @todo：数组 为了后续兼容同时存在analysis等
        if (!Array.isArray(node.content.notes)) node.content.notes = [];
        node.content.notes.push({
          note_id: idMatcher[1],
          note_content: noteHtml,
        });
      }
    }
  }
}
