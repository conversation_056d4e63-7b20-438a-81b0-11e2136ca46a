// 第0步：预先清理html节点中的信息
import { IElementNode, THtmlNode } from '../../htmlHelper';
import { findAllNodes, findNode, iterateNode } from '../../treeHelper';
import { strapText } from '../../helper';
import { getHtml, parseHtml } from '../../../../core/utils/htmlHelper';

export function preCleanHtml(html: string) {
  const nodes = parseHtml(html);
  for (const { node, index, parent } of iterateNode(nodes)) {
    if (node.type === 'element') { // 删除无意义的span标签。
      if (node.tagName === 'span' && (node.dataset.label === 'rect' || !node.dataset.label) && !node.attrs.style && !node.attrs.class?.includes('img-signal-container')) {
        (parent as any)?.children.splice(index, 1, { type: 'text', content: '' }, ...node.children);
      }
    }
  }
  return getHtml(nodes);
}

// 清理节点中非法信息
export function cleanupNode(nodes: THtmlNode[]) {
  for (const { node } of iterateNode(nodes)) {
    if (node.type === 'element' && node.dataset.label === 'discard') {
      Object.assign(node, { type: 'fragment', children: [] });
    }
    // 删除注释、无地址图片、本地地址图片、无内容的公式
    if (node.type === 'comment' ||
      node.type === 'element' && (
        node.tagName === 'img' && (!node.attrs.src || node.attrs.src.startsWith('file:///')) ||
        node.dataset.label === 'latex' && !node.dataset.value
      )) {
      Object.assign(node, { type: 'fragment', children: [] });
    }
    if (node.type === 'element') {
      // 移除 img-id
      delete node.attrs['data-img-id'];
      // 移除 add-miss
      if (node.attrs.class) {
        node.attrs.class = strapText(node.attrs.class.replace(/add-miss/g, ''));
      }
      // 移除 img 的空白 alt 属性
      if (node.tagName === 'img' && node.attrs.alt === '') {
        delete node.attrs.alt;
      }
      // 删除坐标信息
      if (node.dataset.label === 'rect') {
        Object.assign(node, { type: 'fragment' });
      }
    }
  }
  const BLOCK_LABELS = ['answer', 'explanation', 'extra'];
  // 将表格中的块标签移动到外层p中
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type !== 'element' || node.tagName !== 'table') {
      continue;
    }
    stopIterateChildren!();
    const tdNodes = findAllNodes(node.children, ({ node }) => {
      return node.type === 'element' && node.tagName === 'td';
    }) as IElementNode[];
    const tagNode = findNode(tdNodes, ({ node }) => {
      return node.type === 'element' && node.tagName === 'p' && BLOCK_LABELS.includes(node.dataset.label!);
    }) as IElementNode | undefined;
    if (!tagNode) {
      continue;
    }
    Object.assign(node.dataset, tagNode.dataset);
    Object.assign(node.attrs, tagNode.attrs);
    tdNodes.forEach((tdNode) => {
      const children = tdNode.children.filter((c) => c.type !== 'text' || c.content.trim());
      if (children.length === 1) {
        const pNode = children[0];
        if (pNode.type === 'element' && pNode.tagName === 'p' && pNode.dataset.label) {
          tdNode.children = pNode.children; // 把 TD 中单独的 P 拆掉
        }
      } else {
        tdNode.children.forEach((node) => {
          if (node.type === 'element' && node.tagName === 'p' && node.dataset.label) {
            delete node.attrs['data-label'];
            delete node.attrs['data-level'];
          }
        });
      }
      // 拆标签过程中可能导致 TD 存在多种对齐方式，清理一下
      if (/text-align/.test(tdNode.attrs.style || '') && /align-/.test(tdNode.attrs.class || '')) {
        tdNode.attrs.class = tdNode.attrs.class!.replace(/(align-center)|(align-left)|(align-right)/g, '');
      }
    });
  }
  // 删除答案、解析、文本中的选项、来源标记；删除文本中的题号标记
  nodes.forEach((blockNode) => {
    if (blockNode.type !== 'element' || !BLOCK_LABELS.includes(blockNode.dataset.label!)) {
      return;
    }
    for (const { node, stopIterateChildren } of iterateNode(blockNode.children)) {
      if (node.type !== 'element') {
        continue;
      }
      if (['choice_option', 'source'].includes(node.dataset.label!) ||
        node.dataset.label === 'quest_num' && blockNode.dataset.label === 'text') {
        stopIterateChildren!();
        delete node.dataset.label;
        delete node.attrs['data-label'];
      }
    }
  });
  // 移除题号、选项等内部嵌套的任何其他标注标签
  const MARK_LABELS = ['quest_num', 'option', 'source', 'knowledge', 'blank', 'bracket'];
  for (const { node, stopIterateChildren } of iterateNode((nodes))) {
    if (node.type !== 'element' || !MARK_LABELS.includes(node.dataset.label!)) {
      continue;
    }
    stopIterateChildren!();
    for (const item of iterateNode(node.children)) {
      const { node } = item;
      if (node.type === 'element' && MARK_LABELS.includes(node.dataset.label!)) {
        delete node.dataset.label;
        delete node.dataset.value;
        delete node.dataset.level;
        delete node.attrs['data-label'];
        delete node.attrs['data-value'];
        delete node.attrs['data-level'];
      }
    }
  }
}

// 合并相邻的题号、选项节点
export function mergeLabelNear(nodes: THtmlNode[]) {
  const LABELS = ['quest_num', 'choice_option'];
  for (const { node, index, siblings, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type !== 'element' || !LABELS.includes(node.dataset.label!)) continue;
    stopIterateChildren!();
    for (let i = index + 1; i < siblings.length; i += 1) {
      const current = siblings[i];
      if (current.type !== 'element' ||
        current.dataset.label !== node.dataset.label ||
        current.dataset.level !== node.dataset.level) {
        break;
      }
      node.children = node.children.concat(current.children);
      Object.assign(current, { type: 'fragment', children: [] });
    }
  }
}

// 答案题号添加答案解析标记
export function addLabelForAnswerQuestNum(nodes: THtmlNode[]) {
  const ANS_EXP_LABELS = ['answer', 'explanation', 'extra'];
  nodes.forEach((node, index) => {
    if (node.type !== 'element' || ANS_EXP_LABELS.includes(node.dataset.label!)) {
      return;
    }
    const numNode = findNode(node.children, ({ node }) => {
      return node.type === 'element' && node.dataset.label === 'quest_num';
    });
    if (!numNode) {
      return;
    }
    let label;
    for (let i = index + 1; i < nodes.length; i += 1) {
      const current = nodes[i];
      if (current.type !== 'element' || !current.dataset.label) {
        continue;
      }
      if (ANS_EXP_LABELS.includes(current.dataset.label)) {
        label = current.dataset.label;
      }
      break;
    }
    node.dataset.label = label;
  });
}
