// 第三步：将组装的信息转成最终json节点
import * as _ from 'lodash';
import { IDataInfo, INodeInfo, IQuestionDataInfo, TSeparator, IMaterialQuestionDataInfo } from './preBuildData';
import { strapText } from '../../helper';
import { escape, getHtml, getText, IElementNode, parseHtml, THtmlNode, unescape } from '../../htmlHelper';
import { findNode, iterateNode, IterateNodePredictor, splitNodes } from '../../treeHelper';
import { filterEmptyNode } from './filterEmptyNode';
import { checkQuestionType } from '../checkQuestionType';
import {
  EJsonErrorRule,
  IBasicQuestionData,
  IBasicQuestionNode,
  IChapterData,
  IChapterNode,
  IExtra,
  IJsonErrorInfo,
  IParagraphData,
  IQuestionContent
} from '../index';
import { parseSerialNumber } from '../../parseSerialNumber';

export interface ISeparatorElement {
  node_type: TSeparator;
  imgId?: string;
  errorInfo?: IJsonErrorInfo[];
  _sequence?: string[];
  extraNodes?: THtmlNode[][];
}

export interface ITextElement extends IParagraphData {
  node_type: 'none' | 'text';
  imgId?: string;
  errorInfo?: IJsonErrorInfo[];
}

export function convertElements(elementsData: [INodeInfo, IDataInfo][]) {
  const results = elementsData.map(([element, data]) => {
    let { errorInfo } = data;
    if (element.node_type === 'chapter') {
      const info = convertChapterElement(data);
      for (const { node } of iterateNode(data.nodes)) {
        if (node.type === 'element' && node.tagName === 'b') {
          errorInfo = errorInfo ?? [];
          errorInfo.push({
            rule: EJsonErrorRule.chapter_b_tag,
            message: '标题中存在加粗样式',
          });
          break;
        }
      }
      return {
        node_level: 0,
        node_type: element.node_type,
        imgId: element.imgId,
        ...info,
        children: [],
        errorInfo,
      } as IChapterNode;
    }
    if (element.node_type === 'none' || element.node_type === 'text') { // 无标记段落或文本段落
      const info = convertParagraphElement(data);
      return {
        node_type: element.node_type,
        imgId: element.imgId,
        ...info,
        errorInfo,
      } as ITextElement;
    }
    if (element.node_type === 'question') {
      const info = convertQuestionElement(data as IQuestionDataInfo);
      if (info.content.meta?.duplicateAnswer) {
        errorInfo = errorInfo ?? [];
        errorInfo.push({
          rule: EJsonErrorRule.duplicate_answer,
          message: '作答空间中含答案，同时又有单独答案',
        });
      }
      if (info.content.meta?.singleChoiceError) {
        errorInfo = errorInfo ?? [];
        errorInfo.push({
          rule: EJsonErrorRule.options_invalid,
          message: '试题只有一个选项',
        });
      }
      return {
        node_level: 0,
        node_type: element.node_type,
        imgId: element.imgId,
        ...info,
        children: [],
        errorInfo,
      } as IBasicQuestionNode;
    }
    if (element.node_type === 'material_start_separator' && (data as IMaterialQuestionDataInfo)?.extraNodes?.length) {
      const content: ISeparatorElement = {
        node_type: element.node_type,
        imgId: element.imgId,
        errorInfo,
        _sequence: (data as IMaterialQuestionDataInfo)._sequence,
      };
      (data as IMaterialQuestionDataInfo).extraNodes.forEach((extra, index) => {
        content[`extra${index + 1}`] = parseExtra(extra);
      });
      return content as ISeparatorElement;
    }
    return {
      node_type: element.node_type,
      imgId: element.imgId,
      errorInfo,
    } as ISeparatorElement;
  });
  return results;
}

// 使用 \text{} 包住中文。
function escapeChinese(c: string) {
  let r = c;
  // \func\text 中 => \func{\text{中}}
  r = r.replace(/(\\[a-z]+)\s*\\(text|mathrm)\s*([\u4E00-\u9FFF\u3400-\u4DFF\uF900-\uFAFF、，。！：；“”‘’])/g, '$1{\\text{$3}}');
  // \func 中 => \func{\text{中}}  \text 中 => \text{中}
  r = r.replace(/\\([a-z]+)\s*([\u4E00-\u9FFF\u3400-\u4DFF\uF900-\uFAFF、，。！：；“”‘’])/g, (_m, f, t) => {
    return ['text', 'mathrm'].includes(f) ? `\\text{${t}}` : `\\${f}{\\text{${t}}}`;
  });
  const texts: string[] = [];
  // \text {中文} => ####1####
  r = r.replace(/\\(text|mathrm)\s*{([\u4E00-\u9FFF\u3400-\u4DFF\uF900-\uFAFF、，。！：；“”‘’]+)}/g, (_m, _f, t) => {
    texts.push(t);
    return `####${texts.length}####`;
  });
  // 中文 => \text{中文}
  r = r.replace(/[\u4E00-\u9FFF\u3400-\u4DFF\uF900-\uFAFF、，。！：；“”‘’]+/g, '\\text{$&}');
  if (texts.length) {
    // ####1#### => \text{中文}
    r = r.replace(/####(\d+)####/g, (_m, i) => `\\text{${texts[i - 1]}}`);
  }
  // \text{中}\text{文} => \text{中文}
  r = r.replace(/(\\text{[^{}]+}){2,}/g, (m) => {
    const t = m.replace(/\\text{([^{}]+)}/g, '$1');
    return `\\text{${t}}`;
  });
  return r;
}

function getHtmlFromNodes(nodes: THtmlNode[], removeEmpty = true) {
  const clonedNodes = _.cloneDeep(nodes);
  // 删除开头空白
  for (const item of iterateNode(clonedNodes)) {
    if (item.node.type === 'element' && ['four_lines', 'blank', 'bracket'].includes(item.node.dataset.label!)) break;
    if (item.node.tagName === 'u') item.stopIterateChildren!();
    if (item.node.type === 'text') {
      if (removeEmpty) item.node.content = item.node.content.replace(/^(\s|&nbsp;)+/, '');
      break;
    }
  }
  // 删除结尾空白
  for (const item of iterateNode(clonedNodes, { back: true })) {
    if (item.node.type === 'element' && ['four_lines', 'blank', 'bracket'].includes(item.node.dataset.label!)) break;
    if (item.node.tagName === 'u') item.stopIterateChildren!();
    if (item.node.type === 'text') {
      item.node.content = item.node.content.replace(/(\s|&nbsp;)+$/, '');
      break;
    }
  }
  // 移除没有特别标签的span
  for (const { node } of iterateNode(clonedNodes)) {
    if (node.type === 'element') {
      if (node.dataset.label === 'rect') {
        Object.keys(node.dataset).forEach((key) => {
          delete node.dataset[key];
          delete node.attrs[`data-${key}`];
        });
      }
    }
    if (node.type === 'element' && node.tagName !== 'table'
      && !node.dataset.label && node.tagName === 'span' && !node.attrs.class && !node.attrs.style) {
      Object.assign(node, { type: 'fragment' });
    }

    if (node.type === 'element' && node.dataset.label === 'latex') {
      let latex = node.dataset.value!;
      latex = latex.replace(/(\s*\\\\)+$/g, '');
      latex = escapeChinese(latex);
      node.dataset.value = latex;
      node.attrs['data-value'] = latex;
      node.children = [{ type: 'text', content: escape(latex) }];
    }
  }
  const html = getHtml(clonedNodes, { wrapLatexWith: '$$' });
  return html.replace(/<p><\/p>/g, '').replace(/\$\$\s*\$\$/g, ' ').trim().replace(/(&nbsp;| )+/g, (m) => {
    const s = m.split(/(&nbsp;| )/g).filter((s) => Boolean(s));
    if (!s[1] || s[1] !== ' ') {
      s[0] = ' ';
    }
    if (s[s.length - 2] && s[s.length - 2] !== ' ') {
      s[s.length - 1] = ' ';
    }
    return s.join('');
  });
}

function convertChapterElement({ level, nodes }: IDataInfo): IChapterData {
  const node_name = strapText(getText(nodes, { wrapLatexWith: '$$' }));
  cleanDataset(nodes);
  const _body = getHtmlFromNodes(nodes, false);
  const body = _body.replace(
    /<img[^>]*? data-image-text="([^"]*?)"[^>]*?\/?>/g,
    (_, imageText) => {
      return imageText;
    }
  );
  const node: IChapterData = { node_name, content: { level: level!, body } };
  if (/<img[^>]*?>/.test(_body)) {
    node.content._body = _body;
  }
  return node;
}

function convertParagraphElement({ nodes }: IDataInfo): IParagraphData {
  const source = extraTag(nodes, 'source', true);
  const tag = extraTag(nodes, 'knowledge');
  cleanDataset(nodes);
  const body = getHtmlFromNodes(nodes);
  const [first] = nodes;
  const level = first.type === 'element' && Number(first.dataset.level!) || 1;
  return { content: { level, body, tag, source } };
}

function convertQuestionElement(data: IQuestionDataInfo) {
  const { _serialNumber, serialNumber, level, nodes, _sequence } = data;
  const { answerNodes, explanationNodes, extraNodes } = data;
  const { bodyAnswers, blankCount, bracketCount } = parseQuestionAnswerInBody(nodes);
  answerNodes.forEach((node) => {
    if (node.type === 'element' && node.dataset.label === 'answer') {
      delete node.dataset.label;
      delete node.attrs['data-label'];
    }
  });
  explanationNodes.forEach((node) => {
    if (node.type === 'element' && node.dataset.label === 'explanation') {
      delete node.dataset.label;
      delete node.attrs['data-label'];
    }
  });
  const questionAnswers = parseQuestionAnswer(answerNodes);
  const answers = questionAnswers.length ? questionAnswers : bodyAnswers;
  if (!questionAnswers.length && bodyAnswers.length) {
    _sequence.push('answer');
  }
  const isTF = answers.some((answer) => answer.isTF)
    || /^([对错]+|[√✔×❌]+)$/.test(answers.map((a) => a.answer).join('').replace(/\s+/g, '').trim());
  const correct = isTF ? answers.map((a) => {
    return a.isTF ? a.correct : isCorrect(a.answer);
    // 其他情况 undefined
  }) : null;
  const answer = answers.map((a) => a.answer);
  // @todo
  const source = extraTag(nodes, 'source', true);
  let [bodyNodes, choicesNodes] = findChoicesNodesInBody(nodes);
  // 少于2个空时，题干中解析选择题选项 或者 题干内有选项并且括号结尾
  let singleChoiceError = false;
  if (choicesNodes.length === 1) {
    singleChoiceError = true;
  }
  const canChoice = (blankCount && bracketCount) < 2 || answer.length < 2 || choicesNodes.length && /<span[^<>]*?data-label="bracket"[^<>]*?>[^<>]*?<\/span[^<>]*?><\/p>$/.test(getHtmlFromNodes(bodyNodes));
  if (!canChoice) {
    bodyNodes = nodes;
    choicesNodes = [];
  }
  const body = getHtmlFromNodes(bodyNodes);
  const analysis = getHtmlFromNodes(explanationNodes);
  const snResult = parseSerialNumber(serialNumber);
  const sn = snResult?.type === 'number' ? snResult.number : undefined;
  const content: IQuestionContent = {
    body,
    serial_number: serialNumber,
    sn,
    level: level!,
    answer,
    source,
    analysis,
    _sequence,
    meta: {
      nodes, // 保留题干节点内容。之后可以用于猜测选择题
      duplicateAnswer: questionAnswers.some((a) => a.answer) && bodyAnswers.some((a) => a.answer),
      singleChoiceError,
    },
  };
  const _snStr = getHtmlFromNodes([_serialNumber]);
  if (/<img[^>]*?>/.test(_snStr)) {
    content._serial_number = _snStr.replace('<p>', '').replace('</p>', '')
      .replace(/<span[^>]*? data-label="quest_num"[^>]*?>([\s\S]*?)<\/span>/g, (_, content) => {
        return content;
      })
      .trim()
      .replace(/\.$/g, '');
  }
  if (blankCount) {
    content.blank_count = blankCount;
  }
  if (bracketCount) {
    content.bracket_count = bracketCount;
  }
  if (correct) {
    content.correct = correct;
  }
  const choices = parseChoiceOptions(choicesNodes);
  if (choices.length) {
    content.choices = choices;
    content.choice_count = choices.length;
  }
  const question_type = checkQuestionType(content);
  extraNodes.forEach((extra, index) => {
    content[`extra${index + 1}`] = parseExtra(extra);
  });
  return { content, question_type } as IBasicQuestionData;
}

// 解析附加内容
function parseExtra(extra: THtmlNode[]): IExtra {
  let title = '';
  for (const { node } of iterateNode(extra)) {
    if (node.type === 'element' && node.dataset.label === 'extra_title') {
      if (!title) {
        title = getHtmlFromNodes(node.children);
      }
      Object.assign(node, { type: 'fragment', children: [] });
    }
    if (node.type === 'element' && node.dataset.label === 'extra') {
      delete node.dataset.label;
      delete node.attrs['data-label'];
    }
  }
  return {
    title,
    body: getHtmlFromNodes(extra),
  };
}

// 解析题干中的答案
function parseQuestionAnswerInBody(nodes: THtmlNode[]) {
  const bodyAnswers: ReturnType<typeof getAnswerHtmlFromNodes>[] = []; // 在题干中的答案
  let blankCount = 0;
  let bracketCount = 0;
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    // 查找填空、括号
    if (node.type !== 'element' || node.dataset.label !== 'blank' && node.dataset.label !== 'bracket') {
      continue;
    }
    stopIterateChildren!();
    const children = node.children;
    const answerItem = getAnswerHtmlFromNodes(children);
    bodyAnswers.push(answerItem);
    node.children = []; // 清空填空、括号里的内容
    if (node.dataset.label === 'blank') { // 包含了填空题横线
      const blankLength = getBlankLength(children);
      node.attrs['data-blank-length'] = `${blankLength}`;
      blankCount += 1;
    } else {
      bracketCount += 1;
    }
  }
  return { bodyAnswers, blankCount, bracketCount };
}

function isCorrect(answer: string) {
  if (/^[对√✔T]$/i.test(answer)) return true;
  if (/^[错×❌f]$/i.test(answer)) return false;
}

function isCorrectLabel(l: string) {
  if (l === 'tf_true') return true;
  if (l === 'tf_false') return false;
}

// 获取横线内文本长度
function getBlankLength(nodes: THtmlNode[]) {
  const answerText = getText(nodes, { wrapLatexWith: '' });
  let count = 0;
  for (let i = answerText.length - 1; i >= 0; i -= 1) {
    const charCode = answerText.charCodeAt(i);
    if (charCode >= 65 && charCode <= 90) { // 大写英文
      count += 0.44;
    } else if (charCode >= 0 && charCode <= 255) { // 其他字符
      count += 0.33;
    } else { // 非 ascii
      count += 0.74;
    }
  }
  return Math.ceil(count);
}

// 解析答案
function parseQuestionAnswer(nodes: THtmlNode[]) {
  const answersNodes = splitAnswer(nodes);
  const answers = answersNodes.map((nodes) => {
    let answerNodes = nodes;
    const node = nodes[0];
    if (nodes.length === 1 && node.type === 'element' && node.tagName !== 'table') {
      answerNodes = node.children;
    }
    const item = getAnswerHtmlFromNodes(answerNodes);
    return item;
  });
  return answers;
}

function getAnswerHtmlFromNodes(nodes: THtmlNode[]) {
  const tfNode = findNode(nodes, ({ node }) => {
    return node.type === 'element' && ['tf', 'tf_true', 'tf_false'].includes(node.dataset.label!);
  }) as IElementNode | undefined;
  if (tfNode) {
    const answer = strapText(getText([tfNode]));
    return {
      answer,
      isTF: true,
      correct: isCorrectLabel(tfNode.dataset.label!) ?? isCorrect(answer),
    };
  }
  let answer = getHtmlFromNodes(nodes);
  const isOnlyText = nodes.every((node) => node.type === 'text');
  if (isOnlyText) {
    answer = unescape(answer);
  }
  answer = strapText(answer);
  return { answer, isTF: false };
}

// 提取目标内容，并在在原位置删除
function extraTag(nodes: THtmlNode[], label: string, isRetain = false) {
  const targetNodes = extractTargetNodes(nodes, ({ node }) => {
    return node.type === 'element' && node.dataset.label === label;
  }, isRetain);
  let tag = strapText(getText(targetNodes, { wrapLatexWith: '$$' }));
  if (label === 'source') {
    tag = getHtmlFromNodes(targetNodes);
  }
  /*
   * return tag.replace(/^\(([\s\S]*?)\)$/, '$1')
   *   .replace(/^（([\s\S]*?)）$/, '$1')
   *   .replace(/^【([\s\S]*?)】$/, '$1')
   *   .replace(/^\[([\s\S]*?)\]$/, '$1')
   *   .replace(/^［([\s\S]*?)］$/, '$1')
   *   .trim();
   */
  return tag.replace(/^[［\[【（\(]([\s\S]*?)[］\]】）\)]$/, '$1').trim();
}

// 对答案按answer-sep分割（填空题多空的情景）
function splitAnswer(answerNodes: THtmlNode[]) {
  const [nodes, seps] = splitNodes(answerNodes, ({ node }) => {
    return node.type === 'element' && node.dataset.label === 'answer-sep';
  });
  const separatorSet = new Set(seps.map(([sep]) => sep));
  let groups: THtmlNode[][] = [];
  let current;
  nodes.forEach((node) => {
    const isSeparator = separatorSet.has(node);
    if (!current || isSeparator) {
      current = [];
      groups.push(current);
    }
    if (!isSeparator) {
      current.push(node);
    }
  });
  groups = groups.map((nodes) => {
    return filterEmptyNode(nodes);
  });
  return groups;
}

function findChoicesNodesInBody(nodes: THtmlNode[]): [THtmlNode[], THtmlNode[][]] {
  const [newNodes, seps] = splitNodes(nodes, ({ node }) => {
    return node.type === 'element' && ['choice_option'].includes(node.dataset.label!);
  });
  const separatorSet = new Set(seps.map(([sep]) => sep));
  const bodyNodes: THtmlNode[] = [];
  const choicesNodes: THtmlNode[][] = [];
  let currentChoiceNodes: THtmlNode[];
  newNodes.forEach((node) => {
    if (separatorSet.has(node)) {
      currentChoiceNodes = [];
      choicesNodes.push(currentChoiceNodes);
    }
    if (currentChoiceNodes) {
      currentChoiceNodes.push(node);
    } else {
      bodyNodes.push(node);
    }
  });
  return [bodyNodes, choicesNodes];
}

export function findChoicesInBody(body?: string | THtmlNode[]): [string, { letter: string, option: string }[]] | undefined {
  if (!body) {
    return;
  }
  const nodes = typeof body === 'string' ? parseHtml(body) : body;
  const [bodyNodes, choicesNodes] = findChoicesNodesInBody(nodes);
  const cleanBody = getHtmlFromNodes(bodyNodes);
  const choices = parseChoiceOptions(choicesNodes);
  return [cleanBody, choices];
}

function parseChoiceOptions(choicesNodes: THtmlNode[][]) {
  const choices = choicesNodes.map((nodes) => {
    const letterNode = nodes[0];
    const optionNodes = nodes.slice(1);
    let letter = getText([letterNode]);
    letter = letter.replace(/[.\s]/g, '');
    const option = getHtmlFromNodes(optionNodes);
    return { letter, option };
  });
  return choices;
}

function cleanDataset(nodes: THtmlNode[]) {
  nodes.forEach((node) => {
    if (node.type !== 'element') return;
    Object.keys(node.attrs).forEach((key) => {
      if (key.startsWith('data-') && key !== 'data-line') {
        delete node.attrs[key];
      }
    });
  });
}

function extractTargetNodes(nodes: THtmlNode[], predictor: IterateNodePredictor<THtmlNode>, isRetain = false) {
  const result: THtmlNode[] = [];
  for (const item of iterateNode(nodes)) {
    if (predictor(item)) {
      const { node } = item;
      result.push({ ...node });
      // 在原位置删除
      if (!isRetain) Object.assign(node, { type: 'fragment', children: [] });
      else Object.assign(node, { attrs: { ...(node as any)?.attrs, style: 'display: none' }, children: [] });
    }
  }
  return result;
}
