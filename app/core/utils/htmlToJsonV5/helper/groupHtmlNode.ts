// 第一步：按类型对html节点分组
import { getText, THtmlNode } from '../../htmlHelper';
import { strapText } from '../../helper';
import { findNode } from '../../treeHelper';

interface IDataSet {
  // 如果无 label，为无标记的文本
  label?: 'header' // 目录
    | 'material_start_separator' | 'material_end_separator' // 材料题分割线
    | 'quest_num' // 题号
    | 'text' // 文本
    | 'extra' // 附加内容
    | 'answer' | 'explanation' // 答案解析
    | 'note'; // 标记

  imgId: string;
  serialNumber?: string;
  _serialNumber?: THtmlNode;
  level?: string;
  p?: string;
}

interface INodeGroupMark extends IDataSet {
  mark?: IDataSet;
}

export interface IHtmlNodeGroup extends INodeGroupMark {
  nodes: THtmlNode[];
}

export function groupNodes(nodes: THtmlNode[]) {
  let nodeGroups: IHtmlNodeGroup[] = [];
  let currentGroup: IHtmlNodeGroup;
  nodes.forEach((node) => {
    const mark = getMark(node as any);
    if (!currentGroup || !canCombine(currentGroup, mark)) {
      currentGroup = { ...mark, nodes: [] };
      nodeGroups.push(currentGroup);
    }
    currentGroup.nodes.push(node);
  });
  nodeGroups.forEach((group) => {
    if (group.label === 'quest_num') {
      group.serialNumber = getSerialNumberFromNode(group.nodes[0]);
      group._serialNumber = group.nodes[0];
    }
    if (group.mark && group.mark.label === 'quest_num') {
      // 这里是答案解析里的题号
      group.mark.serialNumber = getSerialNumberFromNode(group.nodes[0]);
      group.mark._serialNumber = group.nodes[0];
    }
  });
  nodeGroups = filterEmptyGroup(nodeGroups);
  return nodeGroups;
}

// 获取类型
function getMark({ dataset, mark }): INodeGroupMark {
  const imgId = dataset['img-id'];
  if (dataset.label) {
    return { ...dataset, mark, imgId };
  }
  if (mark && mark.label) {
    return { ...mark, imgId };
  }
  return { imgId }; // 其他情况是无标记段落
}

// 指定类型是否可以合并到当前分组中
function canCombine(group: IHtmlNodeGroup, mark: INodeGroupMark) {
  if (group.label === 'header') {
    // 当前组是 header，不能合并新元组
    return false;
  }
  if (group.label === 'material_start_separator' || group.label === 'material_end_separator') {
    // 当前组是分割线，不能合并新元组
    return false;
  }
  if (group.label === 'quest_num') {
    // 当前组是题号，只能合并无标记段落
    return mark.label == null;
  }
  if (group.label !== mark.label) {
    // label 不同不能合并
    return false;
  }
  // 以下 label 相同
  if (group.label === 'answer' || group.label === 'explanation' || group.label === 'extra') {
    // 当前组是答案解析。新元素需要也是答案解析，且没有题号
    return !mark.mark || mark.mark.label !== 'quest_num';
  }
  if (group.label === 'text') {
    // 文本段落需要有相同的层级
    return group.level === mark.level;
  }
  return true;
}

// 读取题号内容
function getSerialNumberFromNode(node) {
  let serialNumber = strapText(getText([node]));
  serialNumber = serialNumber.replace(/[.\s]$/, '');
  return serialNumber;
}

// 过滤空白分组
function filterEmptyGroup(nodeGroups: IHtmlNodeGroup[]) {
  const results = nodeGroups.filter((item) => {
    return item.label === 'material_start_separator'
      || item.label === 'material_end_separator'
      || getText(item.nodes)
      || findNode(item.nodes, ({ node }) => node.type === 'element' && node.tagName === 'img');
  });
  return results;
}
