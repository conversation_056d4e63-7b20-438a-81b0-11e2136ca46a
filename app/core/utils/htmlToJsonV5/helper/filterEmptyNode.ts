// 过滤掉最外层的空白html节点
import { getText, THtmlNode, SELF_CLOSE_TAGS } from '../../htmlHelper';
import { findNode } from '../../treeHelper';

export function filterEmptyNode(nodes: THtmlNode[]) {
  const results = nodes.filter((node) => {
    if (node.type !== 'element') { // 去掉非element节点
      return false;
    }
    if (node.dataset.label === 'page_separator') { // 去掉分页符
      return false;
    }
    if (SELF_CLOSE_TAGS.includes(node.tagName)) { // 保留自闭和节点
      return true;
    }
    if (!node.children || !node.children.length) { // 去掉没有子节点的节点
      return false;
    }
    if (node.attrs.class) { // 保留含class的节点
      return true;
    }
    // 去掉子节点无内容（不存在文本、图片、data-label(或者data-label为font)）的节点
    return node.children.length > 1 ||
      getText(node.children).trim() ||
      findNode(node.children, ({ node }) => {
        return node.type === 'element' && (SELF_CLOSE_TAGS.includes(node.tagName) || Boolean(node.dataset.label) && node.dataset.label !== 'font' || node.tagName === 'u');
      });
  });
  return results;
}
