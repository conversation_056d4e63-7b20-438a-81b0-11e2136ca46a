import {
  TJsonNode,
  IQuestionNode,
  EJsonErrorRule
} from './index';
import { iterateNode } from '../treeHelper';
import { getText, parseHtml, getHtml } from '../htmlHelper';
import { IBasicQuestionNode } from '../htmlToJsonV4';
export function validateJsonRule(json: TJsonNode[], subject?: string) {
  for (const { node, siblings, index } of iterateNode(json)) {
    checkJb(node);
    if (/<p.*?class=".*?align-right.*?".*?>.*?<\/p>\s*?<.*?class=".*?align-right.*?".*?>.*?<\/p>/.test(node.content.body)) {
      node.errorInfo = [...(node.errorInfo || []), {
        rule: EJsonErrorRule.node_content_error,
        message: '存在多行连续居右',
      }];
    }
    if (node.node_type === 'question' || node.node_type === 'paragraph') {
      if (/<\/table>[\s\S]*?续表[\s\S]*?<table/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.node_content_error,
          message: '存在没有合并的续表',
        }];
      }
    }
    if (node.node_type === 'question') {
      const bodyJson = parseHtml(node.content.body);
      const bodyText = getText(bodyJson);
      if (/第.*?题图/.test(bodyText) && !node.content.body.includes('data-label="image_desc"')) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '出现第XX题图字样, 并且没有图说, 请检查',
        }];
      }
      if (/data-description="[^"]*?答图[^"]*?"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '答图的图说出现在题干中',
        }];
      }
      if (node.content.bracket_count && node.question_type !== 'choice' && subject !== 'chinese') {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '该题是否应该是一个选择题',
        }];
      }
      if (/<\/table><table/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '请检查两个表格是否需要合并',
        }];
      }
      checkQuestionAnswerAndAnalysis(node);
    }
    if (node.node_type === 'chapter') {
      const text = getText(parseHtml(node.content.body));
      if (text.includes('学生用书')) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '\'学生用书\'字样不应该出现在标题内',
        }];
      }
      if (/\s\s/.test(text)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.chapter_content_empty,
          message: '标题中存在多个连续空格',
        }];
      }
      if (/data-label="bracket"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '标题里不能有括号',
        }];
      }
      if (/data-label="blank"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '标题里不能有作答横线',
        }];
      }
    }
    if (node.node_type === 'paragraph') {
      if (siblings[index + 1]?.node_type === 'question') {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.paragraph_content_error,
          message: '检查这是否是一个段落',
        }];
      }
      if (/data-label="blank"/.test(node.content.body)) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '段落里不能有作答横线',
        }];
      }
      if (['班级', '姓名', '分数', '题号'].some((item) => node.content.body.includes(item))) {
        node.errorInfo = [...(node.errorInfo || []), {
          rule: EJsonErrorRule.question_content_empty,
          message: '检查该段落是否需要删除',
        }];
      }
    }
  }
}

function checkQuestionAnswerAndAnalysis(node: IQuestionNode) {
  const errorInfo = node.errorInfo || [];
  // 答案
  node.content.answer.forEach((item) => {
    const text = getText(parseHtml(item));
    const hintContent = text.startsWith('【答案】') && '【答案】' || text.startsWith('答案') && '答案' || '';
    if (hintContent) {
      errorInfo.push({
        rule: EJsonErrorRule.answer_error_content,
        message: `确认答案开头的${hintContent}是否需要删除`,
      });
      node.errorInfo = errorInfo;
    }
  });

  if (node.content.answer.some((item) => item.includes('data-label="bracket"') || item.includes('data-label="blank"'))) {
    errorInfo.push({
      rule: EJsonErrorRule.answer_error_content,
      message: '答案中不能有空',
    });
  }

  // 解析
  const analysis = getText(parseHtml(node.content.analysis));
  const hintContent = analysis.startsWith('【解析】') && '【解析】' || analysis.startsWith('解析') && '解析' || '';
  if (hintContent) {
    errorInfo.push({
      rule: EJsonErrorRule.analysis_error_content,
      message: `确认解析开头的${hintContent}是否需要删除`,
    });
    node.errorInfo = errorInfo;
  }

  if (node.content.analysis.includes('data-label="bracket"') || node.content.analysis.includes('data-label="blank"')) {
    errorInfo.push({
      rule: EJsonErrorRule.answer_error_content,
      message: '解析中不能有空',
    });
  }

  const childQuestion = node.children.filter((item) => item.node_type === 'question') as IBasicQuestionNode[];
  if (childQuestion.length > 1 && childQuestion.some((item) => item.content.analysis) && childQuestion.some((item) => !item.content.analysis)) {
    const noAnalysisQuestions = childQuestion.filter((item) => !item.content.analysis);
    const noAnalysisQuestionSns = noAnalysisQuestions.map((item) => item.content.serial_number);
    const hasAnalysisQuestions = childQuestion.filter((item) => item.content.analysis);
    hasAnalysisQuestions.forEach((item) => {
      if (noAnalysisQuestionSns.some((sn) => getText(parseHtml(item.content.analysis)).includes(sn))) {
        const errorInfo = item.errorInfo || [];
        errorInfo.push({
          rule: EJsonErrorRule.analysis_error_content,
          message: '检查解析内是否包含了其它试题的解析',
        });
        item.errorInfo = errorInfo;
      }
    });
  }
}

function checkJb(node: TJsonNode) {
  const { body } = node.content;
  const errorInfo = node.errorInfo || [];
  if (hasMulJb(body) || node.node_type === 'question' && node.content._sequence.some((k) => (Array.isArray(node.content?.[k]) ? node.content?.[k].some((item) => hasMulJb(item)) : hasMulJb(node.content?.[k])))) {
    errorInfo.push({
      rule: EJsonErrorRule.duplicate_level_jb,
      message: '一级以上的大括号请截图',
    });
  }
  node.errorInfo = errorInfo;
}

function hasMulJb(html = '') {
  const json = parseHtml(html);
  for (const { node } of iterateNode(json)) {
    if (node.type === 'element' && node.dataset.label === 'jb-container') {
      const html = getHtml([node]);
      if (/.*?data-label="jb".*?data-label="jb".*?data-label="jb".*?/.test(html)) {
        return true;
      }
    }
  }
  return false;
}
