import { parse, parseDefaults } from 'himalaya';
import { escape, unescape } from 'he';
import { strapText } from '../helper';
import { iterateNode } from '../treeHelper';

const cheerio = require('cheerio');

export { escape } from 'he';
export const SELF_CLOSE_TAGS: string[] = parseDefaults.voidTags;

function decode(str: string, options: { isAttributeValue?: boolean; strict?: boolean; nbspToSp?: boolean } = {}) {
  const text: string = unescape(str, options);
  const { nbspToSp = true } = options;
  return nbspToSp ? text.replace(/\u00a0/g, ' ') : text;
}

export { decode as unescape };

export interface IHtmlNodeProperties {
  dataset: { [key: string]: string | undefined };
  attrs: { [key: string]: string | undefined };
  cls: { [key: string]: true | undefined };
  style?: { [key: string]: string | undefined };
}

// 纯文本节点
export interface ITextNode {
  type: 'text';
  tagName?: never;
  content: string;
}

// 注释节点
export interface ICommentNode {
  type: 'comment';
  tagName?: never;
  content: string;
}

// 相当于innerHtml，只是为了方便拼接html
export interface ISourceHtmlNode {
  type: 'html';
  tagName?: never;
  content: string;
}

// 元素节点
export interface IElementNode extends IHtmlNodeProperties {
  type: 'element';
  tagName: string;
  children: THtmlNode[];
}

// 节点片段
export interface IFragmentNode {
  type: 'fragment';
  tagName?: never;
  children: THtmlNode[];
}

export type THtmlNode = ITextNode | ICommentNode | ISourceHtmlNode | IElementNode | IFragmentNode;

export function parseHtml(html: string, { formatStyleDef = false } = {}): THtmlNode[] {
  const nodeTree = parse(html);
  formatNodesAttrs(nodeTree, { formatStyleDef });
  return nodeTree;
}
// 将所有节点的 attributes 转换为 attrs+dataset
function formatNodesAttrs(nodes: any[], { formatStyleDef = false }) {
  for (const { node } of iterateNode(nodes)) {
    if (node.type !== 'element') {
      continue;
    }
    const { attrs, dataset, cls, style } = formatAttrs(node, { formatStyleDef });
    delete node.attributes;
    node.attrs = attrs;
    node.dataset = dataset;
    node.cls = cls;
    node.style = style;
  }
  let i = 0;
  nodes.forEach((node) => {
    if (node.type === 'element') {
      i = i + 1;
      node.dataset.line = i;
      node.attrs['data-line'] = i;
    }
  });
}

/*
 * 将 attributes 转换为 attrs+dataset
 * [{key: 'data-label', value: 'test'}, {'key': 'class', value: 'align-right'}]  ==>
 * attrs: {'data-label': 'test', class: 'align-right'},  dataset: {label: 'test'}
 */
function formatAttrs({ attributes }, { formatStyleDef = false }): IHtmlNodeProperties {
  const attrs = {};
  const dataset = {};
  const cls = {};
  const style = {};
  const input = attributes || [];
  input.forEach((attr) => {
    // 标准html <span disabled> 中 disabled 的属性值为 true
    const value = attr.value == null ? 'true' : unescape(attr.value);
    if (attr.key.startsWith('data-')) {
      dataset[attr.key.substring('data-'.length)] = value;
    }
    attrs[attr.key] = value;
  });
  if (attrs['class']) {
    attrs['class'].trim().split(/\s+/).forEach((c) => {
      cls[c] = true;
    });
  }
  if (formatStyleDef && attrs['style']) {
    attrs['style'].split(';').map((s) => s.trim()).filter((s) => s).forEach((s) => {
      const [k, v] = s.split(':').map((s) => s.trim());
      style[k] = v;
    });
  }
  return { attrs, dataset, cls, style };
}

function stringifyAttrs({ attrs }) {
  const result: string[] = [];
  if (attrs) {
    Object.keys(attrs).forEach((key) => {
      let value = attrs[key];
      if (key === 'class') {
        value = value.replace(/\s+/, ' ').trim();
        if (!value) {
          return;
        }
      }
      if (value === 'true') {
        result.push(key);
      } else {
        value = escape(`${value}`);
        result.push(`${key}="${value}"`);
      }
    });
  }
  return result.length ? ` ${result.join(' ')}` : '';
}

type TextOpts = { wrapLatexWith?: string, strip?: boolean };

// 将dom转换为纯文本
export function getText(nodes: THtmlNode[], { wrapLatexWith, strip = false }: TextOpts = {}) {
  const result: string[] = [];
  for (const { node, index, parent } of iterateNode(nodes, { order: 'post' })) {
    if (node.type === 'element' && node.tagName === 'p') {
      if (index !== nodes.length - 1) {
        result.push('\n');
      }
    } else if (parent &&
      parent.type === 'element' &&
      parent.dataset.label === 'latex' &&
      wrapLatexWith != null) {
      if (parent.dataset.value && parent.dataset.value.trim()) {
        result.push(`${wrapLatexWith}${formatLatex(parent.dataset.value)}${wrapLatexWith}`);
      }
    } else if (node.type === 'element' && node.dataset['image-text']) {
      result.push(node.dataset['image-text']);
    } else if (node.type === 'text') {
      let content = strip ? strapText(node.content, false) : node.content;
      content = unescape(content);
      result.push(content);
    }
    // type=comment/html 将被忽略
  }
  return result.join('');
}

type HtmlOpts = {
  strip?: boolean,
  reserveComment?: boolean,
  xmlMode?: boolean,
  wrapLatexWith?: string,
};

// 将dom转换为html
export function getHtml(nodes: THtmlNode[], {
  strip = false,
  reserveComment = true,
  xmlMode = false,
  wrapLatexWith,
}: HtmlOpts = {}): string {
  const result = nodes.map((node) => {
    if (node.type === 'comment') {
      if (!reserveComment) {
        return '';
      }
      return `<!--${strip ? strapText(node.content, false) : node.content}-->`;
    }
    if (node.type === 'text') {
      return strip ? strapText(node.content, false) : node.content;
    }
    if (node.type === 'fragment') {
      return getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    }
    if (node.type === 'html') {
      return node.content;
    }
    if ((node as any).type !== 'element') return ''; // 不清楚为什么有其他类型
    if (SELF_CLOSE_TAGS.includes(node.tagName) || xmlMode && !node.children.length) {
      return `<${node.tagName}${stringifyAttrs(node)}/>`;
    }
    if (node.dataset.label === 'latex' && wrapLatexWith != null) {
      if (!node.dataset.value || !node.dataset.value.trim()) return '';
      return `${wrapLatexWith}${formatLatex(node.dataset.value)}${wrapLatexWith}`;
    }
    const content = getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    return `<${node.tagName}${stringifyAttrs(node)}>${content}</${node.tagName}>`;
  });
  return result.join('');
}

function formatLatex(latex) {
  if (!latex) return '';
  return latex.replace(/</g, '\\lt ').replace(/>/g, '\\gt ');
}

export function addTextForNode(nodes: THtmlNode[], text: string, mode: 'prepend' | 'append' = 'prepend') {
  // html node 头部或者尾部插入 inline 文本
  const textNode: THtmlNode = { type: 'text', content: text };
  if (!nodes.length) { // 如果为空，直接插入文本节点
    nodes.push(textNode);
    return nodes;
  }
  for (const { node, siblings } of iterateNode(nodes, { back: mode === 'append' })) {
    if (node.type === 'element' && node.tagName !== 'p' || ['text', 'html'].includes(node.type)) {
      // 找到第一个(最后一个)block节点，在其头(尾)部添加文本节点
      if (mode === 'prepend') {
        siblings.unshift(textNode);
      } else {
        siblings.push(textNode);
      }
      break;
    }
  }
  return nodes;
}

export function addTextForHtml(html: string, text: string, mode: 'prepend' | 'append' = 'prepend') {
  const nodes = parseHtml(html);
  addTextForNode(nodes, text, mode);
  const result = getHtml(nodes);
  return result;
}

export function getNodeSize({ attrs }: IElementNode, prop: 'width' | 'height') {
  // 获取节点样式中的宽度、高度属性。（如表格宽度、单元格宽度）
  let length = attrs[prop];
  if (!length) {
    if (!attrs.style) return;
    const styles: any = {};
    attrs.style.split(';').map((s) => s.trim()).filter((s) => s).forEach((item) => {
      const [key, value] = item.split(':').map((s) => s.trim());
      if (!key || !value) return;
      styles[key] = value;
    });
    length = styles[prop];
  }
  length = length && length.trim();
  if (!length) return;
  const match = length.match(/(\d*\.?\d*)(px|%)?/);
  if (!match || !match[1]) return;
  const size = Number(match[1]);
  const type = (match[2] || 'px') as 'px' | '%';
  return { type, size };
}

/**
 * 质检空 tr
 * @param {Cheerio} table
 * @returns {boolean}
 */
function hasEmptyTr(table, $) {
  const trs = table.find('tr');
  for (let i = 0; i < trs.length; i++) {
    const tr = $(trs[i]);
    if (tr.find('td').length === 0) {
      return true;
    }
  }
  return false;
}

/**
 * 获取表格列数目（同时判断行）
 * @param {Cheerio} table
 * @returns {array}
 */
function getSetColTable(table, $) {
  const trs = table.find('tr');
  const sum = new Array(trs.length).fill(0);

  trs.each((index, trElement) => {
    const tr = $(trElement);
    const tds = tr.find('td');
    tds.each((_, tdElement) => {
      const td = $(tdElement);
      const rowSpan = parseInt(td.attr('rowspan') || '1');
      const colSpan = parseInt(td.attr('colspan') || '1');

      for (let i = 0; i < rowSpan; i++) {
        if (sum[index + i] === undefined) {
          sum[index + i] = 0;
        }
        sum[index + i] += colSpan;
      }
    });
  });
  return sum;
}

/**
 * 检查是否有错误的列或空行的表格
 * @param {string | Cheerio} domOrStr
 * @returns {boolean}
 */
export function hasErrorColOrTrTable(domOrStr) {
  try {
    let $;
    if (typeof domOrStr === 'string') {
      $ = cheerio.load(domOrStr);
    } else {
      $ = domOrStr;
    }

    const tables = $('table');

    for (let i = 0; i < tables.length; i++) {
      const table = $(tables[i]);

      // 质检 col
      const tableColSum = getSetColTable(table, $);
      if (new Set(tableColSum).size !== 1) {
        return true;
      }

      // 质检空 tr
      if (hasEmptyTr(table, $)) {
        return true;
      }
    }
  } catch (e) {
    throw new Error(e);
  }
  return false;
}
