import { chinese2Number } from './chinese2number';
import { roman2number } from './roman2number';

const ROMAN = 'ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ';
const CIRCLE = '①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳㉑㉒㉓㉔㉕㉖㉗㉘㉙㉚㉛㉜㉝㉞㉟㊱㊲㊳㊴㊵㊶㊷㊸㊹㊺㊻㊼㊽㊾㊿';
const CIRCLE_DARK = '❶❷❸❹❺❻❼❽❾❿';
const BRACKET = '⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃';
const BRACKET_CHINESE = '㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩';
const DOT = '⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗';

const CHAR_CODE_A = 'A'.charCodeAt(0);
const CHAR_CODE_Z = 'Z'.charCodeAt(0);

const DIGITS = [
  {
    digits: (c: string) => {
      const number = Number(c);
      return Number.isNaN(number) ? undefined : number;
    },
    name: 'ascii', // 数字
    re: '\\d+',
  },
  {
    digits: ROMAN.split(''),
    name: 'roman', // 罗马数字
    re: `[${ROMAN}]`,
  },
  {
    digits: roman2number,
    name: 'roman', // 罗马数字英文字符 和罗马数字一样
    re: '[IVX]+',
  },
  {
    digits: (c: string) => {
      const charCode = c.charCodeAt(0);
      return charCode >= CHAR_CODE_A && charCode <= CHAR_CODE_Z ? charCode - CHAR_CODE_A + 1 : undefined;
    },
    name: 'alphabet',
    re: '[A-Z]',
  },
  {
    digits: chinese2Number,
    name: 'chinese', // 中文
    re: '[一二三四五六七八九十百千万]+',
  },
  {
    digits: CIRCLE.split(''),
    name: 'circle', // 圆圈数字
    re: `[${CIRCLE}]`,
  },
  {
    digits: CIRCLE_DARK.split(''),
    name: 'circleDark', // 黑色圆圈数字
    re: `[${CIRCLE_DARK}]`,
  },
  {
    digits: BRACKET.split(''),
    name: 'bracket', // 括号数字
    re: `[${BRACKET}]`,
  },
  {
    digits: BRACKET_CHINESE.split(''),
    name: 'bracketChinese', // 括号中文
    re: `[${BRACKET_CHINESE}]`,
  },
  {
    digits: DOT.split(''),
    name: 'dot', // 数字加点
    re: `[${DOT}]`,
  }
];

const LABEL_TPL = '(例|例题|练习|练习题|示例|典例|案例|典型例题|经典例题|变式)';
const LABEL_TPL_GROUP_COUNT = 1;

const RE_TPLS = [
  {
    tpl: `(${LABEL_TPL})`,
    labelGroup: 1,
  },
  {
    tpl: `【(${LABEL_TPL})】`,
    labelGroup: 1,
  },
  {
    tpl: `(${LABEL_TPL})?({re})、`, // 17、
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `(${LABEL_TPL})?({re})[AB](\\.)?`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `(${LABEL_TPL})?({re})(\\.)?`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `(${LABEL_TPL})?\\(({re})\\)`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `(${LABEL_TPL})?（({re})）`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `【(${LABEL_TPL})?({re})(\\.)?】`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `【(${LABEL_TPL})?\\(({re})\\)】`,
    labelGroup: 1,
    numberGroup: 2,
  },
  {
    tpl: `【(${LABEL_TPL})?（({re})）】`,
    labelGroup: 1,
    numberGroup: 2,
  }
];

interface IParseSerialNumberResultBase {
  type: 'number' | 'label';
  label?: string; // 标签
  tpl: string; // 修饰正则模板
  fullRe: string; // 正则
  lower: boolean; // 是否为小写
  match: (string | undefined)[]; // 匹配的分组
  labelGroupIndex: number; // 标签匹配位置
}

interface IParseSerialNumberResult extends IParseSerialNumberResultBase {
  type: 'number';
  number: number; // 序号数字
  name: string; // 序号类型
  group: string; // 序号
  re: string; // 序号匹配正则
  numberGroupIndex?: number; // 序号匹配位置
}

interface IParseSerialNumberLabelResult extends IParseSerialNumberResultBase {
  type: 'label';
  label: string; // 标签
}

export function parseSerialNumber(text: string) {
  const char = text.replace(/\s+/, ' ').trim();
  const upper = char.toUpperCase();
  for (const { tpl, labelGroup, numberGroup } of RE_TPLS) {
    const labelGroupIndex = labelGroup;
    const lower = upper !== char;
    if (numberGroup == null) {
      const fullRe = tpl;
      const match = upper.match(new RegExp(`^${fullRe}$`));
      if (!match) {
        continue;
      }
      const label = match[labelGroup];
      return {
        label,
        tpl,
        fullRe,
        lower,
        match,
        labelGroupIndex,
        type: 'label',
      } as IParseSerialNumberLabelResult;
    }
    for (const { digits, name, re } of DIGITS) {
      const fullRe = tpl.replace('{re}', re);
      const match = upper.match(new RegExp(`^${fullRe}$`));
      if (!match) {
        continue;
      }
      const label = match[labelGroup];
      let number: number | undefined;
      const numberGroupIndex = LABEL_TPL_GROUP_COUNT + numberGroup;
      const group = match[numberGroupIndex];
      if (typeof digits === 'function') {
        number = digits(group);
      } else {
        const index = digits.indexOf(group);
        if (index > -1) {
          number = index + 1;
        }
      }
      if (number == null) {
        continue;
      }
      return {
        number,
        name,
        group,
        label,
        re,
        tpl,
        fullRe,
        lower,
        match,
        labelGroupIndex,
        numberGroupIndex,
        type: 'number',
      } as IParseSerialNumberResult;
    }

  }
}

export function sameSerialNumberType(a?: IParseSerialNumberResult, b?: IParseSerialNumberResult) {
  return a && b
    && a.name === b.name // 序号类型一致
    && a.tpl === b.tpl // 序号+修饰模板一致
    && a.lower === b.lower // 大小写一致
    // 每一个正则捕获一致（除编号的捕获外）
    && a.match.every((group, i) => i === 0 || i === a.numberGroupIndex || group === b.match[i]);
}
