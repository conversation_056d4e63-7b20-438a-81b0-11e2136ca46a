import * as docx from 'docx';
import { IDocxStyleOptions } from './docxJson';

type SuperMutable<T> = { -readonly [U in keyof T]: SuperMutable<T[U]> };

export interface IStyleDefs {
  paragraph?: docx.IParagraphPropertiesOptions;
  run?: docx.IRunPropertiesOptions;
}

export type TStyleKey =
  'defaults'
  | 'normal'
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'heading4'
  | 'heading5'
  | 'heading6'
  | 'title'
  | 'subTitle';

export type TDocxStyleOption = { [key in TStyleKey]?: IStyleDefs };

function getDefaultStyles(options: TDocxStyleOption = {}) {
  const {
    defaults = {},
    normal = {},
    heading1 = {},
    heading2 = {},
    heading3 = {},
    heading4 = {},
    heading5 = {},
    heading6 = {},
    title = {},
    subTitle = {},
  } = options;

  const styles: SuperMutable<IDocxStyleOptions> = {
    defaultStyles: {
      paragraph: defaults.paragraph,
      run: {
        font: { ascii: 'Times New Roman', eastAsia: 'Songti SC' },
        ...defaults.run,
      },
    },
    paragraphStyles: [
      {
        id: 'Normal',
        name: 'Normal',
        quickFormat: true,
        paragraph: { spacing: { line: 300, lineRule: 'auto' }, ...normal.paragraph },
        run: { size: 21, ...normal.run },
      },
      {
        id: 'Heading1',
        name: 'heading 1',
        uiPriority: 9,
        quickFormat: true,
        paragraph: { outlineLevel: 0, ...heading1.paragraph },
        run: { size: 32, ...heading1.run },
      },
      {
        id: 'Heading2',
        name: 'heading 2',
        uiPriority: 9,
        unhideWhenUsed: true,
        quickFormat: true,
        paragraph: { outlineLevel: 1, ...heading2.paragraph },
        run: { size: 26, ...heading2.run },
      },
      {
        id: 'Heading3',
        name: 'heading 3',
        uiPriority: 9,
        semiHidden: true,
        unhideWhenUsed: true,
        quickFormat: true,
        paragraph: { outlineLevel: 2, ...heading3.paragraph },
        run: { size: 24, ...heading3.run },
      },
      {
        id: 'Heading4',
        name: 'heading 4',
        uiPriority: 9,
        semiHidden: true,
        unhideWhenUsed: true,
        quickFormat: true,
        paragraph: { outlineLevel: 3, ...heading4.paragraph },
        run: heading4.run,
      },
      {
        id: 'Heading5',
        name: 'heading 5',
        uiPriority: 9,
        semiHidden: true,
        unhideWhenUsed: true,
        quickFormat: true,
        paragraph: { outlineLevel: 4, ...heading5.paragraph },
        run: heading5.run,
      },
      {
        id: 'Heading6',
        name: 'heading 6',
        uiPriority: 9,
        semiHidden: true,
        unhideWhenUsed: true,
        quickFormat: true,
        paragraph: { outlineLevel: 5, ...heading6.paragraph },
        run: heading6.run,
      },
      {
        id: 'Title',
        name: 'Title',
        uiPriority: 10,
        quickFormat: true,
        paragraph: title.paragraph,
        run: { size: 56, ...title.run },
      },
      {
        id: 'Subtitle',
        name: 'Subtitle',
        basedOn: 'Normal',
        next: 'Normal',
        link: 'SubtitleChar',
        uiPriority: 11,
        quickFormat: true,
        paragraph: { bullet: { level: 1 }, spacing: { after: 160 }, ...subTitle.paragraph },
        run: { characterSpacing: 15, size: 22, ...subTitle.run },
      }
    ],
    characterStyles: [
      {
        id: 'DefaultParagraphFont',
        name: 'Default Paragraph Font',
        semiHidden: true,
      },
      {
        id: 'SubtitleChar',
        basedOn: 'DefaultParagraphFont',
        link: 'Subtitle',
        run: { size: 22, characterSpacing: 15 },
      }
    ],
  };
  return styles;
}

export default getDefaultStyles;
