import { promisify } from 'util';
import * as request from 'request';
import * as url from 'url';
import { getNodeSize, IElementNode, THtmlNode } from '../htmlHelper';
import { findAllNodes } from '../treeHelper';
import { IImageInfo, replaceInternalUrl } from '../index';

const sharp = require('sharp');

export async function getImage(src: string) {
  const BASE64_MARKER = ';base64,';
  const i = src.indexOf(BASE64_MARKER);
  if (i > -1) {
    const buffer = Buffer.from(src.substring(i + BASE64_MARKER.length), 'base64');
    return buffer;
  }
  const internalUrl = replaceInternalUrl(src);
  let href = '';
  try {
    href = new url.URL(internalUrl).href;
  } catch (e) {
    console.error(src, e);
    return;
  }
  try {
    const resp = await promisify(request.get)(href, { encoding: null });
    const buffer = resp.body as Buffer;
    if (resp.statusCode !== 200 && resp.statusCode !== 201) {
      console.error(src, buffer.toString('utf-8'));
      return;
    }
    return buffer;
  } catch (e) {
    console.error(src, e);
  }
}

export async function getImageSize(buffer: Buffer, width?: number, height?: number): Promise<[number, number]> {
  if (width && height) {
    return [width, height];
  }
  const info = await sharp(buffer).metadata();
  const naturalWidth = info.width;
  const naturalHeight = info.height;
  if (!width && !height) {
    return [naturalWidth!, naturalHeight!];
  }
  let [w, h] = [width, height];
  if (width) {
    h = Math.round(naturalHeight! * width / naturalWidth!);
  } else {
    w = Math.round(naturalWidth! * height! / naturalHeight!);
  }
  return [w!, h!];
}

export async function loadImages(nodes: THtmlNode[]) {
  const images = findAllNodes(nodes, ({ node }) =>
    node.type === 'element' &&
    node.tagName === 'img') as IElementNode[];
  const imageInfos = await Promise.all(images.map(loadImage));
  const map = new Map(images.map((node, index) => [node, imageInfos[index]]));
  return map;
}

export async function loadImage(node: IElementNode) {
  const { attrs } = node;
  const src = attrs.src;
  if (!src) {
    return;
  }
  try {
    const image = await getImage(src);
    if (!image) return;
    let [width, height] = await getImageSize(image, getNodeSizePx(node, 'width'), getNodeSizePx(node, 'height'));
    const maxWidth = 600;
    if (width > maxWidth) {
      [width, height] = [maxWidth, height * maxWidth / width];
    }
    return { image, width, height } as IImageInfo;
  } catch (e) {
    console.error(src);
    console.error(e);
  }
}

function getNodeSizePx(node: IElementNode, prop: 'width' | 'height') {
  const data = getNodeSize(node, prop);
  if (data && data.type === 'px') {
    return data.size;
  }
}
