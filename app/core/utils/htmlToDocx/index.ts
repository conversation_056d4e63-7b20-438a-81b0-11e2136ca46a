import { html2docx } from './html2docx';
import { parseCleanNodes } from './html2khtml';
import { getHtml } from '../htmlHelper';
import getDefaultStyles, { TDocxStyleOption } from './docxStyles';
import { iterateNode } from '../treeHelper';

export { convertHtmlToKatexHtml } from './html2khtml';

export async function convertHtmlToDocx(html: string, styleOptions?: TDocxStyleOption) {
  const nodes = parseCleanNodes(html);
  for (const { node } of iterateNode(nodes)) {
    if (node.type === 'element' && (Number(node.dataset.level) < 0 || Number(node.attrs['data-level']) < 0)) {
      node.children = [{ type: 'text', content: '' }];
    }
  }
  const newHtml = getHtml(nodes);
  const res = await html2docx({ html: newHtml, styles: getDefaultStyles(styleOptions) });
  return res;
}
