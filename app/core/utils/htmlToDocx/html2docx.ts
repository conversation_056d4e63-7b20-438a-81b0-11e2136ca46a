import * as docx from 'docx';
import * as J<PERSON><PERSON><PERSON> from 'jszip';

import { parseHtml, THtmlNode } from '../htmlHelper';
import { loadImages } from './helper';
import { iterateNode } from '../treeHelper';
import { docxJson2Docx, htmlJsonToDocxJson, IDocxStyleOptions } from './docxJson';
import { formatLatexHtml } from '../latexHelper';
import { escapeHtml, removePropIfEmpty } from '../index';
import { addRunPropsForMathOmml, latex2omml } from '../latexConverter';
import getDefaultStyles, { TDocxStyleOption } from './docxStyles';

export interface IHtml2DocxOption {
  html: string;
  externalStyles?: string; // 可以指定样式 styles.xml
  styles?: IDocxStyleOptions;
  styleOptions?: TDocxStyleOption;
  postProcess?: (o: { documentXml: string }) => string; // 可以对最后生成的 word 中的 document.xml 进行处理
}

export async function html2docx(opt: IHtml2DocxOption & { logger?: (msg: string) => void }) {
  let {
    html,
    externalStyles,
    styles,
    styleOptions,
    postProcess,
    logger,
  } = opt;
  styles = styles || styleOptions && getDefaultStyles(styleOptions) || undefined;
  styles = styles ? removePropIfEmpty({
    paragraphStyles: styles.paragraphStyles,
    characterStyles: styles.characterStyles,
    importedStyles: styles.defaultStyles ? [new docx.DocumentDefaults(styles.defaultStyles)] : undefined,
  }) : undefined;
  const doc = new docx.Document(removePropIfEmpty({ externalStyles, styles }));

  let s = Date.now();
  let newHtml = html.replace(
    /<span data-label="blank" data-blank-length="(\d+)"><\/span>/g,
    (_m, count) => {
      const space = '&nbsp;'.repeat(count * 4);
      return `<span data-label="blank" data-blank-length="${count}">${space}</span>`;
    }
  );
  newHtml = formatLatexHtml(newHtml);
  const nodes = parseHtml(newHtml, { formatStyleDef: true });

  for (const item of iterateNode(nodes)) {
    if (item.node.type === 'element') {
      if (item.node.dataset['render-src']) {
        item.siblings[item.index] = {
          type: 'element',
          tagName: 'img',
          attrs: { src: item.node.dataset['render-src'] },
          dataset: {},
          cls: {},
          children: [],
        };
      } else if (item.node.dataset.label === 'four_lines') {
        item.node.children.unshift({
          type: 'element',
          tagName: 'span',
          children: [{ type: 'text', content: '!四线格' }],
          attrs: { style: 'color: #ff0000' },
          style: { color: '#ff0000' },
          dataset: {},
          cls: {},
        });
      }
    }
  }

  for (const item of iterateNode(nodes)) {
    if (item.node.type === 'element' && item.node.dataset.label === 'latex') {
      item.node.children = [];
      item.stopIterateChildren!();
    }
  }

  const latex = getLatex(nodes);

  logger && logger(`[html2docx] parse html cost ${Date.now() - s}ms`);

  s = Date.now();
  const imageMap = await loadImages(nodes);
  logger && logger(`[html2docx] [loadImages] cost ${Date.now() - s}ms`);

  s = Date.now();
  const docxJson = htmlJsonToDocxJson(nodes, imageMap);
  logger && logger(`[html2docx] [htmlJsonToDocxJson] cost ${Date.now() - s}ms`);

  s = Date.now();
  const paras = docxJson2Docx(doc, docxJson);
  doc.addSection({ children: paras });
  logger && logger(`[html2docx] [docxJson2Docx] cost ${Date.now() - s}ms`);

  const xmlFileMapping = (docx.Packer as any).compiler.xmlifyFile(doc);

  let xml: string = xmlFileMapping.Document.data;
  xml = xml.replace(/(&#65279;)|\ufeff/g, '').replace(/(&#8203;)|\u200b/g, ' ');
  const xml1 = xml;
  s = Date.now();
  if (latex.length) {
    const omml = await latex2omml(latex);
    xml = replaceLatexToOmml(xml, omml, latex);
  }
  logger && logger(`[html2docx] [latex2omml] cost ${Date.now() - s}ms`);

  if (postProcess) {
    xml = postProcess({ documentXml: xml });
  }
  const xml2 = xml;
  xmlFileMapping.Document.data = xml;

  s = Date.now();
  const zip = new JSZip();
  Object.keys(xmlFileMapping).forEach((key) => {
    if (!xmlFileMapping[key]) {
      return;
    }

    const obj = xmlFileMapping[key];

    if (Array.isArray(obj)) {
      for (const subFile of obj) {
        zip.file(subFile.path, subFile.data);
      }
    } else {
      zip.file(obj.path, obj.data);
    }
  });

  for (const data of doc.Media.Array) {
    const mediaData = data.stream;
    zip.file(`word/media/${data.fileName}`, mediaData);
  }

  const buffer = await zip.generateAsync({
    type: 'nodebuffer',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    compression: 'DEFLATE',
  });
  logger && logger(`[html2docx] zip cost ${Date.now() - s}ms`);
  return { buffer, html, xml1, xml2 };
}

function getLatex(nodes: THtmlNode[]) {
  const latex: string[] = [];
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type === 'element' && node.dataset.label === 'latex') {
      stopIterateChildren!();
      const l = node.dataset.value!;
      if (l) {
        node.dataset.value = `latex#${latex.length}`;
        latex.push(l);
      }
    }
  }
  return latex;
}

function replaceLatexToOmml(xml: string, ommls: string[], latex: string[]) {
  const newXml = xml.replace(/<w:r>[\s\S]*?<\/w:r>/g, (run) => {
    const match = run.match(/<w:r>([\s\S]*?)<w:t xml:space="preserve">\$\$latex#(\d+)\$\$<\/w:t><\/w:r>/);
    if (!match) return run;
    const id = Number(match[2]);
    let math = ommls[id];
    if (!math) return run.replace(/\$\$latex#\d+\$\$/, () => `$${escapeHtml(latex[id])}$`);
    const runProps = match[1];
    math = addRunPropsForMathOmml(math, runProps);
    return `${math}<w:r w:rsidR="0036738D"><w:t xml:space="preserve"> </w:t></w:r>`;
  });
  return newXml;
}
