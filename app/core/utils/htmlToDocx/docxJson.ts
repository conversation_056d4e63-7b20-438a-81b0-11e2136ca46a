import * as _ from 'lodash';
import * as docx from 'docx';

import { IImageInfo, merge, removeIfEmpty, removePropIfEmpty, unitConvert } from '../index';

import { getNodeSize, getText, IElementNode, THtmlNode, unescape } from '../htmlHelper';
import { findAllNodes, iterateNode } from '../treeHelper';

type Mutable<T> = { -readonly [P in keyof T]: T[P] };

type TDocxParagraphRun = IDocxParagraph | IDocxPageBreak | IDocxTable;

type TDocxTextRun = IDocxImage | IDocxText | IDocxLatex;

export interface IDocxParagraph {
  type: 'paragraph';
  children: TDocxTextRun[];
  style?: docx.IParagraphPropertiesOptions;
  textStyle?: docx.IRunPropertiesOptions;
}

export interface IDocxPageBreak {
  type: 'pageBreak';
}

export interface IDocxTable {
  type: 'table';
  rows: IDocxCell[][];
  style?: Omit<docx.ITableOptions, 'rows'>;
}

export interface IDocxCell {
  type: 'cell';
  children: (IDocxParagraph | IDocxTable)[];
  style?: Omit<docx.ITableCellOptions, 'children'>;
}

export interface IDocxImage extends IImageInfo {
  type: 'image';
  style?: docx.IDrawingOptions;
}

export interface IDocxText {
  type: 'text';
  text: string;
  style?: docx.IRunPropertiesOptions;
}

export interface IDocxLatex {
  type: 'latex';
  latex: string;
  style?: docx.IRunPropertiesOptions;
}

export interface IDocxStyleOptions {
  paragraphStyles?: docx.IParagraphStyleOptions[];
  characterStyles?: docx.ICharacterStyleOptions[];
  defaultStyles?: docx.IDocumentDefaultsOptions;
}

export function htmlJsonToDocxJson(nodes: THtmlNode[], imageMap?: Map<IElementNode, IImageInfo | undefined>) {
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type === 'fragment') {
      continue;
    }
    if (node.type !== 'element' || node.tagName !== 'section') {
      stopIterateChildren!();
      continue;
    }
    if (node.dataset.label === 'hanging' && node.dataset.value) {
      const ps = findAllNodes(node.children, ({ node }) => {
        return node.type === 'element' && (node.tagName === 'p' || node.tagName === 'table' && node.dataset.label === 'layout');
      }) as IElementNode[];
      const indent = Number(node.dataset.value);
      ps.forEach((p, index) => {
        p.dataset.indent_left = `${(Number(p.dataset.indent_left!) || 0) + indent}`;
        if (!index) {
          p.dataset.indent_hanging = `${(Number(p.dataset.indent_hanging!) || 0) + indent}`;
        }
      });
    }
  }

  function handleTable(node: IElementNode, baseTextStyle?: IDocxText['style']) {
    const trs = findAllNodes(node.children, ({ node }) => {
      return node.type === 'element' && node.tagName === 'tr';
    }) as IElementNode[];
    if (!trs.length) {
      return;
    }
    const maxWidth = 9364;
    const tableStyle = getTableStyle(node) as any;
    const rows: IDocxCell[][] = [];
    for (const tr of trs) {
      const row: IDocxCell[] = [];
      for (const td of tr.children) {
        if (td.type !== 'element') {
          continue;
        }
        const cellChildren = td.children.filter((node) => node.type !== 'text' || node.content.trim());
        const cellStyle = getTableCellStyle(td, node) as any;
        if (cellStyle.width && cellStyle.width.type === docx.WidthType.DXA && cellStyle.width.size > maxWidth) {
          cellStyle.width = {
            type: docx.WidthType.PERCENTAGE,
            size: tableStyle.width && tableStyle.width.type === docx.WidthType.DXA
              ? Math.round(cellStyle.width.size / tableStyle.width.size * 5000)
              : 5000,
          };
        }
        row.push({
          type: 'cell',
          style: cellStyle,
          children: handlePara(cellChildren, undefined, baseTextStyle) as (IDocxParagraph | IDocxTable)[],
        });
      }
      if (row.length) {
        rows.push(row);
      }
    }
    if (!rows.length) {
      return;
    }
    if (tableStyle.width && tableStyle.width.type === docx.WidthType.DXA && tableStyle.width.size > maxWidth) {
      tableStyle.width = { type: docx.WidthType.PERCENTAGE, size: 5000 };
    }
    const table: IDocxTable = { type: 'table', rows, style: tableStyle };
    return table;
  }

  function handleLayout(node: IElementNode, baseStyle?: IDocxParagraph['style'], baseTextStyle?: IDocxText['style']) {
    // 使用制表位和制表符布局
    let paras: (IDocxParagraph | IDocxTable)[] = [];
    const cells = findAllNodes(
      node.children,
      ({ node }) => node.type === 'element' && node.tagName === 'td'
    ) as IElementNode[];
    if (!cells.length) {
      return paras;
    }
    const colCount = Number(node.dataset.col!) || 1; // 每行放多少单元格
    const rowCount = Math.ceil(cells.length / colCount); // 需要多少行
    const style = colCount > 1 ? { // 制表位。平均分配一行
      tabStops: _.range(colCount - 1).map((i) => ({
        type: docx.TabStopType.LEFT,
        position: Math.round(docx.TabStopPosition.MAX / colCount * (i + 1)),
      })),
    } : undefined;
    for (let rowIdx = 0; rowIdx < rowCount; rowIdx += 1) {
      const rowCells: IElementNode[] = []; // 本行所有单元格
      for (let colIdx = 0; colIdx < colCount; colIdx += 1) {
        const idx = rowIdx * colCount + colIdx;
        const cell = cells[idx];
        if (!cell) {
          break;
        }
        rowCells.push(cell);
      }
      const nodes = mergeCells(rowCells); // 一行所有单元格使用制表符'\t'拼接起来
      paras = paras.concat(handlePara(
        nodes,
        mergeParaStyle(baseStyle, { indent: getIndent(node) }, style),
        baseTextStyle
      ) as (IDocxParagraph | IDocxTable)[]);
    }
    return paras;
  }

  function handlePara(nodes: THtmlNode[], baseStyle?: IDocxParagraph['style'], baseTextStyle?: IDocxText['style']) {
    if (!nodes || !nodes.length) {
      return [];
    }
    let paras: TDocxParagraphRun[] = [];
    let paraWrapper: IDocxParagraph | undefined; // 用于收集不在在block节点中的所有inline节点内容（散落的inline）
    for (const node of nodes) {
      if (node.type === 'fragment') {
        paras = paras.concat(handlePara(node.children, baseStyle, baseTextStyle));
      } else if (node.type === 'text') {
        // 段落间的内容，空格无意义
        const content = node.content.replace(/\s+/g, ' ').trim();
        if (content) {
          if (!paraWrapper) {
            paraWrapper = { type: 'paragraph', style: baseStyle, textStyle: baseTextStyle, children: [] };
            paras.push(paraWrapper);
          }
          paraWrapper.children.push({ type: 'text', text: unescape(content) });
        }
      } else if (node.type === 'element') {
        let closeParaWrapper = true;
        if (node.tagName === 'section' || node.tagName === 'div') {
          const style = mergeParaStyle(baseStyle, getParaStyle(node));
          const textStyle = mergeTextStyle(baseTextStyle, getTextStyle(node));
          paras = paras.concat(handlePara(node.children, style, textStyle));
        } else if (node.tagName === 'hr') {
          if (node.dataset.label === 'page_separator') {
            // 换页
            paras.push({ type: 'pageBreak' });
          }
        } else if (node.tagName === 'table') {
          const caption = node.children.find((node) => {
            return node.type === 'element' && node.tagName === 'caption';
          }) as IElementNode | undefined;
          if (caption) {
            paras.push({
              type: 'paragraph',
              style: mergeParaStyle(baseStyle, { alignment: docx.AlignmentType.CENTER }),
              textStyle: baseTextStyle,
              children: handleText(caption.children),
            });
          }
          if (node.dataset.label === 'layout') {
            paras = paras.concat(handleLayout(node, baseStyle, baseTextStyle));
          } else {
            const pre = paras[paras.length - 1];
            const table = handleTable(node, baseTextStyle);
            if (table) {
              if (pre && pre.type === 'table') { // 两个表格之间，用一行空白隔开
                paras.push({ type: 'paragraph', style: baseStyle, textStyle: baseTextStyle, children: [] });
              }
              paras.push(table);
            }
          }
        } else if (node.tagName === 'p' || /^h(\d+)$/.test(node.tagName)) {
          const style = mergeParaStyle(baseStyle, getParaStyle(node));
          const textStyle = mergeTextStyle(baseTextStyle, getTextStyle(node));
          paras.push({ type: 'paragraph', style, textStyle, children: handleText(node.children) });
        } else {
          closeParaWrapper = false;
          if (!paraWrapper) {
            paraWrapper = { type: 'paragraph', style: baseStyle, textStyle: baseTextStyle, children: [] };
            paras.push(paraWrapper);
          }
          paraWrapper.children = paraWrapper.children.concat(handleText([node]));
        }
        if (closeParaWrapper) {
          // 如果出现新的block节点，则之前的散落的inline节点已经处理完了。当出现新的散落inline时，可以创建新的 paraWrapper
          paraWrapper = undefined;
        }
      }
    }
    paras.forEach((p) => {
      removePropIfEmpty(p, 'style', 'textStyle');
      if (p.type === 'paragraph') {
        p.children.forEach((t) => {
          removePropIfEmpty(t, 'style');
        });
        p.children = mergeText(p.children);
      }
    });
    return paras;
  }

  function handleText(nodes: THtmlNode[], parentStyles?: IDocxText['style']) {
    if (!nodes) return [];
    let textRuns: (IDocxText | IDocxImage | IDocxLatex)[] = [];
    for (const node of nodes) {
      if (node.type === 'text') {
        if (node.content) {
          // 需要保留 `\t`
          const text = unescape(node.content.replace(/ +/g, ' '));
          textRuns.push({ type: 'text', text, style: parentStyles });
        }
        continue;
      } else if (node.type === 'fragment') {
        textRuns = textRuns.concat(handleText(node.children, parentStyles));
        continue;
      } else if (node.type !== 'element') {
        continue;
      }
      if (node.tagName === 'img') {
        const imageInfo = imageMap && imageMap.get(node);
        if (!imageInfo) {
          continue;
        }
        let { image, width, height } = imageInfo;
        const maxWidth = 600;
        if (width > maxWidth) {
          [width, height] = [maxWidth, height * maxWidth / width];
        }
        const style = getImageStyle(node);
        textRuns.push({ type: 'image', image, width, height, style });
        continue;
      }
      const style = mergeTextStyle(parentStyles, getTextStyle(node));
      if (node.dataset.label === 'latex') {
        if (node.dataset.value) {
          textRuns.push({ type: 'latex', latex: node.dataset.value, style });
        }
        continue;
      }
      const missBracket = node.dataset.label === 'bracket' && !getText(node.children).trim().match(/^(\([\s\S]*\))|(（[\s\S]*）)$/);
      if (missBracket) {
        textRuns.push({ type: 'text', text: '( ', style });
      }
      textRuns = textRuns.concat(handleText(node.children, style));
      if (missBracket) {
        textRuns.push({ type: 'text', text: ' )', style });
      }
    }
    return textRuns;
  }

  const paras = handlePara(nodes);
  return paras;
}

export function docxJson2Docx(doc: docx.Document, docxJson: TDocxParagraphRun[]) {
  function handlePara(para: TDocxParagraphRun): docx.Paragraph | docx.Table {
    if (para.type === 'pageBreak') {
      return new docx.Paragraph({ children: [new docx.PageBreak()] });
    }
    if (para.type === 'paragraph') {
      const textRuns = para.children.map((run) => {
        if (run.type === 'text') {
          return new docx.TextRun({ ...mergeTextStyle(para.textStyle, run.style), text: run.text });
        }
        if (run.type === 'latex') {
          return new docx.TextRun({ ...mergeTextStyle(para.textStyle, run.style), text: `$$${run.latex}$$` });
        }
        // image
        return docx.Media.addImage(doc, run.image, run.width, run.height, run.style);
      });
      return new docx.Paragraph({ ...para.style, children: textRuns });
    }
    // table
    const rows = para.rows.map((cells) => {
      const children = cells.map((cell) => new docx.TableCell({
        ...cell.style,
        children: cell.children.map((item) => handlePara(item)),
      }));
      return new docx.TableRow({ children });
    });
    return new docx.Table({ ...para.style, rows });
  }

  const paras = docxJson.map((item) => handlePara(item));
  return paras;
}

function getParaStyle(node: IElementNode) {
  const style: IDocxParagraph['style'] = {
    alignment: getAlignment(node),
    indent: getIndent(node),
    heading: getHeading(node),
    spacing: getSpacing(node),
  };
  return style;
}

function mergeParaStyle(...args: IDocxParagraph['style'][]) {
  const styles = removeIfEmpty(args);
  if (!styles) return;
  return merge(...styles);
}

const ALIGNMENTS: { [align: string]: docx.AlignmentType } = {
  center: docx.AlignmentType.CENTER,
  left: docx.AlignmentType.LEFT,
  right: docx.AlignmentType.RIGHT,
  justify: docx.AlignmentType.JUSTIFIED,
  both: docx.AlignmentType.JUSTIFIED,
};

function getAlignment({ attrs, style }: IElementNode) {
  const match = attrs.class && attrs.class.match(/align-(center|left|right|justify)/);
  return match && ALIGNMENTS[match[1]] || style && style['text-align'] && ALIGNMENTS[style['text-align']] || undefined;
}

function getHeading({ dataset, tagName }: IElementNode) {
  if (dataset.label === 'header' && dataset.level) {
    const level = `HEADING_${dataset.level}` as keyof typeof docx.HeadingLevel;
    return docx.HeadingLevel[level];
  }
  const match = tagName.match(/^h(\d+)$/i);
  if (match) {
    const level = `HEADING_${match[1]}` as keyof typeof docx.HeadingLevel;
    return docx.HeadingLevel[level];
  }
}

function getSpacing({ style }: IElementNode) {
  const match = style && style['line-height'] && style['line-height'].match(/^((\d+\.?\d*)|(\d*\.?\d+))(em)?$/);
  const height = match && Number(match[1]) || undefined;
  if (!height) return;
  return { line: Math.round(height * 240), lineRule: 'auto' };
}

function getIndent({ attrs, dataset }: IElementNode) {
  const match = attrs.class && attrs.class.match(/indent-(\d)/);
  const indent = {
    firstLine: match ? 240 * (Number(match[1])) : undefined,
    hanging: Number(dataset.indent_hanging!) * 120 || undefined,
    left: Number(dataset.indent_left!) * 120 || undefined,
  };
  return indent;
}

// 自定义样式
const LABEL_STYLES: { [label: string]: IDocxText['style'] } = {
  blank: { underline: { type: docx.UnderlineType.SINGLE } },
  emphasis_double: { underline: { type: docx.UnderlineType.DOUBLE } },
  emphasis_wave: { underline: { type: docx.UnderlineType.WAVE } },
  emphasis_dot: { emphasisMark: { type: docx.EmphasisMarkType.DOT } },
};

// 默认标签样式
const TAG_STYLES: { [tag: string]: IDocxText['style'] } = {
  b: { bold: true },
  i: { italics: true },
  u: { underline: { type: docx.UnderlineType.SINGLE } },
  s: { strike: true },
  sub: { subScript: true },
  sup: { superScript: true },
};

function getTextStyle(node: IElementNode) {
  const { tagName, dataset } = node;
  const labelStyle = LABEL_STYLES[dataset.label!];
  const tagStyle = TAG_STYLES[tagName];
  const style: IDocxText['style'] = {
    ...labelStyle,
    ...tagStyle,
    font: getFont(node),
    color: getColor(node),
    size: getFontSize(node),
  };
  return style;
}

function mergeTextStyle(...args: IDocxText['style'][]) {
  const styles = removeIfEmpty(args);
  if (!styles) return;
  return merge(...styles);
}

function getImageStyle(node: IElementNode) {
  const style: Mutable<IDocxImage['style']> = { floating: getFloating(node) };
  return style;
}

function getFont({ attrs, style }: IElementNode): docx.IRunPropertiesOptions['font'] {
  // 获取节点样式中的字体
  if (attrs['font-east-asia'] || attrs['font-ascii'] || attrs['font-h-ansi']) {
    return { eastAsia: attrs['font-east-asia'], ascii: attrs['font-ascii'], hAnsi: attrs['font-h-ansi'] };
  }
  const font = style && style['font-family'];
  if (font) {
    const match = font.match(/(\w+)|("(.*?)")/);
    const name = match && (match[1] || match[3]);
    return name ? { name } : undefined;
  }
}

function getColor({ style }: IElementNode) {
  const color = style && style.color && style.color.replace(/\s+/g, '');
  if (!color) return;
  const match = color.match(/rgba?\((\d+),(\d+),(\d+)(,(\d+))?\)/);
  if (!match) {
    return color.replace(/^#/, '');
  }
  const [, r, g, b] = match;
  return [r, g, b].map((i) => (Number(i)).toString(16).padStart(2, '0')).join('');
}

function getFontSize({ style }: IElementNode) {
  const size = style && style['font-size'];
  if (!size) return;
  const value = unitConvert(size, undefined, 'pt');
  if (typeof value === 'number') return value * 2;
}

function getWidth(node: IElementNode) {
// 获取节点样式中的宽度属性。（如表格宽度、单元格宽度）
  const width = getNodeSize(node, 'width');
  if (!width) {
    return;
  }
  if (width.type === 'px') {
    return { type: docx.WidthType.DXA, size: Math.round(20 * unitConvert(width.size, 'px', 'pt')) };
  }
  if (width.type === '%') {
    return { type: docx.WidthType.PERCENTAGE, size: Math.round(50 * width.size) };
  }
}

function getFloating({ attrs }: IElementNode) {
  const match = attrs.class && attrs.class.match(/align-(left|right)/);
  if (!match) return;
  return {
    horizontalPosition: {
      relative: docx.HorizontalPositionRelativeFrom.MARGIN,
      align: match[1] === 'left' ? docx.HorizontalPositionAlign.LEFT : docx.HorizontalPositionAlign.RIGHT,
    },
    verticalPosition: {
      relative: docx.VerticalPositionRelativeFrom.LINE,
      offset: 0,
    },
    wrap: {
      type: docx.TextWrappingType.SQUARE,
      side: docx.TextWrappingSide.BOTH_SIDES,
    },
  };
}

function getBorder(border?: string) {
  const size = border ? Number(border) : 1;
  return {
    style: size ? docx.BorderStyle.SINGLE : docx.BorderStyle.NIL,
    size,
    color: '#666',
  };
}

function getBorders(border?: string) {
  const b = getBorder(border);
  return { left: b, top: b, right: b, bottom: b };
}

function getTableStyle(table: IElementNode) {
  return {
    width: getWidth(table) || { type: docx.WidthType.PERCENTAGE, size: 50 * 100 },
    borders: getBorders(table.attrs.border),
  } as Mutable<IDocxTable['style']>;
}

function getTableCellStyle(td: IElementNode, table: IElementNode) {
  const colspan = td.attrs.colspan && Number(td.attrs.colspan) || undefined;
  const rowspan = td.attrs.rowspan && Number(td.attrs.rowspan) || undefined;
  return {
    width: getWidth(td),
    borders: getBorders(td.attrs.border || table.attrs.border),
    columnSpan: !colspan || colspan <= 1 ? undefined : colspan,
    rowSpan: !rowspan || rowspan <= 1 ? undefined : rowspan,
  } as Mutable<IDocxCell['style']>;
}

function mergeCells(cells: IElementNode[]) {
// 一行所有单元格使用制表符'\t'拼接起来
  if (cells.length === 1) {
    return cells[0].children;
  }
  const node: IElementNode = { type: 'element', tagName: 'p', children: [], attrs: {}, dataset: {}, cls: {} };
  cells.forEach((cell, index) => {
    const children = findAllNodes(cell.children, ({ node }) => {
      return node.type !== 'fragment' && (node.type !== 'element' || node.tagName !== 'section');
    }).filter((node) => node.type !== 'text' || node.content.trim());
    const firstNode = children[0];
    const isOnePara = children.length === 1 && firstNode.type === 'element' && firstNode.tagName === 'p';
    let nodes: THtmlNode[];
    if (isOnePara) { // 如果单元格内只有一个p，则将p下所有内容添加到行下
      const pNode = firstNode as IElementNode;
      Object.assign(node.attrs, pNode.attrs);
      Object.assign(node.dataset, pNode.dataset);
      Object.assign(node.cls, pNode.cls);
      nodes = pNode.children;
    } else {
      // 否则，认为单元格内没有p，是由一系列inline元素组成的。
      nodes = children;
    }
    if (index) {
      // 使用 `\t` 分隔
      node.children = [...node.children, { type: 'text', content: '\t' }, ...nodes];
    } else {
      node.children = nodes;
    }
  });
  return [node];
}

function mergeText(textRuns: (IDocxText | IDocxImage | IDocxLatex)[]) {
// 合并相同样式的文本
  const res: (IDocxText | IDocxImage | IDocxLatex)[] = [];
  let pre: IDocxText | undefined;
  textRuns.forEach((textRun) => {
    if (!pre || textRun.type !== 'text' || !_.isEqual(pre.style, textRun.style)) {
      res.push(textRun);
      pre = textRun.type === 'text' ? textRun : undefined;
    } else {
      // 合并相同样式的文本
      pre.text += textRun.text;
    }
  });
  return res;
}
