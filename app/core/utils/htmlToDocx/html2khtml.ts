import { addTextForNode, getHtml, getText, IElementNode, parseHtml, THtmlNode, unescape } from '../htmlHelper';
import { findAllNodes, findNode, iterateNode } from '../treeHelper';
import { latex2mathml, replaceLatex } from '../latexConverter';
import { formatLatexHtml } from '../helper';

export async function convertHtmlToKatexHtml(html: string, replaceEquation = false): Promise<string> {
  const htmlStr = formatLatexHtml(html);
  const nodes = parseCleanNodes(htmlStr);
  await convertLatex(nodes, /data-label="latex"/.test(htmlStr), replaceEquation);
  const result = getHtml(nodes);
  return result;
}

export function parseCleanNodes(html: string) {
  let nodes = parseHtml(html.replace(/■/g, '; '));
  nodes = filterNodes(nodes);
  addAnswerLabel(nodes);
  handleBlockNode(nodes);
  handleInlineNode(nodes);
  return nodes;
}

async function convertLatex(nodes: THtmlNode[], useDataset: boolean, replaceEquation = false) {
  const latexNodes = findAllNodes(nodes, ({ node }) => {
    return node.type === 'element' &&
      (useDataset ? node.dataset.label === 'latex' : Boolean(node.attrs.class) && /\blatex\b/.test(node.attrs.class || ''));
  }) as IElementNode[];
  if (!latexNodes.length) return;
  let latexs = latexNodes.map((node) => node.dataset.value || getText(node.children).replace(/\$/g, ''));
  latexs = replaceEquation ? latexs.map(replaceLatex) : latexs;
  const mathmls = await latex2mathml(latexs);
  latexNodes.forEach((node, index) => {
    const mathml = mathmls[index];
    if (!mathml) { // 失败的公式直接显示latex
      const latex = latexs[index];
      Object.assign(node, { type: 'text', content: latex ? `$$${latex}$$` : '' });
    } else {
      Object.assign(node, { type: 'html', content: mathml });
    }
  });
}

function filterNodes(nodes: THtmlNode[]) {
  const SEPARATORS = ['material_start_separator', 'material_end_separator', 'answer_separator'];
  const result = nodes.filter((node) => {
    if (node.type !== 'element') {
      return false;
    }
    if (SEPARATORS.includes(node.dataset.label!)) {
      // 移除分割线
      return false;
    }
    if (node.tagName !== 'p') {
      return true;
    }
    // 去掉空行（保留有内容的）
    return findNode(node.children, ({ node }) => {
      return node.type === 'element' && (Boolean(node.dataset.label) || node.tagName === 'img') ||
        node.type === 'text' && Boolean(unescape(node.content).trim()) ||
        node.type === 'html';
    });
  });
  return result;
}

function addAnswerLabel(nodes: THtmlNode[]) {
  // 添加答案解析字样
  const LABELS = { answer: '答案', explanation: '解析' };
  nodes.forEach((node, index) => {
    if (node.type !== 'element' || !['answer', 'explanation'].includes(node.dataset.label!)) {
      return;
    }
    const pre = nodes[index - 1];
    if (pre && pre.type === 'element' && node.dataset.label === pre.dataset.label) {
      return;
    }
    const label = `${LABELS[node.dataset.label!]}：`;
    addTextForNode(node.children, label);
  });
}

function handleBlockNode(nodes) {
  nodes.forEach((node) => {
    if (node.type !== 'element') {
      return;
    }
    if (node.dataset.label === 'header') {
      // 转换目录
      node.tagName = `h${node.dataset.level}`;
    }
  });
}

function handleInlineNode(nodes: THtmlNode[]) {
  for (const { node } of iterateNode(nodes)) {
    if (node.type !== 'element') {
      continue;
    }
    if (node.dataset.label === 'blank') {
      // 转换填空横线
      Object.assign(node, {
        children: [
          { type: 'text', content: '&nbsp;' },
          { ...node, tagName: 'u' },
          { type: 'text', content: '&nbsp;' }
        ],
      });
      delete node.attrs['data-label'];
      delete node.dataset.label;
    } else if (node.dataset.label === 'bracket') {
      // 处理括号
      node.children.unshift({ type: 'text', content: '(&nbsp;' });
      node.children.push({ type: 'text', content: '&nbsp;)' });
      delete node.attrs['data-label'];
      delete node.dataset.label;
    }
  }
}
