import { getText, parseHtml, SELF_CLOSE_TAGS } from './htmlHelper';
import { findAllNodes, iterateNode } from './treeHelper';
import { strapText } from './helper';
import { TJsonNode } from './htmlToJsonV4';

function defaultDict(defaultInit) {
  return new Proxy({}, {
    get(target, name) {
      if (name === 'toJSON') {
        return target[name];
      }
      if (!(name in target)) {
        target[name] = typeof defaultInit === 'function' ? defaultInit() : defaultInit;
      }
      return target[name];
    },
  });
}

function checkTreeNodeType(node, parent) {
  if (!parent) { // 树根
    return 'root';
  }
  if (!node.children.length) { // 树叶（材料题的大题也有可能是树叶）
    return 'only_leaf';
  }
  return 'fork'; // 树枝
}

/**
 * 工具函数, 统计字符串中匹配正则的数量
 * @param str
 * @param re
 */
function matchCount(str, re) {
  const match = str.match(re);
  return match ? match.length : 0;
}

// 统计文本中包含：字符数、英文字符数、中文字符数、英文单词数、英文符号数、中文符号数
function getTextStat(text: string) {
  const latexReg = /\$\$.*?\$\$/g;
  let cleanText = text;
  if (latexReg.test(text)) {
    cleanText = text.replace(/\$\$(.*?)\$\$/g, '$1');
  }
  const char = matchCount(cleanText, /\S/ug);
  const en_char = matchCount(cleanText, /[a-z\d]/ig);
  const cn_char = matchCount(cleanText, /[\u4E00-\u9FFF\u3400-\u4DFF\uF900-\uFAFF]/ig);
  const en_word = matchCount(cleanText, /\b[a-z'\-]+\b/ig);
  const num_char = matchCount(cleanText, /\d/ig);
  let en_punct_char = 0;
  let cn_punct_char = 0;
  if (text.match(latexReg)?.length) {
    const _cleanText = text.replace(latexReg, '');
    en_punct_char = matchCount(_cleanText, /[`~!@#$%^&*()\-=_+[\]{};':",./<>?\\|]/g);
    cn_punct_char = matchCount(_cleanText, /[，。、《》？；：‘’“”【】「」！￥…（）—]/g);
  } else {
    en_punct_char = matchCount(cleanText, /[`~!@#$%^&*()\-=_+[\]{};':",./<>?\\|]/g);
    cn_punct_char = matchCount(cleanText, /[，。、《》？；：‘’“”【】「」！￥…（）—]/g);
  }

  const punct_char = en_punct_char + cn_punct_char;
  return { char, punct_char, en_char, cn_char, en_word, en_punct_char, cn_punct_char, num_char };
}

function addTextForNode(nodes: any, text, mode = 'prepend') {
  // html node 头部或者尾部插入 inline 文本
  const textNode = { type: 'text', content: text };
  if (!nodes.length) { // 如果为空，直接插入文本节点
    nodes.push(textNode);
    return nodes;
  }
  for (const { node, siblings } of iterateNode(nodes, { back: mode === 'append' })) {
    if ((node as any).type === 'element' && (node as any).tagName !== 'p' || ['text', 'html'].includes((node as any).type)) {
      // 找到第一个(最后一个)block节点，在其头(尾)部添加文本节点
      if (mode === 'prepend') {
        siblings.unshift(textNode);
      } else {
        siblings.push(textNode);
      }
      break;
    }
  }
  return nodes;
}

function isHtmlStr(text) { // 字符串内容是否为富文本
  return /<\/\w+>|<\w+.*?\/>|&[\w\d]+;/.test(`${text}`);
}

function formatLatexHtml(html: string) {
  const htmlStr = !isHtmlStr(html) && /\$\$/.test(html) ? html : html;
  return htmlStr.replace(/\$\$([\s\S]*?)\$\$/g, (_match, latex) => {
    return `<span data-label="latex" data-value="${latex.replace(/"/g, '&quot;')}">${_match}</span>`;
  });
}

function formatLatex(latex) {
  if (!latex) return '';
  return latex.replace(/</g, '\\lt ').replace(/>/g, '\\gt ');
}

function stringifyAttrs({ attrs }) {
  const result: any[] = [];
  if (attrs) {
    Object.keys(attrs).forEach((key) => {
      let value = attrs[key];
      if (key === 'class') {
        value = value.replace(/\s+/, ' ').trim();
        if (!value) {
          return;
        }
      }
      if (value === 'true') {
        result.push(key);
      } else {
        value = `${value}`;
        result.push(`${key}="${value}"`);
      }
    });
  }
  return result.length ? ` ${result.join(' ')}` : '';
}

// 将dom转换为html
function getHtml(nodes: any, {
  strip = false,
  reserveComment = true,
  xmlMode = false,
  wrapLatexWith = '',
}) {
  const result = nodes.map((node) => {
    if (node.type === 'comment') {
      if (!reserveComment) {
        return '';
      }
      return `<!--${strip ? strapText(node.content, false) : node.content}-->`;
    }
    if (node.type === 'text') {
      return strip ? strapText(node.content, false) : node.content;
    }
    if (node.type === 'fragment') {
      return getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    }
    if (node.type === 'html') {
      return node.content;
    }
    if ((node).type !== 'element') return ''; // 不清楚为什么有其他类型
    if (SELF_CLOSE_TAGS.includes(node.tagName) || xmlMode && !node.children.length) {
      return `<${node.tagName}${stringifyAttrs(node)}/>`;
    }
    if (node.dataset.label === 'latex' && wrapLatexWith != null) {
      if (!node.dataset.value || !node.dataset.value.trim()) return '';
      return `${wrapLatexWith}${formatLatex(node.dataset.value)}${wrapLatexWith}`;
    }
    const content = getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    return `<${node.tagName}${stringifyAttrs(node)}>${content}</${node.tagName}>`;
  });
  return result.join('');
}

function isBlockNode(node: any) {
  // 是否为block节点
  return node.type === 'element' && (node.tagName === 'p' || node.tagName === 'table' || node.tagName === 'hr');
}

function getBlockHtml(nodes: any) {
  // 获取节点的html字符串。并且保证其为block
  if (nodes.some(isBlockNode)) {
    // 如果部分节点为block，需要将散的inline用p包裹，保证结果为多个block
    const blockNodes: any[] = [];
    let p;
    nodes.forEach((node: any) => {
      if (isBlockNode(node)) {
        // block节点
        blockNodes.push(node);
        p = undefined;
      } else {
        if (!p) {
          p = { type: 'element', tagName: 'p', children: [], dataset: {}, attrs: {}, cls: {} };
          blockNodes.push(p);
        }
        p.children.push(node);
      }
    });
    return getHtml(blockNodes, {});
  }
  // 如果节点不包含任何block，直接使用p包裹
  const html = getHtml(nodes, {});
  return `<p>${html}</p>`;
}

function getChoicesHtml(choices) {
  return choices.map((choice) => {
    const { option, letter } = choice;
    const nodes = parseHtml(option);
    addTextForNode(nodes, `${letter}.`, 'prepend');
    return getHtml(nodes, {});
  }).join('');
}

function getBodyHtml(question: any) {
  const htmls: any = [];
  for (const { node } of iterateNode([question])) {
    const bodyNodes = parseHtml(node.content.body);
    if (node.node_type !== 'question') continue;
    if (node === question) {
      if (!bodyNodes.length) {
        htmls.push(`<p>${node.content.serial_number? node.content.serial_number + '.' : ''}</p>`);
      } else {
        addTextForNode(bodyNodes, `${node.content.serial_number? node.content.serial_number + '.' : ''}`, 'prepend');
      }
      if ((node as any).content.source) {
        addTextForNode(bodyNodes, `${node.content.source}`, 'prepend');
      }
      if (node.question_type === 'material' && !node.content.body) {
        continue;
      }
    } else if (node.node_type === 'question') {
      const serialNumberText = `${node.content.serial_number? node.content.serial_number + '.' : ''}`;
      addTextForNode(bodyNodes, serialNumberText, 'prepend');
    }
    htmls.push(getHtml(bodyNodes, {}));
    if (node.content.choices) {
      htmls.push(getChoicesHtml(node.content.choices));
    }
  }
  return formatLatexHtml(htmls.join(''));
}

function getAnswerHtml(question: any) {
  // if (!question?.content?.answer || question?.content?.answer?.length) return ('');
  // 拼接试题答案html
  const htmls: any = [];
  let serialNumber = '';
  const answersNodes: any = [];
  for (const { node } of iterateNode([question])) {
    if (node.node_type !== 'question') {
      continue;
    }
    serialNumber = `${node.content.serial_number? node.content.serial_number + '.' : node.content.serial_number}`;
    // if (node.children.length) {
    //   continue;
    // }
    let questionAnswerNodes = [];
    node.content.answer.forEach((answer) => {
      let nodes;
      if (isHtmlStr(answer)) {
        nodes = parseHtml(answer);
      } else {
        nodes = [{ type: 'text', content: answer }];
      }
      questionAnswerNodes = questionAnswerNodes.concat(nodes);
    });
    if (serialNumber && node?.content?.answer.length) {
      addTextForNode(questionAnswerNodes, serialNumber, 'prepend');
      serialNumber = '';
    }
    answersNodes.push(questionAnswerNodes);
  }
  if (answersNodes.every((nodes) => !nodes.some(isBlockNode))) {
    // 如果答案都是inline，多个答案之间用4个空格分隔
    answersNodes.forEach((nodes, index) => {
      if (index !== answersNodes.length - 1) {
        addTextForNode(nodes, ' ', 'append');
      }
    });
    const answerNodes = answersNodes.reduce((a, b) => a.concat(b), []);
    addTextForNode(answerNodes, '', 'prepend');
    htmls.push(getBlockHtml(answerNodes));
  } else {
    // 如果含 block 答案，所有答案用 block
    answersNodes.forEach((nodes, index) => {
      if (index === 0) {
        addTextForNode(nodes, '', 'prepend');
      }
      htmls.push(getBlockHtml(nodes));
    });
  }
  return formatLatexHtml(htmls.join(''));
}

function getAnalysisHtml(question) {
  // 拼接试题解析html
  const htmls: any = [];
  let serialNumber = '';
  let analysisNodes: any = [];
  let materialAnalysisNodes: any = [];
  let hasAnalysis = false;
  let hasMaterialAnalysis = false;
  if (question.content.level === 0) {
    hasMaterialAnalysis = Boolean(question.content.analysis);
    if (hasMaterialAnalysis) {
      materialAnalysisNodes = parseHtml(question.content.analysis);
    }
  }
  for (const { node } of iterateNode([question])) {
    if (node.node_type !== 'question') {
      continue;
    }
    serialNumber = `${node.content.serial_number? node.content.serial_number + '.' : node.content.serial_number}`;
    if (node !== question) {
    }
    // if (node.children.length) {
    //   continue;
    // }
    hasAnalysis = hasAnalysis || Boolean(node.content.analysis);
    const questionAnalysisNodes = parseHtml(node.content.analysis || '');
    if (serialNumber && node?.content?.analysis) {
      addTextForNode(questionAnalysisNodes, serialNumber, 'prepend');
      serialNumber = '';
    }
    analysisNodes = analysisNodes.concat(questionAnalysisNodes);
  }
  let nodes = materialAnalysisNodes;
  if (hasAnalysis) {
    nodes = nodes.concat(analysisNodes);
  }
  if (hasAnalysis || hasMaterialAnalysis) {
    htmls.push(getBlockHtml(nodes));
  } else {
    htmls.push('');
  }

  return formatLatexHtml(htmls.join(''));
}

function getExtraHtml(question: any) {
  const htmls: any = [];
  for (const { node } of iterateNode([question])) {
    if (node.node_type !== 'question') {
      continue;
    }
    if (node !== question) {

    }
    // 寻找所有 key 包含 extra
    let extraContent: any = [];
    Object.keys(node.content).forEach((v) => {
      if (v.includes('extra') && node.content[v]?.body) {
        extraContent.push(node.content[v]?.body);
      }
    });
    if (extraContent.length) {
      extraContent = `<p>${node.content.serial_number ? node.content.serial_number + '.' : ''}${extraContent.join('')}</p>`;
    }
    htmls.push(extraContent);
  }
  return formatLatexHtml(htmls.join(''));
}

function json2Html(json: any) {
  const questions = findAllNodes(json, ({ node }) => (node as any).node_type === 'question');
  const questionHtml = questions.map((question) => {
    return [getBodyHtml(question), getAnswerHtml(question), getAnalysisHtml(question), getExtraHtml(question)].join('');
  }).join('');
  const paragraphs: any = [];
  const chapters: any = [];
  for (const item of iterateNode(json, {})) {
    if ((item as any).node.node_type === 'chapter') {
      chapters.push((item as any).node?.content?.body);
    } else if ((item as any).node.node_type === 'paragraph') {
      paragraphs.push((item as any).node?.content?.body);
    }
  }
  const paragraphHtml = paragraphs.join('');
  const chapterHtml = chapters.join('');
  return [formatLatexHtml(chapterHtml), formatLatexHtml(paragraphHtml), questionHtml].join('');
}

// html 统计：各类型标签数量、字符数、英文字符数、中文字符数、英文单词数、英文符号数、中文符号数
export function statHtml(json: any) {
  const dict = defaultDict(() => defaultDict(0));
  const addStat = (label, textStat) => {
    const prop = label.replace(/^[^a-z]+/i, '').replace(/-/g, '_').replace(/[^\w\d_]/g, '');
    if (!prop) return;
    dict[prop]['count'] += 1;
    Object.keys(textStat).forEach((key) => {
      dict[prop][key] += textStat[key];
    });
  };
  // 获取 html
  const html = json2Html(json);
  const nodes = parseHtml(html);
  // 统计时，公式中的 \text \mathrm 不计入字符数
  for (const { node, stopIterateChildren } of iterateNode(nodes)) {
    if (node.type === 'element' && node.dataset.label === 'latex') {
      stopIterateChildren!();
      node.dataset.value = node.dataset.value!.replace(/\\(text|mathrm)/g, '');
    }
  }
  for (const { node } of iterateNode(nodes)) {
    if (node.type !== 'element') { // 只统计节点
      continue;
    }
    let textStat;
    if (!SELF_CLOSE_TAGS.includes(node.tagName)) {
      const text = strapText(getText([node], { wrapLatexWith: '$$' }));
      textStat = getTextStat(text);
    } else {
      textStat = {};
    }
    addStat('all', textStat);
    addStat(node.tagName, textStat); // 统计标签
    if (node.dataset.label) { // 统计自定义标签
      addStat(node.dataset.label, textStat);
      if (node.dataset.level) { // 如标题和题号，记录各种level的统计
        addStat(`${node.dataset.label}_${node.dataset.level}`, textStat);
      }
    }
    if (node.attrs.class) {
      // 统计class
      node.attrs.class.trim().split(/\s+/g).forEach((clazz) => {
        if (clazz.startsWith('indent')) { // 汇总缩进统计
          addStat('class-indent', textStat);
        } else if (clazz.startsWith('align')) { // 汇总对齐统计
          addStat('class-align', textStat);
        }
        addStat(`class-${clazz}`, textStat);
      });
    }
  }
  return JSON.parse(JSON.stringify(dict));
}

// json 统计：各类型节点数量，各类型试题数量，叶子试题数量，非叶子试题数量
export function statJson(elements: TJsonNode[]) {
  const dict = defaultDict(() => defaultDict(0));

  function addSubStat(children: TJsonNode[], key: string) {
    children.forEach((child) => {
      dict[key]['children'] = dict[key]['children'] || defaultDict(0);
      const stat = dict[key]['children'];
      stat['count'] += 1;
      stat[child.node_type] += 1;
      if (child.node_type === 'question') {
        // 兼容老板json
        const qType = child.question_type as string === 'option' ? 'choice' : child.question_type;
        const _key = `${child.node_type}_${qType}`;
        stat[_key] += 1;
      }
    });
  }

  for (const { node, parent } of iterateNode(elements)) {
    // 节点统计
    const treeNodeType = checkTreeNodeType(node, parent);
    const leafType = node.children.length ? 'non_leaf' : 'leaf';
    dict['info'][node.node_type] += 1;
    dict['info'][treeNodeType] += 1;
    dict['info'][leafType] += 1;
    dict['info'][`level_${node.node_level}`] += 1;
    if (node.errorInfo) {
      dict['info']['error_node'] += 1;
    }
    addSubStat(node.children, node.node_type);
    if (node.node_type === 'question' || node.node_type === 'chapter') {
      // 试题、章节节点统计
      const sameParent = parent && parent.node_type === node.node_type ? parent : null;
      const _treeNodeType = checkTreeNodeType(node, sameParent);
      const _leafType = node.children.length ? 'non_leaf' : 'leaf';
      dict[node.node_type].count += 1;
      dict[node.node_type][_treeNodeType] += 1;
      dict[node.node_type][_leafType] += 1;
      dict[node.node_type][`level_${node.content.level || 0}`] += 1;
      // 试题题型统计
      if (node.node_type === 'question') {
        // 兼容老板json
        const qType = node.question_type as string === 'option' ? 'choice' : node.question_type;
        const key = `${node.node_type}_${qType}`;
        dict[key].count += 1;
        dict[key][_treeNodeType] += 1;
        dict[key][_leafType] += 1;
        const realLeaf = !node.children.length && node.content.level > 1; // 真小题
        if (realLeaf) {
          dict[node.node_type]['real_leaf'] += 1;
          dict[key]['real_leaf'] += 1;
        }
        dict[key][`level_${node.content.level || 0}`] += 1;
        addSubStat(node.children, key);
      }
    }
  }
  return JSON.parse(JSON.stringify(dict));
}
