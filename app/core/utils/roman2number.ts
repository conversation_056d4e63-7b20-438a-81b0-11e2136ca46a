type TCharNumMap = { [c: string]: number };

const values: TCharNumMap = { M: 1000, D: 500, C: 100, L: 50, X: 10, V: 5, I: 1 };

export function roman2number(romanStr: string) {
  let result = 0;
  let previous = 0;
  for (let i = romanStr.length - 1; i >= 0; i -= 1) {
    const char = romanStr[i];
    const current = values[char];
    if (current >= previous) {
      result += current;
    } else {
      result -= current;
    }
    previous = current;
  }
  return result;
}
