/**
 * @file 自定义redis lua命令
 * <AUTHOR>
 * @desc 默认会读取./lua 下的所有lua文件，请遵从此命名规范{commandName}.{numberOfKeys}.lua
 */
'use strict';
import { Application } from 'egg';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const commandDir = path.join(__dirname, './lua');
export default async(app: Application) => {
  const { redis } = app;
  const files = await promisify(fs.readdir)(commandDir);
  for (const filename of files) {
    if (!/^[^.]+\.[0-9]+\.lua$/i.test(filename)) {
      return;
    }
    const [command, numberOfKeys] = filename.split('.');
    const script = await promisify(fs.readFile)(`${commandDir}/${filename}`, 'utf8');
    redis.defineCommand(command, {
      numberOfKeys: Number(numberOfKeys),
      lua: script,
    });
  }
};
