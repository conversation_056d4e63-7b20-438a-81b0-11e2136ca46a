/**
 * @file 账户权限验证中间件
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';
import baseError from '../../core/base/baseError';

export default (): any => {
  return async(ctx: Context, next: any) => {
    const { service } = ctx;
    const { userId } = ctx.data;
    // 所有的客户端路由接口都必须依赖此参数
    const { appKey } = ctx.input;
    if (!appKey) {
      return ctx.body = baseError.paramsError('appKey参数错误');
    }
    const [client, appl] = await Promise.all([
      service.client.base.getOne({ where: { userId, isActive: true } }),
      service.appl.getOneByUc({ where: { userId, appKey } })
    ]);
    if (!client) {
      return ctx.body = baseError.dataNotExistError('用户不存在');
    }
    if (!appl) {
      return ctx.body = baseError.dataNotExistError('应用不存在');
    }
    ctx.data = Object.assign(ctx.data || {}, { appl, appKey, client });
    await next();
  };
};
