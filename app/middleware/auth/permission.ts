/**
 * @file 账户权限验证中间件
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';
import baseError from '../../core/base/baseError';

export default (): any => {
  return async(ctx: Context, next: any) => {
    const { userId } = ctx.data;
    const { headers = {} } = ctx.request;
    const name = `${ctx.request.path}`;
    // trick 逻辑，绕开权限验证
    if (headers['x-request-from'] === 'hexin') {
      return await next();
    }
    try {
      const hasGranted = await ctx.service.themis.grantValidateByUserId(userId, name);
      if (!hasGranted) {
        return ctx.body = baseError.permissionError('无访问权限');
      }
    } catch (e) {
      ctx.logger.info('接口接入permission.catch', e);
    }
    await next();
  };
};
