/**
 * @file 应用是否存在验证
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';
import * as sha1 from 'sha1';
import baseError from '../../core/base/baseError';

export default (): any => {
  return async(ctx: Context, next: any) => {
    const { service, logger } = ctx;
    const { headers, url } = ctx.request;
    const { input } = ctx;
    logger.info(`headers: ${JSON.stringify(ctx.request)}`);
    if (headers['x-request-from'] === 'hexin' || headers['request-from'] === 'hexin' || (input && input['request-from'] === 'hexin') || url === '/api/open/task/updateV2') {
      await next();
      return;
    }
    const appKey: string | undefined = headers['app-key'];
    const nonstr: string | undefined = headers['app-nonstr'];
    const signature: string | undefined = headers['app-signature'];
    const timestamp = Number(headers['app-timestamp']);
    if (!appKey || !nonstr || !signature || !timestamp || (Number(new Date()) - timestamp) > 5 * 60 * 1000) {
      return ctx.body = baseError.paramsError('签名参数有误');
    }
    const appl = await service.appl.getOneByUc({ where: { appKey, isActive: true } });
    if (!appl || signature !== sha1(`${appKey}:${nonstr}:${appl.appSecret}:${timestamp}`)) {
      if (!appl) {
        logger.info(`app not exists : ${appKey}`);
      } else {
        logger.info(`sign : ${appKey}:${nonstr}:${appl.appSecret}:${timestamp}, ${sha1(`${appKey}:${nonstr}:${appl.appSecret}:${timestamp}`)}, now : ${Number(new Date())}`);
      }
      if (ctx?.app?.env !== 'dev') {
        return ctx.body = baseError.dataNotExistError('应用不存在或者签名有误');
      }
    }
    ctx.data = Object.assign(ctx.data || {}, { appl }, { appKey });
    await next();
  };
};
