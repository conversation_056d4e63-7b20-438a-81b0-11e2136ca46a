/**
 * @file 账户登录验证中间件
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';
import baseError from '../../core/base/baseError';

export default (): any => {
  return async(ctx: Context, next: any) => {
    /*
     * trick 逻辑，绕开权限验证
     * @todo：赋值管理员账户 uid
     * const { headers = {} } = ctx.request;
     * if (headers['x-request-from'] === 'hexin') {
     *   return await next();
     * }
     */
    const cookie: string | undefined = ctx.request.header.cookie;
    if (!cookie) {
      return ctx.body = baseError.loginError('用户未登录');
    }
    const data = await ctx.service.user.getLoginStatus(cookie);
    if (!data) {
      return ctx.body = baseError.dataAlreadyExistError('用户服务异常，请重试');
    }
    const { isLogin, userId } = data;
    if (!isLogin) {
      return ctx.body = baseError.loginError('用户未登录');
    }
    ctx.data = { ...(ctx.data || {}), cookie, userId };
    await next();
  };
};
