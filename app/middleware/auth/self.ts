/**
 * @file 是否是内部账户（此验证必须在auth.appl之后）
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';
import baseError from '../../core/base/baseError';

export default (): any => {
  return async(ctx: Context, next: any) => {
    const { appKey } = ctx.data || {};
    const { hexinApps } = ctx.service.appl;
    const { headers } = ctx.request;
    if (headers['request-from'] === 'hexin' || headers['x-request-from'] === 'hexin') {
      await next();
      return;
    }
    if (!hexinApps.includes(appKey)) {
      return ctx.body = baseError.permissionError();
    }
    await next();
  };
};
