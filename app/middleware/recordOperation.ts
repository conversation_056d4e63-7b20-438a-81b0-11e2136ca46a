/**
 * @file 记录必要的操作记录
 * <AUTHOR>
 */
'use strict';
import { Context } from 'egg';

export default (): any => {
  return async(ctx: Context, next: any) => {
    // 需要兼容 open api 的情况
    await next();
    ctx.runInBackground(async() => {
      await ctx.service.operation.create({
        userId: ctx.data?.userId || ctx.data?.appKey || 'private',
        path: ctx.request.path,
        inputData: JSON.stringify(ctx.input),
      });
    });
  };
};
