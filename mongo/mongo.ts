import { MongoClient } from 'mongodb';

export async function createMongo(config) {
  const client = await MongoClient.connect(config.uri, { useNewUrlParser: true, useUnifiedTopology: true });
  const db = client.db(config.db);
  return { client, db };
}

export async function createMongoIndex(db) {
  await db.collection('stat').createIndex({ task_id: 1, type: 1 }, { unique: true });
  await db.collection('stat').createIndex({ task_id: 1, user_id: 1 });
  await db.collection('stat').createIndex({ type: 1 });
  await db.collection('stat').createIndex({ user_id: 1 });
  await db.collection('stat').createIndex({ stat_at: 1 });
  await db.collection('stat').createIndex({ subject: 1 });
  await db.collection('error_node_stat').createIndex({ task_id: 1, user_id: 1, action: 1 });
  await db.collection('error_node_stat').createIndex({ user_id: 1, action: 1 });
  await db.collection('error_node_stat').createIndex({ action: 1 });
  await db.collection('error_node_stat').createIndex({ timestamp: 1 });
  await db.collection('error_node_stat').createIndex({ count: 1 });
}
