/**
 * @file 测试环境配置文件
 * <AUTHOR>
 */
'use strict';
import EnvConfig from '../typings/config/default';

const config: EnvConfig.CustomEnv = {
  /*
   * 绕开 mac 没有权限写 /data/logs/xdoc 问题
   * logger: {
   *   dir: './logs/xdoc',
   *   consoleLevel: 'INFO',
   * },
   * redis配置
   */
  redis: {
    client: {
      port: 6379,
      host: 'redis.hexin.im',
      password: '',
      db: 22,
    },
  },
  // mysql 配置
  sequelize: {
    dialect: 'mysql',
    database: 'xdoc',
    username: 'root',
    password: 'sigmalove',
    host: 'mysql.hexin.im',
    port: 3306,
    logging: true,
    timezone: '+08:00',
    pool: {
      max: 100,
      min: 0,
      idle: 10000,
    },
    define: {
      engine: 'InnoDB',
      timestamps: false,
      createdAt: 'createTime',
      updatedAt: 'updateTime',
      charset: 'utf8',
    },
  },
  // mongodb配置
  mongo: {
    client: {
      uri: 'mongodb://mongo.hexin.im',
      db: 'xdoc',
    },
  },
  // 阿里云的oss的存储配置
  aliOss: {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'xdoc-test',
    host: 'https://xdoc-test.oss-cn-shanghai.aliyuncs.com/',
    privateHost: 'https://xdoc-test.oss-cn-shanghai.aliyuncs.com/',
  },
  // b端账户系统
  uc: {
    api: 'http://uc.hexin.im/api',
    appKey: 'a5a421eaa0c3f453680bee38',
    appSecret: 'cb7be849afc81fd34dabcfa16c8e7585bda19141',
  },
  // B端权限系统
  themis: {
    appKey: 'themis540797f857',
    appSecret: '7ac171c6ef60c378e60fa0e7e48ed8ec',
    api: 'http://themis.hexin.im/api/open',
  },
  // 唯一id的生成服务
  idOnly: {
    api: 'http://only.hexin.im/api',
    taskId: {
      appKey: 'xdoc_task_id',
      appSecret: '955de1a1cc57ab8f6352a9d7a6ac9a3b',
      maxSizePerTimes: 1000,
    },
  },
  // 新内容服务
  content: { api: 'http://content-server.hexin.im/api/content' },
  // rpc服务
  remoteRpcServer: {
    imageCrop: {
      host: 'tcp://************:20518',
      method: 'crop_img',
      timeout: 20,
    },
    imageCropRerun: {
      host: 'tcp://*************:8888',
      method: 'crop_img',
      timeout: 20,
    },
  },
  wordRbs: { initQueueUrl: 'http://rbs-word.hexin.im/api/proxy/word/init' },
  taskV2Rbs: {
    initQueueWordUrl: 'http://rbs-founder.hexin.im/api/proxy/word/init',
    initQueueFbdUrl: 'http://rbs-founder.hexin.im/api/proxy/fbd/init',
    removeQueueUrl: 'http://rbs-founder.hexin.im/api/proxy/task/remove',
  },
  python: { bin: 'python3.6' },
  // json预处理
  jsonPreprocess: { api: 'http://jsonpreprocess.hexinedu.com' },
  workOrder: {
    bucket: 'hexin-worksheet',
    api: 'http://worksheet.hexin.im',
    fileSysPath: '/files-dev',
  },
  // solr服务
  solr: {
    host: 'http://solr.hexinedu.com',
    pdfSearch: '/solr/pdf_search/select',
  },
  ngrok: { callbackUrl: 'http://xdoc.open.hexinedu.com' },
};

export default config;
