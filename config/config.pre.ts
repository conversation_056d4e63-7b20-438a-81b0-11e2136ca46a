/**
 * @file 正式环境配置文件
 * <AUTHOR>
 */

/**
 * 2022.9.25 @linwentao
 * 服务端把 .ali.hexin.im 替换为 .hexinedu.com
 * 然后做下备注，迁移完统一做 k8s 间的内网通信，然后再改回来
 */
'use strict';

import EnvConfig from '../typings/config/default';

const config: EnvConfig.CustomEnv = {
  logger: {
    dir: '/data/logs/xdoc',
    consoleLevel: 'INFO',
  },
  // redis 配置
  redis: {
    client: {
      port: 6379,
      host: 'r-uf6c1chzshionnoflp.redis.rds.aliyuncs.com',
      password: '',
      db: 9,
    },
  },
  // mysql 配置
  sequelize: {
    dialect: 'mysql',
    database: 'xdoc',
    username: 'sigma',
    password: 'sigmaLOVE2017',
    host: 'rm-uf68040g28501oyn1rw.mysql.rds.aliyuncs.com',
    port: 3306,
    logging: false,
    timezone: '+08:00',
    pool: {
      max: 100,
      min: 0,
      idle: 10000,
    },
    define: {
      engine: 'InnoDB',
      timestamps: false,
      createdAt: 'createTime',
      updatedAt: 'updateTime',
      charset: 'utf8',
    },
  },
  mongo: {
    client: {
      uri: 'mongodb://root:<EMAIL>:3717,dds-uf6fcc4e461ee5a43.mongodb.rds.aliyuncs.com:3717/admin?replicaSet=mgset-7558683&readPreference=secondaryPreferred',
      db: 'xdoc',
    },
  },
  // 阿里云的 oss 的存储配置
  aliOss: {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'xdoc-stable',
    host: 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/',
    privateHost: 'https://xdoc-stable.oss-cn-shanghai-internal.aliyuncs.com/',
  },
  // b 端账户系统
  uc: {
    // api: 'http://uc.ali.hexin.im/api',
    api: 'http://**************/api',
    apiTask: 'http://uc.hexinedu.com/api',
    appKey: 'a5a421eaa0c3f453680bee38',
    appSecret: 'cb7be849afc81fd34dabcfa16c8e7585bda19141',
  },
  // B 端权限系统
  themis: {
    appKey: 'themis63250c4628',
    appSecret: '4dd2ef63e16e6589584dcb11b73598ed',
    // api: 'http://themis.ali.hexin.im/api/open',
    api: 'http://************/api/open',
    apiTask: 'http://themis.hexinedu.com/api/open',
  },
  // 唯一 id 的生成服务
  idOnly: {
    api: 'http://only.ali.hexin.im/api',
    taskId: {
      appKey: 'xdoc_task_id',
      appSecret: 'd9b4e7098b7517d0149a984de4188e27',
      maxSizePerTimes: 1000,
    },
  },
  // 新内容服务
  content: {
    // apiIm: 'http://content-server.ali.hexin.im/api/content',
    api: 'http://*************/api/content',
    apiTask: 'http://content-server.hexinedu.com/api/content',
  },
  // rpc 服务
  remoteRpcServer: {
    imageCrop: {
      host: 'tcp://*************:80',
      method: 'crop_img',
      timeout: 20,
    },
    imageCropRerun: {
      host: 'tcp://************:8888',
      method: 'crop_img',
      timeout: 20,
    },
  },
  wordRbs: { initQueueUrl: 'http://rbs-word.ali.hexin.im/api/proxy/word/init' },
  taskV2Rbs: {
    initQueueWordUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/word/init',
    initQueueFbdUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/fbd/init',
    removeQueueUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/task/remove',
    initQueueWordUrlNewTask: 'http://rbs-founder.hexinedu.com/api/proxy/task/init',

    initQueueWordUrl: 'http://*************/api/proxy/word/init',
    initQueueFbdUrl: 'http://*************/api/proxy/fbd/init',
    removeQueueUrl: 'http://*************/api/proxy/task/remove',
    initQueueWordUrlNew: 'http://*************/api/proxy/task/init',
  },
  python: { bin: 'python3.6' },
  jsonPreprocess: {
    api: 'http://************',
    apiTask: 'http://jsonpreprocess.hexinedu.com',
  },
  workOrder: {
    bucket: 'hexin-worksheet',
    api: 'http://*************',
    apiTask: 'http://api.hexinedu.com',
    fileSysPath: '/files',
  },
  // solr服务
  solr: {
    host: 'http://*************:8983',
    pdfSearch: '/solr/pdf_search/select',
  },
  ngrok: { callbackUrl: 'http://open.xdoc.hexinedu.com' },
};

export default config;
