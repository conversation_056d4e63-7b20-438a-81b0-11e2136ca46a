/**
 * @file 默认配置文件（环境配置优化此配置）
 * <AUTHOR>
 */

'use strict';
import { EggAppConfig, PowerPartial } from 'egg';
import EnvConfig from '../typings/config/default';
import { BaseError } from '../app/core/base/errors';
// for config.{env}.ts
export type DefaultConfig = PowerPartial<EggAppConfig> & EnvConfig.CustomEnv & EnvConfig.DefaultEnv;
// app special config scheme

const config = {
  // egg schedule 的锁状态
  scheduleLockKey: {
    cleanData: 'clean:data:task:schedule:lock',
    clearLogFile: 'clear:log:file:task:schedule:lock',
    updateImageTask: 'update:image:task:schedule:lock',
    updateImageTaskColumnProcessor: 'update:image:task:column:schedule:lock',
    updateImageTaskStructProcessor: 'update:image:task:struct:schedule:lock',
    updateDynamicIpPoolTask: 'update:dynamic:ip:pool:task:schedule:lock',
    updatePlanTaskCounts: 'update:plan:task:counts:schedule:lock',
    updatePlanGroupTaskCounts: 'update:plan:group:task:counts:schedule:lock',
    preCropCheckImage: 'xdoc:lock:pre_crop_check_image',
    preCropImage: 'xdoc:lock:pre_crop_image',
    updateTaskCountdown: 'xdoc:lock:task:update_countdown',
    checkPdf2Img: 'xdoc:lock:check_pdf2img',
    publishProject: 'xdoc:lock:publish_project',
    publishTask: 'xdoc:lock:publish_task',
    exportData: 'xdoc:lock:export_data',
    generateImages: 'xdoc:lock:generate_images',
    applyProject: 'xdoc:lock:apply_project',
    watchInitializedTask: 'xdoc:lock:watch_initialized_task',
    timeoutMonitor: 'xdoc:lock:timeout_monitor',
  },
  logger: {
    dir: '/data/logs/xdoc',
    consoleLevel: 'INFO',
  },
} as DefaultConfig;

config.cluster = { listen: { port: 8020 } };
config.keys = 'LKHLKLHKLH^&*453699!!@@##SADASDIIsadol)';
config.onerror = {
  json(err, ctx) {
    /*
     * 在此处定义针对所有响应类型的错误处理方法
     * 注意，定义了 config.all 之后，其他错误处理方法不会再生效
     * 所有其他错误都status 1005 返回
     * 其他的返回状态码不要覆盖1005
     */
    if (err instanceof BaseError) {
      ctx.body = err.asBaseError();
    } else {
      ctx.body = {
        status: 1005,
        statusInfo: err.message || '未知错误',
      };
    }
    ctx.status = 200;
  },
  html(err, ctx) {
    // html hander
    if (err instanceof BaseError) {
      ctx.body = JSON.stringify(err.asBaseError());
    } else {
      ctx.body = JSON.stringify({
        status: 1005,
        statusInfo: err.message || '未知错误',
      });
    }
    ctx.status = 200;
  },
};
config.middleware = ['inputParser', 'responseTime'];
config.security = {
  xframe: { enable: false },
  csrf: { enable: false },
};
config.cors = {
  origin:'*',
  allowMethods: 'GET',
};
config.multipart = {
  fieldSize: 2 * 1024 * 1024,
  fields: 30,
  mode: 'file',
};
config.bodyParser = {
  jsonLimit: '500mb',
  formLimit: '500mb',
  enable: true,
  encoding: 'utf8',
  strict: true,
  queryString: {
    arrayLimit: 5000,
    depth: 20,
    parameterLimit: 5000,
  },
};

export default config;
