# 项目依赖

项目的依赖在 [package.json](mdc:package.json) 文件中定义。

## 主要依赖

- `egg`：基础框架
- `egg-redis`：Redis 插件
- `egg-sequelize`：Sequelize ORM 插件
- `mongodb`：MongoDB 驱动
- `ali-oss`：阿里云对象存储
- `zerorpc`：ZeroRPC 客户端
- `docx`：文档处理库
- `sharp`：图像处理库

## 开发依赖

- `typescript`：TypeScript 支持
- `egg-ts-helper`：Egg.js TypeScript 助手
- `eslint`/`tslint`：代码质量检查工具

## 自定义依赖

部分依赖来自私有仓库：

- `docx`：`git+ssh://*************:sigmaai/docx.git`
- `eslint-config-hexin`：`git+ssh://*************:sigmaai/eslint-config-hexin.git`