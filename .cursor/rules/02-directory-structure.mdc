# 项目目录结构

本项目遵循 Egg.js 框架的标准目录结构，主要包含以下目录：

## 应用代码

- [app/](mdc:app/) - 应用代码目录
  - [controller/](mdc:app/controller/) - 控制器目录，处理 HTTP 请求
  - [service/](mdc:app/service/) - 服务目录，包含业务逻辑
  - [model/](mdc:app/model/) - 数据模型目录
  - [middleware/](mdc:app/middleware/) - 中间件目录
  - [extend/](mdc:app/extend/) - 框架扩展目录
  - [schedule/](mdc:app/schedule/) - 定时任务目录
  - [router/](mdc:app/router/) - 路由配置目录
  - [public/](mdc:app/public/) - 静态资源目录
  - [core/](mdc:app/core/) - 核心功能目录

## 配置

- [config/](mdc:config/) - 配置文件目录
  - [config.default.ts](mdc:config/config.default.ts) - 默认配置
  - [config.local.ts](mdc:config/config.local.ts) - 本地开发配置
  - [config.dev.ts](mdc:config/config.dev.ts) - 开发环境配置
  - [config.pre.ts](mdc:config/config.pre.ts) - 预发布环境配置
  - [config.prod.ts](mdc:config/config.prod.ts) - 生产环境配置
  - [plugin.ts](mdc:config/plugin.ts) - 插件配置

## 数据库

- [mongo/](mdc:mongo/) - MongoDB 相关代码
  - [mongo.ts](mdc:mongo/mongo.ts) - MongoDB 连接和初始化