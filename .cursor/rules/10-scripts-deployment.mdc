# 脚本和部署

项目提供了多种脚本用于开发、测试和部署，这些脚本在 [package.json](mdc:package.json) 的 `scripts` 部分定义。

## 常用脚本

- `npm run dev`：本地开发模式启动
- `npm run tsc`：编译 TypeScript 代码
- `npm run lint`：运行代码质量检查
- `npm run start`：生产环境启动
- `npm run restart`：重启生产环境

## 环境特定脚本

- `npm run start-local`：本地环境启动
- `npm run start-dev`：开发环境启动
- `npm run start-pre`：预发布环境启动
- `npm run start-prod`：生产环境启动

## 数据库迁移脚本

- `npm run migrate:init`：初始化迁移
- `npm run migrate:new`：创建新迁移
- `npm run migrate:up`：执行迁移
- `npm run migrate:down`：回滚迁移

## 部署

项目使用 Docker 容器化部署，Dockerfile 位于项目根目录：[Dockerfile](mdc:Dockerfile)。

自动化部署脚本位于 `run/` 目录中，使用 CI/CD 工具（如 TravisCI、AppVeyor）进行自动化构建和部署。