# 数据库和缓存

本项目使用 MongoDB 作为主要数据库，Redis 作为缓存。

## MongoDB

MongoDB 连接和初始化逻辑在 [mongo/mongo.ts](mdc:mongo/mongo.ts) 文件中定义。

应用启动时，会在 [app.ts](mdc:app.ts) 中调用 `createMongo` 和 `createMongoIndex` 函数来初始化 MongoDB 连接并创建索引。

## Redis

Redis 用于缓存和分布式锁，配置在各环境配置文件中定义。

自定义 Redis 命令加载逻辑在 [app/core/loadRedisCommand.ts](mdc:app/core/loadRedisCommand.ts) 文件中。

应用启动时，会清除之前的定时任务锁：

```typescript
// 删除定时器的单一串行锁
const commandList: string[][] = [];
Object.keys(config.scheduleLockKey).forEach((key) => {
  commandList.push(['del', config.scheduleLockKey[key]]);
});
await redis.pipeline(commandList).exec();
```