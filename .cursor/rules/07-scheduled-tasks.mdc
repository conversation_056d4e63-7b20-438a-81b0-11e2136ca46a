# 定时任务

项目中的定时任务位于 [app/schedule/](mdc:app/schedule/) 目录，遵循 Egg.js 框架的定时任务规范。

## 定时任务配置

每个定时任务文件导出一个对象，包含：

- `schedule` 属性：定义任务的执行时间和方式
  - `interval` 类型：按时间间隔执行，如 '1m'、'1h'、'1d'
  - `cron` 类型：按 cron 表达式执行
- `disable` 属性：是否禁用任务
- `immediate` 属性：是否在应用启动时立即执行一次
- `subscribe` 方法：订阅其他任务
- `task` 方法：任务的实际执行逻辑

## 定时任务锁

为了防止定时任务在分布式环境中重复执行，项目使用 Redis 实现了分布式锁机制。

锁的键名在配置文件的 `scheduleLockKey` 属性中定义，应用启动时会清除这些锁。