# 配置和环境

本项目支持多环境配置，遵循 Egg.js 的配置加载规则。

## 环境配置文件

- [config.default.ts](mdc:config/config.default.ts) - 默认配置，所有环境都会加载
- [config.local.ts](mdc:config/config.local.ts) - 本地开发环境配置
- [config.dev.ts](mdc:config/config.dev.ts) - 开发环境配置
- [config.pre.ts](mdc:config/config.pre.ts) - 预发布环境配置
- [config.prod.ts](mdc:config/config.prod.ts) - 生产环境配置

## 插件配置

- [plugin.ts](mdc:config/plugin.ts) - 插件配置，定义项目使用的 Egg.js 插件

## 环境变量

项目使用 `EGG_SERVER_ENV` 环境变量来决定加载哪个环境的配置文件。启动命令在 [package.json](mdc:package.json) 的 scripts 部分定义。