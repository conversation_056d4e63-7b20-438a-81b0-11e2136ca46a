# 服务层

项目的业务逻辑主要在服务层实现，位于 [app/service/](mdc:app/service/) 目录。

## 核心服务

- [image.ts](mdc:app/service/image.ts) - 图像处理服务
- [book.ts](mdc:app/service/book.ts) - 书籍/文档处理服务
- [oss.ts](mdc:app/service/oss.ts) - 对象存储服务
- [user.ts](mdc:app/service/user.ts) - 用户相关服务

## 专项服务

- [task/](mdc:app/service/task/) - 任务处理相关服务
- [project/](mdc:app/service/project/) - 项目相关服务
- [plan/](mdc:app/service/plan/) - 计划相关服务

## 工具服务

- [formula.ts](mdc:app/service/formula.ts) - 公式处理服务
- [archiver.ts](mdc:app/service/archiver.ts) - 文件归档服务