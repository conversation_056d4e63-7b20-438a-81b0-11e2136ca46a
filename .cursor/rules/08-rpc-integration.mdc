# RPC 远程调用

本项目使用 ZeroRPC 进行远程过程调用，主要用于图像处理等功能。

## ZeroRPC 配置

ZeroRPC 的配置位于各环境配置文件中的 `remoteRpcServer` 字段，例如：

```typescript
config.remoteRpcServer = {
  imageCrop: {
    host: 'tcp://127.0.0.1:4242',
    method: 'crop_image',
  },
};
```

## 连接测试

应用启动时会测试 ZeroRPC 连接是否可用，实现在 [app.ts](mdc:app.ts) 文件的 `testZerorpcAvailability` 函数中：

```typescript
async function testZerorpcAvailability(app: Application) {
  const { config, logger } = app;

  if (!config.remoteRpcServer || !config.remoteRpcServer.imageCrop) {
    logger.warn('未找到 zerorpc 配置，跳过可用性测试');
    return;
  }

  // ... 连接测试逻辑
}
```

## 使用方式

在服务层中使用 ZeroRPC 客户端调用远程服务，例如图像裁剪功能。