# 控制器

控制器处理 HTTP 请求，位于 [app/controller/](mdc:app/controller/) 目录。

## 控制器分类

- [admin/](mdc:app/controller/admin/) - 管理员接口
- [client/](mdc:app/controller/client/) - 客户端接口
- [open/](mdc:app/controller/open/) - 开放接口
- [godmode/](mdc:app/controller/godmode/) - 超级管理员接口

## 路由

路由定义位于 [app/router/](mdc:app/router/) 目录，将 URL 路径映射到控制器方法。

所有路由都有 `/api` 前缀，这在 [app.ts](mdc:app.ts) 文件中通过 `app.router.prefix('/api')` 设置。