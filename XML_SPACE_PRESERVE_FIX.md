# xml:space="preserve" 属性保持修复

## 问题描述

当插入批注时，会创建新的 `w:t` 元素来分割原始文本。如果这些新创建的 `w:t` 元素没有 `xml:space="preserve"` 属性，那么其中的空格就会被 Word 忽略，导致文档格式被破坏。

**问题示例：**
```xml
<!-- 原始元素 -->
<w:t xml:space="preserve">这是一个 关键词 的测试</w:t>

<!-- 插入批注后，如果新元素没有 xml:space="preserve" -->
<w:t>这是一个 </w:t>                    <!-- 空格可能被忽略 -->
<w:commentRangeStart w:id="1"/>
<w:t>关键词</w:t>
<w:commentRangeEnd w:id="1"/>
<w:t> 的测试</w:t>                      <!-- 空格可能被忽略 -->
```

## 解决方案

### 增强 `preserveTextElementAttributes` 方法

修改该方法以确保：
1. **继承原始属性**：复制原始元素的所有属性
2. **智能添加属性**：检测文本内容，自动添加 `xml:space="preserve"` 属性

```typescript
private preserveTextElementAttributes(originalTextElement: Element, newTextElement: Element): void {
  // 确保新元素有 attributes 对象
  if (!newTextElement.attributes) {
    newTextElement.attributes = {};
  }

  // 如果原始元素有属性，复制所有属性
  if (originalTextElement.attributes) {
    Object.keys(originalTextElement.attributes).forEach((key) => {
      if (!newTextElement.attributes[key]) {
        newTextElement.attributes[key] = originalTextElement.attributes[key];
      }
    });
  }

  // 检查文本内容是否包含空格，如果包含则确保有 xml:space="preserve" 属性
  if (newTextElement.elements && newTextElement.elements[0] && newTextElement.elements[0].text) {
    const textContent = newTextElement.elements[0].text;
    if (typeof textContent === 'string') {
      // 检查是否包含空格字符或空格标记
      if (/[\s\u3000\u00A0]|§SPACE§|§TAB§|§FULLSPACE§|§NBSP§/.test(textContent)) {
        newTextElement.attributes['xml:space'] = 'preserve';
      }
    }
  }
}
```

## 检测的空格类型

| 空格类型 | 检测模式 | 说明 |
|---------|---------|------|
| 普通空格 | `\s` | 包括空格、制表符、换行符等 |
| 全角空格 | `\u3000` | 中文全角空格 |
| 不间断空格 | `\u00A0` | HTML `&nbsp;` 对应的字符 |
| 空格标记 | `§SPACE§` | markSpaces 处理后的普通空格标记 |
| 制表符标记 | `§TAB§` | markSpaces 处理后的制表符标记 |
| 全角空格标记 | `§FULLSPACE§` | markSpaces 处理后的全角空格标记 |
| 不间断空格标记 | `§NBSP§` | markSpaces 处理后的不间断空格标记 |

## 修复效果

### 修复前
```xml
<!-- 可能导致空格丢失 -->
<w:t>这是一个 </w:t>
<w:commentRangeStart w:id="1"/>
<w:t>关键词</w:t>
<w:commentRangeEnd w:id="1"/>
<w:t> 的测试</w:t>
```

### 修复后
```xml
<!-- 确保空格被保持 -->
<w:t xml:space="preserve">这是一个 </w:t>
<w:commentRangeStart w:id="1"/>
<w:t xml:space="preserve">关键词</w:t>
<w:commentRangeEnd w:id="1"/>
<w:t xml:space="preserve"> 的测试</w:t>
```

## 测试验证

通过测试验证了以下场景：
- ✅ **原始元素有属性，新元素包含空格**：正确继承并添加 `xml:space="preserve"`
- ✅ **原始元素无属性，新元素包含空格**：自动添加 `xml:space="preserve"`
- ✅ **新元素包含空格标记**：正确识别并添加属性
- ✅ **新元素包含制表符标记**：正确识别并添加属性
- ✅ **新元素包含全角空格标记**：正确识别并添加属性
- ✅ **文本分割场景**：所有包含空格的分割部分都有正确的属性

## 工作原理

### 1. 属性继承
- 复制原始 `w:t` 元素的所有属性到新创建的元素
- 保持原有的格式设置和样式

### 2. 智能检测
- 分析新元素的文本内容
- 检测各种类型的空格字符和空格标记
- 自动添加必要的 `xml:space="preserve"` 属性

### 3. 兼容性保证
- 与现有的 `markSpaces`/`restoreSpaces` 机制完全兼容
- 不影响不包含空格的文本元素
- 向后兼容所有现有功能

## 优势

1. **自动化**：无需手动指定，自动检测和处理
2. **全面性**：支持所有类型的空格和空格标记
3. **安全性**：只在需要时添加属性，不会过度处理
4. **兼容性**：与现有系统完全兼容

现在插入批注时，所有新创建的 `w:t` 元素都会正确保持 `xml:space="preserve"` 属性，确保文档中的空格不会被忽略！
