// 新接口
import * as redis from 'ioredis';
import * as sequelize from 'sequelize';

declare namespace EnvConfig {

  type RpcServerOption = {
    host: string;
    method: string;
    timeout: number;
  };

  type idOnlyOption = {
    appKey: string;
    appSecret: string;
    maxSizePerTimes: number;
  };

  interface DefaultEnv {
    scheduleLockKey: {
      clearLogFile: string;
      updateImageTask: string;
      updateImageTaskColumnProcessor: string;
      updateImageTaskStructProcessor: string;
      updateDynamicIpPoolTask: string;
      updatePlanTaskCounts: string;
      updatePlanGroupTaskCounts: string;
      preCropCheckImage: string;
      preCropImage: string;
      cleanData: string;
      updateTaskCountdown: string;
      checkPdf2Img: string;
      publishProject: string;
      publishTask: string;
      exportData: string;
      generateImages: string;
      applyProject: string;
      createImagesTask: string;
      watchInitializedTask: string;
      timeoutMonitor: string;
    };
  }

  interface CustomEnv {
    redis: {
      client: redis.RedisOptions;
    };
    sequelize: sequelize.Options;
    mongo: {
      client: {
        uri: string,
        db: string
      }
    };
    aliOss: {
      region: string;
      accessKeyId: string;
      accessKeySecret: string;
      bucket: string;
      host: string;
      privateHost: string;
    };
    uc: {
      api: string;
      apiTask?: string;
      appKey: string;
      appSecret: string;
    };
    themis: {
      appKey: string;
      appSecret: string;
      api: string;
      apiTask?: string;
    };
    idOnly: {
      api: string;
      taskId: idOnlyOption;
    };
    content: {
      api: string;
      apiTask?: string;
    };
    remoteRpcServer: {
      imageCrop: RpcServerOption;
      imageCropRerun: RpcServerOption;
    };
    wordRbs: {
      initQueueUrl: string;
    };
    taskV2Rbs: {
      initQueueWordUrl: string;
      initQueueFbdUrl: string;
      removeQueueUrl: string;
      initQueueWordUrlNew?: string;
      initQueueWordUrlTask?: string;
      initQueueFbdUrlTask?: string;
      removeQueueUrlTask?: string;
      initQueueWordUrlNewTask?: string;
    };
    logger?: {
      dir: string,
      consoleLevel: string,
    };
    python: {
      bin: string;
    };
    jsonPreprocess: {
      api: string;
      apiTask?: string;
    };
    workOrder: {
      bucket: string;
      api: string;
      apiTask?: string;
      fileSysPath: string;
    };
    solr: {
      host: string;
      pdfSearch: string;
    };
    ngrok: {
      callbackUrl: string,
    };
  }
}

export default EnvConfig;
