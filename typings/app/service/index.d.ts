// This file is created by egg-ts-helper@1.25.2
// Do not modify this file!!!!!!!!!

import 'egg';
import ExportAi from '../../../app/service/ai';
import ExportAppl from '../../../app/service/appl';
import ExportArchiver from '../../../app/service/archiver';
import ExportAuthCode from '../../../app/service/authCode';
import ExportBook from '../../../app/service/book';
import ExportBookHistory from '../../../app/service/bookHistory';
import ExportColumn from '../../../app/service/column';
import ExportContent from '../../../app/service/content';
import ExportFormula from '../../../app/service/formula';
import ExportImage from '../../../app/service/image';
import ExportOperation from '../../../app/service/operation';
import ExportOss from '../../../app/service/oss';
import ExportRbs from '../../../app/service/rbs';
import ExportRobot from '../../../app/service/robot';
import ExportSourceImage from '../../../app/service/sourceImage';
import ExportStatV2 from '../../../app/service/statV2';
import ExportSubjectScript from '../../../app/service/subjectScript';
import ExportSubjectScriptConfig from '../../../app/service/subjectScriptConfig';
import ExportThemis from '../../../app/service/themis';
import ExportTimeLimitUserSubject from '../../../app/service/timeLimitUserSubject';
import ExportTimeout from '../../../app/service/timeout';
import ExportUser from '../../../app/service/user';
import ExportWorkSheet from '../../../app/service/workSheet';
import ExportCheckHtmlTry from '../../../app/service/checkHtml/try';
import ExportClientBase from '../../../app/service/client/base';
import ExportClientMeta from '../../../app/service/client/meta';
import ExportPlanBase from '../../../app/service/plan/base';
import ExportPlanFlow from '../../../app/service/plan/flow';
import ExportPlanGroup from '../../../app/service/plan/group';
import ExportPlanTask from '../../../app/service/plan/task';
import ExportPlanUser from '../../../app/service/plan/user';
import ExportProjectBase from '../../../app/service/project/base';
import ExportProjectHistory from '../../../app/service/project/history';
import ExportProjectMeta from '../../../app/service/project/meta';
import ExportProxyDynamicIp from '../../../app/service/proxy/dynamicIp';
import ExportProxyPool from '../../../app/service/proxy/pool';
import ExportTaskBase from '../../../app/service/task/base';
import ExportTaskCallback from '../../../app/service/task/callback';
import ExportTaskFile from '../../../app/service/task/file';
import ExportTaskHistory from '../../../app/service/task/history';
import ExportTaskMeta from '../../../app/service/task/meta';
import ExportTaskStat from '../../../app/service/task/stat';
import ExportTaskTaskV2 from '../../../app/service/task/taskV2';

declare module 'egg' {
  interface IService {
    ai: ExportAi;
    appl: ExportAppl;
    archiver: ExportArchiver;
    authCode: ExportAuthCode;
    book: ExportBook;
    bookHistory: ExportBookHistory;
    column: ExportColumn;
    content: ExportContent;
    formula: ExportFormula;
    image: ExportImage;
    operation: ExportOperation;
    oss: ExportOss;
    rbs: ExportRbs;
    robot: ExportRobot;
    sourceImage: ExportSourceImage;
    statV2: ExportStatV2;
    subjectScript: ExportSubjectScript;
    subjectScriptConfig: ExportSubjectScriptConfig;
    themis: ExportThemis;
    timeLimitUserSubject: ExportTimeLimitUserSubject;
    timeout: ExportTimeout;
    user: ExportUser;
    workSheet: ExportWorkSheet;
    checkHtml: {
      try: ExportCheckHtmlTry;
    }
    client: {
      base: ExportClientBase;
      meta: ExportClientMeta;
    }
    plan: {
      base: ExportPlanBase;
      flow: ExportPlanFlow;
      group: ExportPlanGroup;
      task: ExportPlanTask;
      user: ExportPlanUser;
    }
    project: {
      base: ExportProjectBase;
      history: ExportProjectHistory;
      meta: ExportProjectMeta;
    }
    proxy: {
      dynamicIp: ExportProxyDynamicIp;
      pool: ExportProxyPool;
    }
    task: {
      base: ExportTaskBase;
      callback: ExportTaskCallback;
      file: ExportTaskFile;
      history: ExportTaskHistory;
      meta: ExportTaskMeta;
      stat: ExportTaskStat;
      taskV2: ExportTaskTaskV2;
    }
  }
}
