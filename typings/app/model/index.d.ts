// This file is created by egg-ts-helper@1.25.2
// Do not modify this file!!!!!!!!!

import 'egg';
import ExportApplication from '../../../app/model/application';
import ExportBook from '../../../app/model/book';
import ExportBookHistory from '../../../app/model/bookHistory';
import ExportClient from '../../../app/model/client';
import ExportClientMeta from '../../../app/model/clientMeta';
import ExportFormula from '../../../app/model/formula';
import ExportImage from '../../../app/model/image';
import ExportOperation from '../../../app/model/operation';
import ExportPlan from '../../../app/model/plan';
import ExportPlanFlow from '../../../app/model/planFlow';
import ExportPlanGroup from '../../../app/model/planGroup';
import ExportPlanTask from '../../../app/model/planTask';
import ExportPlanUser from '../../../app/model/planUser';
import ExportProject from '../../../app/model/project';
import ExportProjectHistory from '../../../app/model/projectHistory';
import ExportProjectMeta from '../../../app/model/projectMeta';
import ExportSourceImage from '../../../app/model/sourceImage';
import ExportSubjectScript from '../../../app/model/subjectScript';
import ExportSubjectScriptConfig from '../../../app/model/subjectScriptConfig';
import ExportTask from '../../../app/model/task';
import ExportTaskFile from '../../../app/model/taskFile';
import ExportTaskHistory from '../../../app/model/taskHistory';
import ExportTaskMeta from '../../../app/model/taskMeta';
import ExportTaskStat from '../../../app/model/taskStat';
import ExportTimeLimitUserSubject from '../../../app/model/timeLimitUserSubject';

declare module 'sequelize' {
  interface Sequelize {
    Application: ReturnType<typeof ExportApplication>;
    Book: ReturnType<typeof ExportBook>;
    BookHistory: ReturnType<typeof ExportBookHistory>;
    Client: ReturnType<typeof ExportClient>;
    ClientMeta: ReturnType<typeof ExportClientMeta>;
    Formula: ReturnType<typeof ExportFormula>;
    Image: ReturnType<typeof ExportImage>;
    Operation: ReturnType<typeof ExportOperation>;
    Plan: ReturnType<typeof ExportPlan>;
    PlanFlow: ReturnType<typeof ExportPlanFlow>;
    PlanGroup: ReturnType<typeof ExportPlanGroup>;
    PlanTask: ReturnType<typeof ExportPlanTask>;
    PlanUser: ReturnType<typeof ExportPlanUser>;
    Project: ReturnType<typeof ExportProject>;
    ProjectHistory: ReturnType<typeof ExportProjectHistory>;
    ProjectMeta: ReturnType<typeof ExportProjectMeta>;
    SourceImage: ReturnType<typeof ExportSourceImage>;
    SubjectScript: ReturnType<typeof ExportSubjectScript>;
    SubjectScriptConfig: ReturnType<typeof ExportSubjectScriptConfig>;
    Task: ReturnType<typeof ExportTask>;
    TaskFile: ReturnType<typeof ExportTaskFile>;
    TaskHistory: ReturnType<typeof ExportTaskHistory>;
    TaskMeta: ReturnType<typeof ExportTaskMeta>;
    TaskStat: ReturnType<typeof ExportTaskStat>;
    TimeLimitUserSubject: ReturnType<typeof ExportTimeLimitUserSubject>;
  }
}
