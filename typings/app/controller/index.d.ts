// This file is created by egg-ts-helper@1.25.2
// Do not modify this file!!!!!!!!!

import 'egg';
import ExportAdminAppl from '../../../app/controller/admin/appl';
import ExportAdminAuthCode from '../../../app/controller/admin/authCode';
import ExportAdminBook from '../../../app/controller/admin/book';
import ExportAdminBookReview from '../../../app/controller/admin/bookReview';
import ExportAdminClient from '../../../app/controller/admin/client';
import ExportAdminColumn from '../../../app/controller/admin/column';
import ExportAdminDevHelper from '../../../app/controller/admin/devHelper';
import ExportAdminFormula from '../../../app/controller/admin/formula';
import ExportAdminImage from '../../../app/controller/admin/image';
import ExportAdminMark from '../../../app/controller/admin/mark';
import ExportAdminPermission from '../../../app/controller/admin/permission';
import ExportAdminProject from '../../../app/controller/admin/project';
import ExportAdminProjectReview from '../../../app/controller/admin/projectReview';
import ExportAdminRelay from '../../../app/controller/admin/relay';
import ExportAdminReview from '../../../app/controller/admin/review';
import ExportAdminRole from '../../../app/controller/admin/role';
import ExportAdminScriptManage from '../../../app/controller/admin/scriptManage';
import ExportAdminSourceImage from '../../../app/controller/admin/sourceImage';
import ExportAdminSplit from '../../../app/controller/admin/split';
import ExportAdminStatV2 from '../../../app/controller/admin/statV2';
import ExportAdminSubjectScript from '../../../app/controller/admin/subjectScript';
import ExportAdminTimeLimitUserSubject from '../../../app/controller/admin/timeLimitUserSubject';
import ExportAdminXkwWordCfg from '../../../app/controller/admin/xkw_word_cfg';
import ExportClientAppl from '../../../app/controller/client/appl';
import ExportClientBase from '../../../app/controller/client/base';
import ExportClientImage from '../../../app/controller/client/image';
import ExportClientProject from '../../../app/controller/client/project';
import ExportClientTask from '../../../app/controller/client/task';
import ExportGodmodeTask from '../../../app/controller/godmode/task';
import ExportOpenAppl from '../../../app/controller/open/appl';
import ExportOpenClient from '../../../app/controller/open/client';
import ExportOpenColumn from '../../../app/controller/open/column';
import ExportOpenDynamicIp from '../../../app/controller/open/dynamicIp';
import ExportOpenFormula from '../../../app/controller/open/formula';
import ExportOpenOther from '../../../app/controller/open/other';
import ExportOpenProject from '../../../app/controller/open/project';
import ExportOpenTask from '../../../app/controller/open/task';
import ExportOpenTaskV2 from '../../../app/controller/open/taskV2';
import ExportOpenTaskV3 from '../../../app/controller/open/taskV3';
import ExportOpenTimeoutExample from '../../../app/controller/open/timeoutExample';
import ExportAdminPlanBase from '../../../app/controller/admin/plan/base';
import ExportAdminPlanFlow from '../../../app/controller/admin/plan/flow';
import ExportAdminPlanGroup from '../../../app/controller/admin/plan/group';
import ExportAdminPlanTask from '../../../app/controller/admin/plan/task';
import ExportAdminPlanUser from '../../../app/controller/admin/plan/user';
import ExportAdminTaskBase from '../../../app/controller/admin/task/base';
import ExportAdminTaskCallback from '../../../app/controller/admin/task/callback';
import ExportAdminTaskFbdTask from '../../../app/controller/admin/task/fbdTask';
import ExportAdminTaskHtmlTask from '../../../app/controller/admin/task/htmlTask';
import ExportAdminTaskLimit from '../../../app/controller/admin/task/limit';
import ExportAdminTaskStat from '../../../app/controller/admin/task/stat';
import ExportAdminTaskTaskV2 from '../../../app/controller/admin/task/taskV2';
import ExportAdminTaskWordTask from '../../../app/controller/admin/task/wordTask';

declare module 'egg' {
  interface IController {
    admin: {
      appl: ExportAdminAppl;
      authCode: ExportAdminAuthCode;
      book: ExportAdminBook;
      bookReview: ExportAdminBookReview;
      client: ExportAdminClient;
      column: ExportAdminColumn;
      devHelper: ExportAdminDevHelper;
      formula: ExportAdminFormula;
      image: ExportAdminImage;
      mark: ExportAdminMark;
      permission: ExportAdminPermission;
      project: ExportAdminProject;
      projectReview: ExportAdminProjectReview;
      relay: ExportAdminRelay;
      review: ExportAdminReview;
      role: ExportAdminRole;
      scriptManage: ExportAdminScriptManage;
      sourceImage: ExportAdminSourceImage;
      split: ExportAdminSplit;
      statV2: ExportAdminStatV2;
      subjectScript: ExportAdminSubjectScript;
      timeLimitUserSubject: ExportAdminTimeLimitUserSubject;
      xkwWordCfg: ExportAdminXkwWordCfg;
      plan: {
        base: ExportAdminPlanBase;
        flow: ExportAdminPlanFlow;
        group: ExportAdminPlanGroup;
        task: ExportAdminPlanTask;
        user: ExportAdminPlanUser;
      }
      task: {
        base: ExportAdminTaskBase;
        callback: ExportAdminTaskCallback;
        fbdTask: ExportAdminTaskFbdTask;
        htmlTask: ExportAdminTaskHtmlTask;
        limit: ExportAdminTaskLimit;
        stat: ExportAdminTaskStat;
        taskV2: ExportAdminTaskTaskV2;
        wordTask: ExportAdminTaskWordTask;
      }
    }
    client: {
      appl: ExportClientAppl;
      base: ExportClientBase;
      image: ExportClientImage;
      project: ExportClientProject;
      task: ExportClientTask;
    }
    godmode: {
      task: ExportGodmodeTask;
    }
    open: {
      appl: ExportOpenAppl;
      client: ExportOpenClient;
      column: ExportOpenColumn;
      dynamicIp: ExportOpenDynamicIp;
      formula: ExportOpenFormula;
      other: ExportOpenOther;
      project: ExportOpenProject;
      task: ExportOpenTask;
      taskV2: ExportOpenTaskV2;
      taskV3: ExportOpenTaskV3;
      timeoutExample: ExportOpenTimeoutExample;
    }
  }
}
