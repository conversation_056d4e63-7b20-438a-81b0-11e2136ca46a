// This file is created by egg-ts-helper@1.25.2
// Do not modify this file!!!!!!!!!

import 'egg';
import ExportInputParser from '../../../app/middleware/inputParser';
import ExportRecordOperation from '../../../app/middleware/recordOperation';
import ExportResponseTime from '../../../app/middleware/responseTime';
import ExportAuthAppl from '../../../app/middleware/auth/appl';
import ExportAuthClient from '../../../app/middleware/auth/client';
import ExportAuthLogin from '../../../app/middleware/auth/login';
import ExportAuthPermission from '../../../app/middleware/auth/permission';
import ExportAuthPrivate from '../../../app/middleware/auth/private';
import ExportAuthSelf from '../../../app/middleware/auth/self';

declare module 'egg' {
  interface IMiddleware {
    inputParser: typeof ExportInputParser;
    recordOperation: typeof ExportRecordOperation;
    responseTime: typeof ExportResponseTime;
    auth: {
      appl: typeof ExportAuthAppl;
      client: typeof ExportAuthClient;
      login: typeof ExportAuthLogin;
      permission: typeof ExportAuthPermission;
      private: typeof ExportAuthPrivate;
      self: typeof ExportAuthSelf;
    }
  }
}
