import * as IORedis from 'ioredis';

// @todo：向ts-helper增加CusCommand的自动生成代码
interface CusCommand {
  isGlobalLock (key: string, callback: (err: Error, res: number) => void): void;

  isGlobalLock (key: string): Promise<number>;

  hasPoolLock (key: string, poolNumber: number, callback: (err: Error, res: number) => void): void;

  hasPoolLock (key: string, poolNumber: number): Promise<number>;

  /**
   * 如果 key 中的值和指定的 val 相同，则删除 key，并返回 1；否则返回 0
   */
  delIfEquals (key: string, val: string): Promise<number>;
}

declare module 'egg' {
  interface Application {
    redis: IORedis.Redis & CusCommand;
    mongo: { client: any, db: any };
  }
}
